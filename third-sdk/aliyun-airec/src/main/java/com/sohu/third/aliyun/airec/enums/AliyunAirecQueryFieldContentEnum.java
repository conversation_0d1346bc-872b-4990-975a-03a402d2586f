package com.sohu.third.aliyun.airec.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 内容行业查询字段
 * 参考文档：https://help.aliyun.com/zh/airec/airec/use-cases/use-the-recommendation-filtering-feature-to-customize-the-filtering-of-feed-streams#bmt1E
 */
@Getter
@AllArgsConstructor
public enum AliyunAirecQueryFieldContentEnum {

    /**
     * 内容id
     */
    ITEM_ID("item_id", "主键id"),
    /**
     * 单值字段
     * 内容的类型
     */
    ITEM_TYPE("item_type","内容的类型"),
    /**
     * 单值字段
     * 类目层级数
     */
    CATEGORY_LEVEL("category_level","类目层级数"),
    /**
     * 单值字段
     * 类目路径
     */
    CATEGORY_PATH("category_path","类目路径"),
    /**
     * 单值字段
     * item的加权
     */
    WEIGHT("weight","内容加权"),
    /**
     * 单值字段(暂不支持)
     */
    PUB_ID("pub_id","pub_id"),
    /**
     * 单值字段
     * 物料经由哪个平台进入场景
     */
    SOURCE_ID("source_id","物料来源"),
    /**
     * 单值字段
     * 国家编码
     */
    COUNTRY("country","国家编码"),
    /**
     * 单值字段
     * 城市名称
     */
    CITY("city","城市名称"),
    /**
     * 多值字段
     * 标签
     */
    TAGS("tags","标签"),
    /**
     * 多值字段
     * 频道
     */
    CHANNEL("channel","频道"),
    /**
     * 多值字段
     * 机构列表
     */
    ORGANIZATION("organization","机构列表"),
    /**
     * 多值字段
     * 作者
     */
    AUTHOR("author","作者");

    private String code;
    private String msg;
}
