# 邀请客户留存明细接口文档

## 接口概述

`getRetentionDetail` 接口用于获取代理商邀请客户的留存明细数据，支持按日期范围查询，并计算各个留存周期的客户活跃情况。

## 接口定义

```java
List<SohuAgentRetentionVo> getRetentionDetail(String startDate, String endDate)
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| startDate | String | 是 | 开始日期 | "20250101" 或 "2025-01-01" |
| endDate | String | 是 | 结束日期 | "20250131" 或 "2025-01-31" |

### 支持的日期格式

- `yyyyMMdd`: 如 "20250101"
- `yyyy-MM-dd`: 如 "2025-01-01"

## 返回值说明

返回 `List<SohuAgentRetentionVo>` 列表，每个元素包含以下字段：

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| date | String | 邀请日期 | "2025-01-01" |
| invitedClientCount | Long | 当日邀请客户数 | 100 |
| day1After | Long | 1天后留存数 | 90 |
| day2After | Long | 2天后留存数 | 85 |
| day3After | Long | 3天后留存数 | 80 |
| day4After | Long | 4天后留存数 | 75 |
| day5After | Long | 5天后留存数 | 70 |
| day6After | Long | 6天后留存数 | 65 |
| day7After | Long | 7天后留存数 | 60 |
| day14After | Long | 14天后留存数 | 45 |
| day30After | Long | 30天后留存数 | 30 |

## 业务逻辑

### 1. 数据查询流程

1. **用户验证**: 检查当前登录用户是否有效
2. **日期解析**: 支持多种日期格式的安全解析
3. **客户数据获取**: 查询指定时间范围内邀请成功的客户
4. **数据分组**: 按邀请完成日期对客户进行分组
5. **留存计算**: 计算各个留存周期的活跃用户数

### 2. 留存计算方式

- **留存定义**: 用户在邀请日期后的第N天仍然活跃（有登录记录）
- **活跃判断**: 通过 `RemoteLogService.getOnlineUserNumByUserIdsAndTime()` 查询用户活跃度
- **留存周期**: 支持 1, 2, 3, 4, 5, 6, 7, 14, 30 天的留存统计

### 3. 数据来源

- **邀请数据**: `sohu_agent_user` 表中状态为 `SUCCESS` 的记录
- **活跃数据**: 通过系统登录日志服务获取用户活跃情况

## 使用示例

### Java 调用示例

```java
@Autowired
private ISohuAgentUserService agentUserService;

public void getRetentionData() {
    // 查询2025年1月的留存数据
    List<SohuAgentRetentionVo> retentionData = agentUserService.getRetentionDetail("20250101", "20250131");
    
    for (SohuAgentRetentionVo retention : retentionData) {
        System.out.println("日期: " + retention.getDate());
        System.out.println("邀请客户数: " + retention.getInvitedClientCount());
        System.out.println("7天留存: " + retention.getDay7After());
        System.out.println("30天留存: " + retention.getDay30After());
        System.out.println("---");
    }
}
```

### 返回数据示例

```json
[
    {
        "date": "2025-01-01",
        "invitedClientCount": 100,
        "day1After": 90,
        "day2After": 85,
        "day3After": 80,
        "day4After": 75,
        "day5After": 70,
        "day6After": 65,
        "day7After": 60,
        "day14After": 45,
        "day30After": 30
    },
    {
        "date": "2025-01-02",
        "invitedClientCount": 80,
        "day1After": 72,
        "day2After": 68,
        "day3After": 64,
        "day4After": 60,
        "day5After": 56,
        "day6After": 52,
        "day7After": 48,
        "day14After": 36,
        "day30After": 24
    }
]
```

## 异常处理

### 常见异常情况

1. **用户未登录**: 返回空列表
2. **日期格式错误**: 抛出 `IllegalArgumentException`
3. **查询失败**: 记录错误日志并返回空列表
4. **无邀请数据**: 返回空列表

### 错误日志示例

```
ERROR - 获取邀请客户留存明细失败，用户ID: 12345, 开始日期: 20250101, 结束日期: 20250131
ERROR - 日期解析失败，日期字符串: invalid_date
ERROR - 查询用户活跃数据失败，用户ID列表: [1,2,3], 开始时间: 2025-01-01, 结束时间: 2025-01-02
```

## 性能考虑

1. **数据量控制**: 建议查询时间范围不超过3个月
2. **缓存策略**: 可考虑对历史数据进行缓存
3. **异步处理**: 大数据量查询可考虑异步处理
4. **索引优化**: 确保相关数据库表有适当的索引

## 相关表结构

### sohu_agent_user 表
- `agent_id`: 代理商ID
- `user_id`: 客户用户ID  
- `state`: 邀请状态 (SUCCESS/WAIT_JOIN/REFUSE/TIMEOUT)
- `finish_time`: 邀请完成时间
- `create_time`: 创建时间

### sohu_user_retention_stat 表 (可选的统计表)
- `user_id`: 服务商用户ID
- `invite_date`: 邀请日期
- `retention_day`: 留存天数
- `retained_count`: 留存用户数

## 注意事项

1. 接口需要用户登录状态
2. 只统计邀请状态为 `SUCCESS` 的客户
3. 留存计算基于用户登录活跃度
4. 支持多种日期格式输入
5. 异常情况下返回空列表而不是抛出异常
