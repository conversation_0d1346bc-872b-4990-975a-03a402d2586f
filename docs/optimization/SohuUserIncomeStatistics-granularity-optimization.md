# SohuUserIncomeStatistics 日期粒度优化方案

## 优化概述

针对 `SohuUserIncomeStatisticsServiceImpl` 中的三个核心方法进行了优化，支持前端传递日期粒度参数，并确保数据的连续性填充。

## 🎯 **核心需求**

1. **前端传递日期粒度**：支持前端直接指定时间粒度（day/week/month）
2. **数据连续性保证**：确保返回的数据在指定时间范围内是连续的
3. **默认值填充**：当数据库中没有数据或数据不连续时，自动填充默认值
4. **向后兼容**：保持原有接口的兼容性

## 🔧 **优化方案**

### 1. **新增带粒度参数的方法**

#### 接口层优化
```java
// 原有方法（保持兼容）
List<OrderTradeAnalysisVo> orderTradeAnalysis(Long stationId, String roleType, String busyType, String startDate, String endDate);

// 新增方法（支持指定粒度）
List<OrderTradeAnalysisVo> orderTradeAnalysisWithGranularity(Long stationId, String roleType, String busyType, String startDate, String endDate, String granularity);
```

#### 实现层优化
```java
@Override
public List<OrderTradeAnalysisVo> orderTradeAnalysis(Long stationId, String roleType, String busyType, String startDate, String endDate) {
    // 自动判断时间粒度（保持向后兼容）
    Date start = parseDateSafely(startDate);
    Date end = parseDateSafely(endDate);
    SohuDateEnum granularityEnum = SohuDateEnum.getGranularityEnum(start, end);
    
    return orderTradeAnalysisWithGranularity(stationId, roleType, busyType, startDate, endDate, granularityEnum.getCode());
}

public List<OrderTradeAnalysisVo> orderTradeAnalysisWithGranularity(Long stationId, String roleType, String busyType, 
                                                                    String startDate, String endDate, String granularity) {
    // 验证日期粒度参数
    SohuDateEnum granularityEnum = validateAndParseGranularity(granularity);
    
    // 根据时间粒度进行处理
    switch (granularityEnum) {
        case DAY:
            return processDayGranularity(stationId, roleType, busyType, startDate, endDate);
        case WEEK:
            return processWeekGranularity(stationId, roleType, busyType, startDate, endDate);
        case MONTH:
            return processMonthGranularity(stationId, roleType, busyType, startDate, endDate);
        default:
            throw new IllegalArgumentException("不支持的时间粒度: " + granularityEnum);
    }
}
```

### 2. **日期粒度验证机制**

```java
/**
 * 验证并解析日期粒度参数
 * @param granularity 日期粒度字符串
 * @return SohuDateEnum 枚举值
 */
private SohuDateEnum validateAndParseGranularity(String granularity) {
    if (StringUtils.isBlank(granularity)) {
        throw new IllegalArgumentException("日期粒度参数不能为空");
    }
    
    try {
        // 根据传入的字符串查找对应的枚举
        for (SohuDateEnum dateEnum : SohuDateEnum.values()) {
            if (dateEnum.getCode().equalsIgnoreCase(granularity)) {
                return dateEnum;
            }
        }
        throw new IllegalArgumentException("不支持的日期粒度: " + granularity);
    } catch (Exception e) {
        log.error("日期粒度解析失败，粒度参数: {}", granularity, e);
        throw new IllegalArgumentException("无效的日期粒度: " + granularity + "，支持的粒度: day, week, month");
    }
}
```

### 3. **数据连续性保证机制**

#### 现有的连续性处理
代码中已经实现了完善的数据连续性保证机制：

```java
/**
 * 生成完整的时间序列数据（通用方法）
 */
private <T> List<OrderTradeAnalysisVo> generateCompleteTimeSeriesData(
        Map<String, List<T>> groupedData, 
        List<String> timeKeys, 
        java.util.function.Function<List<T>, OrderTradeAnalysisVo> aggregator) {
    
    return timeKeys.stream()
            .map(timeKey -> {
                if (groupedData.containsKey(timeKey)) {
                    OrderTradeAnalysisVo vo = aggregator.apply(groupedData.get(timeKey));
                    vo.setDisplayDate(formatDisplayDate(timeKey));
                    return vo;
                } else {
                    return createOrderAnalysisVo(timeKey); // 填充默认值
                }
            })
            .sorted(Comparator.comparing(OrderTradeAnalysisVo::getDisplayDate))
            .collect(Collectors.toList());
}
```

#### 各粒度的时间序列生成

**日粒度**：
```java
private List<String> generateDayKeys(String startDateStr, String endDateStr) {
    LocalDate start = parseLocalDateSafely(startDateStr);
    LocalDate end = parseLocalDateSafely(endDateStr);

    return start.datesUntil(end.plusDays(1))
            .map(date -> date.format(DAY_FORMATTER))
            .collect(Collectors.toList());
}
```

**周粒度**：
```java
private List<String> generateWeekKeys(String startDateStr, String endDateStr) {
    Date current = parseDateSafely(startDateStr);
    Date end = parseDateSafely(endDateStr);

    Set<String> uniqueWeeks = new LinkedHashSet<>();
    while (!current.after(end)) {
        uniqueWeeks.add(DateUtils.formatToYearWeek(current));
        current = DateUtil.offsetDay(current, 1);
    }
    return new ArrayList<>(uniqueWeeks);
}
```

**月粒度**：
```java
private List<String> generateMonthKeys(String startDateStr, String endDateStr) {
    YearMonth start = YearMonth.from(parseLocalDateSafely(startDateStr));
    YearMonth end = YearMonth.from(parseLocalDateSafely(endDateStr));

    List<String> months = new ArrayList<>();
    YearMonth current = start;
    while (!current.isAfter(end)) {
        months.add(current.format(MONTH_FORMATTER));
        current = current.plusMonths(1);
    }
    return months;
}
```

### 4. **控制器层支持**

```java
@Operation(summary = "获取站长站点订单量分析（指定粒度）", description = "负责人:张良峰 获取站长站点订单量分析，支持指定时间粒度")
@GetMapping("/order/analysis/granularity")
public R<List<OrderTradeAnalysisVo>> orderAnalysisWithGranularity(@RequestParam(value = "stationId") Long stationId,
                                                                  @RequestParam(value = "roleType") String roleType,
                                                                  @RequestParam(value = "busyType", required = false) String busyType,
                                                                  @RequestParam(value = "startDate") String startDate,
                                                                  @RequestParam(value = "endDate") String endDate,
                                                                  @RequestParam(value = "granularity") String granularity) {
    return R.ok(incomeStatisticsService.orderTradeAnalysisWithGranularity(stationId, roleType, busyType, startDate, endDate, granularity));
}
```

## 📊 **数据填充策略**

### 1. **默认值初始化**
```java
private void initializeOrderAnalysisVo(OrderTradeAnalysisVo vo) {
    vo.setWishOrderNum(0);
    vo.setProductOrderNum(0);
    vo.setShortDramaOrderNum(0);
    vo.setNovelOrderNum(0);
    vo.setNovelIncome(BigDecimal.ZERO);
    vo.setShortDramaIncome(BigDecimal.ZERO);
    vo.setProductIncome(BigDecimal.ZERO);
    vo.setWishIncome(BigDecimal.ZERO);
}
```

### 2. **数据聚合策略**
- **有数据的时间点**：聚合数据库中的实际数据
- **无数据的时间点**：填充默认的零值
- **数据不连续**：自动补齐缺失的时间点

### 3. **日期格式化**
```java
private String formatDisplayDate(String date) {
    if (date.length() == 8) {
        // yyyyMMdd 格式 -> yyyy-MM-dd
        return LocalDate.parse(date, DAY_FORMATTER).format(DISPLAY_DATE_FORMATTER);
    } else if (date.length() == 6 && date.matches("\\d{6}")) {
        try {
            // yyyyMM 格式 -> yyyy-MM
            YearMonth ym = YearMonth.parse(date, MONTH_FORMATTER);
            return ym.format(DISPLAY_MONTH_FORMATTER);
        } catch (Exception e) {
            // 周格式 -> yyyy-Wxx
            return date.substring(0, 4) + "-W" + date.substring(4);
        }
    }
    return date;
}
```

## 🎯 **使用示例**

### 前端调用示例

```javascript
// 自动判断粒度（原有接口）
GET /order/analysis?stationId=1&roleType=site&startDate=20250101&endDate=20250131

// 指定日粒度
GET /order/analysis/granularity?stationId=1&roleType=site&startDate=20250101&endDate=20250131&granularity=day

// 指定周粒度
GET /order/analysis/granularity?stationId=1&roleType=site&startDate=20250101&endDate=20250331&granularity=week

// 指定月粒度
GET /order/analysis/granularity?stationId=1&roleType=site&startDate=20250101&endDate=20251231&granularity=month
```

### 返回数据示例

```json
[
    {
        "displayDate": "2025-01-01",
        "wishOrderNum": 10,
        "productOrderNum": 5,
        "shortDramaOrderNum": 3,
        "novelOrderNum": 2,
        "wishIncome": 100.00,
        "productIncome": 50.00,
        "shortDramaIncome": 30.00,
        "novelIncome": 20.00
    },
    {
        "displayDate": "2025-01-02",
        "wishOrderNum": 0,
        "productOrderNum": 0,
        "shortDramaOrderNum": 0,
        "novelOrderNum": 0,
        "wishIncome": 0.00,
        "productIncome": 0.00,
        "shortDramaIncome": 0.00,
        "novelIncome": 0.00
    }
]
```

## 🛡️ **错误处理**

### 1. **参数验证**
- 日期格式验证：支持 `yyyyMMdd` 和 `yyyy-MM-dd` 格式
- 粒度参数验证：只支持 `day`、`week`、`month`
- 空值检查：所有必要参数都进行空值验证

### 2. **异常处理**
```java
// 日期解析异常
throw new IllegalArgumentException("无效的日期格式: " + dateStr + "，支持格式: yyyyMMdd 或 yyyy-MM-dd");

// 粒度参数异常
throw new IllegalArgumentException("无效的日期粒度: " + granularity + "，支持的粒度: day, week, month");
```

## 📈 **优化效果**

### 1. **功能增强**
- ✅ 支持前端指定时间粒度
- ✅ 保证数据连续性
- ✅ 自动填充默认值
- ✅ 保持向后兼容

### 2. **代码质量**
- ✅ 参数验证完善
- ✅ 错误处理健壮
- ✅ 代码结构清晰
- ✅ 可扩展性强

### 3. **用户体验**
- ✅ 前端可控制展示粒度
- ✅ 数据展示连续完整
- ✅ 接口响应稳定
- ✅ 错误提示清晰

通过这次优化，系统现在完全支持前端传递日期粒度参数，并能够保证数据的连续性和完整性，大大提升了数据分析的灵活性和用户体验。
