package com.sohu.middleService.dubbo;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuPlatformIndustryBo;
import com.sohu.middle.api.bo.SohuUserIndustryBo;
import com.sohu.middle.api.service.RemoteUserIndustryService;
import com.sohu.middle.api.vo.SohuIndustryVo;
import com.sohu.middle.api.vo.SohuPlatformIndustryUserVo;
import com.sohu.middle.api.vo.SohuPlatformIndustryVo;
import com.sohu.middle.api.vo.SohuUserIndustryVo;
import com.sohu.middle.api.vo.diy.SohuDiyTemplateVo;
import com.sohu.middleService.service.ISohuPlatformIndustryService;
import com.sohu.middleService.service.ISohuPlatformIndustryUserService;
import com.sohu.middleService.service.diy.ISohuDiyTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: leibo
 * @Date: 2025/5/28 14:29
 **/
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteUserIndustryServiceImpl implements RemoteUserIndustryService {

    private final ISohuPlatformIndustryUserService industryUserService;
    private final ISohuPlatformIndustryService industryService;
    private final ISohuDiyTemplateService diyTemplateService;

    @Override
    public List<SohuIndustryVo> getUserIndustryList(Long userId) {
        // 判断userId 是否为空, 为空返回默认可用的推荐行业
        if (Objects.isNull(userId)) {
            return industryService.getDefaultIndustry(Boolean.TRUE);
        }
        // 基于userId 查询用户配置的行业，为空则返回默认可用的推荐行业
        List<SohuPlatformIndustryUserVo> industryUserList = industryUserService.queryListByUserId(userId);
        if (CollectionUtils.isEmpty(industryUserList)) {
            return industryService.getDefaultIndustry(Boolean.TRUE);
        } else {
            // 基于设置的推荐行业查询对应行业信息剔除行业id为0的数据及模版信息并组装返回
            List<Long> industryIds = industryUserList.stream()
                    .map(SohuPlatformIndustryUserVo::getPlatformIndustryId)
                    // 过滤掉null和0的值
                    .filter(industryId -> industryId != null && industryId != 0L)
                    .collect(Collectors.toList());
            return industryService.getIndustryByIds(industryIds, Boolean.TRUE);
        }
    }

    @Override
    public SohuUserIndustryVo getUserIndustry() {
        // 查询全部可用行业
        List<SohuPlatformIndustryVo> industryAllList = industryService.queryList(new SohuPlatformIndustryBo());
        Map<Long, SohuDiyTemplateVo> templateVoMap = diyTemplateService.queryMapByIndustryIdsForSite(industryAllList.stream()
                .map(SohuPlatformIndustryVo::getId).collect(Collectors.toList()));
        if (Objects.isNull(templateVoMap)) {
            throw new ServiceException("当前无可用行业");
        }
        // 过滤 industryAllList，只保留在 templateVoMap 中存在的行业
        List<SohuPlatformIndustryVo> filteredIndustryList = industryAllList.stream()
                .filter(industry -> templateVoMap.containsKey(industry.getId()))
                .collect(Collectors.toList());

        Long userId = LoginHelper.getUserId();
        List<SohuIndustryVo> industryVoList = new ArrayList<>();
        // 基于userId 查询用户配置的行业，为空则返回默认可用的推荐行业
        List<SohuPlatformIndustryUserVo> industryUserList = industryUserService.queryListByUserId(userId);
        if (CollectionUtils.isEmpty(industryUserList)) {
            industryVoList = industryService.getDefaultIndustry(Boolean.FALSE);
        } else {
            // 不为空则处理基于设置的推荐行业查询对应行业信息剔除行业id为0的数据及模版信息并组装返回
            List<Long> industryIds = industryUserList.stream()
                    .map(SohuPlatformIndustryUserVo::getPlatformIndustryId)
                    // 过滤掉null和0的值
                    .filter(industryId -> industryId != null && industryId != 0L)
                    .collect(Collectors.toList());
            industryVoList = industryService.getIndustryByIds(industryIds, Boolean.FALSE);
        }
        Map<Long, SohuPlatformIndustryVo> allIndustryMap = filteredIndustryList.stream()
                .collect(Collectors.toMap(SohuPlatformIndustryVo::getId, vo -> vo));
        SohuUserIndustryVo userIndustryVo = new SohuUserIndustryVo();
        List<SohuUserIndustryVo.IndustryVo> userIndustryList = new ArrayList<>();
        List<SohuUserIndustryVo.IndustryVo> unUserIndustryList = new ArrayList<>();
        // 首先按照 industryVoList 的顺序添加用户选择的行业
        for (SohuIndustryVo industryVo : industryVoList) {
            SohuPlatformIndustryVo platformIndustryVo = allIndustryMap.get(industryVo.getId());
            if (platformIndustryVo != null) {
                SohuUserIndustryVo.IndustryVo userIndustryVoItem = new SohuUserIndustryVo.IndustryVo();
                userIndustryVoItem.setId(platformIndustryVo.getId());
                userIndustryVoItem.setName(platformIndustryVo.getName());
                userIndustryList.add(userIndustryVoItem);
            }
        }
        // 然后添加未选择的行业
        for (SohuPlatformIndustryVo platformIndustryVo : filteredIndustryList) {
            // 如果不在用户选择的列表中，且未被添加到userIndustryList中
            if (!industryVoList.stream().anyMatch(vo -> vo.getId().equals(platformIndustryVo.getId()))) {
                SohuUserIndustryVo.IndustryVo industryVo = new SohuUserIndustryVo.IndustryVo();
                industryVo.setId(platformIndustryVo.getId());
                industryVo.setName(platformIndustryVo.getName());
                unUserIndustryList.add(industryVo);
            }
        }
        userIndustryVo.setUserIndustryList(userIndustryList);
        userIndustryVo.setUnUserIndustryList(unUserIndustryList);
        return userIndustryVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleUserIndustry(SohuUserIndustryBo userIndustryBo) {
        // 填充行业id为0的关联数据
        Long userId = LoginHelper.getUserId();
        List<Long> industryIdList = userIndustryBo.getIndustryIdList();
        if (CollectionUtils.isEmpty(industryIdList)) {
            industryIdList = new ArrayList<>();
        }
        industryIdList.add(0L);
        // 基于id删除行业与用户关联关系
        industryUserService.removeByUserId(userId);
        // 新增关联关系
        industryUserService.add(userId, industryIdList);
        return Boolean.TRUE;
    }
}
