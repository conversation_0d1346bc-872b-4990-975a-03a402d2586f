package com.sohu.middleService.dubbo;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuPlatformIndustryBo;
import com.sohu.middle.api.bo.SohuPlatformIndustryHandleBo;
import com.sohu.middle.api.bo.SohuPlatformIndustryRelationBo;
import com.sohu.middle.api.bo.SohuSiteBo;
import com.sohu.middle.api.service.RemotePlatformIndustryService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.diy.SohuDiyTemplateVo;
import com.sohu.middleService.domain.SohuPlatformIndustryRelation;
import com.sohu.middleService.service.*;
import com.sohu.middleService.service.diy.ISohuDiyTemplateService;
import com.sohu.shopgoods.api.RemoteProductCategoryPcService;
import com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 平台行业
 *
 * @Author: leibo
 * @Date: 2025/5/26 12:10
 **/
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemotePlatformIndustryServiceImpl implements RemotePlatformIndustryService {

    private final ISohuPlatformIndustryService platformIndustryService;
    private final ISohuPlatformIndustryRelationService platformIndustryRelationService;
    private final ISohuIndustryCategoryService industryCategoryService;
    private final ISohuCategoryService categoryService;
    private final ISohuDiyTemplateService diyTemplateService;
    private final ISohuSiteService siteService;
    @DubboReference
    private RemoteProductCategoryPcService productCategoryPcService;

    @Override
    public TableDataInfo<SohuPlatformIndustryVo> queryPageList(SohuPlatformIndustryBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuPlatformIndustryVo> queryPageList = platformIndustryService.queryPageList(bo, pageQuery);
        if (CollectionUtils.isEmpty(queryPageList.getData())) {
            return queryPageList;
        }
        for (SohuPlatformIndustryVo platformIndustryVo : queryPageList.getData()) {
            SohuPlatformIndustryRelationBo industryRelationBo = new SohuPlatformIndustryRelationBo();
            industryRelationBo.setPlatformIndustryId(platformIndustryVo.getId());
            List<SohuPlatformIndustryRelationVo> relationList = platformIndustryRelationService.queryList(industryRelationBo);
            if (CollectionUtils.isEmpty(relationList)) {
                continue;
            }
            // 解析关联关系,基于业务类型分组
            Map<String, List<SohuPlatformIndustryRelationVo>> groups = relationList.stream().collect(Collectors.groupingBy(SohuPlatformIndustryRelationVo::getBusyType));
            // 查询相关分组数据并组装返回
            if (groups.containsKey(BusyType.BusyTask.getType())) {
                // 商单
                List<Long> categoryIds = groups.get(BusyType.BusyTask.getType()).stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
                platformIndustryVo.setIndustryCategoryNames(industryCategoryService.queryListByIds(categoryIds).stream().map(SohuIndustryCategoryVo::getIndustryName).collect(Collectors.toList()));
            }
            if (groups.containsKey(BusyType.Goods.getType())) {
                // 商品
                List<Long> categoryIds = groups.get(BusyType.Goods.getType()).stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
                platformIndustryVo.setProductCategoryNames(productCategoryPcService.listByIds(categoryIds).stream().map(SohuProductCategoryPcVo::getName).collect(Collectors.toList()));
            }
            if (groups.containsKey(BusyType.Content.getType())) {
                // 内容
                List<Long> categoryIds = groups.get(BusyType.Content.getType()).stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
                platformIndustryVo.setProjectCategoryNames(categoryService.queryList(categoryIds).stream().map(SohuCategoryVo::getName).collect(Collectors.toList()));
            }
        }
        return queryPageList;
    }

    @Override
    public SohuPlatformIndustryInfoVo getInfo(Long id) {
        return platformIndustryService.getInfo(id, Boolean.TRUE);
    }

    @Override
    public Boolean add(SohuPlatformIndustryHandleBo bo) {
        // 基于行业名称查询行业信息
        SohuPlatformIndustryVo platformIndustryVo = platformIndustryService.getByName(bo.getName());
        if (Objects.nonNull(platformIndustryVo)) {
            throw new ServiceException("行业名称已存在，请调整后再试");
        }
        // 校验分类是否已被行业使用
        this.checkCategory(bo);
        // 校验分类是否存在
        List<Long> industryCategoryIds = this.handleCategoryIds(bo.getIndustryCategoryIds(), BusyType.BusyTask.getType());
        List<Long> productCategoryIds = this.handleCategoryIds(bo.getProductCategoryIds(), BusyType.Goods.getType());
        List<Long> projectCategoryIds = this.handleCategoryIds(bo.getProjectCategoryIds(), BusyType.Content.getType());
        if (CollectionUtils.isEmpty(industryCategoryIds) && CollectionUtils.isEmpty(productCategoryIds)
                && CollectionUtils.isEmpty(projectCategoryIds)) {
            throw new ServiceException("设置的分类均不存在，请确认后再试");
        }
        SohuPlatformIndustryBo platformIndustryBo = new SohuPlatformIndustryBo();
        platformIndustryBo.setName(bo.getName());
        platformIndustryBo.setIsDefault(bo.getIsDefault());
        platformIndustryBo.setSort(bo.getSort());
        // 新增行业
        platformIndustryService.insertByBo(platformIndustryBo);
        Long id = platformIndustryBo.getId();
        // 新增行业分类关联关系
        List<SohuPlatformIndustryRelation> relationList = new ArrayList<>();
        relationList.addAll(this.buildRelation(industryCategoryIds, BusyType.BusyTask.getType(), id));
        relationList.addAll(this.buildRelation(productCategoryIds, BusyType.Goods.getType(), id));
        relationList.addAll(this.buildRelation(projectCategoryIds, BusyType.Content.getType(), id));
        platformIndustryRelationService.updateRelation(relationList, id);
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateByBo(SohuPlatformIndustryHandleBo bo) {
        // 基于行业名称查询行业信息
        SohuPlatformIndustryVo platformIndustryVo = platformIndustryService.getByName(bo.getName());
        if (Objects.nonNull(platformIndustryVo) && !platformIndustryVo.getId().equals(bo.getId())) {
            throw new ServiceException("行业名称已存在，请调整后再试");
        }
        // 校验分类是否已被行业使用
        this.checkCategory(bo);
        // 校验分类是否存在
        List<Long> industryCategoryIds = this.handleCategoryIds(bo.getIndustryCategoryIds(), BusyType.BusyTask.getType());
        List<Long> productCategoryIds = this.handleCategoryIds(bo.getProductCategoryIds(), BusyType.Goods.getType());
        List<Long> projectCategoryIds = this.handleCategoryIds(bo.getProjectCategoryIds(), BusyType.Content.getType());
        if (CollectionUtils.isEmpty(industryCategoryIds) && CollectionUtils.isEmpty(productCategoryIds)
                && CollectionUtils.isEmpty(projectCategoryIds)) {
            throw new ServiceException("设置的分类均不存在，请确认后再试");
        }
        // 编辑行业
        SohuPlatformIndustryBo platformIndustryBo = new SohuPlatformIndustryBo();
        platformIndustryBo.setId(bo.getId());
        platformIndustryBo.setName(bo.getName());
        platformIndustryBo.setIsDefault(bo.getIsDefault());
        platformIndustryBo.setSort(bo.getSort());
        // 新增行业
        platformIndustryService.updateByBo(platformIndustryBo);
        // 新增行业分类关联关系
        List<SohuPlatformIndustryRelation> relationList = new ArrayList<>();
        relationList.addAll(this.buildRelation(industryCategoryIds, BusyType.BusyTask.getType(), bo.getId()));
        relationList.addAll(this.buildRelation(productCategoryIds, BusyType.Goods.getType(), bo.getId()));
        relationList.addAll(this.buildRelation(projectCategoryIds, BusyType.Content.getType(), bo.getId()));
        platformIndustryRelationService.updateRelation(relationList, bo.getId());
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeByIds(List<Long> ids) {
        return platformIndustryService.deleteWithValidByIds(ids, Boolean.TRUE);
    }

    @Override
    public List<SohuPlatformIndustryVo> listAll(Boolean canUse) {
        List<SohuPlatformIndustryVo> industryVoList = platformIndustryService.queryList(new SohuPlatformIndustryBo());
        if (Objects.nonNull(canUse)) {
            if (canUse) {
                // 查询可用的并返回
                Map<Long, SohuDiyTemplateVo> templateVoMap = diyTemplateService.queryMapByIndustryIdsForSite(industryVoList.stream()
                        .map(SohuPlatformIndustryVo::getId).collect(Collectors.toList()));
                return industryVoList.stream()
                        .filter(vo -> templateVoMap.containsKey(vo.getId()))
                        .collect(Collectors.toList());
            } else {
                List<SohuSiteVo> sohuSiteVos = siteService.queryList(new SohuSiteBo());
                Map<Long, SohuSiteVo> sohuSiteVoMap = sohuSiteVos.stream()
                        .filter(vo -> Objects.nonNull(vo.getPlatformIndustryId()))
                        .collect(Collectors.toMap(
                                SohuSiteVo::getPlatformIndustryId,
                                Function.identity(),
                                (oldValue, newValue) -> newValue
                        ));
               for (SohuPlatformIndustryVo industryVo : industryVoList) {
                    if (sohuSiteVoMap.containsKey(industryVo.getId())) {
                        industryVo.setIsUse(Boolean.TRUE);
                    } else {
                        industryVo.setIsUse(Boolean.FALSE);
                    }
               }
            }
        }
        return industryVoList;
    }

    @Override
    public List<SohuPlatformIndustryRelationVo> queryList(Long industryId, String type) {
        SohuPlatformIndustryRelationBo relationBo = new SohuPlatformIndustryRelationBo();
        relationBo.setPlatformIndustryId(industryId);
        relationBo.setBusyType(type);
        return platformIndustryRelationService.queryList(relationBo);
    }

    @Override
    public Long queryIndustryIdByCategoryIdAndBusyType(Long categoryId, String busyType) {
        SohuPlatformIndustryRelationVo relationVo = platformIndustryRelationService.queryByCategoryIdAndBusyType(categoryId, busyType);
        if (Objects.isNull(relationVo)) {
            return null;
        }
        return relationVo.getPlatformIndustryId();
    }

    /**
     * 校验分类是否已使用
     *
     * @return
     */
    private void checkCategory(SohuPlatformIndustryHandleBo bo) {
        List<Long> industryCategoryIds = bo.getIndustryCategoryIds();
        List<Long> productCategoryIds = bo.getProductCategoryIds();
        List<Long> projectCategoryIds = bo.getProjectCategoryIds();
        Boolean isUse = Boolean.FALSE;
        if (CollectionUtils.isNotEmpty(industryCategoryIds)) {
            isUse = platformIndustryRelationService.checkCategory(industryCategoryIds, BusyType.BusyTask.getType(), bo.getId());
            if (isUse) {
                throw new ServiceException("所选愿望行业分类已被其他行业使用");
            }
        }
        if (CollectionUtils.isNotEmpty(productCategoryIds)) {
            isUse = platformIndustryRelationService.checkCategory(productCategoryIds, BusyType.Goods.getType(), bo.getId());
            if (isUse) {
                throw new ServiceException("所选商品分类已被其他行业使用");
            }
        }
        if (CollectionUtils.isNotEmpty(projectCategoryIds)) {
            isUse = platformIndustryRelationService.checkCategory(projectCategoryIds, BusyType.Content.getType(), bo.getId());
            if (isUse) {
                throw new ServiceException("所选内容分类已被其他行业使用");
            }
        }
    }

    /**
     * 处理分类id
     *
     * @param categoryIds
     * @param busyType
     * @return
     */
    private List<Long> handleCategoryIds(List<Long> categoryIds, String busyType) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        if (busyType.equals(BusyType.BusyTask.getType())) {
            List<SohuIndustryCategoryVo> industryCategoryVoList = industryCategoryService.queryListByIds(categoryIds);
            if (CollectionUtils.isEmpty(industryCategoryVoList)) {
                return new ArrayList<>();
            } else {
                return industryCategoryVoList.stream().map(SohuIndustryCategoryVo::getId).collect(Collectors.toList());
            }
        } else if (busyType.equals(BusyType.Goods.getType())) {
            List<SohuProductCategoryPcVo> productCategoryPcVoList = productCategoryPcService.listByIds(categoryIds);
            if (CollectionUtils.isEmpty(productCategoryPcVoList)) {
                return new ArrayList<>();
            } else {
                return productCategoryPcVoList.stream().map(SohuProductCategoryPcVo::getId).collect(Collectors.toList());
            }
        } else {
            List<SohuCategoryVo> sohuCategoryVos = categoryService.queryList(categoryIds);
            if (CollectionUtils.isEmpty(sohuCategoryVos)) {
                return new ArrayList<>();
            } else {
                return sohuCategoryVos.stream().map(SohuCategoryVo::getId).collect(Collectors.toList());
            }
        }
    }

    /**
     * 组装关联关系
     *
     * @param categoryIds
     * @param busyType
     * @param platformIndustryId
     * @return
     */
    private List<SohuPlatformIndustryRelation> buildRelation(List<Long> categoryIds, String busyType, Long platformIndustryId) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        List<SohuPlatformIndustryRelation> relationList = new ArrayList<>();
        for (Long categoryId : categoryIds) {
            SohuPlatformIndustryRelation relation = new SohuPlatformIndustryRelation();
            relation.setPlatformIndustryId(platformIndustryId);
            relation.setBusyCategoryId(categoryId);
            relation.setBusyType(busyType);
            relationList.add(relation);
        }
        return relationList;
    }
}
