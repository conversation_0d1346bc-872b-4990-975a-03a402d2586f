package com.sohu.middleService.dubbo;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuBindStationCheckBo;
import com.sohu.middle.api.bo.SohuSiteBo;
import com.sohu.middle.api.bo.SohuSiteV2QueryBo;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.common.SohuSiteOfEnableVo;
import com.sohu.middleService.service.ISohuSiteService;
import com.sohu.middleService.service.ISohuSiteV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 行业分类
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleSiteServiceImpl implements RemoteMiddleSiteService {

    private final ISohuSiteService sohuSiteService;
    private final ISohuSiteV2Service sohuSiteV2Service;


    @Override
    public SohuSiteVo queryById(Long id) {
        return sohuSiteService.queryById(id);
    }

    @Override
    public TableDataInfo<SohuSiteVo> queryPageList(SohuSiteBo bo, PageQuery pageQuery) {
        return sohuSiteService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuSiteVo> queryList(SohuSiteBo bo) {
        return sohuSiteService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuSiteBo bo) {
        return sohuSiteService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuSiteBo bo) {
        return sohuSiteService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuSiteService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public List<SohuSiteVo> getSiteTree(SohuSiteBo bo) {
        return sohuSiteService.getSiteTree(bo);
    }

    @Override
    public List<SohuSiteVo> getSiteClearTree(SohuSiteBo bo) {
        return sohuSiteService.getSiteClearTree(bo);
    }

    @Override
    public List<SohuSiteOfEnableVo> getSiteTreeOfEnable() {
        return sohuSiteService.getSiteTreeOfEnable();
    }

    @Override
    public List<SohuSiteOfEnableVo> getSiteListOfEnable() {
        return sohuSiteService.getSiteListOfEnable();
    }

    @Override
    public Object getLocationDetail(String location) {
        return sohuSiteService.getLocationDetail(location);
    }

    @Override
    public Boolean openSite(SohuSiteBo bo) {
        return sohuSiteService.openSite(bo);
    }

    @Override
    public Boolean online(Long siteId) {
        return sohuSiteService.online(siteId);
    }

    @Override
    public Boolean offline(Long siteId) {
        return sohuSiteService.offline(siteId);
    }

    @Override
    public Map<Long, SohuSiteVo> queryMap(Set<Long> siteIds) {
        return sohuSiteService.queryMap(siteIds);
    }

    @Override
    public Map<Long, SohuSiteVo> queryIndustryMap(Set<Long> siteIds) {
        return sohuSiteService.queryIndustryMap(siteIds);
    }

    @Override
    public List<SohuSiteVo> selectSiteList(Collection<Long> ids) {
        return sohuSiteService.selectSiteList(ids);
    }

    @Override
    public SohuSiteVo selectSiteByPid(Long id) {
        return sohuSiteService.selectSiteByPid(id);
    }

    @Override
    public Long selectCountryIdByCityId(Long cityId) {
        return sohuSiteService.selectCountryIdByCityId(cityId);
    }

    @Override
    public SohuSiteVo selectSiteByUserId(Long userId) {
        return sohuSiteService.selectSiteByUserId(userId);
    }

    @Override
    public List<Long> queryChildSiteIds(Long pid) {
        return sohuSiteService.queryChildSiteIds(pid);
    }

    @Override
    public Long getSiteIdByEnName(String categoryEnName) {
        return sohuSiteService.getSiteIdByEnName(categoryEnName);
    }

    @Override
    public SohuSiteVo queryByStationmasterId(Long stationmasterId) {
        return sohuSiteService.queryByStationmasterId(stationmasterId);
    }

    @Override
    public List<SohuSiteVo> listByStationmasterId(Long stationmasterId, Integer stationmasterStatus) {
        return sohuSiteService.listByStationmasterId(stationmasterId, stationmasterStatus);
    }

    @Override
    public SohuSiteVo getSiteByIp(String ip) {
        return sohuSiteService.getSiteByIp(ip);
    }

    @Override
    public SohuSiteVo getBySiteNameOfEnable(String siteName) {
        return sohuSiteService.getBySiteNameOfEnable(siteName);
    }

    @Override
    public Boolean addV2(SohuSiteBo bo) {
        return sohuSiteV2Service.addV2(bo);
    }

    @Override
    public TableDataInfo<SohuSiteVo> queryPageListV2(SohuSiteV2QueryBo bo, PageQuery pageQuery) {
        return sohuSiteV2Service.queryPageListV2(bo, pageQuery);
    }

    @Override
    public SohuSiteVo getInfoV2(Long id) {
        return sohuSiteV2Service.getInfoV2(id);
    }

    @Override
    public Boolean updateV2(SohuSiteBo bo) {
        return sohuSiteV2Service.updateV2(bo);
    }

    @Override
    public SohuSiteVo queryByPlatformIndustry(Long platformIndustryId) {
        return sohuSiteV2Service.queryByPlatformIndustry(platformIndustryId);
    }

    @Override
    public Boolean bindStationCheck(String stationmasterPhone) {
        SohuBindStationCheckBo checkBo = SohuBindStationCheckBo.builder().stationmasterPhone(stationmasterPhone).build();
        return sohuSiteV2Service.bindStationCheck(checkBo);
    }

}
