package com.sohu.middleService.dubbo;

import cn.hutool.core.collection.CollectionUtil;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.enums.SohuTradeRecordEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.vo.SohuStatVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.SohuTradeRecordIndependentBo;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.playlet.PlayletVirtualRecordVo;
import com.sohu.middleService.service.ISohuTradeRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 用户流水明细
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteMiddleTradeRecordServiceImpl implements RemoteMiddleTradeRecordService {

    private final ISohuTradeRecordService sohuTradeRecordService;


    @Override
    public SohuTradeRecordVo queryById(Long id) {
        return sohuTradeRecordService.queryById(id);
    }

    @Override
    public SohuTradeRecordVo queryByPayNumber(String payNumber) {
        return sohuTradeRecordService.queryByPayNumber(payNumber);
    }

    @Override
    public SohuTradeRecordVo queryByPayNumber(String payNumber, String amountType) {
        return sohuTradeRecordService.queryByPayNumber(payNumber, amountType);
    }

    @Override
    public TableDataInfo<SohuTradeRecordVo> queryPageList(SohuTradeRecordBo bo, PageQuery pageQuery) {
        return sohuTradeRecordService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuTradeRecordVo> queryList(SohuTradeRecordBo bo) {
        return sohuTradeRecordService.queryList(bo);
    }

    @Override
    public Boolean insertByBo(SohuTradeRecordBo bo) {
        return sohuTradeRecordService.insertByBo(bo);
    }

    @Override
    public Boolean insertBatch(List<SohuTradeRecordBo> entityList) {
        return sohuTradeRecordService.insertBatch(entityList);
    }

    @Override
    public Boolean updateByBo(SohuTradeRecordBo bo) {
        return sohuTradeRecordService.updateByBo(bo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return sohuTradeRecordService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean deleteByByPayNumber(String payNumber) {
        return sohuTradeRecordService.deleteByByPayNumber(payNumber);
    }

    @Override
    public Boolean updatePayStatus(String payNumber, String transactionId, PayStatus payStatus, String amountType) {
        return sohuTradeRecordService.updatePayStatus(payNumber, transactionId, payStatus, amountType);
    }

    @Override
    public BigDecimal todayIncome() {
        return sohuTradeRecordService.todayIncome();
    }

    @Override
    public SohuTradeRecordVo queryByPayNumber(String payNumber, String type, Long userId) {
        return sohuTradeRecordService.queryByPayNumber(payNumber, type, userId);
    }

    @Override
    public SohuTradeRecordVo queryOne(Long userId, String type, String consumeType, String consumeCode, String payStatus) {
        return sohuTradeRecordService.queryOne(userId, type, consumeType, consumeCode, payStatus);
    }

    @Override
    public SohuTradeRecordVo queryOne(SohuTradeRecordBo bo) {
        return sohuTradeRecordService.queryOne(bo);
    }

    @Override
    public Map<String, SohuTradeRecordVo> queryMap(Long userId, String type, String consumeType, List<String> consumeCodeList, String payStatus) {
        return sohuTradeRecordService.queryMap(userId, type, consumeType, consumeCodeList, payStatus);
    }

    @Override
    public BigDecimal countTotalIncome() {
        return sohuTradeRecordService.countTotalIncome();
    }

    @Override
    public BigDecimal countTodayIncome() {
        return sohuTradeRecordService.countTodayIncome();
    }

    @Override
    public BigDecimal countPlayletIncome() {
        return sohuTradeRecordService.countPlayletIncome();
    }

    @Override
    public BigDecimal countTaskIncome() {
        return sohuTradeRecordService.countTaskIncome();
    }

    @Override
    public BigDecimal countShareAndInviteIncome() {
        return sohuTradeRecordService.countShareAndInviteIncome();
    }

    @Override
    public BigDecimal countShareIncome() {
        return sohuTradeRecordService.countShareIncome();
    }

    @Override
    public BigDecimal countInviteIncome() {
        return sohuTradeRecordService.countInviteIncome();
    }

    @Override
    public TableDataInfo<SohuTradeRecordIndependentVo> queryIndependentList(SohuTradeRecordIndependentBo independentBo) {
        return sohuTradeRecordService.queryIndependentList(independentBo);
    }

    @Override
    public SohuTradeRecordDetailIndependentVo taskDetail(Long id, String payNumber, String independentObject) {
        return sohuTradeRecordService.taskDetail(id, payNumber, independentObject);
    }

    @Override
    public BigDecimal independentTotal(SohuTradeRecordIndependentBo independentBo) {
        return sohuTradeRecordService.independentTotal(independentBo);
    }

    @Override
    public WalletIncomeStatVo getWalletStat() {
        return sohuTradeRecordService.getWalletStat();
    }

    @Override
    public TableDataInfo<SohuTradeRecordVo> getVirtualPayRecord(SohuTradeRecordBo bo, PageQuery pageQuery) {
        return sohuTradeRecordService.getVirtualPayRecord(bo, pageQuery);
    }

    @Override
    public Boolean updatePayStatus(String payNumber, String transactionId, String oldPayStatus, String newPayStatus) {
        return sohuTradeRecordService.updatePayStatus(payNumber, transactionId, oldPayStatus, newPayStatus);
    }

    @Override
    public SohuTradeRecordVo queryFirstRecord(Long userId, String accountType) {
        return sohuTradeRecordService.queryFirstRecord(userId, accountType);
    }

    @Override
    public SohuInComeVo income() {
        return sohuTradeRecordService.income();
    }

    @Override
    public List<SohuInComeListVo> incomeList(Long days) {
        return sohuTradeRecordService.incomeList(days);
    }

    @Override
    public List<SohuInComeListVo> incomeListByDate(String date) {
        return sohuTradeRecordService.incomeListByDate(date);
    }

    @Override
    public List<SohuSalesRankVo> salesRank() {
        return sohuTradeRecordService.salesRank();
    }

    @Override
    public List<SohuTradeRecordIndependentVo> getList(SohuTradeRecordIndependentBo bo) {
        return sohuTradeRecordService.getList(bo);
    }

    @Override
    public TableDataInfo<PlayletVirtualRecordVo> getPlayletFoxCoinRecord(String uuid, PageQuery pageQuery) {
        return sohuTradeRecordService.getPlayletFoxCoinRecord(uuid, pageQuery);
    }

    @Override
    public BigDecimal sumUserAmount(Long userId, String amountType, List<String> typeList) {
        return sohuTradeRecordService.sumUserAmount(userId, amountType, typeList);
    }

    @Override
    public BigDecimal sumGuestAmount(String uuid, String amountType) {
        return sohuTradeRecordService.sumGuestAmount(uuid, amountType);
    }

    @Override
    public Boolean bindGuest(String uuid) {
        return sohuTradeRecordService.bindGuest(uuid);
    }

    @Override
    public SohuTradeRecordVo queryByPayNumberOne(String payNumber, String payType) {
        return sohuTradeRecordService.queryByPayNumberOne(payNumber, payType);
    }

    @Override
    public SohuTradeRecordVo queryConsume(String payType, String consumeType, String consumeCode) {
        return sohuTradeRecordService.queryConsume(payType, consumeType, consumeCode);
    }

    @Override
    public TableDataInfo<SohuTradeRecordVo> queryNovelPageList(SohuTradeRecordBo bo, PageQuery pageQuery) {
        return sohuTradeRecordService.queryNovelPageList(bo, pageQuery);
    }

    @Override
    public SohuNovelTradeRecordVo queryNovelById(Long id) {
        return sohuTradeRecordService.queryNovelById(id);
    }

    @Override
    public void cancelNovelOrder(SohuTradeRecordBo tradeRecordBo) {
        sohuTradeRecordService.cancelNovelOrder(tradeRecordBo);
    }

    @Override
    public Boolean updateIndependentStatus(String orderNo, Integer sourceStatus, Integer targetStatus) {
        return sohuTradeRecordService.updateIndependentStatus(orderNo, sourceStatus, targetStatus);
    }

    @Override
    public SohuLiteratureTradeRecordVo queryLiteratureById(Long id) {
        return sohuTradeRecordService.queryLiteratureById(id);
    }

    @Override
    public TableDataInfo<SohuTradeRecordVo> queryLiteraturePageList(SohuTradeRecordBo bo, PageQuery pageQuery) {
        return sohuTradeRecordService.queryLiteraturePageList(bo, pageQuery);
    }

    @Override
    public Long getNewPayUserNumByCreateTime(Date startTime, Date endTime) {
        return sohuTradeRecordService.getNewPayUserNumByCreateTime(startTime, endTime);
    }

    @Override
    public SohuStatVo getStat(Date endTime) {
        return sohuTradeRecordService.getStat(endTime);
    }

    @Override
    public List<SohuTradeNovelRecordVo> pageUserNovelRecord(PageQuery pageQuery) {
        // 获取登陆用户id
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        SohuTradeRecordBo recordBo = SohuTradeRecordBo.builder().build();
        recordBo.setUserId(userId);
        recordBo.setType(SohuTradeRecordEnum.Type.NOVEL.getCode());
        recordBo.setPayStatus(PayStatus.Paid.name());
        TableDataInfo<SohuTradeRecordVo> dataInfo = sohuTradeRecordService.queryNovelPageList(recordBo, pageQuery);
        List<SohuTradeNovelRecordVo> novelRecordList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataInfo.getData())) {
            for (SohuTradeRecordVo tradeRecordVo : dataInfo.getData()) {
                SohuTradeNovelRecordVo novelRecordVo = new SohuTradeNovelRecordVo();
                novelRecordVo.setAmount(tradeRecordVo.getAmount());
                novelRecordVo.setPayNumber(tradeRecordVo.getPayNumber());
                novelRecordVo.setConsumeType(tradeRecordVo.getConsumeType());
                novelRecordVo.setMsg(tradeRecordVo.getMsg());
                novelRecordVo.setPayStatus(tradeRecordVo.getPayStatus());
                novelRecordList.add(novelRecordVo);
            }
        }
        return novelRecordList;
    }

}
