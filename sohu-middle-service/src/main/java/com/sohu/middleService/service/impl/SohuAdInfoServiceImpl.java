package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.RemoteBusyTaskSiteService;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.utils.file.FileUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.bo.playlet.PlayletOpenAdsQueryBo;
import com.sohu.middle.api.bo.playlet.PlayletPatchesAdsQueryBo;
import com.sohu.middle.api.bo.playlet.SohuPlayletRelateBo;
import com.sohu.middle.api.enums.AdPlaceEnum;
import com.sohu.middle.api.enums.AdPlacePortEnum;
import com.sohu.middle.api.enums.RechargeChannelEnum;
import com.sohu.middle.api.service.RemoteMiddleCommonLabelService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.playlet.*;
import com.sohu.middleService.domain.SohuAdInfo;
import com.sohu.middleService.domain.SohuAdInfoLabelRelation;
import com.sohu.middleService.domain.SohuAdPlace;
import com.sohu.middleService.mapper.SohuAdInfoLabelRelationMapper;
import com.sohu.middleService.mapper.SohuAdInfoMapper;
import com.sohu.middleService.mapper.SohuAdPlaceMapper;
import com.sohu.middleService.mapper.SohuVideoInfoMapper;
import com.sohu.middleService.service.*;
import com.sohu.middleService.service.adInfo.impl.AdInfoBuilderFactory;
import com.sohu.middleService.service.playlet.ISohuPlayletRelateService;
import com.sohu.novel.api.RemoteNovelService;
import com.sohu.novel.api.vo.CourseVo;
import com.sohu.resource.api.RemoteFileService;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.vo.SohuProductVo;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 站点广告主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
@DS(value = "master")
public class SohuAdInfoServiceImpl extends SohuBaseServiceImpl<SohuAdInfoMapper, SohuAdInfo, SohuAdInfoVo> implements ISohuAdInfoService {

    /**
     * 索引最大值
     */
    private static final int SORT_INDEX_MAX_VALUE = 999;

    /**
     * 广告间隔数
     */
    private final static Integer AD_INFO_INTERVAL_NUM = 9;

    /**
     * 视频常量定义
     */
    private final static String VIDEO_CONSTANT = "video";

    private final SohuAdPlaceMapper sohuAdPlaceMapper;
    private final SohuAdInfoLabelRelationMapper sohuAdInfoLabelRelationMapper;
    private final SohuVideoInfoMapper sohuVideoInfoMapper;

    private final ISohuPlayletRelateService sohuPlayletRelateService;
    private final ISohuPlayletService sohuPlayletService;
    private final ISohuVideoService sohuVideoService;
    private final ISohuArticleService sohuArticleService;
    private final ISohuUserService sohuUserService;
    private final ISohuUserLikeService sohuUserLikeService;
    private final ISohuEventReportService sohuEventReportService;
    private final AdInfoBuilderFactory adInfoBuilderFactory;
    @DubboReference
    private RemoteNovelService remoteNovelService;
    @DubboReference
    private RemoteBusyTaskSiteService remoteBusyTaskSiteService;
    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    private RemoteFileService remoteFileService;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;


    @Override
    public Long getAuthorId(Long id) {
        if (CalUtils.isNullOrZero(id)) {
            return 0L;
        }
        SohuAdInfoVo sohuAdInfoVo = baseMapper.selectVoById(id);
        return Objects.isNull(sohuAdInfoVo) ? 0L : 1;
    }

    /**
     * 查询站点广告主体
     */
    @Override
    public SohuAdInfoVo queryById(Long id) {
        SohuAdInfoVo vo = baseMapper.selectVoById(id);
        if (Objects.nonNull(vo)) {
            // 1. 查询广告标签
            List<SohuAdInfoLabelRelationVo> adInfoLabelList = getAdInfoLabels(id);
            if (CollUtil.isNotEmpty(adInfoLabelList)) {
                vo.setLabelList(adInfoLabelList);
                vo.setLabelIdList(adInfoLabelList.stream().map(SohuAdInfoLabelRelationVo::getLabelId).collect(Collectors.toList()));
            }

            // 2. 构建广告信息
            adInfoBuilderFactory.buildAdInfo(vo);
        }
        return vo;
    }

    /**
     * 查询广告标签
     *
     * @param adInfoId 广告ID
     * @return 广告标签列表
     */
    private List<SohuAdInfoLabelRelationVo> getAdInfoLabels(Long adInfoId) {
        LambdaQueryWrapper<SohuAdInfoLabelRelation> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAdInfoLabelRelation::getAdInfoId, adInfoId);
        List<SohuAdInfoLabelRelationVo> adInfoLabelList = sohuAdInfoLabelRelationMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(adInfoLabelList)) {
            // 1. 获取标签ID列表
            List<Long> labelIds = adInfoLabelList.stream().map(SohuAdInfoLabelRelationVo::getLabelId).distinct().collect(Collectors.toList());

            // 2. 批量查询标签名称
            Map<Long, String> labelMap = remoteMiddleCommonLabelService.queryLabelNamesByIds(labelIds);

            // 3. 设置标签名称
            adInfoLabelList.forEach(labelVo -> labelVo.setLabelName(labelMap.get(labelVo.getLabelId())));
        }
        return adInfoLabelList;
    }

    /**
     * 查询站点广告主体列表(分页)
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<SohuAdInfoVo> queryPageList(SohuAdInfoBo bo, PageQuery pageQuery) {
        log.info("adinfo queryPageList:{}", JSONUtil.toJsonStr(bo));
        // 1. 判断是否需要进行DIY处理（根据渠道和版本号）
        boolean diy = isDiy(bo.getPlatform(), bo.getVersion());

        // 2. 构建查询条件
        LambdaQueryWrapper<SohuAdInfo> lqw = buildQueryWrapper(bo);

        // 3. 执行分页查询
        Page<SohuAdInfoVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);

        if (CollUtil.isEmpty(result.getRecords())) {
            return TableDataInfoUtils.build();
        }

        // 4. 获取广告位和OSS ID相关数据
        processAdInfoList(result.getRecords(), diy);

        return TableDataInfoUtils.build(result);
    }

    /**
     * 判断是否需要进行DIY处理（根据渠道和版本号）
     *
     * @param platform 平台
     * @param version  版本号
     * @return 是否需要DIY处理
     */
    private boolean isDiy(String platform, String version) {
        if (StrUtil.isBlank(version)) {
            return false;
        }
        RechargeChannelEnum channelEnum = RechargeChannelEnum.getByPlatform(platform);
        if (channelEnum != RechargeChannelEnum.APPLE) {
            return false;
        }

        // 1. 获取系统配置
        SysDictData dictData = remoteDictService.getDictData("system_config", DictEnum.IosAdFake.getKey());
        if (dictData == null || StrUtil.isBlank(dictData.getDictValue())) {
            return false;
        }

        // 2. 版本号比较
        int compareResult = VersionComparator.INSTANCE.compare(version, dictData.getDictValue());
        log.info("version compare：{}", compareResult);
        return compareResult > 0;
    }

    /**
     * 处理广告信息列表，获取广告位和OSS ID相关数据
     *
     * @param records 广告信息列表
     * @param diy     是否需要进行DIY处理
     */
    private void processAdInfoList(List<SohuAdInfoVo> records, boolean diy) {
        // 1. 获取广告位和OSS ID相关数据
        Set<String> placeCodes = records.stream().map(SohuAdInfoVo::getAdPlace).collect(Collectors.toSet());
        Set<Long> ossIds = records.stream().filter(r -> StringUtils.isNotBlank(r.getImage()) && NumberUtil.isNumber(r.getImage()))
                .map(r -> Long.valueOf(r.getImage())).collect(Collectors.toSet());

        Map<Long, String> ossMap = remoteFileService.map(ossIds);
        Map<String, SohuAdPlace> adPlaceMap = getAdPlaceMap(placeCodes);

        // 2. 填充广告信息
        for (SohuAdInfoVo record : records) {
            // 2.1 设置广告位信息
            SohuAdPlace adPlace = adPlaceMap.get(record.getAdPlace());
            if (adPlace != null) {
                record.setAdPlacePage(adPlace.getPlacePage());
                record.setType(adPlace.getType());
            }

            // 2.2 设置图片信息
            if (StringUtils.isNotBlank(record.getImage()) && NumberUtil.isNumber(record.getImage())) {
                Long ossId = Long.valueOf(record.getImage());
                record.setOssId(remoteFileService.save(record.getImage()));
                record.setImage(ossMap.get(ossId));
            }

            // 2.3 如果需要DIY处理，则修改图片和链接
            if (diy) {
                record.setImage("https://sohugloba.oss-cn-beijing.aliyuncs.com/20230511/13571753269c88af02be3a279ce00e073bd827e96d6fe69591fb250fcf799838.png");
                record.setLink("");
            }
            // 2.4 构建广告信息
            adInfoBuilderFactory.buildAdInfo(record);
        }
    }

    /**
     * 获取广告位Map
     *
     * @param placeCodes 广告位编码集合
     * @return 广告位Map，key为广告位编码，value为广告位信息
     */
    private Map<String, SohuAdPlace> getAdPlaceMap(Collection<String> placeCodes) {
        if (CollUtil.isEmpty(placeCodes)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<SohuAdPlace> query = Wrappers.lambdaQuery();
        query.in(SohuAdPlace::getPlaceCode, placeCodes);
        List<SohuAdPlace> sohuAdPlaces = sohuAdPlaceMapper.selectList(query);
        return sohuAdPlaces.stream().collect(Collectors.toMap(SohuAdPlace::getPlaceCode, u -> u));
    }

    /**
     * 查询站点广告主体列表(分页) V2，包含广告曝光数和点击数
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<SohuAdInfoVo> queryPageListV2(SohuAdInfoQueryBo bo, PageQuery pageQuery) {
        // 1. 执行分页查询
        Page<SohuAdInfoVo> result = baseMapper.queryPageList(PageQueryUtils.build(pageQuery), bo);

        if (CollUtil.isNotEmpty(result.getRecords())) {
            // 2. 获取广告ID集合
            Set<String> adIds = result.getRecords().stream().map(p -> p.getId().toString()).collect(Collectors.toSet());

            // 3. 批量查询广告报表数据
            Map<String, AdReportVo> adReportMap = this.sohuEventReportService.selectAdReportMap(adIds);

            // 4. 填充广告报表数据
            for (SohuAdInfoVo vo : result.getRecords()) {
                AdReportVo adReportVo = adReportMap.get(vo.getId().toString());
                if (adReportVo != null) {
                    vo.setExposureNum(adReportVo.getGgbgNum());
                    vo.setClickNum(adReportVo.getGgdjNum());
                    vo.setClickRate(CalUtils.calculateRate(adReportVo.getGgbgNum(), adReportVo.getGgdjNum()));
                }
                // 5. 构建广告信息
                adInfoBuilderFactory.buildAdInfo(vo);

                // 6.构建标签信息
                List<SohuAdInfoLabelRelationVo> adinfoLabelList = getAdInfoLabels(vo.getId());
                if (CollUtil.isNotEmpty(adinfoLabelList)) {
                    vo.setLabelList(adinfoLabelList);
                    vo.setLabelIdList(adinfoLabelList.stream().map(SohuAdInfoLabelRelationVo::getLabelId).collect(Collectors.toList()));
                }
            }
        }

        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询站点广告主体列表
     */
    @Override
    @DS(value = "master")
    public List<SohuAdInfoVo> queryList(SohuAdInfoBo bo) {
        LambdaQueryWrapper<SohuAdInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuAdInfo> buildQueryWrapper(SohuAdInfoBo bo) {
        LambdaQueryWrapper<SohuAdInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSiteId() != null, SohuAdInfo::getSiteId, bo.getSiteId());
        lqw.eq(StrUtil.isNotBlank(bo.getAdPlace()), SohuAdInfo::getAdPlace, bo.getAdPlace());
        lqw.eq(StrUtil.isNotBlank(bo.getTitle()), SohuAdInfo::getTitle, bo.getTitle());
        lqw.eq(StrUtil.isNotBlank(bo.getState()), SohuAdInfo::getState, bo.getState());
        lqw.like(StringUtils.isNotBlank(bo.getPort()), SohuAdInfo::getPort, bo.getPort());
        lqw.eq(bo.getStartTime() != null, SohuAdInfo::getStartTime, bo.getStartTime());
        lqw.eq(bo.getOverTime() != null, SohuAdInfo::getOverTime, bo.getOverTime());
        lqw.orderByAsc(SohuAdInfo::getSortIndex);
        lqw.orderByDesc(SohuAdInfo::getId);
        if (StrUtil.isAllBlank(bo.getPlacePage(), bo.getType())) {
            return lqw;
        }

        // 如果PlacePage或Type不为空，则根据SohuAdPlace进行关联查询
        LambdaQueryWrapper<SohuAdPlace> adPlaceQuery = Wrappers.lambdaQuery();
        adPlaceQuery.like(StrUtil.isNotBlank(bo.getPlacePage()), SohuAdPlace::getPlacePage, bo.getPlacePage());
        adPlaceQuery.eq(StrUtil.isNotBlank(bo.getType()), SohuAdPlace::getType, bo.getType());
        List<SohuAdPlace> sohuAdPlaces = sohuAdPlaceMapper.selectList(adPlaceQuery);

        if (CollUtil.isEmpty(sohuAdPlaces)) {
            lqw.in(SohuAdInfo::getAdPlace, Collections.singletonList("0"));
        } else {
            List<String> adPlaceCodes = sohuAdPlaces.stream().map(SohuAdPlace::getPlaceCode).collect(Collectors.toList());
            lqw.in(SohuAdInfo::getAdPlace, adPlaceCodes);
        }
        lqw.orderByAsc(SohuAdInfo::getSortIndex);
        lqw.orderByDesc(SohuAdInfo::getId);
        return lqw;
    }

    /**
     * 新增站点广告主体
     */
    @Override
    public Boolean insertByBo(SohuAdInfoBo bo) {
        // 1. 设置默认值
        bo.setState(CommonState.OnShelf.getCode());
        Date now = new Date();
        bo.setCreateTime(now);
        bo.setUpdateTime(now);
        bo.setUserId(LoginHelper.getUserId());

        // 2. 校验广告类型是否是开屏Open,并判断广告倒计时是否在3-15秒之间
        if (isInvalidOpenAdType(bo.getType(), bo.getDuration())) {
            throw new ServiceException("广告倒计时必须在3-15秒之间");
        }

        // 3. 转换为实体类
        SohuAdInfo add = BeanUtil.toBean(bo, SohuAdInfo.class);

        // 4. 保存前的数据校验
        validEntityBeforeSave(add);

        // 5. 构建其他字段
        buildOtherField(add);

        // 6. 插入数据
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            return false;
        }

        // 7. 保存广告标签
        bo.setId(add.getId());
        saveAdInfoLable(bo);

        // 延迟队列--更新广告缓存信息
        MsgContent msgContent = new MsgContent(bo.getId(), bo.getState(), BusyType.AdInfo.name());
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
        RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);

        // 8. 如果是插播广告，则插入关联关系
        if (AdsType.PATCHES.getCode().equalsIgnoreCase(bo.getType())) {
            insertPlayletRelate(bo);
        }

        // 9. 刷新排序
        this.refreshSort();
        return true;
    }

    /**
     * 校验开屏广告类型和倒计时
     *
     * @param type     广告类型
     * @param duration 广告倒计时
     * @return 是否无效的开屏广告类型
     */
    private boolean isInvalidOpenAdType(String type, Integer duration) {
        return AdsType.OPEN.getCode().equalsIgnoreCase(type) && (duration == null || duration < 3 || duration > 15);
    }

    /**
     * 保存广告标签
     *
     * @param bo 站点广告主体BO
     */
    private void saveAdInfoLable(SohuAdInfoBo bo) {
        // 1. 删除旧的标签关联
        this.sohuAdInfoLabelRelationMapper.delete(new LambdaQueryWrapper<SohuAdInfoLabelRelation>().eq(SohuAdInfoLabelRelation::getAdInfoId, bo.getId()));

        if (CollUtil.isEmpty(bo.getLabelIdList())) {
            return;
        }

        // 2. 批量插入新的标签关联
        List<SohuAdInfoLabelRelation> list = bo.getLabelIdList().stream()
                .map(labelId -> {
                    SohuAdInfoLabelRelation lable = new SohuAdInfoLabelRelation();
                    lable.setAdInfoId(bo.getId());
                    lable.setLabelId(labelId);
                    return lable;
                })
                .collect(Collectors.toList());
        this.sohuAdInfoLabelRelationMapper.insertBatch(list);
    }

    /**
     * 构建其他字段
     *
     * @param entity 站点广告主体
     */
    private void buildOtherField(SohuAdInfo entity) {
        LambdaQueryWrapper<SohuAdPlace> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAdPlace::getPlaceCode, entity.getAdPlace());
        lqw.last("limit 1");
        SohuAdPlace sohuAdPlace = this.sohuAdPlaceMapper.selectOne(lqw);
        if (sohuAdPlace != null) {
            entity.setType(sohuAdPlace.getType());
            entity.setPort(sohuAdPlace.getPort());
        }

        // 判断素材类型
        if (StrUtil.isNotBlank(entity.getImage())) {
            String fileType = FileUtils.getFileType(entity.getImage());
            entity.setMaterialType(fileType);
        }
    }

    /**
     * 刷新排序
     */
    private void refreshSort() {
        // 1. 序号改为0
        LambdaQueryWrapper<SohuAdInfo> lqw0 = new LambdaQueryWrapper<>();
        lqw0.eq(SohuAdInfo::getPrice, "-1.00");
        lqw0.gt(SohuAdInfo::getSortIndex, 0);
        List<SohuAdInfo> adInfoList0 = this.baseMapper.selectList(lqw0);
        if (CollUtil.isNotEmpty(adInfoList0)) {
            adInfoList0.forEach(entity -> entity.setSortIndex(0L));
            this.baseMapper.updateBatchById(adInfoList0);
        }

        // 2. 按钱重新排序
        LambdaQueryWrapper<SohuAdInfo> lqw = new LambdaQueryWrapper<>();
        lqw.gt(SohuAdInfo::getSortIndex, 0);
        lqw.isNotNull(SohuAdInfo::getPrice);
        lqw.orderByDesc(SohuAdInfo::getPrice);
        lqw.orderByDesc(SohuAdInfo::getCreateTime);
        List<SohuAdInfo> adInfoList = this.baseMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(adInfoList)) {
            for (int i = 0; i < adInfoList.size(); i++) {
                SohuAdInfo entity = adInfoList.get(i);
                entity.setSortIndex((long) Math.min(i + 1, SORT_INDEX_MAX_VALUE));
            }
            this.baseMapper.updateBatchById(adInfoList);
        }
    }

    /**
     * 新增广告短剧关联表
     */
    private void insertPlayletRelate(SohuAdInfoBo bo) {
        SohuPlayletRelateBo relateBo = BeanCopyUtils.copy(bo, SohuPlayletRelateBo.class);
        relateBo.setId(null);
        relateBo.setBusyType(AdsRelateType.Video.getCode());
        relateBo.setBusyCode(bo.getId());
        relateBo.setState(CommonState.WaitShelf.getCode());
        sohuPlayletRelateService.insertByBo(relateBo);
    }

    /**
     * 修改站点广告主体
     *
     * @param bo 站点广告主体BO
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SohuAdInfoBo bo) {
        // 1. 设置更新时间
        bo.setUpdateTime(new Date());

        // 2. 查询原始数据
        SohuAdInfo entity = baseMapper.selectById(bo.getId());
        if (entity == null) {
            throw new ServiceException("数据不存在");
        }

        // 3. 校验广告类型是否是开屏Open,并判断广告倒计时是否在3-15秒之间
        if (isInvalidOpenAdType(bo.getType(), bo.getDuration())) {
            throw new ServiceException("广告倒计时必须在3-15秒之间");
        }

        // 4. 复制属性
        SohuAdInfo update = BeanUtil.copyProperties(bo, SohuAdInfo.class);

        // 5. 更新插播广告关联关系
        if (AdsType.PATCHES.getCode().equalsIgnoreCase(bo.getType())) {
            updatePlayletRelate(bo);
        }

        // 6. 构建其他字段
        buildOtherField(update);

        // 7. 更新数据
        baseMapper.updateById(update);

        // 8. 保存广告标签
        saveAdInfoLable(bo);

        // 9. 刷新排序
        this.refreshSort();

        // 10. 发送延迟消息，更新广告缓存信息
        sendDelayMessage(update.getId(), update.getState(), BusyType.AdInfo.name());
        return true;
    }

    /**
     * 发送延迟消息，更新广告缓存信息
     *
     * @param adInfoId 广告ID
     * @param adState  广告状态
     * @param busyType 业务类型
     */
    private void sendDelayMessage(Long adInfoId, String adState, String busyType) {
        MsgContent msgContent = new MsgContent(adInfoId, adState, busyType);
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
        RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
    }

    /**
     * 修改广告短剧关联表
     *
     * @param bo 站点广告主体BO
     */
    private void updatePlayletRelate(SohuAdInfoBo bo) {
        SohuPlayletRelateVo sohuPlayletRelateVo = sohuPlayletRelateService.queryByBusyCode(bo.getId());
        if (sohuPlayletRelateVo == null) {
            log.warn("广告短剧关联表不存在, adInfoId: {}", bo.getId());
            return;
        }

        SohuPlayletRelateBo relateBo = BeanCopyUtils.copy(bo, SohuPlayletRelateBo.class);
        relateBo.setBusyType(AdsRelateType.Video.getCode());
        relateBo.setBusyCode(relateBo.getId());
        relateBo.setId(sohuPlayletRelateVo.getId());
        if (StrUtil.isBlank(bo.getState())) {
            relateBo.setState(CommonState.WaitShelf.getCode());
        }
        sohuPlayletRelateService.updateByBo(relateBo);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 站点广告主体
     */
    private void validEntityBeforeSave(SohuAdInfo entity) {
        if (entity == null) {
            throw new ServiceException("保存对象不能为空");
        }

        String image = entity.getImage();
        if (StrUtil.isBlank(image) || !NumberUtil.isNumber(image)) {
            return;
        }

        String url = remoteFileService.getUrl(Long.valueOf(image));
        entity.setImage(StrUtil.isBlankIfStr(url) ? image : url);
    }


    private void validEntityExist(SohuAdInfo entity) {
        if (Objects.isNull(entity) || (entity.getId() == null || entity.getId() <= 0L)) {
            throw new ServiceException(MessageUtils.message(""));
        }
    }

    /**
     * 批量删除站点广告主体
     *
     * @param ids  广告ID集合
     * @param type 广告类型
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, String type) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }

        // 1. 删除数据
        baseMapper.deleteBatchIds(ids);

        // 2. 如果是插播广告，则删除关联关系
        if (AdsType.PATCHES.getCode().equalsIgnoreCase(type)) {
            ids.forEach(sohuPlayletRelateService::deleteByBusyCode);
        }

        // 3. 发送延迟消息，更新广告缓存信息
        ids.forEach(id -> sendDelayMessage(id, CommonState.Delete.getCode(), BusyType.AdInfo.name()));
        return true;
    }


    /**
     * 修改广告状态(上架/下架)
     *
     * @param bo 站点广告主体BO，包含广告ID
     * @return 是否修改成功
     */
    @Override
    public Boolean operate(SohuAdInfoBo bo) {
        // 1. 获取广告ID
        Long id = bo.getId();
        SohuAdInfo adInfo = baseMapper.selectById(id);
        if (adInfo == null) {
            throw new ServiceException("广告不存在");
        }

        // 2. 切换状态
        String state = adInfo.getState();
        adInfo.setState(CommonState.OffShelf.getCode().equalsIgnoreCase(state) ? CommonState.OnShelf.getCode() : CommonState.OffShelf.getCode());

        // 3. 更新数据
        baseMapper.updateById(adInfo);

        // 4. 发送延迟消息，更新广告缓存信息
        sendDelayMessage(id, adInfo.getState(), BusyType.AdInfo.name());
        return true;
    }

    /**
     * 查询短剧开屏广告列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<PlayletOpenAdsListVo> queryOpenAdsList(PlayletOpenAdsQueryBo bo, PageQuery pageQuery) {
        Page<PlayletOpenAdsListVo> openAdsList = baseMapper.selectOpenAdsList(bo, PageQueryUtils.build(pageQuery));
        return TableDataInfoUtils.build(openAdsList);
    }

    /**
     * 查询短剧插播广告列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<PlayletPatchesAdsListVo> queryPatchesAdsList(PlayletPatchesAdsQueryBo bo, PageQuery pageQuery) {
        Page<PlayletPatchesAdsListVo> patchesAdsList = baseMapper.selectPatchesAdsList(bo, PageQueryUtils.build(pageQuery));
        return TableDataInfoUtils.build(patchesAdsList);
    }

    /**
     * 根据ID查询短剧插播广告详情
     *
     * @param id 广告ID
     * @return 短剧插播广告详情
     */
    @Override
    public PlayletPatchesAdsVo queryPatchesById(Long id) {
        // 1. 查询广告信息
        SohuAdInfo sohuAdInfo = baseMapper.selectById(id);
        if (sohuAdInfo == null) {
            return null;
        }

        // 2. 复制属性
        PlayletPatchesAdsVo patchesAdsVo = BeanCopyUtils.copy(sohuAdInfo, PlayletPatchesAdsVo.class);

        // 3. 查询关联关系
        SohuPlayletRelateVo relateVo = sohuPlayletRelateService.queryByBusyCode(id);
        if (relateVo == null) {
            return patchesAdsVo;
        }

        patchesAdsVo.setVideoId(relateVo.getVideoId());
        patchesAdsVo.setPlayletId(relateVo.getPlayletId());
        patchesAdsVo.setVideoStyle(relateVo.getVideoStyle());

        // 4. 查询视频和剧集信息
        SohuVideoVo videoVo = sohuVideoService.queryById(patchesAdsVo.getVideoId());
        if (videoVo == null) {
            return patchesAdsVo;
        }

        SohuPlayletVo playletVo = sohuPlayletService.queryByEpisodeRelevance(videoVo.getEpisodeRelevance());
        if (playletVo != null && StrUtil.isNotBlank(playletVo.getTitle())) {
            patchesAdsVo.setPlayletTitle(playletVo.getTitle());
        }

        patchesAdsVo.setEpisodeNumber(videoVo.getEpisodeNumber());
        return patchesAdsVo;
    }


    /**
     * 根据视频ID查询短剧插播广告
     *
     * @param videoId 视频ID
     * @return 短剧插播广告详情
     */
    @Override
    public PlayletPatchesAdsVo queryPatchesAdsByVideoId(Long videoId) {
        SohuPlayletRelateVo relateVo = sohuPlayletRelateService.queryByVideoId(videoId, AdsRelateType.Video.getCode());
        if (relateVo == null) {
            return null;
        }

        SohuAdInfo sohuAdInfo = baseMapper.selectById(relateVo.getBusyCode());
        if (sohuAdInfo == null) {
            return null;
        }

        PlayletPatchesAdsVo patchesAdsVo = BeanCopyUtils.copy(sohuAdInfo, PlayletPatchesAdsVo.class);
        patchesAdsVo.setVideoStyle(relateVo.getVideoStyle());
        return patchesAdsVo;
    }

    /**
     * 定时任务处理，用于更新广告状态
     *
     * @return 是否处理成功
     */
    @Override
    public Boolean advertisementJobHandler() {
        Date now = new Date();

        // 1. 查询已上架且在有效期范围内的广告数据
        LambdaQueryWrapper<SohuAdInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAdInfo::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuAdInfo::getAdPlace, AdPlaceEnum.MY_GUESS_YOU_LIKE.getPlaceCode());
        lqw.eq(SohuAdInfo::getDelFlag, Constants.ZERO);
        lqw.lt(SohuAdInfo::getStartTime, now);
        lqw.gt(SohuAdInfo::getOverTime, now);
        lqw.orderByAsc(SohuAdInfo::getSortIndex).orderByAsc(SohuAdInfo::getCreateTime);
        List<SohuAdInfo> sohuAdInfoVos = baseMapper.selectList(lqw);

        if (CollUtil.isEmpty(sohuAdInfoVos)) {
            RedisUtils.deleteObject(CacheConstants.ADVERTISEMENT_LIST);
            log.info("暂无有效广告数据");
            return true;
        }

        // 2. 根据业务类型分组，查询各业务表的数据状态
        List<Long> adIds = processAdInfoListByBusinessType(sohuAdInfoVos);

        // 3. 缓存有效广告ID集合
        if (CollUtil.isNotEmpty(adIds)) {
            RedisUtils.deleteObject(CacheConstants.ADVERTISEMENT_LIST);
            RedisUtils.setCacheList(CacheConstants.ADVERTISEMENT_LIST, adIds);
        }
        return true;
    }

    /**
     * 根据业务类型处理广告信息列表
     *
     * @param adInfoList 广告信息列表
     * @return 有效的广告ID列表
     */
    private List<Long> processAdInfoListByBusinessType(List<SohuAdInfo> adInfoList) {
        List<Long> adIds = new ArrayList<>();
        Map<String, List<SohuAdInfo>> map = adInfoList.stream().collect(Collectors.groupingBy(SohuAdInfo::getObjType));

        for (Map.Entry<String, List<SohuAdInfo>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<SohuAdInfo> value = entry.getValue();

            switch (key) {
                case "Video":
                    adIds.addAll(processVideoAds(value));
                    break;
                case "Playlet":
                    adIds.addAll(processPlayletAds(value));
                    break;
                case "Article":
                    adIds.addAll(processArticleAds(value));
                    break;
                case "Goods":
                    adIds.addAll(processGoodsAds(value));
                    break;
                case "Novel":
                    adIds.addAll(processNovelAds(value));
                    break;
                case "BusyOrder":
                    adIds.addAll(processBusyOrderAds(value));
                    break;
                case "AdInfo":
                    adIds.addAll(value.stream().map(SohuAdInfo::getId).collect(Collectors.toList()));
                    break;
                default:
                    break;
            }
        }
        return adIds;
    }

    /**
     * 处理视频广告
     *
     * @param adInfoList 广告信息列表
     * @return 有效的广告ID列表
     */
    private List<Long> processVideoAds(List<SohuAdInfo> adInfoList) {
        List<Long> videoIds = adInfoList.stream().map(SohuAdInfo::getObjId).collect(Collectors.toList());
        List<SohuVideoVo> videoVos = sohuVideoService.queryVideoByIds(videoIds, CommonState.OnShelf.getCode());
        if (CollUtil.isEmpty(videoVos)) {
            return Collections.emptyList();
        }
        List<Long> tempIds = videoVos.stream().map(SohuVideoVo::getId).collect(Collectors.toList());
        return adInfoList.stream().filter(adInfo -> tempIds.contains(adInfo.getObjId())).map(SohuAdInfo::getId).collect(Collectors.toList());
    }

    /**
     * 处理短剧广告
     *
     * @param adInfoList 广告信息列表
     * @return 有效的广告ID列表
     */
    private List<Long> processPlayletAds(List<SohuAdInfo> adInfoList) {
        List<Long> playletIds = adInfoList.stream().map(SohuAdInfo::getObjId).collect(Collectors.toList());
        List<SohuPlayletVo> playletVos = sohuPlayletService.queryPlayletByIds(playletIds, CommonState.OnShelf.getCode());
        if (CollUtil.isEmpty(playletVos)) {
            return Collections.emptyList();
        }
        List<Long> tempIds = playletVos.stream().map(SohuPlayletVo::getId).collect(Collectors.toList());
        return adInfoList.stream().filter(adInfo -> tempIds.contains(adInfo.getObjId())).map(SohuAdInfo::getId).collect(Collectors.toList());
    }

    /**
     * 处理文章广告
     *
     * @param adInfoList 广告信息列表
     * @return 有效的广告ID列表
     */
    private List<Long> processArticleAds(List<SohuAdInfo> adInfoList) {
        List<Long> articleIds = adInfoList.stream().map(SohuAdInfo::getObjId).collect(Collectors.toList());
        List<SohuArticleVo> articleVos = sohuArticleService.queryArticleByIds(articleIds, CommonState.OnShelf.getCode());
        if (CollUtil.isEmpty(articleVos)) {
            return Collections.emptyList();
        }
        List<Long> tempIds = articleVos.stream().map(SohuArticleVo::getId).collect(Collectors.toList());
        return adInfoList.stream().filter(adInfo -> tempIds.contains(adInfo.getObjId())).map(SohuAdInfo::getId).collect(Collectors.toList());
    }

    /**
     * 处理商品广告
     *
     * @param adInfoList 广告信息列表
     * @return 有效的广告ID列表
     */
    private List<Long> processGoodsAds(List<SohuAdInfo> adInfoList) {
        List<Long> goodsIds = adInfoList.stream().map(SohuAdInfo::getObjId).collect(Collectors.toList());
        List<SohuProductVo> goodsVos = remoteProductService.queryGoodsByIds(goodsIds, CommonState.OnShelf.getCode());
        if (CollUtil.isEmpty(goodsVos)) {
            return Collections.emptyList();
        }
        List<Long> tempIds = goodsVos.stream().map(SohuProductVo::getId).collect(Collectors.toList());
        return adInfoList.stream().filter(adInfo -> tempIds.contains(adInfo.getObjId())).map(SohuAdInfo::getId).collect(Collectors.toList());
    }

    /**
     * 处理小说广告
     *
     * @param adInfoList 广告信息列表
     * @return 有效的广告ID列表
     */
    private List<Long> processNovelAds(List<SohuAdInfo> adInfoList) {
        List<Long> novelIds = adInfoList.stream().map(SohuAdInfo::getObjId).collect(Collectors.toList());
        List<CourseVo> novelVos = remoteNovelService.queryNovelByIds(novelIds, CommonState.OnShelf.getCode());
        if (CollUtil.isEmpty(novelVos)) {
            return Collections.emptyList();
        }
        List<Long> tempIds = novelVos.stream().map(CourseVo::getCourseId).collect(Collectors.toList());
        return adInfoList.stream().filter(adInfo -> tempIds.contains(adInfo.getObjId())).map(SohuAdInfo::getId).collect(Collectors.toList());
    }

    /**
     * 处理运营单广告
     *
     * @param adInfoList 广告信息列表
     * @return 有效的广告ID列表
     */
    private List<Long> processBusyOrderAds(List<SohuAdInfo> adInfoList) {
        List<Long> orderIds = adInfoList.stream().map(SohuAdInfo::getObjId).collect(Collectors.toList());
        List<SohuBusyTaskSiteVo> orderVos = remoteBusyTaskSiteService.queryBusyOrderByIds(orderIds, CommonState.OnShelf.getCode());
        if (CollUtil.isEmpty(orderVos)) {
            return Collections.emptyList();
        }
        List<Long> tempIds = orderVos.stream().map(SohuBusyTaskSiteVo::getId).collect(Collectors.toList());
        return adInfoList.stream().filter(adInfo -> tempIds.contains(adInfo.getObjId())).map(SohuAdInfo::getId).collect(Collectors.toList());
    }

    /**
     * 刷新广告缓存
     * 1 广告已删除、已下架、已过期广告 删除对象
     * 2 广告处于有效期内，如果上架，新增对象
     * 3 广告处于有效期内，如果下架/强制下架，删除对象
     *
     * @param msgContent 消息内容
     * @return 是否刷新成功
     */
    @Override
    public Boolean refreshAdvertisementCache(MsgContent msgContent) {
        log.info("刷新广告缓存，消息内容：{}", msgContent);
        Long objId = msgContent.getObjId();
        String objType = msgContent.getObjType();
        String objStatus = msgContent.getObjStatus();

        // 1. 查询业务对象是否关联广告
        SohuAdInfo adInfo = getAdInfoByObjIdAndType(objId, objType);
        if (adInfo == null) {
            return true;
        }

        // 2. 获取缓存中的广告ID列表
        List<Long> adIds = RedisUtils.getCacheList(CacheConstants.ADVERTISEMENT_LIST);
        if (adIds == null) {
            adIds = new ArrayList<>();
        }

        Date now = new Date();
        // 判断是否在有效期内
        boolean valid = adInfo.getStartTime().before(now) && adInfo.getOverTime().after(now);

        // 3. 判断广告状态，并更新缓存
        List<String> invalidStatus = List.of(CommonState.OffShelf.getCode(), CommonState.CompelOff.getCode(), CommonState.Delete.getCode());

        if ((adInfo.getDelFlag().equals("2") || invalidStatus.contains(adInfo.getState()) || !valid) && adIds.contains(adInfo.getId())) {
            // 广告已删除、已下架、已过期，且存在于缓存中，则删除
            adIds.remove(adInfo.getId());
            updateAdvertisementCache(adIds);
            return true;
        }

        if (objStatus.equals(CommonState.OnShelf.getCode()) && valid && !adIds.contains(adInfo.getId())) {
            // 广告已上架、在有效期内、且不存在于缓存中，则新增
            adIds.add(adInfo.getId());
            updateAdvertisementCache(adIds);
            return true;
        }

        if (invalidStatus.contains(objStatus) && valid && adIds.contains(adInfo.getId())) {
            // 广告已下架/强制下架、在有效期内、且存在于缓存中，则删除
            adIds.remove(adInfo.getId());
            updateAdvertisementCache(adIds);
            return true;
        }

        return true;
    }

    /**
     * 更新广告缓存
     *
     * @param adIds 广告ID列表
     */
    private void updateAdvertisementCache(List<Long> adIds) {
        RedisUtils.deleteObject(CacheConstants.ADVERTISEMENT_LIST);
        RedisUtils.setCacheList(CacheConstants.ADVERTISEMENT_LIST, adIds);
    }

    /**
     * 根据业务对象ID和类型获取广告信息
     *
     * @param objId   业务对象ID
     * @param objType 业务对象类型
     * @return 广告信息
     */
    private SohuAdInfo getAdInfoByObjIdAndType(Long objId, String objType) {
        LambdaQueryWrapper<SohuAdInfo> lqw = Wrappers.lambdaQuery();
        if (BusyType.AdInfo.getType().equals(objType)) {
            lqw.eq(SohuAdInfo::getId, objId);
        } else {
            lqw.eq(SohuAdInfo::getObjId, objId);
            lqw.eq(SohuAdInfo::getObjType, objType);
        }
        lqw.eq(SohuAdInfo::getAdPlace, AdPlaceEnum.MY_GUESS_YOU_LIKE.getPlaceCode());
        lqw.last(" limit 1");
        return baseMapper.selectOne(lqw);
    }


    /**
     * 查询猜你喜欢列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<SohuGuessYouLikeVo> queryPageListOfAirec(SohuGuessYouLikeQueryBo bo, PageQuery pageQuery) {
        // 1. 参数校验
        if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
            throw new ServiceException("WRONG_PARAMS");
        }
        if (!AliyunAirecConstant.SCENE_VIDEO_MONEYMAKING.equals(bo.getAiRecSceneId())) {
            throw new ServiceException("WRONG_PARAMS");
        }
        if (Objects.nonNull(LoginHelper.getUserId())) {
            bo.setAiUserId(LoginHelper.getUserId().toString());
        }
        bo.setAiReturnCount(pageQuery.getPageSize());

        TableDataInfo<SohuGuessYouLikeVo> result = TableDataInfoUtils.build();
        result.setData(new ArrayList<>());

        // 2. 获取广告列表
        List<Long> adIds = RedisUtils.getCacheList(CacheConstants.ADVERTISEMENT_LIST);
        List<SohuAdInfoVo> adInfoList = new ArrayList<>();

        if (CollUtil.isNotEmpty(adIds)) {
            adInfoList = getAdInfoList(adIds, bo);
        }

        // 3. 填充广告列表到结果
        for (SohuAdInfoVo infoVo : adInfoList) {
            SohuGuessYouLikeVo vo = new SohuGuessYouLikeVo();
            vo.setObjType(BusyType.AdInfo.name());
            vo.setAdInfoVo(infoVo);
            result.getData().add(vo);
        }

        result.setTotal(result.getData().size());

        // 4. 如果广告数量不足，则使用视频进行兜底
        if (result.getData().size() < pageQuery.getPageSize()) {
            int remainingCount = pageQuery.getPageSize() - result.getData().size();
            SohuBusinessVideoBo videoBo = new SohuBusinessVideoBo();
            videoBo.setSiteId(bo.getSiteId());
            videoBo.setPageNum(pageQuery.getPageNum());
            videoBo.setPageSize(remainingCount);
            videoBo.setAiRec(true);
            videoBo.setAiRecImei(bo.getAiRecImei());
            videoBo.setAiUserId(bo.getAiUserId());
            videoBo.setAiRecSceneId(bo.getAiRecSceneId());
            TableDataInfo<SohuVideoVo> videoTableDataInfo = sohuVideoService.businessVideoListOfAirec(videoBo);

            if (CollUtil.isNotEmpty(videoTableDataInfo.getData())) {
                for (SohuVideoVo videoVo : videoTableDataInfo.getData()) {
                    SohuGuessYouLikeVo vo = new SohuGuessYouLikeVo();
                    vo.setObjType(BusyType.Video.name());
                    vo.setVideoVo(videoVo);
                    result.getData().add(vo);
                }
                result.setTotal(result.getData().size());
            }
        }

        return result;
    }

    /**
     * 获取广告信息列表
     *
     * @param adIds 广告ID列表
     * @param bo    查询条件
     * @return 广告信息列表
     */
    private List<SohuAdInfoVo> getAdInfoList(List<Long> adIds, SohuGuessYouLikeQueryBo bo) {
        LambdaQueryWrapper<SohuAdInfo> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuAdInfo::getId, adIds);
        lqw.eq(Objects.nonNull(bo.getSiteId()), SohuAdInfo::getSiteId, bo.getSiteId());
        lqw.eq(StrUtil.isNotBlank(bo.getAdPlace()), SohuAdInfo::getAdPlace, bo.getAdPlace());

        // 构建端口查询条件
        buildAdPortCondition(lqw, bo.getAdPort());

        lqw.orderByAsc(SohuAdInfo::getSortIndex);
        lqw.orderByDesc(SohuAdInfo::getCreateTime);

        List<SohuAdInfoVo> adInfoVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(adInfoVos)) {
            List<String> adPlaces = adInfoVos.stream().map(SohuAdInfoVo::getAdPlace).collect(Collectors.toList());
            //查询广告位名称
            List<SohuAdPlaceVo> adPlaceVos = sohuAdPlaceMapper.selectVoList(Wrappers.<SohuAdPlace>lambdaQuery().in(SohuAdPlace::getPlaceCode, adPlaces));
            Map<String, String> adPlaceMap = adPlaceVos.stream().collect(Collectors.toMap(SohuAdPlaceVo::getPlaceCode, SohuAdPlaceVo::getPlacePage));
            for (SohuAdInfoVo infoVo : adInfoVos) {
                infoVo.setAdPlacePage(adPlaceMap.get(infoVo.getAdPlace()));
            }
            adInfoVos.forEach(adInfoBuilderFactory::buildAdInfo);
        }
        return adInfoVos;
    }

    /**
     * 构建广告端口查询条件
     *
     * @param lqw    查询条件
     * @param adPort 广告端口
     */
    private void buildAdPortCondition(LambdaQueryWrapper<SohuAdInfo> lqw, String adPort) {
        if (AdPlacePortEnum.ALL.getCode().equals(adPort)) {
            return;
        }

        List<String> portList = new ArrayList<>();
        portList.add(AdPlacePortEnum.ALL.getCode());
        // 是否写死
        portList.add(AdPlacePortEnum.HSS_APP.getCode());

        if (AdPlacePortEnum.HIH_ALL.getCode().equals(adPort)) {
            portList.add(AdPlacePortEnum.HIH_ALL.getCode());
            portList.add(AdPlacePortEnum.HIH_APP.getCode());
            portList.add(AdPlacePortEnum.HIH_PC.getCode());
        } else if (AdPlacePortEnum.HIH_APP.getCode().equals(adPort)) {
            portList.add(AdPlacePortEnum.HIH_ALL.getCode());
            portList.add(AdPlacePortEnum.HIH_APP.getCode());
        } else if (AdPlacePortEnum.HIH_PC.getCode().equals(adPort)) {
            portList.add(AdPlacePortEnum.HIH_ALL.getCode());
            portList.add(AdPlacePortEnum.HIH_PC.getCode());
        } else if (StrUtil.isNotBlank(adPort)) {
            portList.add(adPort);
        }

        lqw.in(SohuAdInfo::getPort, portList);
    }


    /**
     * 查询视频列表（包含广告）
     */
    @Override
    public TableDataInfo<SohuVideoAdInfoVo> queryPageListOfVideo(SohuVideoAdInfoQueryBo bo, PageQuery pageQuery) {
        // 参数校验
        if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
            throw new ServiceException("WRONG_PARAMS");
        }
        if (!AliyunAirecConstant.SCENE_VIDEO_ALL.equals(bo.getAiRecSceneId())) {
            throw new ServiceException("WRONG_PARAMS");
        }

        TableDataInfo<SohuVideoAdInfoVo> result = TableDataInfoUtils.build();
        result.setData(new ArrayList<>());

        // 1. 查询视频列表
        TableDataInfo<SohuVideoVo> videoTableDataInfo = sohuVideoService.queryPageOfAirec(bo, pageQuery);
        List<SohuVideoVo> videoList = videoTableDataInfo.getData();

        if (CollectionUtils.isEmpty(videoList)) {
            return TableDataInfoUtils.build(Collections.emptyList());
        }

        // 2. 计算广告位置并获取广告
        List<Integer> adPositions = calculateAdPositions(pageQuery.getPageNum(), pageQuery.getPageSize(), videoList.size(), AD_INFO_INTERVAL_NUM);
        // 广告位端口暂时写死,后续可根据业务需求调整
        List<SohuAdInfoVo> adInfoVoList = getRandomAds(adPositions.size(), AdPlaceEnum.APP_VIDEO_LIST_INFO.getPlaceCode(), AdPlacePortEnum.HSS_APP.getCode(), bo.getSiteId());

        // 3. 构建混合列表并返回结果
        List<SohuVideoAdInfoVo> mixedList = buildVideoMixedList(videoList, adInfoVoList, adPositions);
        result.setData(mixedList);
        result.setTotal(videoTableDataInfo.getTotal());
        return result;
    }

    /**
     * 构建视频和广告混合列表
     *
     * @param videoList    视频列表
     * @param adInfoVoList 广告列表
     * @param adPositions  广告位置列表
     * @return 混合列表
     */
    private List<SohuVideoAdInfoVo> buildVideoMixedList(List<SohuVideoVo> videoList, List<SohuAdInfoVo> adInfoVoList, List<Integer> adPositions) {
        List<SohuVideoAdInfoVo> mixedList = new ArrayList<>(videoList.size() + adPositions.size());
        // 首先检查adInfoVoList是否为空
        if (adInfoVoList == null || adInfoVoList.isEmpty()) {
            // 如果广告列表为空，只添加商单
            for (SohuVideoVo sohuVideoVo : videoList) {
                SohuVideoAdInfoVo sohuVideoAdInfoVo = new SohuVideoAdInfoVo();
                sohuVideoAdInfoVo.setObjType(BusyType.Video.name());
                sohuVideoAdInfoVo.setVideoVo(sohuVideoVo);
                mixedList.add(sohuVideoAdInfoVo);
            }
            return mixedList;
        }
        int adIndex = 0;

        for (int i = 0; i < videoList.size(); i++) {
            // 添加视频
            SohuVideoAdInfoVo videoAdInfoVo = new SohuVideoAdInfoVo();
            videoAdInfoVo.setObjType(BusyType.Video.name());
            log.info("查看广告视频对象:{}", videoList.get(i));
            videoAdInfoVo.setVideoVo(videoList.get(i));
            mixedList.add(videoAdInfoVo);

            // 添加广告
            if (adIndex < adPositions.size() && i == adPositions.get(adIndex)) {
                videoAdInfoVo = new SohuVideoAdInfoVo();
                videoAdInfoVo.setObjType(BusyType.AdInfo.name());
                videoAdInfoVo.setAdInfoVo(adInfoVoList.get(adIndex));
                mixedList.add(videoAdInfoVo);
                adIndex++;
            }
        }

        return mixedList;
    }

    /**
     * 查询运营单列表（包含广告）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<SohuTaskSiteAdInfoVo> queryPageListOfTaskSite(SohuTaskSiteAdInfoQueryBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuTaskSiteAdInfoVo> result = TableDataInfoUtils.build();
        result.setData(new ArrayList<>());

        // 1. 查询置顶广告
        TableDataInfo<SohuTaskSiteAdInfoVo> topAdInfoTableData = getTopAdInfoList(bo, pageQuery);
        List<SohuTaskSiteAdInfoVo> mixedList = new ArrayList<>(topAdInfoTableData.getData());
        int adSize = topAdInfoTableData.getData().size();
        int pageSize = pageQuery.getPageSize();
        //广告已经占用的页数
        long adPageNum = topAdInfoTableData.getTotal() / pageSize;
        if (mixedList.size() >= pageSize) {
            result.setData(mixedList);
            result.setTotal(topAdInfoTableData.getTotal());
            return result;
        }
        // 2. 计算剩余商单数量
        int remainingCount = pageSize - mixedList.size();
        //计算taskSite的pageNum
        PageQuery taskSitePageQuery = new PageQuery(pageQuery.getPageNum() - (int) adPageNum, remainingCount);

        // 3. 查询商单列表
        TableDataInfo<SohuBusyTaskSiteVo> taskSiteTableData = getTaskSiteList(bo, taskSitePageQuery);
        List<SohuBusyTaskSiteVo> taskSiteList = taskSiteTableData.getData();

        // 4. 计算广告位
        List<Integer> adPositions = calculateAdPositions(pageQuery.getPageNum(), pageSize, taskSiteList.size(), AD_INFO_INTERVAL_NUM);

        // 5. 获取随机广告
        List<SohuAdInfoVo> adInfoVoList = getRandomAds(adPositions.size(), AdPlaceEnum.APP_TASK_LIST_INFO.getPlaceCode(), AdPlacePortEnum.HSS_APP.getCode(), bo.getSiteId());

        // 6. 构建混合列表
        buildTaskSiteMixedList(mixedList, taskSiteList, adInfoVoList, adPositions);
        result.setData(mixedList);
        result.setTotal(adSize + taskSiteTableData.getTotal());
        return result;
    }

    /**
     * 获取置顶广告列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 置顶广告列表
     */
    private TableDataInfo<SohuTaskSiteAdInfoVo> getTopAdInfoList(SohuTaskSiteAdInfoQueryBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuTaskSiteAdInfoVo> result = TableDataInfoUtils.build();
        result.setData(new ArrayList<>());

        Date now = new Date();
        LambdaQueryWrapper<SohuAdInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAdInfo::getState, CommonState.OnShelf.getCode());
        lqw.lt(SohuAdInfo::getStartTime, now);
        lqw.gt(SohuAdInfo::getOverTime, now);
        lqw.eq(Objects.nonNull(bo.getSiteId()), SohuAdInfo::getSiteId, bo.getSiteId());
        lqw.eq(SohuAdInfo::getAdPlace, AdPlaceEnum.TASK_TOP.getPlaceCode());

        // 构建端口查询条件
        buildAdPortCondition(lqw, bo.getAdPort());

        lqw.orderByAsc(SohuAdInfo::getSortIndex);
        lqw.orderByDesc(SohuAdInfo::getCreateTime);

        Page<SohuAdInfoVo> adInfoPage = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isNotEmpty(adInfoPage.getRecords())) {
            adInfoPage.getRecords().forEach(adInfoBuilderFactory::buildAdInfo);
            List<String> adPlaces = adInfoPage.getRecords().stream().map(SohuAdInfoVo::getAdPlace).collect(Collectors.toList());
            //查询广告位名称
            List<SohuAdPlaceVo> adPlaceVos = sohuAdPlaceMapper.selectVoList(Wrappers.<SohuAdPlace>lambdaQuery().in(SohuAdPlace::getPlaceCode, adPlaces));
            Map<String, String> adPlaceMap = adPlaceVos.stream().collect(Collectors.toMap(SohuAdPlaceVo::getPlaceCode, SohuAdPlaceVo::getPlacePage));

            List<SohuTaskSiteAdInfoVo> taskSiteAdInfoVos = new ArrayList<>();
            if (CollUtil.isNotEmpty(adInfoPage.getRecords())) {
                for (SohuAdInfoVo infoVo : adInfoPage.getRecords()) {
                    SohuTaskSiteAdInfoVo vo = new SohuTaskSiteAdInfoVo();
                    vo.setObjType(BusyType.AdInfo.name());
                    vo.setAdInfoVo(infoVo);
                    infoVo.setAdPlacePage(adPlaceMap.get(infoVo.getAdPlace()));
                    taskSiteAdInfoVos.add(vo);
                }
            }
            result.setData(taskSiteAdInfoVos);
            result.setTotal(adInfoPage.getTotal());
            if (adInfoPage.getRecords().size() == pageQuery.getPageSize()) {
                return result;
            }
        }
        return result;
    }

    /**
     * 获取商单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商单列表
     */
    private TableDataInfo<SohuBusyTaskSiteVo> getTaskSiteList(SohuTaskSiteAdInfoQueryBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.queryPageTaskList(bo, pageQuery);
    }

    /**
     * 构建商单和广告混合列表
     *
     * @param mixedList    混合列表
     * @param taskSiteList 商单列表
     * @param adInfoVoList 广告列表
     * @param adPositions  广告位置
     */
    private void buildTaskSiteMixedList(List<SohuTaskSiteAdInfoVo> mixedList, List<SohuBusyTaskSiteVo> taskSiteList, List<SohuAdInfoVo> adInfoVoList, List<Integer> adPositions) {
        // 首先检查adInfoVoList是否为空
        if (adInfoVoList == null || adInfoVoList.isEmpty()) {
            // 如果广告列表为空，只添加商单
            for (SohuBusyTaskSiteVo taskSite : taskSiteList) {
                SohuTaskSiteAdInfoVo taskSiteAdInfoVo = new SohuTaskSiteAdInfoVo();
                taskSiteAdInfoVo.setObjType(BusyType.BusyOrder.name());
                taskSiteAdInfoVo.setTaskSiteVo(taskSite);
                mixedList.add(taskSiteAdInfoVo);
            }
            return;
        }

        int adIndex = 0;
        for (int i = 0; i < taskSiteList.size(); i++) {
            // 添加商单
            SohuTaskSiteAdInfoVo taskSiteAdInfoVo = new SohuTaskSiteAdInfoVo();
            taskSiteAdInfoVo.setObjType(BusyType.BusyOrder.name());
            taskSiteAdInfoVo.setTaskSiteVo(taskSiteList.get(i));
            mixedList.add(taskSiteAdInfoVo);

            // 添加广告
            if (adIndex < adPositions.size() && i == adPositions.get(adIndex)) {
                // 确保不会超出adInfoVoList的范围
                if (adIndex < adInfoVoList.size()) {
                    taskSiteAdInfoVo = new SohuTaskSiteAdInfoVo();
                    taskSiteAdInfoVo.setObjType(BusyType.AdInfo.name());
                    taskSiteAdInfoVo.setAdInfoVo(adInfoVoList.get(adIndex));
                    mixedList.add(taskSiteAdInfoVo);
                    adIndex++;
                }
            }
        }
    }

    @Override
    public PlayletOpenAdsVo queryOpenDetail() {
        LambdaQueryWrapper<SohuAdInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAdInfo::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuAdInfo::getType, AdsType.OPEN.getCode());
        List<SohuAdInfoVo> sohuAdInfoVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(sohuAdInfoVos)) {
            SohuAdInfoVo sohuAdInfoVo = sohuAdInfoVos.get(0);
            // 使用 AdInfoBuilderFactory 构建广告信息
            SohuAdPlaceVo adPlaceVo = sohuAdPlaceMapper.selectVoOne(Wrappers.<SohuAdPlace>lambdaQuery().eq(SohuAdPlace::getPlaceCode, sohuAdInfoVo.getAdPlace()));
            if (Objects.nonNull(adPlaceVo)) {
                sohuAdInfoVo.setAdPlacePage(adPlaceVo.getPlacePage());
            }
            adInfoBuilderFactory.buildAdInfo(sohuAdInfoVo);
            PlayletOpenAdsVo adsVo = BeanCopyUtils.copy(sohuAdInfoVo, PlayletOpenAdsVo.class);
            return adsVo;
        }
        return null;
    }


    @Override
    public Boolean updateAdInfoState() {
        // 当前时间大于结束时间时，将广告状态修改为已过期
        Date nowTime = new Date();

        // 1. 查询已上架且已过期的广告ID
        LambdaQueryWrapper<SohuAdInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAdInfo::getState, CommonState.OnShelf.getCode());
        lqw.lt(SohuAdInfo::getOverTime, nowTime);
        List<Long> expiredAdIds = baseMapper.selectList(lqw).stream().map(SohuAdInfo::getId).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(expiredAdIds)) {
            // 2. 批量更新广告状态为已过期
            SohuAdInfo entity = new SohuAdInfo();
            entity.setState(CommonState.OffShelf.getCode());
            LambdaUpdateWrapper<SohuAdInfo> luw = Wrappers.lambdaUpdate();
            luw.in(SohuAdInfo::getId, expiredAdIds);
            baseMapper.update(entity, luw);
            log.info("定时任务: 批量更新 {} 条过期广告状态", expiredAdIds.size());
        }

        return true;
    }


    /**
     * 校验开屏广告数据
     */
    private void verifyAds(SohuAdInfo adInfo) {
        LambdaQueryWrapper<SohuAdInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAdInfo::getState, CommonState.OnShelf.getCode());
        lqw.eq(SohuAdInfo::getType, adInfo.getType());
        Long count = baseMapper.selectCount(lqw);
        Date date = new Date();
        if (count > 0) {
            //开屏广告在当前有效期内，仅有一个“已上架”的广告
            throw new ServiceException(MessageUtils.message("ads.is.exist"));
        }
        //开始时间不能大于结束时间
        if (!adInfo.getOverTime().after(adInfo.getStartTime())) {
            throw new ServiceException(MessageUtils.message("end.is.before.start"));
        }
        //显示时间不能大于当前时间
        //方便测试 暂时注释掉
        /*if (!adInfo.getStartTime().after(date)) {
            throw new ServiceException(MessageUtils.message("start.is.before.now"));
        }*/
    }

    @Override
    public Boolean refreshAdInfoByAdPlace(String adPlace) {
        // 1. 查询广告位信息
        LambdaQueryWrapper<SohuAdPlace> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAdPlace::getPlaceCode, adPlace);
        SohuAdPlace sohuAdPlace = this.sohuAdPlaceMapper.selectOne(lqw);

        if (sohuAdPlace == null) {
            log.warn("广告位 {} 不存在", adPlace);
            return true;
        }

        // 2. 查询需要更新的广告
        LambdaQueryWrapper<SohuAdInfo> infoLqw = Wrappers.lambdaQuery();
        infoLqw.eq(SohuAdInfo::getAdPlace, sohuAdPlace.getPlaceCode());
        List<SohuAdInfo> sohuAdInfoList = this.baseMapper.selectList(infoLqw);

        if (CollUtil.isNotEmpty(sohuAdInfoList)) {
            // 3. 批量更新广告信息
            sohuAdInfoList.forEach(sohuAdInfo -> {
                sohuAdInfo.setType(sohuAdPlace.getType());
                sohuAdInfo.setPort(sohuAdPlace.getPort());
            });
            baseMapper.updateBatchById(sohuAdInfoList);
            log.info("手动触发: 批量更新 {} 条广告信息，adPlace: {}", sohuAdInfoList.size(), adPlace);
        }

        return true;
    }

    @Override
    public Boolean refreshAdInfoTest() {
        // 1. 查询所有广告位
        List<SohuAdPlace> adPlaceList = this.sohuAdPlaceMapper.selectList();
        if (CollUtil.isEmpty(adPlaceList)) {
            return false;
        }

        // 2. 转换为 Map，方便后续查找
        Map<String, SohuAdPlace> adPlaceMap = adPlaceList.stream().collect(Collectors.toMap(SohuAdPlace::getPlaceCode, Function.identity()));

        // 3. 查询所有需要更新的广告
        LambdaQueryWrapper<SohuAdInfo> infoLqw = Wrappers.lambdaQuery();
        infoLqw.in(SohuAdInfo::getAdPlace, adPlaceMap.keySet());
        List<SohuAdInfo> sohuAdInfoList = this.baseMapper.selectList(infoLqw);

        if (CollUtil.isNotEmpty(sohuAdInfoList)) {
            // 4. 批量更新广告信息
            sohuAdInfoList.forEach(sohuAdInfo -> {
                SohuAdPlace sohuAdPlace = adPlaceMap.get(sohuAdInfo.getAdPlace());
                if (sohuAdPlace != null) {
                    sohuAdInfo.setType(sohuAdPlace.getType());
                    sohuAdInfo.setPort(sohuAdPlace.getPort());
                }
            });
            baseMapper.updateBatchById(sohuAdInfoList);
            log.info("定时任务: 批量更新 {} 条广告信息", sohuAdInfoList.size());
        }

        return true;
    }

    /**
     * 查询图文列表（包含广告）
     */
    @Override
    public TableDataInfo<SohuArticleAdInfoVo> queryPageListOfArticle(SohuArticleAdInfoQueryBo bo, PageQuery pageQuery) {
        validateParams(bo);

        // 1. 查询文章列表
        TableDataInfo<SohuArticleVo> articleTableDataInfo = sohuArticleService.queryPageOfAirec(bo, pageQuery);
        List<SohuArticleVo> articleList = articleTableDataInfo.getData();

        if (CollectionUtils.isEmpty(articleList)) {
            return TableDataInfoUtils.build(Collections.emptyList());
        }

        // 2. 计算广告位置并获取广告
        List<Integer> adPositions = calculateAdPositions(pageQuery.getPageNum(), pageQuery.getPageSize(),
                articleList.size(), AD_INFO_INTERVAL_NUM);
        List<SohuAdInfoVo> adInfoVoList = getRandomAds(adPositions.size(), AdPlaceEnum.APP_ARTICLE_LIST_INFO.getPlaceCode(), AdPlacePortEnum.HSS_APP.getCode(), bo.getSiteId());

        // 3. 构建混合列表并返回结果
        List<SohuArticleAdInfoVo> mixedList = buildMixedList(articleList, adInfoVoList, adPositions);
        TableDataInfo<SohuArticleAdInfoVo> tableDataInfo = TableDataInfoUtils.build(mixedList);
        tableDataInfo.setTotal(articleTableDataInfo.getTotal());
        return tableDataInfo;
    }

    /**
     * 参数验证
     */
    private void validateParams(SohuArticleAdInfoQueryBo bo) {
        if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
            throw new ServiceException("user_not_authenticated");
        }
        if (!AliyunAirecConstant.SCENE_ARTICLE_ALL.equals(bo.getAiRecSceneId())) {
            throw new ServiceException("invalid_scene_id");
        }
    }

    /**
     * 计算广告位置
     */
    private List<Integer> calculateAdPositions(int pageNum, int pageSize, int articleListSize, int interval) {
        if (articleListSize == 0 || interval <= 0) {
            return Collections.emptyList();
        }

        List<Integer> adPositions = new ArrayList<>();
        int startIndex = (pageNum - 1) * pageSize;

        for (int i = 0; i < articleListSize; i++) {
            int globalPosition = startIndex + i + 1;
            if (globalPosition % interval == 0) {
                adPositions.add(i);
            }
        }
        return adPositions;
    }

    /**
     * 获取随机广告
     */
    private List<SohuAdInfoVo> getRandomAds(int limitNum, String adPlace, String adPort, Long siteId) {
        if (limitNum < 1) {
            return Collections.emptyList();
        }

        Date now = new Date();
        LambdaQueryWrapper<SohuAdInfo> lqw = Wrappers.lambdaQuery(SohuAdInfo.class)
                .eq(SohuAdInfo::getState, CommonState.OnShelf.getCode())
                .lt(SohuAdInfo::getStartTime, now)
                .gt(SohuAdInfo::getOverTime, now)
                .eq(Objects.nonNull(siteId), SohuAdInfo::getSiteId, siteId)
                .eq(StringUtils.isNotBlank(adPlace), SohuAdInfo::getAdPlace, adPlace);

        // 根据 adPort 构建查询条件
        applyAdPortFilter(lqw, adPort);

        // 使用索引优化随机查询
        lqw.last("ORDER BY RAND() LIMIT " + limitNum);

        List<SohuAdInfoVo> adInfoVos = this.baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(adInfoVos)) {
            List<String> adPlaces = adInfoVos.stream().map(SohuAdInfoVo::getAdPlace).collect(Collectors.toList());
            //查询广告位名称
            List<SohuAdPlaceVo> adPlaceVos = sohuAdPlaceMapper.selectVoList(Wrappers.<SohuAdPlace>lambdaQuery().in(SohuAdPlace::getPlaceCode, adPlaces));
            Map<String, String> adPlaceMap = adPlaceVos.stream().collect(Collectors.toMap(SohuAdPlaceVo::getPlaceCode, SohuAdPlaceVo::getPlacePage));
            for (SohuAdInfoVo infoVo : adInfoVos) {
                infoVo.setAdPlacePage(adPlaceMap.get(infoVo.getAdPlace()));
            }
        }

        // 并行处理广告信息构建
        return adInfoVos.parallelStream()
                .peek(adInfoBuilderFactory::buildAdInfo)
                .collect(Collectors.toList());
    }

    /**
     * 应用广告端口过滤条件
     */
    private void applyAdPortFilter(LambdaQueryWrapper<SohuAdInfo> lqw, String adPort) {
        if (StringUtils.isBlank(adPort) || AdPlacePortEnum.ALL.getCode().equals(adPort)) {
            return;
        }

        List<String> validPorts = new ArrayList<>();
        validPorts.add(AdPlacePortEnum.ALL.getCode());

        if (AdPlacePortEnum.HIH_ALL.getCode().equals(adPort)) {
            validPorts.add(AdPlacePortEnum.HIH_ALL.getCode());
            validPorts.add(AdPlacePortEnum.HIH_APP.getCode());
            validPorts.add(AdPlacePortEnum.HIH_PC.getCode());
        } else if (AdPlacePortEnum.HIH_APP.getCode().equals(adPort)) {
            validPorts.add(AdPlacePortEnum.HIH_ALL.getCode());
            validPorts.add(AdPlacePortEnum.HIH_APP.getCode());
        } else if (AdPlacePortEnum.HIH_PC.getCode().equals(adPort)) {
            validPorts.add(AdPlacePortEnum.HIH_ALL.getCode());
            validPorts.add(AdPlacePortEnum.HIH_PC.getCode());
        } else {
            validPorts.add(adPort);
        }

        lqw.in(SohuAdInfo::getPort, validPorts);
    }

    /**
     * 构建混合列表
     */
    private List<SohuArticleAdInfoVo> buildMixedList(List<SohuArticleVo> articleList,
                                                     List<SohuAdInfoVo> adInfoVoList,
                                                     List<Integer> adPositions) {
        List<SohuArticleAdInfoVo> mixedList = new ArrayList<>(articleList.size() + adPositions.size());
        // 首先检查adInfoVoList是否为空
        if (adInfoVoList == null || adInfoVoList.isEmpty()) {
            // 如果广告列表为空，只添加商单
            for (SohuArticleVo sohuArticleVo : articleList) {
                SohuArticleAdInfoVo sohuArticleAdInfoVo = new SohuArticleAdInfoVo();
                sohuArticleAdInfoVo.setObjType(BusyType.Article.name());
                sohuArticleAdInfoVo.setArticleVo(sohuArticleVo);
                mixedList.add(sohuArticleAdInfoVo);
            }
            return mixedList;
        }
        int adIndex = 0;

        for (int i = 0; i < articleList.size(); i++) {
            // 添加文章
            mixedList.add(createArticleVO(articleList.get(i)));

            // 如果当前位置需要插入广告且有可用广告
            if (adIndex < adPositions.size() && i == adPositions.get(adIndex) && adIndex < adInfoVoList.size()) {
                mixedList.add(createAdVO(adInfoVoList.get(adIndex)));
                adIndex++;
            }
        }

        return mixedList;
    }

    /**
     * 创建文章VO
     */
    private SohuArticleAdInfoVo createArticleVO(SohuArticleVo article) {
        SohuArticleAdInfoVo vo = new SohuArticleAdInfoVo();
        vo.setObjType(BusyType.Article.name());
        vo.setArticleVo(article);
        return vo;
    }

    /**
     * 创建广告VO
     */
    private SohuArticleAdInfoVo createAdVO(SohuAdInfoVo ad) {
        SohuArticleAdInfoVo vo = new SohuArticleAdInfoVo();
        vo.setObjType(BusyType.AdInfo.name());
        vo.setAdInfoVo(ad);
        return vo;
    }

    @Override
    public SohuAdInfoVo getRandomAd(List<Long> excludeIds) {
        // 构建基础查询条件：已上架，指定广告位
        LambdaQueryWrapper<SohuAdInfo> lqw =
                Wrappers.lambdaQuery(SohuAdInfo.class)
                        .eq(SohuAdInfo::getState, CommonState.OnShelf.getCode())
                        .eq(SohuAdInfo::getAdPlace, AdPlaceEnum.APP_SD_AIFBSD.getPlaceCode());

        // 如果前端传递了需要排除的广告ID列表，则在查询条件中排除这些ID
        if (CollUtil.isNotEmpty(excludeIds)) {
            // 先判断 excludeIds 中是否包含视频广告
            long videoCount =
                    baseMapper.selectCount(
                            Wrappers.lambdaQuery(SohuAdInfo.class)
                                    .in(SohuAdInfo::getId, excludeIds)
                                    .eq(SohuAdInfo::getMaterialType, VIDEO_CONSTANT));

            // 如果存在视频广告，排除视频类型
            if (videoCount > 0) {
                lqw.ne(SohuAdInfo::getMaterialType, VIDEO_CONSTANT);
            }

            // 排除 excludeIds 中的广告
            lqw.notIn(SohuAdInfo::getId, excludeIds);
        }

        // 查询符合条件的广告列表
        List<SohuAdInfoVo> adList = baseMapper.selectVoList(lqw);

        // 如果广告列表为空，直接返回null
        if (CollUtil.isEmpty(adList)) {
            return null;
        }

        // 随机获取一个广告
        int randomIndex = RandomUtil.randomInt(adList.size());
        SohuAdInfoVo randomAd = adList.get(randomIndex);

        // 构建广告详细信息
        adInfoBuilderFactory.buildAdInfo(randomAd);

        return randomAd;
    }

    @Override
    public List<SohuAdInfoVo> getAdListByAdPlaceCode(String adPlaceCode) {
        LambdaQueryWrapper<SohuAdInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAdInfo::getAdPlace, adPlaceCode);
        lqw.eq(SohuAdInfo::getState, CommonState.OnShelf.getCode());
        lqw.orderByAsc(SohuAdInfo::getSortIndex);
        List<SohuAdInfoVo> adInfoVoList = this.baseMapper.selectVoList(lqw);

        if (CollUtil.isNotEmpty(adInfoVoList)) {
            return adInfoVoList.parallelStream()
                    .peek(adInfoBuilderFactory::buildAdInfo)
                    .collect(Collectors.toList());
        }
        return adInfoVoList;
    }
}
