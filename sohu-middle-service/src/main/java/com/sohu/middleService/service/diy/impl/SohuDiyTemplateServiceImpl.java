package com.sohu.middleService.service.diy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.diy.SohuDiyTemplateAuditBo;
import com.sohu.middle.api.bo.diy.SohuDiyTemplateAuditQueryBo;
import com.sohu.middle.api.bo.diy.SohuDiyTemplateBo;
import com.sohu.middle.api.bo.diy.SohuDiyTemplateShelfBo;
import com.sohu.middle.api.enums.SohuDiyTemplateEnum;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.diy.SohuDiyTemplateVo;
import com.sohu.middleService.domain.SohuPlatformIndustry;
import com.sohu.middleService.domain.SohuSite;
import com.sohu.middleService.domain.diy.SohuDiyTemplate;
import com.sohu.middleService.mapper.SohuPlatformIndustryMapper;
import com.sohu.middleService.mapper.SohuSiteMapper;
import com.sohu.middleService.mapper.diy.SohuDiyTemplateMapper;
import com.sohu.middleService.service.ISohuUserService;
import com.sohu.middleService.service.diy.ISohuDiyTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 装修模版Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RequiredArgsConstructor
@Service
public class SohuDiyTemplateServiceImpl implements ISohuDiyTemplateService {

    private final SohuDiyTemplateMapper baseMapper;
    private final SohuPlatformIndustryMapper sohuPlatformIndustryMapper;
    private final SohuSiteMapper sohuSiteMapper;
    private final ISohuUserService sohuUserService;

    /**
     * 查询装修模版
     */
    @Override
    public SohuDiyTemplateVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询装修模版列表
     */
    @Override
    public TableDataInfo<SohuDiyTemplateVo> queryPageList(SohuDiyTemplateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuDiyTemplate> lqw = buildQueryWrapper(bo);
        lqw.eq(SohuDiyTemplate::getUserId, LoginHelper.getUserId());
        Page<SohuDiyTemplateVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuDiyTemplateVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        for (SohuDiyTemplateVo record : records) {
            if (Objects.equals(record.getSceneType(), SohuDiyTemplateEnum.SCENE_TYPE.SITE.getCode()) || Objects.equals(record.getSceneType(), SohuDiyTemplateEnum.SCENE_TYPE.INDUSTRY.getCode())) {
                record.setShelfState(StrUtil.equalsAnyIgnoreCase(record.getAuditState(), CommonState.Edit.getCode()) ? null : record.getShelfState());
                if (StrUtil.equalsAnyIgnoreCase(record.getAuditState(), CommonState.WaitApprove.getCode())) {
                    record.setShelfState(null);
                }
            }
        }
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询装修模版列表
     */
    @Override
    public List<SohuDiyTemplateVo> queryList(SohuDiyTemplateBo bo) {
        LambdaQueryWrapper<SohuDiyTemplate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuDiyTemplate> buildQueryWrapper(SohuDiyTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuDiyTemplate> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuDiyTemplate::getName, bo.getName());
        lqw.eq(!CalUtils.isNullOrZero(bo.getUserId()), SohuDiyTemplate::getUserId, bo.getUserId());
        lqw.eq(bo.getSceneType() != null, SohuDiyTemplate::getSceneType, bo.getSceneType());
        lqw.in(CollUtil.isNotEmpty(bo.getSceneTypeList()), SohuDiyTemplate::getSceneType, bo.getSceneTypeList());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditState()), SohuDiyTemplate::getAuditState, bo.getAuditState());
        lqw.eq(StringUtils.isNotBlank(bo.getShelfState()), SohuDiyTemplate::getShelfState, bo.getShelfState());
        lqw.eq(StringUtils.isNotBlank(bo.getEffectConfig()), SohuDiyTemplate::getEffectConfig, bo.getEffectConfig());
        lqw.eq(StringUtils.isNotBlank(bo.getEditConfig()), SohuDiyTemplate::getEditConfig, bo.getEditConfig());
        lqw.eq(bo.getLastPublishTime() != null, SohuDiyTemplate::getLastPublishTime, bo.getLastPublishTime());
        lqw.eq(bo.getStatus() != null, SohuDiyTemplate::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增装修模版
     */
    @Override
    public Boolean insertByBo(SohuDiyTemplateBo bo) {
        SohuDiyTemplate add = BeanUtil.toBean(bo, SohuDiyTemplate.class);
        validEntityBeforeSave(add);
        add.setUserId(LoginHelper.getUserId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改装修模版
     */
    @Override
    public Boolean updateByBo(SohuDiyTemplateBo bo) {
        SohuDiyTemplate update = BeanUtil.toBean(bo, SohuDiyTemplate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuDiyTemplate entity) {
        //TODO 做一些数据校验,如唯一约束
        if (Objects.equals(entity.getSceneType(), SohuDiyTemplateEnum.SCENE_TYPE.SITE.getCode()) || Objects.equals(entity.getSceneType(), SohuDiyTemplateEnum.SCENE_TYPE.INDUSTRY.getCode())) {
            if (StrUtil.isBlankIfStr(entity.getAuditState()) || StrUtil.equalsAnyIgnoreCase(entity.getAuditState(), CommonState.WaitApprove.getCode())
                    || !StrUtil.equalsAnyIgnoreCase(entity.getAuditState(), CommonState.Edit.getCode())) {
                entity.setAuditState(CommonState.WaitApprove.getCode());
            }
            if (StrUtil.equalsAnyIgnoreCase(entity.getAuditState(), CommonState.WaitApprove.getCode())) {
                entity.setShelfState(null);
            }
            String name = entity.getName();
            LambdaQueryWrapper<SohuDiyTemplate> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SohuDiyTemplate::getSceneType, entity.getSceneType());
            lqw.eq(SohuDiyTemplate::getName, name);
            lqw.last("limit 1");
            SohuDiyTemplate exist = baseMapper.selectOne(lqw);
            if (Objects.nonNull(exist)) {
                if (CalUtils.isNullOrZero(entity.getId())) {
                    throw new ServiceException("模板名称已存在");
                } else {
                    if (!Objects.equals(exist.getId(), entity.getId())) {
                        throw new ServiceException("模板名称已存在");
                    }
                }
            }
        }
    }

    /**
     * 批量删除装修模版
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 检查数据是否存在
     *
     * @param diyTemplate 装修
     */
    public void checkExist(SohuDiyTemplate diyTemplate) {
        if (Objects.isNull(diyTemplate)) {
            throw new ServiceException(MessageUtils.message("data.is.null"));
        }
    }

    @Override
    public Boolean audit(SohuDiyTemplateAuditBo bo) {
        SohuDiyTemplate diyTemplate = this.baseMapper.selectById(bo.getId());
        checkExist(diyTemplate);
        if (StrUtil.equalsAnyIgnoreCase(bo.getAuditState(), CommonState.Pass.getCode(), CommonState.Refuse.getCode())) {
            if (!LoginHelper.hasRole(RoleCodeEnum.ADMIN)) {
                throw new ServiceException(MessageUtils.message("no.power"));
            }
        }
        Long loginId = LoginHelper.getUserId();
        diyTemplate.setAuditState(bo.getAuditState());
        diyTemplate.setRejectReason(bo.getRejectReason());
        diyTemplate.setUpdateTime(new Date());
        // 设置审核人ID
        diyTemplate.setAuditUser(loginId);
        return this.baseMapper.updateById(diyTemplate) > 0;
    }

    @Override
    public Map<Long, SohuDiyTemplateVo> queryMapByIndustryIdsForSite(Collection<Long> industryIds) {
        if (CollUtil.isEmpty(industryIds)) {
            return new HashMap<>();
        }
        List<Long> siteIds = sohuSiteMapper.queryByPlatformIndustryIds(industryIds, Constants.Y);
        if (CollUtil.isEmpty(siteIds)) {
            return new HashMap<>();
        }
        List<SohuSite> sites = sohuSiteMapper.selectBatchIds(siteIds);
        if (CollUtil.isEmpty(sites)) {
            return new HashMap<>();
        }
        Map<Long, SohuSite> industryMap = new HashMap<>();
        Map<Long, SohuSite> siteMap = new HashMap<>();
        for (SohuSite site : sites) {
            industryMap.put(site.getPlatformIndustryId(), site);
            siteMap.put(site.getId(), site);
        }
        LambdaQueryWrapper<SohuDiyTemplate> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuDiyTemplate::getSceneType, Arrays.asList(SohuDiyTemplateEnum.SCENE_TYPE.SITE.getCode(), SohuDiyTemplateEnum.SCENE_TYPE.INDUSTRY.getCode()));
        lqw.eq(SohuDiyTemplate::getAuditState, CommonState.Pass.getCode());
        lqw.eq(SohuDiyTemplate::getShelfState, CommonState.OnShelf.getCode());
        lqw.in(SohuDiyTemplate::getSceneCode, siteIds);
        List<SohuDiyTemplateVo> sohuDiyTemplates = this.baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(sohuDiyTemplates)) {
            return new HashMap<>();
        }
        Map<Long, SohuDiyTemplateVo> result = new HashMap<>();
        for (SohuDiyTemplateVo sohuDiyTemplate : sohuDiyTemplates) {
            Long sceneCode = sohuDiyTemplate.getSceneCode();
            SohuSite site = siteMap.get(sceneCode);
            if (Objects.isNull(site)) {
                continue;
            }
            SohuSite exist = industryMap.get(site.getPlatformIndustryId());
            if (Objects.isNull(exist)) {
                continue;
            }
            result.put(site.getPlatformIndustryId(), sohuDiyTemplate);
        }
        return result;
    }

    @Override
    public Map<String, SohuDiyTemplateVo> queryMapByCityCodes(Collection<String> cityCodes) {
        List<SohuSite> sites = sohuSiteMapper.selectList(SohuSite::getCityCode, cityCodes);
        if (CollUtil.isEmpty(sites)) {
            return new HashMap<>();
        }
        Set<Long> siteIds = new HashSet<>();
        Map<String, SohuSite> cityCodeMap = new HashMap<>();
        Map<Long, SohuSite> siteMap = new HashMap<>();
        for (SohuSite site : sites) {
            siteIds.add(site.getId());
            if (StrUtil.isNotBlank(site.getCityCode())) {
                cityCodeMap.put(site.getCityCode(), site);
            }
            siteMap.put(site.getId(), site);
        }
        LambdaQueryWrapper<SohuDiyTemplate> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuDiyTemplate::getSceneType, Arrays.asList(SohuDiyTemplateEnum.SCENE_TYPE.SITE.getCode()));
        lqw.in(SohuDiyTemplate::getSceneCode, siteIds);
        lqw.eq(SohuDiyTemplate::getShelfState, CommonState.OnShelf.getCode());
        List<SohuDiyTemplateVo> sohuDiyTemplates = this.baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(sohuDiyTemplates)) {
            return new HashMap<>();
        }
        Map<String, SohuDiyTemplateVo> result = new HashMap<>();
        for (SohuDiyTemplateVo template : sohuDiyTemplates) {
            Long sceneCode = template.getSceneCode();
            SohuSite site = siteMap.get(sceneCode);
            if (StrUtil.isNotBlank(site.getCityCode())) {
                result.put(site.getCityCode(), template);
            }
        }
        return result;
    }

    @Override
    public Map<Long, SohuDiyTemplateVo> queryMapBySiteIds(Collection<Long> siteIds) {
        LambdaQueryWrapper<SohuDiyTemplate> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuDiyTemplate::getSceneType, Arrays.asList(SohuDiyTemplateEnum.SCENE_TYPE.SITE.getCode()));
        lqw.in(SohuDiyTemplate::getSceneCode, siteIds);
        lqw.eq(SohuDiyTemplate::getAuditState, CommonState.Pass.getCode());
        lqw.eq(SohuDiyTemplate::getShelfState, CommonState.OnShelf.getCode());
        List<SohuDiyTemplateVo> sohuDiyTemplates = this.baseMapper.selectVoList(lqw);
        return CollUtil.isEmpty(sohuDiyTemplates) ? new HashMap<>() : sohuDiyTemplates.stream().collect(Collectors.toMap(SohuDiyTemplateVo::getSceneCode, u -> u));
    }

    @Override
    public TableDataInfo<SohuDiyTemplateVo> auditList(SohuDiyTemplateAuditQueryBo bo, PageQuery pageQuery) {
        Page<SohuDiyTemplateVo> result = baseMapper.auditList(bo, PageQueryUtils.build(pageQuery));
        List<SohuDiyTemplateVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        Set<Long> siteIds = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        for (SohuDiyTemplateVo record : records) {
            siteIds.add(record.getSceneCode());
            if (!CalUtils.isNullOrZero(record.getAuditUser())) {
                userIds.add(record.getAuditUser());
            }
            if (!CalUtils.isNullOrZero(record.getStationmasterId())) {
                userIds.add(record.getStationmasterId());
            }
        }
        LambdaQueryWrapper<SohuSite> siteLqw = Wrappers.lambdaQuery();
        siteLqw.in(SohuSite::getId, siteIds);
        List<SohuSiteVo> siteVos = sohuSiteMapper.selectVoList(siteLqw);
        Map<Long, SohuSiteVo> siteVoMap = CollUtil.isEmpty(siteVos) ? new HashMap<>() : siteVos.stream().collect(Collectors.toMap(SohuSiteVo::getId, s -> s));
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        List<SohuPlatformIndustry> platformIndustryList = sohuPlatformIndustryMapper.selectList();
        Map<Long, String> platformIndustryMap = CollUtil.isEmpty(platformIndustryList) ? new HashMap<>() : platformIndustryList.stream().filter(p -> Objects.equals(p.getIsDel(), 0L)).collect(Collectors.toMap(SohuPlatformIndustry::getId, SohuPlatformIndustry::getName));

        for (SohuDiyTemplateVo record : records) {
            LoginUser auditUser = userMap.get(record.getAuditUser());
            record.setAuditUserName(Objects.nonNull(auditUser) ? StringUtils.getValidString(auditUser.getNickname(), auditUser.getUsername()) : null);
            SohuSiteVo siteVo = siteVoMap.get(record.getSceneCode());
            if (Objects.isNull(siteVo)) {
                continue;
            }
            LoginUser stationmasterUser = userMap.get(record.getStationmasterId());
            if (Objects.nonNull(stationmasterUser)) {
                siteVo.setStationmasterPhone(stationmasterUser.getPhoneNumber());
                siteVo.setStationmasterAvatar(stationmasterUser.getAvatar());
                siteVo.setStationmasterName(StringUtils.getValidString(stationmasterUser.getNickname(), stationmasterUser.getUsername()));
            }
            Long platformIndustryId = siteVo.getPlatformIndustryId();
            if (!CalUtils.isNullOrZero(platformIndustryId)) {
                siteVo.setPlatformIndustryNames(platformIndustryMap.get(platformIndustryId));
            }
            record.setSite(siteVo);
        }
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public Boolean shelf(SohuDiyTemplateShelfBo bo) {
        SohuDiyTemplate diyTemplate = this.baseMapper.selectById(bo.getId());
        checkExist(diyTemplate);
        if (StrUtil.equalsAnyIgnoreCase(bo.getShelfState(), CommonState.CompelOff.getCode())) {
            // 强制下架
            if (!LoginHelper.hasRole(RoleCodeEnum.ADMIN)) {
                throw new ServiceException(MessageUtils.message("no.power"));
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(bo.getShelfState(), CommonState.OnShelf.getCode())) {
            // 上架操作，其它上架的就要下架
            LambdaUpdateWrapper<SohuDiyTemplate> lqw = new LambdaUpdateWrapper<>();
            lqw.eq(SohuDiyTemplate::getUserId, diyTemplate.getUserId());
            lqw.eq(SohuDiyTemplate::getSceneType, diyTemplate.getSceneType());
            lqw.eq(SohuDiyTemplate::getSceneCode, diyTemplate.getSceneCode());
            lqw.set(SohuDiyTemplate::getShelfState, CommonState.OffShelf.getCode());
            this.baseMapper.update(new SohuDiyTemplate(), lqw);
        }
        diyTemplate.setShelfState(bo.getShelfState());
        diyTemplate.setShelfReason(bo.getShelfReason());
        diyTemplate.setUpdateTime(new Date());
        return this.baseMapper.updateById(diyTemplate) > 0;
    }

}
