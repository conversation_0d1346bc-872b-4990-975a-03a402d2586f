package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.PlatformRoleCodeEnum;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.enums.SiteType;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuBindStationCheckBo;
import com.sohu.middle.api.bo.SohuSiteBo;
import com.sohu.middle.api.bo.SohuSiteV2QueryBo;
import com.sohu.middle.api.model.SohuAccountModel;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middleService.domain.SohuPlatformIndustry;
import com.sohu.middleService.domain.SohuSite;
import com.sohu.middleService.mapper.SohuPlatformIndustryMapper;
import com.sohu.middleService.mapper.SohuSiteMapper;
import com.sohu.middleService.service.ISohuSiteV2Service;
import com.sohu.middleService.service.ISohuUserService;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.system.api.RemotePlatformRoleService;
import com.sohu.system.api.RemoteSysNoticeService;
import com.sohu.system.api.RemoteSysRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuSiteV2ServiceImpl extends SohuBaseServiceImpl<SohuSiteMapper, SohuSite, SohuSiteVo> implements ISohuSiteV2Service {

    private final SohuPlatformIndustryMapper sohuPlatformIndustryMapper;
    private final SohuSiteMapper sohuSiteMapper;

    private final ISohuUserService sohuUserService;

    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemotePlatformRoleService remotePlatformRoleService;
    @DubboReference
    private RemoteSysNoticeService remoteSysNoticeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addV2(SohuSiteBo bo) {
        log.info("新增绑定站长");
        bo.setId(null);
        SohuBindStationCheckBo checkBo = SohuBindStationCheckBo.builder().stationmasterPhone(bo.getStationmasterPhone()).build();
        return siteBindStation(bo, checkBo);
    }

    @Override
    public TableDataInfo<SohuSiteVo> queryPageListV2(SohuSiteV2QueryBo bo, PageQuery pageQuery) {
        Page<SohuSiteVo> page = baseMapper.queryPageListV2(bo, PageQueryUtils.build(pageQuery));
        if (CollUtil.isEmpty(page.getRecords())) {
            return TableDataInfoUtils.build();
        }
        TableDataInfo<SohuSiteVo> result = TableDataInfoUtils.build(page);
        List<SohuPlatformIndustry> platformIndustryList = sohuPlatformIndustryMapper.selectList();
        Map<Long, String> platformIndustryMap = CollUtil.isEmpty(platformIndustryList) ? new HashMap<>() :
                platformIndustryList.stream().filter(p -> Objects.equals(p.getIsDel(), 0L)).collect(Collectors.toMap(SohuPlatformIndustry::getId, SohuPlatformIndustry::getName));
        List<SohuSiteVo> records = page.getRecords();
        Set<Long> operatorList = records.stream().map(SohuSiteVo::getOperator).collect(Collectors.toSet());
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(operatorList);
        for (SohuSiteVo record : records) {
            if (!CalUtils.isNullOrZero(record.getOperator())) {
                LoginUser user = userMap.get(record.getOperator());
                record.setOperatorName(Objects.nonNull(user) ? StringUtils.getValidString(user.getNickname(), user.getUsername()) : null);
            }
            String platformIndustryIds = record.getPlatformIndustryIds();
            if (StrUtil.isBlankIfStr(platformIndustryIds)) {
                continue;
            }
            // 解析行业ID
            List<Long> industryIds = Arrays.stream(platformIndustryIds.split(StrPool.COMMA))
                    .map(Long::parseLong).collect(Collectors.toList());
            // 设置行业名称（逗号拼接）
            List<String> industryNames = industryIds.stream()
                    .map(platformIndustryMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            record.setPlatformIndustryNames(String.join(",", industryNames));
        }
        result.setData(records);
        return result;
    }

    @Override
    public SohuSiteVo getInfoV2(Long id) {
        SohuSiteVo siteVo = sohuSiteMapper.selectVoById(id);
        if (Objects.isNull(siteVo)) {
            return new SohuSiteVo();
        }
        if (!CalUtils.isNullOrZero(siteVo.getStationmasterId())) {
            SohuAccountVo accountVo = remoteAccountService.queryByUserId(siteVo.getStationmasterId());
            if (Objects.nonNull(accountVo)) {
                SohuAccountModel accountModel = BeanUtil.copyProperties(accountVo, SohuAccountModel.class);
                siteVo.setAccount(accountModel);
            }
            LoginUser stationmasterUser = sohuUserService.queryById(siteVo.getStationmasterId());
            siteVo.setStationmasterPhone(stationmasterUser.getPhoneNumber());
            siteVo.setStationmasterAvatar(stationmasterUser.getAvatar());
            siteVo.setStationmasterName(StringUtils.getValidString(stationmasterUser.getNickname(), stationmasterUser.getUsername()));
        }
        return siteVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateV2(SohuSiteBo bo) {
        log.info("编辑绑定站长");
        SohuBindStationCheckBo checkBo = SohuBindStationCheckBo.builder().stationmasterPhone(bo.getStationmasterPhone()).checkHasStationRole(false).build();
        return siteBindStation(bo, checkBo);
    }

    /**
     * 绑定站长
     *
     * @param bo
     * @return
     */
    private Boolean siteBindStation(SohuSiteBo bo, SohuBindStationCheckBo checkBo) {
        // 1. 验证用户信息
        LoginUser user = validateUser(bo, checkBo);

        // 2. 处理账户信息（移出循环）
        processAccount(bo, user.getUserId());

        // 3. 处理站点绑定
        Set<SiteType> roleTypes = new HashSet<>();
        if (BooleanUtil.isTrue(bo.getSiteCate())) {
            roleTypes = processCityStations(bo, user);
        } else {
            roleTypes = processIndustryStations(bo, user);
        }

        // 4. 处理角色授予（移出循环）
        grantRoles(bo.getStationmasterStatus(), roleTypes, user.getUserId());

        // 5. 刷新缓存（移出循环）
        sohuUserService.flushLoginCacheByUserId(user.getUserId());

        // 6. 发送站长需要看到的公告
        remoteSysNoticeService.sendNotice(user.getUserId(), RoleCodeEnum.CityStationAgent.getCode(), new Date());
        return true;
    }

    private LoginUser validateUser(SohuSiteBo bo, SohuBindStationCheckBo checkBo) {
        LoginUser user = sohuUserService.getUserInfoByPhone(bo.getStationmasterPhone());
        bindStationCheck(checkBo);
        return user;
    }

    private void processAccount(SohuSiteBo bo, Long userId) {
        SohuAccountModel account = bo.getAccount();
        if (account == null) {
            throw new ServiceException("账户信息不能为空");
        }

        if (CalUtils.isNullOrZero(account.getId())) {
            SohuAccountBo accountBo = BeanUtil.copyProperties(account, SohuAccountBo.class);
            accountBo.setId(null);
            accountBo.setState(null);
            accountBo.setUserId(userId);
            remoteAccountService.save(accountBo);
        } else {
            SohuAccountBo accountBo = BeanUtil.copyProperties(account, SohuAccountBo.class);
            accountBo.setUserId(userId);
            remoteAccountService.update(accountBo);
        }
    }

    private Set<SiteType> processCityStations(SohuSiteBo bo, LoginUser user) {
        Set<SiteType> roleTypes = new HashSet<>();
        Long userId = user.getUserId();
        Long operatorId = LoginHelper.getUserId();

        List<Long> siteIds = bo.getSiteIds();
        for (Long siteId : siteIds) {
            SohuSite site = sohuSiteMapper.selectById(siteId);
            if (Objects.isNull(site)) {
                throw new ServiceException(MessageUtils.message("SITE_NOT_FOUNT"));
            }
            site.setState(bo.getState());
            // 更新站点信息
            site.setStationmasterId(userId);
            site.setStationmasterStatus(bo.getStationmasterStatus());
            site.setStationmasterBeginTime(bo.getStationmasterBeginTime());
            site.setStationmasterEndTime(bo.getStationmasterEndTime());
            site.setOperator(operatorId);
            site.setUpdateTime(new Date());
            sohuSiteMapper.updateById(site);

            // 收集需要授权的角色类型
            roleTypes.add(SiteType.valueOf(site.getType()));
        }
        return roleTypes;
    }

    private Set<SiteType> processIndustryStations(SohuSiteBo bo, LoginUser user) {
        Set<SiteType> roleTypes = new HashSet<>();
        String platformIndustryIds = bo.getPlatformIndustryIds();
        if (StrUtil.isBlank(platformIndustryIds)) {
            throw new ServiceException("请选择行业");
        }

        // 解析行业ID
        List<Long> industryIds = Arrays.stream(platformIndustryIds.split(StrPool.COMMA))
                .map(Long::parseLong).collect(Collectors.toList());


        // 获取行业信息
        Map<Long, String> industryMap = sohuPlatformIndustryMapper.selectBatchIds(industryIds).stream()
                .filter(p -> Objects.equals(p.getIsDel(), 0L))
                .collect(Collectors.toMap(SohuPlatformIndustry::getId, SohuPlatformIndustry::getName));

        // 获取现有站点
        Map<Long, SohuSite> siteMap = sohuSiteMapper.selectList(
                new LambdaQueryWrapper<SohuSite>().in(SohuSite::getPlatformIndustryId, industryIds)
        ).stream().collect(Collectors.toMap(SohuSite::getPlatformIndustryId, s -> s));

        // 检查冲突
        checkIndustryConflicts(industryMap, siteMap, user);

        // 更新或创建站点
        Long userId = user.getUserId();
        Long operatorId = LoginHelper.getUserId();

        for (Long industryId : industryIds) {
            SohuSite site = siteMap.get(industryId);
            if (site != null) {
                // 更新现有站点
                site.setState(bo.getState());
                site.setStationmasterStatus(bo.getStationmasterStatus());
                site.setStationmasterBeginTime(bo.getStationmasterBeginTime());
                site.setStationmasterEndTime(bo.getStationmasterEndTime());
                site.setOperator(operatorId);
                site.setUpdateTime(new Date());
                sohuSiteMapper.updateById(site);
            } else {
                // 创建新站点
                site = createNewSite(bo, industryMap.get(industryId), industryId, userId, operatorId);
                site.setState(bo.getState());
                site.setUpdateTime(new Date());
                sohuSiteMapper.insert(site);
            }
            roleTypes.add(SiteType.valueOf(site.getType()));
        }
        return roleTypes;
    }

    private void checkIndustryConflicts(Map<Long, String> industryMap, Map<Long, SohuSite> siteMap, LoginUser user) {
        for (Map.Entry<Long, SohuSite> entry : siteMap.entrySet()) {
            SohuSite site = entry.getValue();
            if (site != null &&
                    !CalUtils.isNullOrZero(site.getStationmasterId()) &&
                    !Objects.equals(site.getStationmasterId(), user.getUserId())) {

                String industryName = industryMap.get(entry.getKey());
                LoginUser stationmasterUser = sohuUserService.queryById(site.getStationmasterId());
                throw new ServiceException(String.format("%s行业已被%s站长选择", industryName,
                        StringUtils.getValidString(stationmasterUser.getNickname(), stationmasterUser.getUsername())));
            }
        }
    }

    private SohuSite createNewSite(SohuSiteBo bo, String industryName, Long industryId, Long userId, Long operatorId) {
        SohuSite site = new SohuSite();
        site.setName(industryName);
        site.setState(Constants.Y);
        site.setType(SiteType.City.name());
        site.setSiteCate(false);
        site.setIsDefault(Constants.N);
        site.setStationmasterId(userId);
        site.setStationmasterStatus(bo.getStationmasterStatus());
        site.setStationmasterBeginTime(bo.getStationmasterBeginTime());
        site.setStationmasterEndTime(bo.getStationmasterEndTime());
        site.setPlatformIndustryId(industryId);
        site.setOperator(operatorId);
        return site;
    }

    private void grantRoles(Integer stationmasterStatus, Set<SiteType> roleTypes, Long userId) {
        if (stationmasterStatus == 0) {
            for (SiteType type : roleTypes) {
                if (type == SiteType.City) {
                    remoteSysRoleService.insertUserRole(RoleCodeEnum.CityStationAgent.getCode(), userId);
                    remotePlatformRoleService.insertUserRole(PlatformRoleCodeEnum.CityStationAgent.getCode(), userId);
                } else if (type == SiteType.Country) {
                    remoteSysRoleService.insertUserRole(RoleCodeEnum.CountryStationAgent.getCode(), userId);
                    remotePlatformRoleService.insertUserRole(PlatformRoleCodeEnum.CountryStationAgent.getCode(), userId);
                }
            }
        } else {
            // 禁用站长状态
            //取消用户城市站长角色
            this.remoteSysRoleService.deleteUserRole(RoleCodeEnum.CityStationAgent.getCode(), userId);
        }
    }


    @Override
    public SohuSiteVo queryByPlatformIndustry(Long platformIndustryId) {
        return sohuSiteMapper.queryByPlatformIndustry(platformIndustryId, null);
    }

    @Override
    public Boolean bindStationCheck(SohuBindStationCheckBo checkBo) {
        String stationmasterPhone = checkBo.getStationmasterPhone();
        LoginUser user = sohuUserService.getUserInfoByPhone(stationmasterPhone);
        if (Objects.isNull(user)) {
            throw new ServiceException("用户未注册，请先联系用户注册许愿狐");
        }
        if (BooleanUtil.isTrue(checkBo.isCheckHasAgentRole()) && LoginHelper.hasRole(user, RoleCodeEnum.Agent)) {
            throw new ServiceException("当前账号为服务商，不支持添加为站长");
        }
        List<SohuSite> siteList = sohuSiteMapper.selectList(SohuSite::getStationmasterId, user.getUserId());
        if (BooleanUtil.isTrue(checkBo.isCheckHasStationRole()) && CollUtil.isNotEmpty(siteList)) {
            throw new ServiceException("当前账号已被添加");
        }
        SohuAccountVo accountVo = remoteAccountService.queryByUserId(user.getUserId());
        if (BooleanUtil.isTrue(checkBo.isCheckPersonAccount()) && Objects.nonNull(accountVo) && StrUtil.equalsAnyIgnoreCase(accountVo.getAccountType(), "personal")) {
            throw new ServiceException("当前账号已认证个人，请联系站长升级为企业认证后添加");
        }
        return Boolean.TRUE;
    }
}
