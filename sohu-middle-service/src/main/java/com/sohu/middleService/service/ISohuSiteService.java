package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuSiteBo;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.common.SohuSiteOfEnableVo;
import com.sohu.middleService.domain.SohuSite;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 站点Service接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ISohuSiteService extends ISohuBaseService<SohuSite, SohuSiteVo> {

//    /**
//     * 查询站点
//     */
//    SohuSiteVo queryById(Long id);
//

    /**
     * 查询站点列表
     */
    TableDataInfo<SohuSiteVo> queryPageList(SohuSiteBo bo, PageQuery pageQuery);

    /**
     * 查询站点列表
     */
    List<SohuSiteVo> queryList(SohuSiteBo bo);

    /**
     * 修改站点
     */
    Boolean insertByBo(SohuSiteBo bo);

    /**
     * 修改站点
     */
    Boolean updateByBo(SohuSiteBo bo);

//    /**
//     * 校验并批量删除站点信息
//     */
//    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询站点结构树
     */
    List<SohuSiteVo> getSiteTree(SohuSiteBo bo);


    /**
     * 查询站点结构树
     */
    List<SohuSiteVo> getSiteClearTree(SohuSiteBo bo);

    /**
     * 获取已启用的站点结构树
     */
    List<SohuSiteOfEnableVo> getSiteTreeOfEnable();

    /**
     * 获取已启用的站点集合
     */
    List<SohuSiteOfEnableVo> getSiteListOfEnable();

    /**
     * 获取腾讯地图逆地址解析结果
     *
     * @param location
     * @return
     */
    Object getLocationDetail(String location);

    /**
     * 开通站点
     *
     * @param bo
     * @return
     */
    Boolean openSite(SohuSiteBo bo);

    /**
     * 上线站点
     *
     * @param siteId 站点ID
     * @return
     */
    Boolean online(Long siteId);


    /**
     * 下线站点
     *
     * @param siteId 站点ID
     * @return
     */
    Boolean offline(Long siteId);

    /**
     * 根据站点集合查询set集合
     *
     * @param siteIds
     * @return
     */
    Map<Long, SohuSiteVo> queryMap(Set<Long> siteIds);

    /**
     * 根据行业站点集合查询set集合
     *
     * @param siteIds
     * @return
     */
    Map<Long, SohuSiteVo> queryIndustryMap(Set<Long> siteIds);

    /**
     * 根据站点id查询列表
     *
     * @param ids
     * @return
     */
    List<SohuSiteVo> selectSiteList(Collection<Long> ids);

    /**
     * 根据城市站点查询国家站数据
     *
     * @param id
     * @return
     */
    SohuSiteVo selectSiteByPid(Long id);

    /**
     * 根据国家站点查询国家编码
     *
     * @param id
     * @return
     */
    String selectCountryCodeById(Long id);

    SohuSiteVo selectSiteByUserId(Long userId);

    /**
     * 查询所有子站点
     *
     * @param pid
     * @return
     */
    List<Long> queryChildSiteIds(Long pid);

    /**
     * 站点英文名称
     *
     * @param categoryEnName 站点英文名称
     * @return {@link Long} 站点ID
     */
    Long getSiteIdByEnName(String categoryEnName);

    /**
     * 根据用户id查询站点信息
     *
     * @param stationmasterId 站长用户ID
     * @return {@link SohuSiteVo}
     */
    SohuSiteVo queryByStationmasterId(Long stationmasterId);

    /**
     * 根据用户id查询站点信息
     *
     * @param stationmasterId 站长用户ID
     * @return {@link SohuSiteVo}
     */
    List<SohuSiteVo> listByStationmasterId(Long stationmasterId, Integer stationmasterStatus);

    /**
     * 根据IP获取站点信息
     *
     * @param ip ip
     * @return {@link SohuSiteVo}
     */
    SohuSiteVo getSiteByIp(String ip);

    /**
     * 根据站点名称获取站点信息
     *
     * @param siteName
     * @return
     */
    SohuSiteVo getBySiteNameOfEnable(String siteName);

    /**
     * 根据城市ID获取国家ID
     *
     * @param cityId 城市ID
     * @return Integer
     */
    Long selectCountryIdByCityId(Long cityId);

    /**
     * 根据站点id获取国家站点
     *
     * @param id
     * @return
     */
    SohuSiteVo getCountrySiteById(Long id);

}
