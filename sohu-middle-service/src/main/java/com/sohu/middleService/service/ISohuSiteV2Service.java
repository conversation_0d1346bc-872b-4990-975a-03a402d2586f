package com.sohu.middleService.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuBindStationCheckBo;
import com.sohu.middle.api.bo.SohuSiteBo;
import com.sohu.middle.api.bo.SohuSiteV2QueryBo;
import com.sohu.middle.api.vo.SohuSiteVo;

import javax.validation.constraints.NotNull;

public interface ISohuSiteV2Service {

    /**
     * 新增站点且完善站长认证信息
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean addV2(SohuSiteBo bo);

    /**
     * 查询站点列表-v2
     *
     * @param bo
     * @param pageQuery
     * @return {@link TableDataInfo}
     */
    TableDataInfo<SohuSiteVo> queryPageListV2(SohuSiteV2QueryBo bo, PageQuery pageQuery);

    /**
     * 获取站点详细信息
     *
     * @param id 站点ID
     * @return {@link SohuSiteVo}
     */
    SohuSiteVo getInfoV2(@NotNull(message = "主键不能为空") Long id);

    /**
     * 修改站点
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean updateV2(SohuSiteBo bo);

    /**
     * 通过行业ID查询站点信息
     *
     * @param platformIndustryId 平台行业ID
     * @return {@link SohuSiteVo}
     */
    SohuSiteVo queryByPlatformIndustry(Long platformIndustryId);

    /**
     * 绑定站长校验站长是否符合条件
     *
     * @param checkBo 入参
     * @return
     */
    Boolean bindStationCheck(SohuBindStationCheckBo checkBo);

}
