package com.sohu.middleService.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.PlatformRoleCodeEnum;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.enums.SiteType;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.utils.ip.RegionUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.middle.api.aspect.UserBehaviorAspect;
import com.sohu.middle.api.bo.SohuSiteBo;
import com.sohu.middle.api.enums.AdPlaceEnum;
import com.sohu.middle.api.enums.InitSiteCategory;
import com.sohu.middle.api.enums.InitSiteTag;
import com.sohu.middle.api.vo.LocationObject;
import com.sohu.middle.api.vo.SohuPlatformIndustryVo;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.common.SohuSiteOfEnableVo;
import com.sohu.middleService.domain.*;
import com.sohu.middleService.mapper.*;
import com.sohu.middleService.service.ISohuAppletConfigService;
import com.sohu.middleService.service.ISohuSiteService;
import com.sohu.middleService.service.ISohuUserService;
import com.sohu.system.api.RemotePlatformRoleService;
import com.sohu.system.api.RemoteSysRoleService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
public class SohuSiteServiceImpl extends SohuBaseServiceImpl<SohuSiteMapper, SohuSite, SohuSiteVo> implements ISohuSiteService {

    private final SohuPlatformIndustryMapper sohuPlatformIndustryMapper;
    private final SohuCategoryMapper sohuCategoryMapper;
    private final SohuAssTagMapper sohuAssTagMapper;
    private final SohuAdInfoMapper sohuAdInfoMapper;

    private final ISohuAppletConfigService sohuAppletConfigService;
    private final ISohuUserService sohuUserService;
    private final UserBehaviorAspect userBehaviorAspect;


    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemotePlatformRoleService remotePlatformRoleService;

//    /**
//     * 查询站点
//     */
//    @Override
//    public SohuSiteVo queryById(Long id) {
//        return baseMapper.selectVoById(id);
//    }

    /**
     * 查询站点列表
     */
    @Override
    public TableDataInfo<SohuSiteVo> queryPageList(SohuSiteBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuSite> lqw = buildQueryWrapper(bo);
        Page<SohuSiteVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuSiteVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        Set<Long> pids = new HashSet<>();
        records.forEach(item -> {
            if (item.getPid() != null && item.getPid() > 0L) {
                pids.add(item.getPid());
            }
        });
        Map<Long, SohuSiteVo> siteMap = this.queryMap(pids);
        records.forEach(item -> {
            if (item.getPid() != null && item.getPid() > 0L) {
                SohuSiteVo site = siteMap.get(item.getPid());
                if (site != null) {
                    item.setParentName(site.getName());
                    item.setParentEnglishName(site.getEnName());
                }
            }
        });
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询站点列表
     */
    @Override
    public List<SohuSiteVo> queryList(SohuSiteBo bo) {
        LambdaQueryWrapper<SohuSite> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuSite> buildQueryWrapper(SohuSiteBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuSite> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPid() != null && bo.getPid() > 0L, SohuSite::getPid, bo.getPid());
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuSite::getName, bo.getName());
        lqw.like(StringUtils.isNotBlank(bo.getEnName()), SohuSite::getEnName, bo.getEnName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), SohuSite::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuSite::getState, bo.getState());
        lqw.eq(bo.getSiteCate() != null, SohuSite::getSiteCate, bo.getSiteCate());
        lqw.eq(bo.getStationmasterId() != null && bo.getStationmasterId() > 0L, SohuSite::getStationmasterId, bo.getStationmasterId());
        return lqw;
    }

    /**
     * 新增站点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SohuSiteBo bo) {
        bo.setState(Constants.Y);
        SohuSite add = BeanUtil.toBean(bo, SohuSite.class);
//        add.setCreateTime(new Date());
//        add.setUpdateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        if (StrUtil.isNotBlank(bo.getType()) && Objects.equals(bo.getType(), SiteType.City.name())) {
            this.initSite(add);
        }
        //授予用户站点角色
        this.grantStationRole(add.getType(), add.getStationmasterId());
        this.sohuUserService.flushLoginCacheByUserId(add.getStationmasterId());
        return flag;
    }

    /**
     * 授予用户站点角色
     *
     * @param siteType
     * @param userId
     */
    private void grantStationRole(String siteType, Long userId) {
        if (StrUtil.isNotBlank(siteType) && userId != null) {
            if (Objects.equals(siteType, SiteType.City.name())) {
                //授予用户城市站长角色
                this.remoteSysRoleService.insertUserRole(RoleCodeEnum.CityStationAgent.getCode(), userId);
                this.remotePlatformRoleService.insertUserRole(PlatformRoleCodeEnum.CityStationAgent.getCode(), userId);
            } else if (Objects.equals(siteType, SiteType.Country.name())) {
                //授予用户国家站长角色
                this.remoteSysRoleService.insertUserRole(RoleCodeEnum.CountryStationAgent.getCode(), userId);
                this.remotePlatformRoleService.insertUserRole(PlatformRoleCodeEnum.CountryStationAgent.getCode(), userId);
            }
        }
    }

    /**
     * 取消用户站点角色
     *
     * @param siteType
     * @param userId
     */
    private void cancelStationRole(String siteType, Long userId) {
        if (StrUtil.isNotBlank(siteType)) {
            if (Objects.equals(siteType, SiteType.City.name())) {
                //取消用户城市站长角色
                this.remoteSysRoleService.deleteUserRole(RoleCodeEnum.CityStationAgent.getCode(), userId);
                this.remotePlatformRoleService.deleteUserRole(PlatformRoleCodeEnum.CityStationAgent.getCode(), userId);
            } else if (Objects.equals(siteType, SiteType.Country.name())) {
                //取消用户国家站长角色
                this.remoteSysRoleService.deleteUserRole(RoleCodeEnum.CountryStationAgent.getCode(), userId);
                this.remotePlatformRoleService.deleteUserRole(PlatformRoleCodeEnum.CountryStationAgent.getCode(), userId);
            }
        }
    }

    /**
     * 站点初始化
     *
     * @param add 站点对象
     */
    private void initSite(SohuSite add) {
        Long siteId = add.getId();
        // 父级站点，国家站点
        Long pid = add.getPid();
        // 初始化站点首页配置
        sohuAppletConfigService.initSiteConfig(siteId);
        // 初始化站点分类
        for (InitSiteCategory siteCategory : InitSiteCategory.values()) {
            SohuCategory parentCategory = new SohuCategory();
            parentCategory.setSiteId(siteId);
            parentCategory.setBusyType(siteCategory.getType());
            parentCategory.setName(siteCategory.getName());
            parentCategory.setSortIndex(siteCategory.getSortIndex());
            sohuCategoryMapper.insert(parentCategory);

            SohuCategory childCategory = new SohuCategory();
            childCategory.setSiteId(siteId);
            childCategory.setBusyType(siteCategory.getType());
            childCategory.setName("其它");
            childCategory.setSortIndex(siteCategory.getSortIndex());
            childCategory.setPid(parentCategory.getPid());
            sohuCategoryMapper.insert(childCategory);
        }
        // 初始化站点标签
        for (InitSiteTag siteTag : InitSiteTag.values()) {
            SohuAssTag assTagDO = new SohuAssTag();
            assTagDO.setType(siteTag.getType());
            assTagDO.setSiteId(siteId);
            assTagDO.setName(siteTag.getName());
            assTagDO.setHot(0L);
            assTagDO.setCreateTime(new Date());
            sohuAssTagMapper.insert(assTagDO);
        }
        // 初始化站点首页轮播
        LambdaQueryWrapper<SohuAdInfo> adInfoLqw = new LambdaQueryWrapper<>();
        adInfoLqw.eq(SohuAdInfo::getSiteId, 11L).in(SohuAdInfo::getAdPlace, AdPlaceEnum.placeCodes)
                .eq(SohuAdInfo::getState, CommonState.OnShelf.getCode());
        List<SohuAdInfo> infoDOList = sohuAdInfoMapper.selectList(adInfoLqw);
        if (CollUtil.isNotEmpty(infoDOList)) {
            for (SohuAdInfo infoDO : infoDOList) {
                infoDO.setId(null);
                infoDO.setSiteId(siteId);
                sohuAdInfoMapper.insert(infoDO);
            }
        }
    }

    /**
     * 修改站点
     */
    @Override
    public Boolean updateByBo(SohuSiteBo bo) {
        SohuSite update = BeanUtil.toBean(bo, SohuSite.class);
//        Long stationmasterId = update.getStationmasterId();
//        String name = baseMapper.selectByStationmasterId(stationmasterId, bo.getId());
//        if (StringUtils.isNotBlank(name)) {
//            throw new RuntimeException("该用户已经是" + name + "站点站长");
//        }
//        update.setUpdateTime(new Date());
        validEntityBeforeSave(update);
        SohuSite entity = this.baseMapper.selectById(bo.getId());
        if (entity == null) {
            throw new RuntimeException("站点数据不存在");
        }
        //站长发生变化
        if (!Objects.equals(update.getStationmasterId(), entity.getStationmasterId())) {
            if (update.getStationmasterId() != null && update.getStationmasterId() > 0L) {
                //授予用户站点角色
                this.grantStationRole(entity.getType(), update.getStationmasterId());
                this.sohuUserService.flushLoginCacheByUserId(update.getStationmasterId());
            }
            if (entity.getStationmasterId() != null && entity.getStationmasterId() > 0L) {
                //取消用户站点角色
                this.cancelStationRole(entity.getType(), entity.getStationmasterId());
//                //强制登出
//                this.sohuUserService.logoutByUserId(entity.getStationmasterId());
                this.sohuUserService.flushLoginCacheByUserId(entity.getStationmasterId());
            }
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuSite entity) {
        if (entity.getPid() != null && entity.getPid() > 0L) {
            SohuSiteVo sohuSiteVo = this.queryById(entity.getPid());
            validateSohuSiteExists(sohuSiteVo);
        }
        entity.setStationmasterId(null);
        /*Long stationmasterId = entity.getStationmasterId();
        if (stationmasterId != null && stationmasterId > 0L) {
            LoginUser loginUser = sohuUserService.selectById(stationmasterId);
            if (Objects.isNull(loginUser)) {
                throw new ServiceException(MessageUtils.message("user.not.exist"));
            }
            Long id = entity.getId();
            SohuSite existSite = this.baseMapper.selectOne(SohuSite::getStationmasterId, stationmasterId);
            //if (exist != null && (id == null || id <= 0L)) {
            if (existSite != null && (!existSite.getId().equals(entity.getId()))) {
                throw new RuntimeException(MessageUtils.message("该用户已经是" + existSite.getName() + "的站长"));
            }
        }
        if (Objects.equals(entity.getType(), SiteType.Country.name()) && StrUtil.isEmpty(entity.getCountryCode())) {
            throw new RuntimeException("国家或地区ISO3166-1alpha-3编码未填");
        }*/
    }

//    /**
//     * 批量删除站点
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

    @Override
    public List<SohuSiteVo> getSiteTree(SohuSiteBo bo) {
        bo.setState(Constants.Y);
        List<SohuSiteVo> all = this.queryList(bo);
        if (CollUtil.isEmpty(all)) {
            return new ArrayList<>();
        }
        Set<Long> pids = new HashSet<>();
        List<Long> userIds = new ArrayList<>();
        for (SohuSiteVo sohuSiteVo : all) {
            userIds.add(sohuSiteVo.getStationmasterId());
            if (sohuSiteVo.getPid() != null && sohuSiteVo.getPid() > 0L) {
                pids.add(sohuSiteVo.getPid());
            }
        }
        Map<Long, SohuSiteVo> siteMap = this.queryMap(pids);
        Map<Long, LoginUser> userMap = sohuUserService.selectMap(userIds);
        Locale locale = LocaleContextHolder.getLocale();
        String languageTag = locale.toString();

        all.forEach(item -> {
            if (item.getPid() != null && item.getPid() > 0L) {
                SohuSiteVo site = siteMap.get(item.getPid());
                if (site != null) {
                    item.setParentName(site.getName());
                    item.setParentEnglishName(site.getEnName());
                }
            }
        });

        List treeNodeList = all.stream().map(category -> {
            TreeNode treeNode = new TreeNode(category.getId(), category.getPid() == null ? 0L : category.getPid(), category.getName(), null);
            // 设置扩展字段
            Map<String, Object> map = new HashMap<>();
            map.put("enName", category.getEnName());
            map.put("ext", category.getExt());
            map.put("state", category.getState());
            map.put("siteCate", category.getSiteCate());
            map.put("type", category.getType());
            map.put("longitude", category.getLongitude());
            map.put("latitude", category.getLatitude());
            LoginUser loginUser = userMap.get(category.getStationmasterId());
            if (Objects.nonNull(loginUser)) {
                map.put("stationmasterId", category.getStationmasterId());
                map.put("stationmasterName", loginUser.getNickname());
                map.put("stationmasterAvatar", loginUser.getAvatar());
            } else {
                map.put("stationmasterId", 0);
                map.put("stationmasterName", "");
                map.put("stationmasterAvatar", "");
            }
            if (category.getPid() != null && category.getPid() > 0L) {
                SohuSiteVo site = siteMap.get(category.getPid());
                if (site != null) {
                    map.put("parentName", site.getName());
                    map.put("parentEnglishName", site.getEnName());
                }
            }
            treeNode.setExtra(map);
            return treeNode;
        }).collect(Collectors.toList());
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setNameKey("name");
        List build = TreeBuildUtils.build(treeNodeList, 0L);
        return build;
    }

    @Override
    public List<SohuSiteVo> getSiteClearTree(SohuSiteBo bo) {
        bo.setState(Constants.Y);
        List<SohuSiteVo> sohuSiteVos = this.queryList(bo);
        if (CollUtil.isNotEmpty(sohuSiteVos)) {
            Set<Long> pIdList = sohuSiteVos.stream().map(SohuSiteVo::getPid).collect(Collectors.toSet());
            Map<Long, SohuSiteVo> siteMap = queryMap(pIdList);
            for (SohuSiteVo siteVo : sohuSiteVos) {
                SohuSiteVo site = siteMap.get(siteVo.getPid());
                if (Objects.nonNull(site)) {
                    siteVo.setParentName(site.getName());
                    siteVo.setParentEnglishName(site.getEnName());
                }
            }
        }
        return sohuSiteVos;
    }

    @Override
    public List<SohuSiteOfEnableVo> getSiteTreeOfEnable() {
        List<SohuSiteOfEnableVo> resultList = new ArrayList<>();
        List<SohuSiteOfEnableVo> voList = getSiteListOfEnable();
        if (CollUtil.isEmpty(voList)) {
            return resultList;
        }
        resultList = voList.stream().filter(p -> p.getPid() == 0).peek(child -> buildTree(child, voList)).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<SohuSiteOfEnableVo> getSiteListOfEnable() {
        List<SohuSiteOfEnableVo> resultList = new ArrayList<>();
        LambdaQueryWrapper<SohuSite> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuSite::getState, Constants.Y)
                .eq(SohuSite::getSiteCate, true);
        return this.baseMapper.selectVoList(lqw, SohuSiteOfEnableVo.class);
    }

    private SohuSiteOfEnableVo buildTree(SohuSiteOfEnableVo node, List<SohuSiteOfEnableVo> list) {
        List<SohuSiteOfEnableVo> children = list.stream()
                .filter(child -> node.getId().equals(child.getPid()))
                .peek(child -> buildTree(child, list))
                .collect(Collectors.toList());
        node.setChildrens(children);
        return node;
    }

    @Override
    public Object getLocationDetail(String location) {
        String response = HttpRequest.get("https://apis.map.qq.com/ws/geocoder/v1/?key=IYQBZ-GHZLQ-YFR5G-GNVPG-YS7A7-7MB2U" + "&location=" + location).execute().body();
        LocationObject bean = JSONUtil.toBean(response, LocationObject.class);
        if (bean != null && bean.getStatus() == 0) {
            String city = bean.getResult().getAddressComponent().getCity();
            LambdaQueryWrapper<SohuSite> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SohuSite::getName, city).or().eq(SohuSite::getEnName, city);
            SohuSite sohuSite = baseMapper.selectOne(lqw);
            if (Objects.isNull(sohuSite)) {
                sohuSite = baseMapper.selectById(11L);
            }
            Long pid = sohuSite.getPid();
            if (pid != null && pid > 0L) {
                SohuSite parentSite = baseMapper.selectById(pid);
                if (Objects.nonNull(parentSite)) {
                    bean.setPid(pid);
                    bean.setParentName(parentSite.getName());
                    bean.setParentEnglishName(parentSite.getEnName());
                }
            }
            bean.setId(sohuSite.getId());
            bean.setName(sohuSite.getName());
            bean.setEnName(sohuSite.getEnName());
            return JSONUtil.toJsonStr(bean);
        }
        return JSONUtil.parseObj(response);
    }

    @Override
    public Boolean openSite(SohuSiteBo bo) {
        if (StrUtil.isBlankIfStr(bo.getStationmasterKey())) {
            throw new ServiceException(MessageUtils.message("手机号或者邮箱为空"));
        }
        SohuSite site = this.baseMapper.selectById(bo.getId());
        validateSohuSiteExists(site);
        LoginUser loginUser = null;
        if (ValidatorUtil.isMobile(bo.getStationmasterKey())) {
            loginUser = sohuUserService.getUserInfoByPhonenumber(bo.getStationmasterKey());
        }
        if (ValidatorUtil.isEmail(bo.getStationmasterKey())) {
            loginUser = sohuUserService.getUserInfoByEmail(bo.getStationmasterKey());
        }
        if (loginUser == null) {
            throw new ServiceException(MessageUtils.message("手机号或者邮箱为空"));
        }
        site.setStationmasterId(loginUser.getUserId());
        site.setState(Constants.Y);
        this.baseMapper.updateById(site);
        return Boolean.TRUE;
    }

    @Override
    public Boolean online(Long siteId) {
        SohuSite site = this.baseMapper.selectById(siteId);
        validateSohuSiteExists(site);
        site.setState(Constants.Y);
        this.baseMapper.updateById(site);
        return Boolean.TRUE;
    }

    @Override
    public Boolean offline(Long siteId) {
        SohuSite site = this.baseMapper.selectById(siteId);
        validateSohuSiteExists(site);
        site.setState(Constants.N);
        this.baseMapper.updateById(site);
        return Boolean.TRUE;
    }

    @Override
    public Map<Long, SohuSiteVo> queryMap(Set<Long> siteIds) {
        List<SohuSiteVo> siteList = this.baseMapper.selectVoBatchIds(siteIds);
        if (CollUtil.isEmpty(siteList)) {
            return new HashMap<>();
        }
        return siteList.stream().collect(Collectors.toMap(SohuSiteVo::getId, s -> s));
    }

    @Override
    public Map<Long, SohuSiteVo> queryIndustryMap(Set<Long> siteIds) {
        List<SohuSiteVo> siteList = this.baseMapper.selectVoList(new LambdaQueryWrapper<SohuSite>().in(SohuSite::getPlatformIndustryId, siteIds));
        if (CollUtil.isEmpty(siteList)) {
            return new HashMap<>();
        }
        return siteList.stream().collect(Collectors.toMap(SohuSiteVo::getPlatformIndustryId, s -> s));
    }

    @Override
    public List<SohuSiteVo> selectSiteList(Collection<Long> ids) {
        return baseMapper.selectVoBatchIds(ids);
    }

    @Override
    public SohuSiteVo selectSiteByPid(Long id) {
        return baseMapper.selectSiteByPid(id);
    }

    @Override
    public String selectCountryCodeById(Long id) {
        return baseMapper.selectCountryCodeById(id);
    }

    @Override
    public SohuSiteVo selectSiteByUserId(Long userId) {
        LambdaQueryWrapper<SohuSite> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuSite::getStationmasterId, userId);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public List<Long> queryChildSiteIds(Long pid) {
        return baseMapper.queryChildSiteIds(pid);
    }

    @Override
    public Long getSiteIdByEnName(String categoryEnName) {
        SohuSite sohuSite = baseMapper.selectOne(SohuSite::getEnName, categoryEnName);
        return Objects.isNull(sohuSite) ? 0L : sohuSite.getId();
    }

    @Override
    public SohuSiteVo queryByStationmasterId(Long stationmasterId) {
        LambdaQueryWrapper<SohuSite> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuSite::getStationmasterId, stationmasterId);
        lqw.last(" limit 1");
        SohuSiteVo siteVo = baseMapper.selectVoOne(lqw);
        if (Objects.isNull(siteVo)) {
            return new SohuSiteVo();
        }
        Long platformIndustryId = siteVo.getPlatformIndustryId();
        if (!CalUtils.isNullOrZero(platformIndustryId)) {
            SohuPlatformIndustry platformIndustry = sohuPlatformIndustryMapper.selectById(platformIndustryId);
            siteVo.setPlatformIndustryName(Objects.nonNull(platformIndustry) ? platformIndustry.getName() : null);
        }
        return siteVo;
    }

    @Override
    public List<SohuSiteVo> listByStationmasterId(Long stationmasterId, Integer stationmasterStatus) {
        LambdaQueryWrapper<SohuSite> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuSite::getStationmasterId, stationmasterId);
        lqw.eq(stationmasterStatus != null, SohuSite::getStationmasterStatus, stationmasterStatus);
        List<SohuSiteVo> siteVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(siteVos)) {
            return new ArrayList<>();
        }
        Set<Long> platformIndustryIds = new HashSet<>();
        for (SohuSiteVo siteVo : siteVos) {
            if (!CalUtils.isNullOrZero(siteVo.getPlatformIndustryId())) {
                platformIndustryIds.add(siteVo.getPlatformIndustryId());
            }
        }
        List<SohuPlatformIndustryVo> platformIndustryVos = CollUtil.isEmpty(platformIndustryIds) ? new ArrayList<>() : sohuPlatformIndustryMapper.selectVoBatchIds(platformIndustryIds);
        Map<Long, SohuPlatformIndustryVo> platformIndustryVoMap = CollUtil.isEmpty(platformIndustryVos) ? new HashMap<>() :
                platformIndustryVos.stream().collect(Collectors.toMap(SohuPlatformIndustryVo::getId, u -> u));
        for (SohuSiteVo siteVo : siteVos) {
            SohuPlatformIndustryVo platformIndustry = platformIndustryVoMap.get(siteVo.getPlatformIndustryId());
            siteVo.setPlatformIndustryName(Objects.nonNull(platformIndustry) ? platformIndustry.getName() : null);
            siteVo.setFinalSiteName(StringUtils.getValidString(siteVo.getPlatformIndustryName(), siteVo.getName()));
        }
        return siteVos;
    }

    @Override
    public SohuSiteVo getSiteByIp(String ip) {
        // 默认站点
        LambdaQueryWrapper<SohuSite> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuSite::getIsDefault, Constants.Y);
        SohuSiteVo sohuSiteVo = baseMapper.selectVoOne(lqw);
        // 父站点
        if (sohuSiteVo.getPid() != null && sohuSiteVo.getPid() > 0L) {
            SohuSite parentSite = baseMapper.selectById(sohuSiteVo.getPid());
            if (Objects.nonNull(parentSite)) {
                sohuSiteVo.setParentName(parentSite.getName());
                sohuSiteVo.setParentEnglishName(parentSite.getEnName());
            }
        }
        // IP定位获取地址信息
        Map<String, Object> ipRegion = RegionUtils.getIpRegion(ip);
        if (Objects.isNull(ipRegion) || ipRegion.isEmpty()) {
            return sohuSiteVo;
        }
        // 国外地址返回国家站点
        LambdaQueryWrapper<SohuSite> countryParam = new LambdaQueryWrapper<>();
        countryParam.eq(SohuSite::getName, ipRegion.get("country")).or().eq(SohuSite::getEnName, ipRegion.get("country"));
        countryParam.eq(SohuSite::getState, Constants.Y);
        SohuSiteVo countryVo = baseMapper.selectVoOne(countryParam);
        if (Objects.isNull(countryVo)) {
            return sohuSiteVo;
        }
        if (!"中国".equals(ipRegion.get("country"))) {
            return countryVo;
        }
        // 国内地址返回城市站点
        LambdaQueryWrapper<SohuSite> cityParam = new LambdaQueryWrapper<>();
        cityParam.eq(SohuSite::getName, ipRegion.get("city")).or().eq(SohuSite::getEnName, ipRegion.get("city"));
        cityParam.eq(SohuSite::getState, Constants.Y);
        SohuSiteVo cityVo = baseMapper.selectVoOne(cityParam);
        if (Objects.isNull(cityVo)) {
            return sohuSiteVo;
        }
        // 父站点
        if (cityVo.getPid() != null && cityVo.getPid() > 0L) {
            SohuSite parentSite = baseMapper.selectById(cityVo.getPid());
            if (Objects.nonNull(parentSite)) {
                cityVo.setParentName(parentSite.getName());
                cityVo.setParentEnglishName(parentSite.getEnName());
            }
        }
        return cityVo;
    }

    @Override
    public SohuSiteVo getBySiteNameOfEnable(String siteName) {
        SohuSite entity = this.baseMapper.selectOne(SohuSite::getName, siteName, SohuSite::getState, Constants.Y);
        return BeanUtil.copyProperties(entity, SohuSiteVo.class);
    }

    private void validateSohuSiteExists(SohuSiteVo sohuSiteVo) {
        if (Objects.isNull(sohuSiteVo)) {
            throw new ServiceException(MessageUtils.message("SITE_NOT_FOUNT"));
        }
    }

    private void validateSohuSiteExists(SohuSite sohuSite) {
        if (Objects.isNull(sohuSite)) {
            throw new ServiceException(MessageUtils.message("SITE_NOT_FOUNT"));
        }
    }

    @Override
    public Long selectCountryIdByCityId(Long cityId) {
        return baseMapper.selectCountryIdByCityId(cityId);
    }

    @Override
    public SohuSiteVo getCountrySiteById(Long id) {
        SohuSiteVo sohuSiteVo = this.baseMapper.selectVoById(id);
        if (Objects.isNull(sohuSiteVo)) {
            return new SohuSiteVo();
        }
        if (Objects.equals(sohuSiteVo.getType(), SiteType.Country.name())) {
            return sohuSiteVo;
        } else if (Objects.equals(sohuSiteVo.getType(), SiteType.City.name())) {
            return this.getCountrySiteById(sohuSiteVo.getPid());
        }
        return null;
    }

}
