package com.sohu.middleService.service.airec.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.enums.SohuTradeRecordEnum;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.airec.SohuAirecBehaviorBo;
import com.sohu.middle.api.vo.airec.SohuAirecBehaviorVo;
import com.sohu.middleService.domain.SohuTradeRecord;
import com.sohu.middleService.domain.airec.SohuAirecBehavior;
import com.sohu.middleService.mapper.airec.SohuAirecBehaviorMapper;
import com.sohu.middleService.service.airec.ISohuAirecBehaviorService;
import com.sohu.middleService.service.impl.SohuBaseServiceImpl;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.third.aliyun.airec.domain.AliyunAirecContentBehavior;
import com.sohu.third.aliyun.airec.enums.AliyunAirecContentBehaviorTypeEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecContentCmdEnum;
import com.sohu.third.aliyun.airec.util.AliyunAirecUtil;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 智能推荐行为Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@RequiredArgsConstructor
@Service
public class SohuAirecBehaviorServiceImpl extends SohuBaseServiceImpl<SohuAirecBehaviorMapper, SohuAirecBehavior, SohuAirecBehaviorVo> implements ISohuAirecBehaviorService {

    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

//    /**
//     * 查询智能推荐行为
//     */
//    @Override
//    public SohuAirecBehaviorVo queryById(Long id) {
//        return baseMapper.selectVoById(id);
//    }

    /**
     * 查询智能推荐行为列表
     */
    @Override
    public TableDataInfo<SohuAirecBehaviorVo> queryPageList(SohuAirecBehaviorBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAirecBehavior> lqw = buildQueryWrapper(bo);
        Page<SohuAirecBehaviorVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询智能推荐行为列表
     */
    @Override
    public List<SohuAirecBehaviorVo> queryList(SohuAirecBehaviorBo bo) {
        LambdaQueryWrapper<SohuAirecBehavior> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuAirecBehavior> buildQueryWrapper(SohuAirecBehaviorBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuAirecBehavior> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getItemId()), SohuAirecBehavior::getItemId, bo.getItemId());
        lqw.eq(StringUtils.isNotBlank(bo.getItemType()), SohuAirecBehavior::getItemType, bo.getItemType());
        lqw.eq(StringUtils.isNotBlank(bo.getBhvType()), SohuAirecBehavior::getBhvType, bo.getBhvType());
        lqw.eq(StringUtils.isNotBlank(bo.getTraceId()), SohuAirecBehavior::getTraceId, bo.getTraceId());
        lqw.eq(StringUtils.isNotBlank(bo.getTraceInfo()), SohuAirecBehavior::getTraceInfo, bo.getTraceInfo());
        lqw.eq(StringUtils.isNotBlank(bo.getSceneId()), SohuAirecBehavior::getSceneId, bo.getSceneId());
        lqw.eq(StringUtils.isNotBlank(bo.getBhvTime()), SohuAirecBehavior::getBhvTime, bo.getBhvTime());
        lqw.eq(StringUtils.isNotBlank(bo.getBhvValue()), SohuAirecBehavior::getBhvValue, bo.getBhvValue());
        lqw.eq(StringUtils.isNotBlank(bo.getUserId()), SohuAirecBehavior::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getPlatform()), SohuAirecBehavior::getPlatform, bo.getPlatform());
        lqw.eq(StringUtils.isNotBlank(bo.getImei()), SohuAirecBehavior::getImei, bo.getImei());
        lqw.eq(StringUtils.isNotBlank(bo.getAppVersion()), SohuAirecBehavior::getAppVersion, bo.getAppVersion());
        lqw.eq(StringUtils.isNotBlank(bo.getNetType()), SohuAirecBehavior::getNetType, bo.getNetType());
        lqw.eq(StringUtils.isNotBlank(bo.getIp()), SohuAirecBehavior::getIp, bo.getIp());
        lqw.eq(StringUtils.isNotBlank(bo.getLogin()), SohuAirecBehavior::getLogin, bo.getLogin());
        lqw.eq(StringUtils.isNotBlank(bo.getReportSrc()), SohuAirecBehavior::getReportSrc, bo.getReportSrc());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceModel()), SohuAirecBehavior::getDeviceModel, bo.getDeviceModel());
        lqw.eq(StringUtils.isNotBlank(bo.getLongitude()), SohuAirecBehavior::getLongitude, bo.getLongitude());
        lqw.eq(StringUtils.isNotBlank(bo.getLatitude()), SohuAirecBehavior::getLatitude, bo.getLatitude());
        lqw.eq(StringUtils.isNotBlank(bo.getModuleId()), SohuAirecBehavior::getModuleId, bo.getModuleId());
        lqw.eq(StringUtils.isNotBlank(bo.getPageId()), SohuAirecBehavior::getPageId, bo.getPageId());
        lqw.eq(StringUtils.isNotBlank(bo.getPosition()), SohuAirecBehavior::getPosition, bo.getPosition());
        lqw.eq(StringUtils.isNotBlank(bo.getMessageId()), SohuAirecBehavior::getMessageId, bo.getMessageId());
        return lqw;
    }

    /**
     * 新增智能推荐行为
     */
    @Override
    public Boolean insertByBo(SohuAirecBehaviorBo bo) {
        SohuAirecBehavior add = BeanUtil.toBean(bo, SohuAirecBehavior.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            //推送至阿里云智能推荐大模型
            AliyunAirecUtil.pushDataOfContentBehavior(AliyunAirecContentCmdEnum.ADD, BeanUtil.copyProperties(add, AliyunAirecContentBehavior.class));
            //发消息到队列
            MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(bo), MqKeyEnum.AIREC_BEHAVIOR_ANALYSE.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 1L);
        }
        return flag;
    }

    /**
     * 修改智能推荐行为
     */
    @Override
    public Boolean updateByBo(SohuAirecBehaviorBo bo) {
        validEntityBo(bo);
        SohuAirecBehavior update = BeanUtil.toBean(bo, SohuAirecBehavior.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuAirecBehavior entity) {
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBo(SohuAirecBehaviorBo bo) {
        String userId = bo.getUserId();
        String imei = bo.getImei();
        if (StringUtil.isAllBlank(userId, imei)) {
            throw new RuntimeException("userId和imei不能同时为空");
        }
    }

//    /**
//     * 批量删除智能推荐行为
//     */
//    @Override
//    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        return baseMapper.deleteBatchIds(ids) > 0;
//    }

    @Override
    public Boolean insertBatch(List<SohuAirecBehaviorBo> boList) {
        List<SohuAirecBehavior> entityList = new ArrayList<>();
        for (SohuAirecBehaviorBo bo : boList) {
            validEntityBo(bo);
            SohuAirecBehavior behavior = BeanCopyUtils.copy(bo, SohuAirecBehavior.class);
            entityList.add(behavior);
        }
        baseMapper.insertBatch(entityList);
        List<AliyunAirecContentBehavior> aliyunAirecBehaviorList = BeanCopyUtils.copyList(boList, AliyunAirecContentBehavior.class);
        //推送至阿里云智能推荐大模型
        AliyunAirecUtil.pushDataOfContentBehaviors(AliyunAirecContentCmdEnum.ADD, aliyunAirecBehaviorList);
        //发消息到队列
        for (SohuAirecBehaviorBo bo : boList) {
            MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(bo), MqKeyEnum.AIREC_BEHAVIOR_ANALYSE.getKey());
            remoteStreamMqService.sendDelayMsg(mqMessaging, 1L);
        }
        return true;
    }

    @Override
    public Long getNewBehaviorUserNumByCreateTime(Date startTime, Date endTime, AliyunAirecContentBehaviorTypeEnum bhvTypeEnum) {
        LambdaQueryWrapper<SohuAirecBehavior> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAirecBehavior::getBhvType, bhvTypeEnum.getCode());
        lqw.between(SohuAirecBehavior::getCreateTime, startTime, endTime);
        lqw.isNotNull(SohuAirecBehavior::getUserId);
        return this.baseMapper.getNewBehaviorUserNum(lqw);
    }

    @Override
    public Long getNewExposeUserNumByCreateTime(Date startTime, Date endTime) {
        return this.getNewBehaviorUserNumByCreateTime(startTime, endTime, AliyunAirecContentBehaviorTypeEnum.EXPOSE);
    }

    @Override
    public Long getNewShareUserNumByCreateTime(Date startTime, Date endTime) {
        return this.getNewBehaviorUserNumByCreateTime(startTime, endTime, AliyunAirecContentBehaviorTypeEnum.SHARE);
    }

}
