package com.sohu.middleService.strategy;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.vo.SohuPlatformIndustryRelationVo;
import com.sohu.middle.api.vo.SohuVideoVo;
import com.sohu.middleService.service.ISohuBusyBlackService;
import com.sohu.middleService.service.ISohuPlatformIndustryRelationService;
import com.sohu.middleService.service.ISohuVideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频
 */
@Slf4j
@Component
public class VideoStrategy implements MiddleStrategy<SohuVideoBo, SohuVideoVo> {

    @Resource
    private ISohuVideoService sohuVideoService;
    @Resource
    private ISohuPlatformIndustryRelationService relationService;
    @Resource
    private ISohuBusyBlackService busyBlackService;

    @Override
    public Long getAuthorId(Long id) {
        return sohuVideoService.getAuthorId(id);
    }

    @Override
    public SohuVideoVo get(Long id) {
        return sohuVideoService.get(id);
    }

    @Override
    public Boolean add(SohuVideoBo entity) {
        return sohuVideoService.insertByBo(entity);
    }

    @Override
    public Boolean update(SohuVideoBo entity) {
        return sohuVideoService.updateByBo(entity);
    }

    @Override
    public SohuVideoVo query(Long id) {
        return sohuVideoService.queryById(id);
    }

    @Override
    public Boolean delete(Long id) {
        return sohuVideoService.logicDeleteById(id);
    }

    @Override
    public Boolean delete(Collection<Long> ids) {
        return sohuVideoService.deleteWithValidByIds(ids, Boolean.TRUE);
    }

    @Override
    public Boolean comment(SohuCommentBo bo, Boolean commentCountAdd) {
        return sohuVideoService.comment(bo, commentCountAdd);
    }

    @Override
    public Boolean like(SohuBusyBO bo) {
        return sohuVideoService.like(bo);
    }

    @Override
    public Boolean collect(SohuBusyBO bo) {
        return sohuVideoService.collect(bo);
    }

    @Override
    public Boolean share(Long id) {
        return null;
    }

    @Override
    public Boolean report(SohuReportInfoBo bo) {
        return null;
    }

    @Override
    public Boolean updateState(SohuBusyUpdateStateBo bo) {
        //return sohuArticleService.updateState(bo);
        if (Objects.equals(bo.getState(), CommonState.OnShelf.name())) {
            return sohuVideoService.auditOnShelf(bo.getBusyCode());
        } else if (Objects.equals(bo.getState(), CommonState.CompelOff.name())) {
            SohuContentRefuseBo sohuContentRefuseBo = new SohuContentRefuseBo();
            sohuContentRefuseBo.setId(bo.getBusyCode());
            sohuContentRefuseBo.setReason(bo.getRejectReason());
            return sohuVideoService.updateCompelOffById(sohuContentRefuseBo);
        } else if (Objects.equals(bo.getState(), CommonState.Refuse.name())) {
            return sohuVideoService.auditRefuse(bo.getBusyCode(), bo.getRejectReason());
        } else {
            //throw new RuntimeException("暂不支持，敬请期待");
            return sohuVideoService.updateState(bo);
        }
    }

    @Override
    public TableDataInfo<SohuVideoVo> queryPageList(SohuVideoBo bo, PageQuery pageQuery) {
        // 判断站点和行业，分别查询黑名单
        List<Long> handleIds = new ArrayList<>();
        if (Objects.nonNull(bo.getIndustryId())) {
            // 行业id不为空,则查询相关行业对应的分类
            SohuPlatformIndustryRelationBo relationBo = new SohuPlatformIndustryRelationBo();
            relationBo.setPlatformIndustryId(bo.getIndustryId());
            relationBo.setBusyType(BusyType.Content.getType());
            List<SohuPlatformIndustryRelationVo> relationList = relationService.queryList(relationBo);
            List<Long> categoryIds = relationList.stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(categoryIds)) {
                return TableDataInfoUtils.build(new ArrayList<>());
            }
            if (CollectionUtils.isEmpty(bo.getCategoryIds())) {
                bo.setCategoryIds(categoryIds);
            } else {
                // 取交集
                categoryIds.retainAll(bo.getCategoryIds());
                bo.setCategoryIds(categoryIds);
            }
            // 查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getIndustryId(), 2, BusyType.Video.getType());
        } else if (Objects.nonNull(bo.getSiteId()) && LoginHelper.hasRole(LoginHelper.getLoginUser(), RoleCodeEnum.CityStationAgent)) {
            // 站点id不为空,则查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Video.getType());
        }
        if (bo.getIsBlack() && CollectionUtils.isEmpty(handleIds)) {
            handleIds.add(0L);
        }
        bo.setHandleIds(handleIds);
        return sohuVideoService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuVideoVo> queryList(SohuVideoBo bo) {
        return sohuVideoService.queryList(bo);
    }

}
