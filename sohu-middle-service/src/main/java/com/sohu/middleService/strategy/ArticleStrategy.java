package com.sohu.middleService.strategy;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.*;
import com.sohu.middle.api.vo.SohuArticleVo;
import com.sohu.middle.api.vo.SohuPlatformIndustryRelationVo;
import com.sohu.middleService.service.ISohuArticleService;
import com.sohu.middleService.service.ISohuBusyBlackService;
import com.sohu.middleService.service.ISohuPlatformIndustryRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ArticleStrategy implements MiddleStrategy<SohuArticleBo, SohuArticleVo> {

    @Resource
    private ISohuArticleService sohuArticleService;
    @Resource
    private ISohuPlatformIndustryRelationService relationService;
    @Resource
    private ISohuBusyBlackService busyBlackService;

    @Override
    public Long getAuthorId(Long id) {
        return sohuArticleService.getAuthorId(id);
    }

    @Override
    public SohuArticleVo get(Long id) {
        return sohuArticleService.get(id);
    }

    @Override
    public Boolean add(SohuArticleBo entity) {
        return sohuArticleService.insertByBo(entity);
    }

    @Override
    public Boolean update(SohuArticleBo entity) {
        return sohuArticleService.updateByBo(entity);
    }

    @Override
    public SohuArticleVo query(Long id) {
        return sohuArticleService.queryById(id);
    }

    @Override
    public Boolean delete(Long id) {
        return sohuArticleService.logicDeleteById(id);
    }

    @Override
    public Boolean delete(Collection<Long> ids) {
        //return sohuArticleService.deleteWithValidByIds(ids, true);
        return sohuArticleService.logicDeleteById(ids);
    }

    @Override
    public Boolean comment(SohuCommentBo bo, Boolean commentCountAdd) {
        return sohuArticleService.comment(bo, commentCountAdd);
    }

    @Override
    public Boolean like(SohuBusyBO bo) {
        return sohuArticleService.like(bo);
    }

    @Override
    public Boolean collect(SohuBusyBO bo) {
        return sohuArticleService.collect(bo);
    }

    @Override
    public Boolean share(Long id) {
        return null;
    }

    @Override
    public Boolean report(SohuReportInfoBo bo) {
        return null;
    }

    @Override
    public Boolean updateState(SohuBusyUpdateStateBo bo) {
        //return sohuArticleService.updateState(bo);
        if (Objects.equals(bo.getState(), CommonState.OnShelf.name())) {
            return sohuArticleService.auditOnShelf(bo.getBusyCode());
        } else if (Objects.equals(bo.getState(), CommonState.CompelOff.name())) {
            SohuContentRefuseBo sohuContentRefuseBo = new SohuContentRefuseBo();
            sohuContentRefuseBo.setId(bo.getBusyCode());
            sohuContentRefuseBo.setReason(bo.getRejectReason());
            return sohuArticleService.updateCompelOffById(sohuContentRefuseBo);
        } else if (Objects.equals(bo.getState(), CommonState.Refuse.name())) {
            return sohuArticleService.auditRefuse(bo.getBusyCode(), bo.getRejectReason());
        } else {
            //throw new RuntimeException("暂不支持，敬请期待");
            return sohuArticleService.updateState(bo);
        }
    }

    @Override
    public TableDataInfo<SohuArticleVo> queryPageList(SohuArticleBo bo, PageQuery pageQuery) {
        // 判断站点和行业，分别查询黑名单
        List<Long> handleIds = new ArrayList<>();
        if (Objects.nonNull(bo.getIndustryId())) {
            // 行业id不为空,则查询相关行业对应的分类
            SohuPlatformIndustryRelationBo relationBo = new SohuPlatformIndustryRelationBo();
            relationBo.setPlatformIndustryId(bo.getIndustryId());
            relationBo.setBusyType(BusyType.Content.getType());
            List<SohuPlatformIndustryRelationVo> relationList = relationService.queryList(relationBo);
            List<Long> categoryIds = relationList.stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(categoryIds)) {
                return TableDataInfoUtils.build(new ArrayList<>());
            }
            if (CollectionUtils.isEmpty(bo.getCategoryIds())) {
                bo.setCategoryIds(categoryIds);
            } else {
                // 取交集
                categoryIds.retainAll(bo.getCategoryIds());
                bo.setCategoryIds(categoryIds);
            }
            // 查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getIndustryId(), 2, BusyType.Article.getType());
        } else if (Objects.nonNull(bo.getSiteId()) && LoginHelper.hasRole(LoginHelper.getLoginUser(), RoleCodeEnum.CityStationAgent)) {
            // 站点id不为空,则查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Article.getType());
        }
        if (bo.getIsBlack() && CollectionUtils.isEmpty(handleIds)) {
            handleIds.add(0L);
        }
        bo.setHandleIds(handleIds);
        return sohuArticleService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuArticleVo> queryList(SohuArticleBo bo) {
        return sohuArticleService.queryList(bo);
    }

}
