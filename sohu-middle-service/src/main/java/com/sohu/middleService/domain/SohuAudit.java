package com.sohu.middleService.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务审核对象 sohu_audit
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@TableName("sohu_audit")
public class SohuAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 审核业务类型:[Article:图文,Video视频,Project:项目,Question:问题,Answer:回答,Card:资源名片,BusyModel:生意模式,BusyOrder:商单]
     * {@link com.sohu.common.core.enums.BusyType}
     */
    private String busyType;
    /**
     * 业务id
     */
    private Long busyCode;
    /**
     * 业务标题（回答的标题指的是：对应问题的标题）
     */
    private String busyTitle;
    /**
     * 业务封面图
     */
    private String busyCoverImg;
    /**
     * 业务归属人
     */
    private Long busyBelonger;
    /**
     * 城市站点ID
     */
    private Long citySiteId;
    /**
     * 国家站点ID
     */
    private Long countrySiteId;
    /**
     * 城市站审核状态
     */
    private String cityAuditState;
    /**
     * 国家站审核状态
     */
    private String countryAuditState;
    /**
     * 管理员审核状态
     */
    private String sysAuditState;
    /**
     * 驳回理由
     */
    private String rejectReason;
    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 是否国家站审核
     */
    @TableField(exist = false)
    private Boolean countryAudit = false;
    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 申请理由
     */
    private String applyMsg;
    /**
     * 申请附件
     */
    private String applyAnnex;

    /**
     * 站点类型 1 城市站 2 行业站
     */
    private Integer siteType;

    /**
     * 站点Id
     */
    private Long siteId;

}
