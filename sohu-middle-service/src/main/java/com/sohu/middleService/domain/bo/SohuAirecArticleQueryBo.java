package com.sohu.middleService.domain.bo;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.enums.VideoEnum;
import com.sohu.third.aliyun.airec.constants.AliyunAirecTagsConstant;
import com.sohu.third.aliyun.airec.domain.AliyunAirecJoinFilterRule;
import com.sohu.third.aliyun.airec.domain.AliyunAirecSingleFilterRule;
import com.sohu.third.aliyun.airec.enums.AliyunAirecContentItemTypeEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecQueryCondEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecQueryFieldContentEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecQueryJoinEnum;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 图文推荐查询参数
 */
@Data
public class SohuAirecArticleQueryBo {
    private String country;
    private String city;
    private String categoryPath;
    /**
     * 作品类型（普通视频-general，狐少少课堂-lesson）,默认为general
     * {@link com.sohu.common.core.enums.VideoEnum}
     */
    private String type;
    private List<Long> blackIds;

    public AliyunAirecJoinFilterRule buildAirecFilterRule() {
        AliyunAirecJoinFilterRule rootRule = new AliyunAirecJoinFilterRule();
        rootRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
        {
            //内容的类型
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_TYPE.getCode());
            rule.setValue(AliyunAirecContentItemTypeEnum.ARTICLE.getCode());
            rootRule.getFilters().add(rule);
        }
        //站点
        if (StrUtil.isNotBlank(city)) {
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.CITY.getCode());
            rule.setValue(city);
            rootRule.getFilters().add(rule);
        }
        if (CollectionUtils.isNotEmpty(blackIds)) {
            for (Long id : blackIds) {
                AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                rule.setCond(AliyunAirecQueryCondEnum.NOT_EQUAL.getCode());
                rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_ID.getCode());
                rule.setValue(id.toString());
                rootRule.getFilters().add(rule);
            }
        }
        if (StrUtil.isNotBlank(country)) {
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.COUNTRY.getCode());
            rule.setValue(country);
            rootRule.getFilters().add(rule);
        }
        if (StrUtil.isNotBlank(categoryPath)) {
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.CATEGORY_MATCH.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.CATEGORY_PATH.getCode());
            rule.setValue(categoryPath);
            rootRule.getFilters().add(rule);
        }
        //存在tags筛选
        if (StrUtil.isNotBlank(type) && (Objects.equals(VideoEnum.Type.general.getCode(), type) || Objects.equals(VideoEnum.Type.general.getCode(), type))) {
            AliyunAirecJoinFilterRule tagsRule = new AliyunAirecJoinFilterRule();
            tagsRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.CONTAIN.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.TAGS.getCode());
            if (Objects.equals(VideoEnum.Type.general.getCode(), type)) {
                rule.setValue(AliyunAirecTagsConstant.MEDIA_TYPE_GENERAL);
            } else if (Objects.equals(VideoEnum.Type.lesson.getCode(), type)) {
                rule.setValue(AliyunAirecTagsConstant.MEDIA_TYPE_LESSON);
            }
            tagsRule.getFilters().add(rule);
            rootRule.getFilters().add(tagsRule);
        }
        return rootRule;
    }

}
