<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuSiteMapper">

    <resultMap type="com.sohu.middleService.domain.SohuSite" id="SohuSiteResult">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="name" column="name"/>
        <result property="enName" column="en_name"/>
        <result property="type" column="type"/>
        <result property="state" column="state"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="stationmasterId" column="stationmaster_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="hot" column="hot"/>
        <result property="ext" column="ext"/>
        <result property="siteCate" column="site_cate"/>
        <result property="isDefault" column="is_default"/>
        <result property="countryCode" column="country_code"/>
        <result property="platformIndustryId" column="platform_industry_id"/>
        <result property="stationmasterBeginTime" column="stationmaster_begin_time"/>
        <result property="stationmasterEndTime" column="stationmaster_end_time"/>
        <result property="stationmasterStatus" column="stationmaster_status"/>
    </resultMap>

    <select id="selectSiteByPid" resultType="com.sohu.middle.api.vo.SohuSiteVo">
        SELECT s.*
        FROM sohu_site s
                 JOIN (SELECT pid AS id FROM sohu_site WHERE id = #{siteId} and type = 'City' and site_cate = 1) t
                      ON s.id = t.id;
    </select>

    <select id="selectByStationmasterId" resultType="String">
        select name
        FROM sohu_site a
        WHERE stationmaster_id = #{stationmasterId}
          AND id != #{id}
    </select>

    <select id="selectSiteByPhone" resultType="com.sohu.middle.api.vo.SohuSiteVo">
        SELECT *
        FROM sys_user a
                 RIGHT JOIN sohu_site b ON a.user_id = b.stationmaster_id
        WHERE a.phone_number = #{stationmasterId}
    </select>

    <select id="queryChildSiteIds" resultType="java.lang.Long">
        SELECT id
        FROM sohu_site
        WHERE pid = #{pid}
    </select>
    <select id="selectCountryCodeById" resultType="java.lang.String">
        select country_code countryCode
        FROM sohu_site a
        WHERE id = #{siteId}
          AND type = 'Country'
          and site_cate = 1
    </select>

    <select id="selectCountryIdByCityId" resultType="java.lang.Long">
        SELECT pid AS id FROM sohu_site WHERE id = #{cityId} and type = 'City' and site_cate = 1
    </select>

    <select id="queryPageListV2" resultType="com.sohu.middle.api.vo.SohuSiteVo">
        SELECT
        MAX(s.id) AS id,
        s.stationmaster_id AS stationmasterId,
        GROUP_CONCAT(s.name SEPARATOR ',') AS name,
        MAX(s.en_name) AS enName,
        MAX(s.type) AS type,
        MAX(s.state) AS state,
        MAX(s.longitude) AS longitude,
        MAX(s.latitude) AS latitude,
        MAX(s.create_time) AS createTime,
        MAX(s.update_time) AS updateTime,
        MAX(s.hot) AS hot,
        MAX(s.ext) AS ext,
        MAX(s.site_cate) AS siteCate,
        MAX(s.is_default) AS isDefault,
        MAX(s.country_code) AS countryCode,
        MAX(s.stationmaster_begin_time) AS stationmasterBeginTime,
        MAX(s.stationmaster_end_time) AS stationmasterEndTime,
        MAX(s.stationmaster_status) AS stationmasterStatus,
        MAX(s.operator) AS operator,
        MAX(s.city_code) AS cityCode,
        GROUP_CONCAT(s.platform_industry_id SEPARATOR ',') AS platformIndustryIds,
        u.user_name AS stationmasterName,
        u.avatar AS stationmasterAvatar,
        u.phone_number AS stationmasterPhone
        FROM sohu_site s
        LEFT JOIN sys_user u ON s.stationmaster_id = u.user_id
        WHERE s.type='City' AND s.stationmaster_id IS NOT NULL AND s.stationmaster_id > 0
        <if test="bo.stationmasterPhone != null and bo.stationmasterPhone!=''">
            AND u.phone_number like concat('%',#{bo.stationmasterPhone},'%')
        </if>
        <if test="bo.id!=null and bo.id!=''">
            AND s.id = #{bo.id}
        </if>
        <if test="bo.stationmasterStatus!=null and bo.stationmasterStatus!=''">
            AND s.stationmaster_status = #{bo.stationmasterStatus}
        </if>
        <if test="bo.platformIndustryId!=null and bo.platformIndustryId!=''">
            AND s.platform_industry_id = #{bo.platformIndustryId}
        </if>
        GROUP BY s.stationmaster_id
        ORDER BY s.update_time DESC
    </select>

    <select id="queryByPlatformIndustryIds" resultType="java.lang.Long">
        SELECT id
        FROM sohu_site
        WHERE  state = #{state} AND platform_industry_id IN
        <foreach item="industryId" collection="industryIds" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </select>

    <select id="queryByPlatformIndustry" resultType="com.sohu.middle.api.vo.SohuSiteVo"
            parameterType="java.lang.Long">
        SELECT *
        FROM sohu_site
        WHERE platform_industry_id =#{platformIndustryId}
        <if test="state!=null and state!=''">
            AND state = #{state}
        </if>
    </select>

</mapper>
