<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuContentMapper">

    <select id="businessContentList" resultType="com.sohu.middle.api.vo.SohuContentVo">
        SELECT
            id,
            siteId,
            userId,
            title,
            categoryId,
            type,
            coverImage,
            videoUrl
        FROM (
                 SELECT
                     id,
                     title,
                     site_id AS siteId,
                     user_id AS userId,
                     category_id AS categoryId,
                     cover_image AS coverImage,
                     'article' AS type,-- 标识为图文类型
                     '' AS videoUrl,
                     ROW_NUMBER() OVER (ORDER BY create_time DESC) AS RowNum  -- 按创建时间排序
                 FROM sohu_article
                 WHERE
                     state = 'OnShelf'
                 <if test="bo.siteId != null">
                     AND site_id = #{bo.siteId}
                 </if>
                 <if test="bo.title != null and bo.title != ''">
                     AND title LIKE CONCAT('%', #{bo.title}, '%')
                 </if>
                 <if test="bo.categoryIds != null and bo.categoryIds.size() > 0">
                     AND category_id IN
                        <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                        #{categoryId}
                        </foreach>
                 </if>
                 UNION ALL

                 SELECT
                     id,
                     title,
                     site_id AS siteId,
                     user_id AS userId,
                     category_id AS categoryId,
                     cover_image AS coverImage,
                     'video' AS type,    -- 标识为视频类型
                     video_url AS videoUrl,
                     ROW_NUMBER() OVER (ORDER BY create_time DESC) AS RowNum  -- 按创建时间排序
                 FROM sohu_video
                 WHERE state = 'OnShelf'
                 <if test="bo.siteId != null">
                    AND site_id = #{bo.siteId}
                </if>
                <if test="bo.title != null and bo.title != ''">
                    AND title LIKE CONCAT('%', #{bo.title}, '%')
                 </if>
                 <if test="bo.categoryIds != null and bo.categoryIds.size() > 0">
                    AND category_id IN
                    <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                </if>
             ) AS content
    </select>


</mapper>
