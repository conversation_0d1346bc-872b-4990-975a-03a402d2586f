<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.SohuArticleMapper">
    <resultMap type="com.sohu.middleService.domain.SohuArticle" id="SohuArticleResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="siteId" column="site_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="title" column="title"/>
        <result property="digest" column="digest"/>
        <result property="original" column="original"/>
        <result property="originUrl" column="origin_url"/>
        <result property="coverImage" column="cover_image"/>
        <result property="images" column="images"/>
        <result property="sortIndex" column="sort_index"/>
        <result property="state" column="state"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="type" column="type"/>
        <result property="publishMediaId" column="publish_media_id"/>
        <result property="visibleType" column="visible_type"/>
        <result property="isShare" column="is_share"/>
        <result property="isDownload" column="is_download"/>
    </resultMap>

    <select id="selectArticlePages" resultType="com.sohu.middle.api.vo.SohuArticleVo">
        SELECT
        v.*
        FROM (
        SELECT
        v.*,
        <choose>
            <when test="query.currentUserId != null and query.currentUserId > 0">
                <!--我好友的 + #我关注人的好友 -->
                IF(EXISTS(SELECT * FROM sohu_friends f WHERE ((f.user_id = #{query.currentUserId} or f.user_id in
                (SELECT focus_user_id FROM sohu_user_follow WHERE user_id = #{query.currentUserId} ) )
                and f.friend_id = v.user_id ) or ( (f.friend_id = #{query.currentUserId} or f.friend_id in
                (SELECT focus_user_id FROM sohu_user_follow WHERE user_id = #{query.currentUserId} )) and f.user_id =
                v.user_id ))
                <!--我关注的-->
                or EXISTS(SELECT * FROM sohu_user_follow uf WHERE uf.user_id = #{query.currentUserId} and
                uf.focus_user_id = v.user_id)
                <!--有关联作品的-->
                ,IF(EXISTS(SELECT * FROM sohu_article_relate r WHERE v.id = r.article_id),2,1),0) as sort
            </when>
            <otherwise>
                0 as sort
            </otherwise>
        </choose>
        FROM sohu_article v
        ) v
        where 1=1
        <if test="query.siteId != null">
            and v.site_id = #{query.siteId}
        </if>
        <if test="query.visibleType != null">
            and v.visible_type = #{query.visibleType}
        </if>
        <if test="query.countrySiteId != null">
            and v.country_site_id = #{query.countrySiteId}
        </if>
        <if test="query.userId != null">
            and v.user_id = #{query.userId}
        </if>
        <if test="query.categoryId != null">
            and v.category_id = #{query.categoryId}
        </if>
        <if test="query.title != null and query.title != ''">
            and v.title like concat('%',#{query.title},'%')
        </if>
        <if test="query.state != null and query.state != '' and query.type!='lesson'">
            and v.state = #{query.state}
        </if>
        <if test="query.isRelate != null and query.isRelate ==false ">
            and v.id not in (select article_id FROM sohu_article_relate)
        </if>
        <if test="query.type != null and query.type != ''">
            and v.type = #{query.type}
        </if>
        <if test="query.lessonLabelId != null ">
            and v.lesson_label_id = #{query.lessonLabelId}
        </if>
        <if test="query.stateList != null and query.stateList.size > 0">
            and v.state in
            <foreach item="item" index="index" collection="query.stateList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.notIds != null and query.notIds.size > 0">
            and v.id not in
            <foreach item="item" index="index" collection="query.notIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.categoryIds != null and query.categoryIds.size > 0">
            and v.category_id in
            <foreach item="item" index="index" collection="query.categoryIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.notCategoryIds != null and query.notCategoryIds.size > 0">
            and v.category_id not in
            <foreach item="item" index="index" collection="query.notCategoryIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.notUserIds != null and query.notUserIds.size > 0">
            and v.user_id not in
            <foreach item="item" index="index" collection="query.notUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.userIds != null and query.userIds.size > 0">
            and v.user_id in
            <foreach item="item" index="index" collection="query.userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.startDate != null ">
            and v.create_time >= #{query.startDate}
        </if>
        <if test="query.endDate != null ">
            and v.create_time &lt;= #{query.endDate}
        </if>
        ORDER BY v.sort_index ASC,v.create_time DESC
    </select>

    <select id="businessArticleList" resultType="com.sohu.middle.api.vo.SohuArticleVo">
        SELECT sa.*,sr.busy_code,sr.busy_title,sr.busy_type,sr.busy_info FROM sohu_article sa INNER JOIN
        sohu_article_relate sr ON sa.id = sr.article_id
        WHERE sa.state = 'OnShelf'
        <if test="bo.siteId!=null">
            AND sa.site_id = #{bo.siteId}
        </if>
        <if test="bo.categoryId!=null">
            AND sa.category_id = #{bo.categoryId}
        </if>
        <if test="bo.searchKey != null and bo.searchKey != ''">
            and sa.title like concat('%',#{bo.searchKey},'%')
        </if>
        ORDER BY sa.id DESC
    </select>

    <select id="getPublishArticleStat" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            sohu_article
        WHERE
            mcn_user_id = #{userId}
        <if test="startTime != null ">
            AND create_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>
    <select id="selectMCNArticleList" resultType="com.sohu.middle.api.vo.SohuArticleVo">
        select *
        FROM
        sohu_article a
        LEFT JOIN sohu_mcn_user mu ON a.user_id=mu.user_id
        WHERE
        mu.del_flag=0
        <if test="bo.startTime != null ">
            AND a.create_time BETWEEN #{bo.startTime} AND #{bo.endTime}
        </if>
        <if test="bo.title != null and bo.title != ''">
            and a.title like concat('%',#{bo.title},'%')
        </if>
        <if test="bo.mcnUserId!=null">
            AND a.mcn_user_id = #{bo.mcnUserId}
        </if>
        <if test="bo.userId!=null">
            AND a.user_id = #{bo.userId}
        </if>
    </select>

    <select id="selectUnSyncArticle" resultType="com.sohu.middleService.domain.SohuArticle"
            parameterType="java.lang.Long">
        SELECT *
        FROM sohu_article
        WHERE id>0
        <if test="userId!=null">
            AND user_id = #{userId}
        </if>
        AND id NOT IN (SELECT obj_id FROM sohu_content_main WHERE obj_type = 'Article')
    </select>

    <select id="queryPageListStat" resultType="com.sohu.middle.api.vo.SohuConentListStatVo">
        SELECT
        SUM(CASE WHEN state ='OnShelf' THEN 1 ELSE 0 END) AS onShelfNum,
        SUM(CASE WHEN state IN ('OffShelf','CompelOff') THEN 1 ELSE 0 END) AS offShelfNum,
        SUM(CASE WHEN state ='WaitApprove' THEN 1 ELSE 0 END) AS waitApproveNum,
        SUM(CASE WHEN state ='Refuse' THEN 1 ELSE 0 END) AS refuseNum,
        SUM(CASE WHEN appeal_status =1 THEN 1 ELSE 0 END) AS appealNum
        FROM sohu_article
        ${ew.getCustomSqlSegment}
    </select>
    <select id="getTopicList" resultType="com.sohu.middle.api.vo.SohuArticleVo">
        SELECT
        sa.*
        FROM sohu_topic_content_relation stcr
        INNER JOIN sohu_article sa ON sa.id = stcr.content_id
        INNER JOIN sohu_article_info sai ON sai.article_id = sa.id
        WHERE sa.state = 'OnShelf' AND sa.audit_state = 'Pass'
        AND stcr.del_flag = 0
        AND stcr.content_type = 'Article'
        <if test="bo.topicPid!=null and bo.topicPid!=''">
            AND stcr.topic_pid = #{bo.topicPid}
        </if>
        <if test="bo.topicId!=null and bo.topicId!=''">
            AND stcr.topic_id = #{bo.topicId}
        </if>
        GROUP BY sa.id
        <if test="bo.isSortByViewCount!=null and bo.isSortByViewCount!=''">
            ORDER BY sai.view_count DESC
        </if>
    </select>
    <select id="queryPageListOfOnShelf" resultType="com.sohu.middle.api.vo.SohuArticleVo">
        SELECT
        sa.*,
        su.user_id AS su_user_id,
        su.nick_name
        FROM
        sohu_article sa
        LEFT JOIN sys_user su ON sa.user_id = su.user_id
        <where>
            <if test="bo.id != null">
                AND sa.id = #{bo.id}
            </if>
            <if test="bo.state != null">
                AND sa.state = #{bo.state}
            </if>
            <if test="bo.categoryIds != null and bo.categoryIds.size() > 0">
                AND sa.category_id IN
                <foreach item="categoryId" collection="bo.categoryIds" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="bo.title != null and bo.title != ''">
                AND sa.title LIKE CONCAT('%', #{bo.title}, '%')
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">
                AND su.nick_name LIKE CONCAT('%', #{bo.nickName}, '%')
            </if>
        </where>
        ORDER BY
        sa.sort_index ASC,
        sa.create_time DESC
    </select>
</mapper>
