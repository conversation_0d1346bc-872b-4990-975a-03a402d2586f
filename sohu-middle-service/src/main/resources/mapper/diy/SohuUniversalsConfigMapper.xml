<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.diy.SohuUniversalsConfigMapper">

    <resultMap type="com.sohu.middleService.domain.diy.SohuUniversalsConfig" id="SohuUniversalsConfigResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="configKey" column="config_key"/>
        <result property="config" column="config"/>
        <result property="configDesc" column="config_desc"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
