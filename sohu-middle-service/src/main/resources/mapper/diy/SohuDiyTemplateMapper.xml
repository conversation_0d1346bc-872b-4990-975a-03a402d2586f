<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.middleService.mapper.diy.SohuDiyTemplateMapper">

    <resultMap type="com.sohu.middleService.domain.diy.SohuDiyTemplate" id="SohuDiyTemplateResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="sceneType" column="scene_type"/>
        <result property="sceneCode" column="scene_code"/>
        <result property="effectConfig" column="effect_config"/>
        <result property="editConfig" column="edit_config"/>
        <result property="lastPublishTime" column="last_publish_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="imageUrl" column="image_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="auditState" column="audit_state"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="auditUser" column="audit_user"/>
        <result property="shelfState" column="shelf_state"/>
        <result property="shelfReason" column="shelf_reason"/>
    </resultMap>

    <select id="auditList" resultType="com.sohu.middle.api.vo.diy.SohuDiyTemplateVo">
        SELECT st.*,s.stationmaster_id as stationmasterId
        FROM sohu_diy_template st
        LEFT JOIN sohu_site s ON st.scene_code = s.id
        LEFT JOIN sys_user su ON s.stationmaster_id = su.user_id
        WHERE st.scene_type in (3, 4) AND st.audit_state IN ('WaitApprove','Refuse','Pass')
        <if test="bo.stationmasterPhone != null and bo.stationmasterPhone!=''">
            AND su.phone_number like concat('%',#{bo.stationmasterPhone},'%')
        </if>
        <if test="bo.cityId!=null and bo.cityId!=''">
            AND s.id = #{bo.cityId}
        </if>
        <if test="bo.siteCate!=null">
            AND s.site_cate = #{bo.siteCate}
        </if>
        <if test="bo.auditState!=null and bo.auditState!=''">
            AND st.audit_state = #{bo.auditState}
        </if>
        <if test="bo.shelfState!=null and bo.cityId!=''">
            AND st.shelf_state = #{bo.shelfState}
        </if>
        <if test="bo.platformIndustryId!=null and bo.platformIndustryId!=''">
            AND s.platform_industry_id = #{bo.platformIndustryId}
        </if>
        ORDER BY st.id DESC
    </select>


</mapper>
