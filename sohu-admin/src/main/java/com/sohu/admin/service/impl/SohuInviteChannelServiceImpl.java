package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.SohuInviteChannelBo;
import com.sohu.admin.api.vo.SohuInviteChannelVo;
import com.sohu.admin.domain.SohuInviteChannel;
import com.sohu.admin.mapper.SohuInviteChannelMapper;
import com.sohu.admin.service.ISohuInviteChannelService;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台拉新渠道Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RequiredArgsConstructor
@Service
public class SohuInviteChannelServiceImpl implements ISohuInviteChannelService {

    private final SohuInviteChannelMapper baseMapper;

    /**
     * 查询平台拉新渠道
     */
    @Override
    public SohuInviteChannelVo queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询平台拉新渠道列表
     */
    @Override
    public TableDataInfo<SohuInviteChannelVo> queryPageList(SohuInviteChannelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuInviteChannel> lqw = buildQueryWrapper(bo);
        Page<SohuInviteChannelVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询平台拉新渠道列表
     */
    @Override
    public List<SohuInviteChannelVo> queryList(SohuInviteChannelBo bo) {
        LambdaQueryWrapper<SohuInviteChannel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuInviteChannel> buildQueryWrapper(SohuInviteChannelBo bo) {
        LambdaQueryWrapper<SohuInviteChannel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), SohuInviteChannel::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SohuInviteChannel::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增平台拉新渠道
     */
    @Override
    public Boolean insertByBo(SohuInviteChannelBo bo) {
        SohuInviteChannel add = BeanUtil.toBean(bo, SohuInviteChannel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改平台拉新渠道
     */
    @Override
    public Boolean updateByBo(SohuInviteChannelBo bo) {
        SohuInviteChannel update = BeanUtil.toBean(bo, SohuInviteChannel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Map<Long, String> queryMap(Set<Long> channelIds) {
        LambdaQueryWrapper<SohuInviteChannel> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuInviteChannel::getId, channelIds);
        List<SohuInviteChannel> infoList = this.baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(infoList)){
            return new HashMap<>();
        }
        return infoList.stream().collect(Collectors.toMap(SohuInviteChannel::getId, SohuInviteChannel::getName));
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuInviteChannel entity) {
        //校验渠道名称唯一性
        SohuInviteChannel sohuInviteChannel = baseMapper.selectOne(new LambdaQueryWrapper<SohuInviteChannel>().eq(SohuInviteChannel::getName, entity.getName()));
        if (Objects.nonNull(sohuInviteChannel)) {
            throw new ServiceException("渠道名称已存在");
        }
    }
}
