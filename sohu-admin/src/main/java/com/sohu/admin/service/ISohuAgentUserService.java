package com.sohu.admin.service;


import com.sohu.admin.api.bo.SohuAgentUserBo;
import com.sohu.admin.api.vo.SohuAgentChannelTopVo;
import com.sohu.admin.api.vo.SohuAgentStatVo;
import com.sohu.admin.api.vo.SohuAgentUserDetailVo;
import com.sohu.admin.api.vo.SohuAgentUserVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.vo.SohuAgentRetentionVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 代理用户记录Service接口
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
public interface ISohuAgentUserService {

    /**
     * 查询代理用户记录
     */
    SohuAgentUserVo queryById(Long id);

    /**
     * 获取代理用户详细信息
     * @param id
     * @return
     */
    SohuAgentUserDetailVo detailInfo(Long id);

    /**
     * 查询代理用户记录列表
     */
    TableDataInfo<SohuAgentUserVo> queryPageList(SohuAgentUserBo bo, PageQuery pageQuery);

    /**
     * 查询代理用户记录列表
     */
    List<SohuAgentUserVo> queryList(SohuAgentUserBo bo);

    /**
     * 获取代理机构所有成员
     */
    List<SohuAgentUserVo> getAgentUserCount(Long agentId);

    /**
     * 统计代理机构累计收益
     */
    BigDecimal agentIncomeStat(Long agentId);

    /**
     * 统计代理机构昨日收益
     */
    BigDecimal agentYesterdayIncomeStat(Long agentId);

    /**
     * 修改代理用户记录
     */
    Boolean insertByBo(SohuAgentUserBo bo);

    /**
     * 修改代理用户记录
     */
    Boolean updateByBo(SohuAgentUserBo bo);

    /**
     * 校验并批量删除代理用户记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取代理用户详细信息
     */
    SohuAgentUserVo getInfo(Integer siteType,Long siteId,String userRole);

    /**
     * 获取商户结算信息
     * @return
     */
    SohuAgentUserVo getMerInfo();

    /**
     * 邀请成功人数统计
     */
    Long getSuccessStat(Long agentId, String startDate, String endDate);

    /**
     * 等待邀请人数统计
     */
    Long getWaitJoinStat(Long agentId, String startDate, String endDate);

    /**
     * 绑定代理机构成员
     */
    @Deprecated
    void bindAgentUser(Long agentId, String roleKey);

    /**
     * 绑定代理机构成员
     */
    void bindAgentUser(Long agentId, String roleKey,Long userId,Long siteId,String agentRole,Long agentChannelId);

    /**
     * 获取机构首页统计信息
     */
    SohuAgentStatVo getAgentStatInfo(String startDate, String endDate);

    /**
     * 查询用户是否入驻代理或MCN机构
     */
    @Deprecated
    Map<String, String> checkUserEntry(String roleKey);

    /**
     * 更新代理列表邀请状态
     */
    void updateInviteState(Long userId,String merchantName,String accountType,String contactEmail);

    /**
     * 判断用户是否存在邀请记录
     */
    Boolean existsAgentUserOfJoin(Long userId,Long agentId);

    /**
     * 判断用户是否存在待认证记录
     * @param userId
     * @return
     */
    Boolean existsAgentUserOfWaitJoin(Long userId);

    /**
     * 代理商邀请成功数
     */
    Long inviteSuccess();


    /**
     * 添加角色
     * @param userId
     * @param roleKey
     * @return
     */
    Boolean addRoleKey(Long userId,String roleKey);

    /**
     * 查询邀请列表
     *
     * @return
     */
    List<SohuAgentUserVo> queryInviteList();

    /**
     * 获取渠道客户数Top
     * @return
     */
    List<SohuAgentChannelTopVo> getChannelTop(String startDate, String endDate);

    /**
     * 查询邀请信息
     * @return
     */
    SohuAgentUserVo queryInviteInfo(Long inviteId, Long consumerUserId);

    /**
     * 获取邀请客户留存明细
     */
    List<SohuAgentRetentionVo> getRetentionDetail(String startDate, String endDate);

    /**
     * 保存用户留存数据
     */
    void saveUserRetentionStats();
}
