package com.sohu.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.bo.SohuAgentUserBo;
import com.sohu.admin.api.bo.SohuInviteChannelBo;
import com.sohu.admin.api.vo.*;
import com.sohu.admin.domain.SohuAgentUser;
import com.sohu.admin.mapper.SohuAgentUserMapper;
import com.sohu.admin.service.ISohuAgentUserService;
import com.sohu.admin.service.ISohuInviteChannelService;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteEntryService;
import com.sohu.entry.api.vo.SohuAccountEnterVo;
import com.sohu.middle.api.enums.InviteEnum;
import com.sohu.middle.api.service.RemoteMiddleBillRecordService;
import com.sohu.middle.api.service.RemoteMiddleInviteService;
import com.sohu.middle.api.service.RemoteMiddleMerTradeRecordService;
import com.sohu.middle.api.service.RemoteMiddleUserBalanceService;
import com.sohu.middle.api.vo.SohuBillRecordVo;
import com.sohu.middle.api.vo.SohuInviteVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.RemoteIndependentOrderService;
import com.sohu.pay.api.RemoteYmService;
import com.sohu.pay.api.model.SohuAccountBankModel;
import com.sohu.pay.api.model.SohuIndependentOrderModel;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.report.api.RemoteIncomeStatisticsService;
import com.sohu.report.api.enums.RetentionPeriodEnum;
import com.sohu.report.api.vo.SohuAgentRetentionVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo;
import com.sohu.report.api.vo.SohuUserRetentionStatVo;
import com.sohu.system.api.RemoteLogService;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 代理用户记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuAgentUserServiceImpl implements ISohuAgentUserService {

    private final SohuAgentUserMapper baseMapper;

    private final ISohuInviteChannelService sohuInviteChannelService;
    @DubboReference
    private RemoteIndependentOrderService remoteIndependentOrderService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteEntryService remoteEntryService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleUserBalanceService remoteMiddleUserBalanceService;
    @DubboReference
    private RemoteMiddleBillRecordService remoteMiddleBillRecordService;
    @DubboReference
    private RemoteMiddleMerTradeRecordService remoteMiddleMerTradeRecordService;
    @DubboReference
    private RemoteMiddleInviteService remoteMiddleInviteService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemoteLogService remoteLogService;
    @DubboReference
    private RemoteIncomeStatisticsService remoteIncomeStatisticsService;

    @DubboReference
    private RemoteYmService remoteYmService;

    /**
     * 统计默认值
     */
    private static final Long DEFAULT_STAT_VALUE = 0L;
    /**
     * 邀请链接过期时间,有效期24小时,单位ms
     */
    private static final int DAYS_THRESHOLD = 3;
    // 使用 LinkedHashSet 保持插入顺序并去重，或者直接使用 List 按照期望的顺序定义
    private static final List<RetentionPeriodEnum> RETENTION_PERIODS_ENUMS = Arrays.asList(
            RetentionPeriodEnum.DAY_1_AFTER,
            RetentionPeriodEnum.DAY_2_AFTER,
            RetentionPeriodEnum.DAY_3_AFTER,
            RetentionPeriodEnum.DAY_4_AFTER,
            RetentionPeriodEnum.DAY_5_AFTER,
            RetentionPeriodEnum.DAY_6_AFTER,
            RetentionPeriodEnum.DAY_7_AFTER,
            RetentionPeriodEnum.DAY_14_AFTER,
            RetentionPeriodEnum.DAY_30_AFTER
    );

    /**
     * 查询代理用户记录
     */
    @Override
    public SohuAgentUserVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public SohuAgentUserDetailVo detailInfo(Long id) {
        SohuAgentUserDetailVo detailVo = new SohuAgentUserDetailVo();
        SohuAgentUserVo agentUserVo = queryById(id);
        detailVo.setBaseInfo(agentUserVo);
        //封装消费信息
        SohuAgentUserDetailVo.ConsumerInfo consumerInfo = new SohuAgentUserDetailVo.ConsumerInfo();
        List<SohuIndependentOrderModel> independentOrderModels = remoteIndependentOrderService.queryByInviteUser(agentUserVo.getUserId(), agentUserVo.getAgentId(), SohuIndependentObject.agency.getKey(),
                Arrays.asList(IndependentStatusEnum.DISTRIBUTED.getCode(), IndependentStatusEnum.DISTRIBUTING.getCode()));
        if (CollUtil.isNotEmpty(independentOrderModels)) {
            List<SohuIndependentOrderModel> distributedList = independentOrderModels.stream().filter(independentOrderModel -> IndependentStatusEnum.DISTRIBUTED.getCode() == independentOrderModel.getIndependentStatus())
                    .sorted(Comparator.comparing(SohuIndependentOrderModel::getCreateTime)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(distributedList)) {
                consumerInfo.setFirstConsumerTime(distributedList.get(0).getCreateTime());
                consumerInfo.setFirstConsumerType(distributedList.get(0).getTradeType());
                consumerInfo.setFirstConsumerAmount(distributedList.get(0).getTaskFullAmount());
                consumerInfo.setBindAfterConsumerDays(DateUtil.between(agentUserVo.getCreateTime(), distributedList.get(0).getCreateTime(), DateUnit.DAY));
                consumerInfo.setTotalConsumerCount(distributedList.size());
                consumerInfo.setTotalConsumerAmount(distributedList.stream().map(SohuIndependentOrderModel::getTaskFullAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            }
            detailVo.setConsumerInfo(consumerInfo);
            detailVo.setIndependentInfo(BeanUtil.copyToList(independentOrderModels, SohuAgentUserDetailVo.IndependentInfo.class));
        }
        return detailVo;
    }

    /**
     * 查询代理用户记录列表
     */
    @Override
    public TableDataInfo<SohuAgentUserVo> queryPageList(SohuAgentUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAgentUser> lqw = buildQueryWrapper(bo);
        Page<SohuAgentUserVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> channelIds = result.getRecords().stream().map(SohuAgentUserVo::getAgentChannelId).collect(Collectors.toSet());
            Map<Long, String> channelMap = sohuInviteChannelService.queryMap(channelIds);
            result.getRecords().forEach(item -> {
                item.setAgentChannelName(StrUtil.isNotEmpty(channelMap.get(item.getAgentChannelId())) ? channelMap.get(item.getAgentChannelId()) : "");
            });
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询代理用户记录列表
     */
    @Override
    public List<SohuAgentUserVo> queryList(SohuAgentUserBo bo) {
        LambdaQueryWrapper<SohuAgentUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<SohuAgentUserVo> getAgentUserCount(Long agentId) {
        LambdaQueryWrapper<SohuAgentUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAgentUser::getUserId, agentId);

        return baseMapper.selectVoList(wrapper);
    }

    private LambdaQueryWrapper<SohuAgentUser> buildQueryWrapper(SohuAgentUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuAgentUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAgentUser::getAgentId, LoginHelper.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getMerchantName()), SohuAgentUser::getMerchantName, bo.getMerchantName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhoneNumber()), SohuAgentUser::getPhoneNumber, bo.getPhoneNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getAgentEmail()), SohuAgentUser::getAgentEmail, bo.getAgentEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountType()), SohuAgentUser::getAccountType, bo.getAccountType());
        lqw.like(StringUtils.isNotBlank(bo.getRoleKey()), SohuAgentUser::getRoleKey, bo.getRoleKey());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuAgentUser::getState, bo.getState());
        lqw.eq(bo.getUserId() != null, SohuAgentUser::getUserId, bo.getUserId());
        lqw.eq(bo.getSiteId() != null, SohuAgentUser::getSiteId, bo.getSiteId());
        lqw.in(CollUtil.isNotEmpty(bo.getAgentRoles()), SohuAgentUser::getAgentRole, bo.getAgentRoles());
        lqw.eq(bo.getAgentChannelId() != null, SohuAgentUser::getAgentChannelId, bo.getAgentChannelId());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(SohuAgentUser::getCreateTime, DateUtils.beginOfTime(bo.getStartTime()));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(SohuAgentUser::getCreateTime, DateUtils.endOfTime(bo.getEndTime()));
        }
        if (StrUtil.isNotEmpty(bo.getBindStartTime())) {
            lqw.ge(SohuAgentUser::getFinishTime, DateUtils.beginOfTime(bo.getBindStartTime()));
        }
        if (StrUtil.isNotEmpty(bo.getBindEndTime())) {
            lqw.le(SohuAgentUser::getFinishTime, DateUtils.endOfTime(bo.getBindEndTime()));
        }
        lqw.orderByDesc(SohuAgentUser::getCreateTime);
        return lqw;
    }

    /**
     * 新增代理用户记录
     */
    @Override
    public Boolean insertByBo(SohuAgentUserBo bo) {
        SohuAgentUser add = BeanUtil.toBean(bo, SohuAgentUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改代理用户记录
     */
    @Override
    public Boolean updateByBo(SohuAgentUserBo bo) {
        SohuAgentUser update = BeanUtil.toBean(bo, SohuAgentUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuAgentUser entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除代理用户记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if(isValid){
//            //TODO 做一些业务上的校验,判断是否需要校验
//        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuAgentUserVo getInfo(Integer siteType, Long siteId, String userRole) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            return new SohuAgentUserVo();
        }
        SohuAgentUserVo sohuAgentUserVo = new SohuAgentUserVo();
        //查询翼码用户金额
//        String merchantId = remoteAccountService.getMerchantId(userId);
//        if (StrUtil.isNotEmpty(merchantId)) {
//            List<AccountBalanceQueryList> balanceQueryList = queryUserBalance(merchantId);
//            if (CollUtil.isNotEmpty(balanceQueryList)) {
//                int totalAvlAmt = balanceQueryList.stream().mapToInt(AccountBalanceQueryList::getAvlAmt).sum();
//                //查询商户可提现余额
//                BigDecimal merchantBalance = remoteMiddleMerTradeRecordService.selectMerBalance(userId, null);
//                sohuAgentUserVo.setWalletBalance(CalUtils.centToYuan(new BigDecimal(totalAvlAmt)).subtract(merchantBalance));
//            }
//        }
        if (StrUtil.isEmpty(userRole)) {
            userRole = UserRoleEnum.PERSONAL.getType();
        }
        //查询用户角色的已分账金额
        BigDecimal distributedAmount = remoteUserService.getUserIncome(userId, siteType, siteId, userRole, IndependentStatusEnum.DISTRIBUTED.getCode(), null, null);
        //查询用户角色的已提现金额
        BigDecimal withdrawalAmount = remoteUserService.getUserWithdrawal(userId, siteType, siteId, userRole, null, null);
        sohuAgentUserVo.setWalletBalance(distributedAmount.subtract(withdrawalAmount));
        // 获取未分账余额
        BigDecimal independentingAmount = remoteUserService.getUserIncome(userId, siteType, siteId, userRole, IndependentStatusEnum.DISTRIBUTING.getCode(), null, null);
        sohuAgentUserVo.setIndependentBalance(Objects.isNull(independentingAmount) ? BigDecimal.ZERO : independentingAmount);

        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserIdOfPass(userId);
        // 获取公司名称
        sohuAgentUserVo.setMerchantName(Objects.nonNull(sohuAccountVo) ? sohuAccountVo.getMerchantName() : null);
        // 获取收款卡号
        SohuAccountBankModel sohuAccountBankModel = remoteAccountService.queryAccountBankByUserId(userId);
        sohuAgentUserVo.setCardNo(Objects.nonNull(sohuAccountBankModel) ? sohuAccountBankModel.getCardNoEncrypt() : null);

        return sohuAgentUserVo;
    }

    @Override
    public SohuAgentUserVo getMerInfo() {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            return new SohuAgentUserVo();
        }
        SohuAgentUserVo sohuAgentUserVo = new SohuAgentUserVo();
        //查询商户可提现余额
        BigDecimal walletBalance = remoteMiddleMerTradeRecordService.selectMerBalance(userId, null);
        sohuAgentUserVo.setWalletBalance(walletBalance);
        // 获取未分账余额
        BigDecimal independentingAmount = remoteMiddleMerTradeRecordService.selectAmountByStatus(userId, null, IndependentStatusEnum.DISTRIBUTING.getCode());
        sohuAgentUserVo.setIndependentBalance(Objects.isNull(independentingAmount) ? BigDecimal.ZERO : independentingAmount);

        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserIdOfPass(userId);
        // 获取公司名称
        sohuAgentUserVo.setMerchantName(Objects.nonNull(sohuAccountVo) ? sohuAccountVo.getMerchantName() : null);
        // 获取收款卡号
        SohuAccountBankModel sohuAccountBankModel = remoteAccountService.queryAccountBankByUserId(userId);
        sohuAgentUserVo.setCardNo(Objects.nonNull(sohuAccountBankModel) ? sohuAccountBankModel.getCardNoEncrypt() : null);

        return sohuAgentUserVo;
    }

    @Override
    public SohuAgentStatVo getAgentStatInfo(String startDate, String endDate) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            return new SohuAgentStatVo();
        }

        SohuAgentStatVo sohuAgentStatVo = new SohuAgentStatVo();
        // 获取入驻信息
        //SohuAccountEnterModel sohuAccountEnterModel = remoteEntryService.selectAccountEnterByUserId(userId);
//        SohuAccountEnterVo sohuAccountEnterVo = remoteEntryService.queryIncludeAccountByUserId(userId);
        SohuAccountEnterVo sohuAccountEnterVo = remoteEntryService.queryIncludeAccountByUserIdAndRoleKey(userId, PlatformRoleCodeEnum.Agent.getCode());
        if (Objects.nonNull(sohuAccountEnterVo)) {
            BeanUtil.copyProperties(sohuAccountEnterVo, sohuAgentStatVo);
//            // 获取可提现余额
//            SohuUserBalanceVo sohuUserBalanceVo = remoteMiddleUserBalanceService.queryByUserId(userId);
//            sohuAgentStatVo.setWalletBalance(Objects.isNull(sohuUserBalanceVo) ? BigDecimal.ZERO : sohuUserBalanceVo.getWalletBalance());
//            // 获取未分账余额
//            BigDecimal independentingAmount = remoteIndependentOrderService.selectIndependentingByUserId(userId, null, null, null, null, IndependentStatusEnum.DISTRIBUTING.getCode(), null, null);
//            sohuAgentStatVo.setIndependentBalance(Objects.isNull(independentingAmount) ? BigDecimal.ZERO : independentingAmount);
//            // 统计昨日收益
//            sohuAgentStatVo.setYesterdayIncome(this.agentYesterdayIncomeStat(userId));
            // 获取邀请成功人数
            sohuAgentStatVo.setSuccessStat(this.getSuccessStat(userId, startDate, endDate));
            // 获取等待认证人数
            sohuAgentStatVo.setWaitJoinStat(this.getWaitJoinStat(userId, startDate, endDate));
            // 统计累计收益
            sohuAgentStatVo.setEarnStat(remoteUserService.getUserInviteIncome(userId, null, null, SohuIndependentObject.agency.getKey(), DateUtil.parse(startDate), DateUtil.parse(endDate)));
            // 统计活跃人数(已认证的客户)
            List<SohuAgentUserVo> agentUserVoList = this.getAgentUserCount(userId);
            // 获取当前用户的状态为success的所有客户id
            List<Long> userIdList = agentUserVoList.stream()
                    .filter(sohuAgentUserVo -> sohuAgentUserVo.getState().equals(InviteEnum.SUCCESS.getCode()))
                    .map(SohuAgentUserVo::getUserId)
                    .collect(Collectors.toList());
            sohuAgentStatVo.setActiveUserCount(CollUtil.isEmpty(userIdList) ? 0L : this.getActiveUserCount(userIdList, startDate, endDate));
        }

        return sohuAgentStatVo;
    }

    private Long getActiveUserCount(List<Long> userIdList, String startDate, String endDate) {
        return remoteLogService.getOnlineUserNumByUserIdsAndTime(userIdList, DateUtil.parse(startDate), DateUtil.parse(endDate));
    }

    @Override
    public void bindAgentUser(Long agentId, String roleKey) {
        if (agentId == null) {
            return;
        }
        // 获取账号信息
        LoginUser userInfo = remoteUserService.selectById(LoginHelper.getUserId());

        // 获取账号创建时间
        Date createTime = userInfo.getCreateTime();
        Duration duration = Duration.between(createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(), LocalDateTime.now());

//        //查询是否已经与当前agentId绑定，如果绑定直接返回
//        if (this.existsAgentUserOfJoin(LoginHelper.getUserId(),agentId)){
//            return;
//        }

        // 更新代理用户信息
        SohuAgentUser updateEntity = new SohuAgentUser();
        updateEntity.setUserId(LoginHelper.getUserId());
        setAccountInfo(agentId, roleKey, updateEntity, userInfo, null, null, null);
        log.info("bindAgentUser:{}", updateEntity);
        if (updateEntity.getId() != null && updateEntity.getId() > 0L) {
            this.baseMapper.updateById(updateEntity);
        } else {
            this.baseMapper.insert(updateEntity);
        }
    }

    @Override
    public void bindAgentUser(Long agentId, String roleKey, Long userId, Long siteId, String agentRole, Long agentChannelId) {
        if (agentId == null) {
            return;
        }
        // 获取账号信息
        LoginUser userInfo = remoteUserService.selectById(userId);

        //查询是否已经与当前agentId绑定，如果绑定直接返回
        if (this.existsAgentUserOfJoin(userId, agentId)) {
            return;
        }
        // 更新代理用户信息
        SohuAgentUser updateEntity = new SohuAgentUser();
        updateEntity.setUserId(userId);
        setAccountInfo(agentId, roleKey, updateEntity, userInfo, siteId, agentRole, agentChannelId);
        log.info("bindAgentUser:{}", updateEntity);
        if (updateEntity.getId() != null && updateEntity.getId() > 0L) {
            this.baseMapper.updateById(updateEntity);
        } else {
            this.baseMapper.insert(updateEntity);
        }
    }

    /**
     * 设置实名认证信息
     */
    private void setAccountInfo(Long agentId, String roleKey, SohuAgentUser updateEntity, LoginUser userInfo, Long siteId, String agentRole, Long agentChannelId) {
        // 设置公共参数
        updateEntity.setAgentId(agentId);
        updateEntity.setRoleKey(roleKey);
        updateEntity.setAgentRole(agentRole);
        updateEntity.setSiteId(siteId);
        updateEntity.setAgentChannelId(agentChannelId);
        // 设置实名认证信息
        SohuAccountVo account = this.remoteAccountService.queryByUserIdOfPass(updateEntity.getUserId());
        // 是否实名
        boolean isUserVerified = Objects.nonNull(account);
        // 判定用户是否已经被绑定
        SohuInviteVo invite = remoteMiddleInviteService.queryByRegUser(updateEntity.getUserId());
        // 先判断用户是否已经被其他代理成功邀请
        if (Objects.nonNull(invite)) {
            updateEntity.setState(InviteEnum.REFUSE.getCode());
            updateEntity.setRejectReason("该客户已被其它代理邀请关联,不能再被邀请关联");
            updateEntity.setPhoneNumber(userInfo.getPhoneNumber());
            updateEntity.setMerchantName(userInfo.getNickname());
            this.baseMapper.insertOrUpdate(updateEntity);
            throw new RuntimeException("您已被其它代理邀请关联,不能再被邀请关联,如需关联请更换账号");
        }

        // 根据实名认证和注册时间判断邀请状态
        if (isUserVerified) {
            updateEntity.setState(InviteEnum.SUCCESS.getCode());
            updateEntity.setMerchantName(account.getMerchantName());
            updateEntity.setPhoneNumber(account.getContactPhone());
            updateEntity.setAccountType(account.getAccountType());
            updateEntity.setAgentEmail(account.getContactEmail());
            updateEntity.setFinishTime(new Date());
            updateEntity.setRegTime(userInfo.getCreateTime());
        } else if (!isUserVerified) {
            updateEntity.setState(InviteEnum.WAIT_JOIN.getCode());
            updateEntity.setPhoneNumber(userInfo.getPhoneNumber());
            updateEntity.setMerchantName(userInfo.getNickname());
            updateEntity.setRegTime(new Date());
        }
    }

    /**
     * 判断用户是否存在邀请记录
     *
     * @param userId
     * @return
     */
    @Override
    public Boolean existsAgentUserOfJoin(Long userId, Long agentId) {
        LambdaQueryWrapper<SohuAgentUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAgentUser::getUserId, userId)
                .in(SohuAgentUser::getState, InviteEnum.SUCCESS.getCode(), InviteEnum.WAIT_JOIN.getCode());
        if (agentId != null) {
            wrapper.eq(SohuAgentUser::getAgentId, agentId);
        }
        return this.baseMapper.exists(wrapper);
    }

    @Override
    public Boolean existsAgentUserOfWaitJoin(Long userId) {
        LambdaQueryWrapper<SohuAgentUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAgentUser::getUserId, userId)
                .eq(SohuAgentUser::getState, InviteEnum.WAIT_JOIN.getCode());
        return this.baseMapper.exists(wrapper);
    }

    /**
     * 代理商邀请成功数
     */
    @Override
    public Long inviteSuccess() {
        LambdaQueryWrapper<SohuAgentUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAgentUser::getAgentId, LoginHelper.getUserId());
        lqw.eq(SohuAgentUser::getState, InviteEnum.SUCCESS.getCode());
        return baseMapper.selectCount(lqw);
    }

    @Override
    public Boolean addRoleKey(Long userId, String roleKey) {
        LambdaQueryWrapper<SohuAgentUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAgentUser::getUserId, userId);
        lqw.eq(SohuAgentUser::getState, InviteEnum.SUCCESS.getCode());
        lqw.last(" limit 1");
        SohuAgentUser sohuAgentUser = baseMapper.selectOne(lqw);
        if (sohuAgentUser == null) {
            return true;
        }
        // 查询邀请人的角色
        Set<String> roles = remoteSysRoleService.selectRoleKeyByUserId(sohuAgentUser.getAgentId());
        //判断用户角色，如果是非普通用户且与邀请人同级解绑
        if (!roleKey.equals(RoleCodeEnum.Article.getCode()) && !roleKey.equals(RoleCodeEnum.COMMON.getCode()) && roles.contains(roleKey)) {
            sohuAgentUser.setState(InviteEnum.EXPIRED.getCode());
            //更新上下级关系
            remoteMiddleInviteService.unbind(sohuAgentUser.getUserId(), sohuAgentUser.getAgentId());
        }
        sohuAgentUser.setRoleKey(sohuAgentUser.getRoleKey() + "," + roleKey);
        return baseMapper.updateById(sohuAgentUser) > 0;
    }

    @Override
    public BigDecimal agentIncomeStat(Long agentId) {
        // 获取机构所有成员
        List<SohuAgentUserVo> sohuAgentUserVoList = this.getAgentUserCount(agentId);
        // 统计机构累计收益
        BigDecimal totalWithdrawAmount = sohuAgentUserVoList.stream()
                .map(sohuAgentUserVo -> remoteMiddleUserBalanceService.accumulateInComeAmount(sohuAgentUserVo.getUserId()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return Optional.of(totalWithdrawAmount).orElse(BigDecimal.ZERO);
    }

    @Override
    public BigDecimal agentYesterdayIncomeStat(Long agentId) {
        // 获取机构所有成员
        List<SohuAgentUserVo> sohuAgentUserVoList = this.getAgentUserCount(agentId);
        // 统计机构昨日收益
        BigDecimal totalIndependentingAmount = sohuAgentUserVoList.stream()
                .flatMap(sohuAgentUserVo -> remoteMiddleBillRecordService.selectYesterdayIncome(sohuAgentUserVo.getUserId()).stream())
                .map(SohuBillRecordVo::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return Optional.of(totalIndependentingAmount).orElse(BigDecimal.ZERO);
    }

    @Override
    public Long getSuccessStat(Long agentId, String startDate, String endDate) {
        LambdaQueryWrapper<SohuAgentUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAgentUser::getState, InviteEnum.SUCCESS);
        wrapper.eq(SohuAgentUser::getAgentId, agentId);
        wrapper.between(SohuAgentUser::getFinishTime, startDate, endDate);
        List<SohuAgentUser> sohuAgentUserList = baseMapper.selectList(wrapper);
        long inviteSuccessCount = sohuAgentUserList.size();

        return Optional.of(inviteSuccessCount).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public Long getWaitJoinStat(Long agentId, String startDate, String endDate) {
        LambdaQueryWrapper<SohuAgentUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAgentUser::getState, InviteEnum.WAIT_JOIN);
        wrapper.eq(SohuAgentUser::getAgentId, agentId);
        wrapper.between(SohuAgentUser::getCreateTime, startDate, endDate);
        List<SohuAgentUser> sohuAgentUserList = baseMapper.selectList(wrapper);
        long inviteWaitJoinCount = sohuAgentUserList.size();

        return Optional.of(inviteWaitJoinCount).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public Map<String, String> checkUserEntry(String roleKey) {
        // 判断用户是否入驻
        return remoteEntryService.checkUserEntry(LoginHelper.getUserId(), roleKey);
    }

    @Override
    public void updateInviteState(Long userId, String merchantName, String accountType, String contactEmail) {
        LambdaUpdateWrapper<SohuAgentUser> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SohuAgentUser::getUserId, userId);
        wrapper.set(SohuAgentUser::getState, InviteEnum.SUCCESS.getCode());
        wrapper.set(SohuAgentUser::getMerchantName, merchantName);
        wrapper.set(SohuAgentUser::getAccountType, accountType);
        wrapper.set(SohuAgentUser::getAgentEmail, contactEmail);
        wrapper.set(SohuAgentUser::getFinishTime, new Date());
        this.baseMapper.update(null, wrapper);
    }

    @Override
    public List<SohuAgentUserVo> queryInviteList() {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(SohuAgentUser.class));
    }

    @Override
    public SohuAgentUserVo queryInviteInfo(Long inviteId, Long consumerUserId) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<SohuAgentUser>()
                .eq(SohuAgentUser::getAgentId, inviteId)
                .eq(SohuAgentUser::getUserId, consumerUserId)
                .eq(SohuAgentUser::getState, InviteEnum.SUCCESS.getCode()).last(" limit 1"));
    }

    @Override
    public List<SohuAgentChannelTopVo> getChannelTop(String startDate, String endDate) {
        Long userId = validateAndGetCurrentUserId();
        if (userId == null) {
            return new ArrayList<>();
        }

        // 解析日期并查询数据
        Date start = parseDateSafely(startDate);
        Date end = parseDateSafely(endDate);

        // 获取所有渠道
        List<SohuInviteChannelVo> sohuInviteChannelVoList = sohuInviteChannelService.queryList(new SohuInviteChannelBo());
        if (CollUtil.isEmpty(sohuInviteChannelVoList)) {
            return new ArrayList<>();
        }
        // 将所有渠道转换为ID到名称的映射，方便后续查找
        Map<Long, String> allChannelMap = sohuInviteChannelVoList.stream()
                .collect(Collectors.toMap(SohuInviteChannelVo::getId, SohuInviteChannelVo::getName));

        List<SohuAgentUserVo> agentUserVoList = queryAgentUsersByDateRange(userId, start, end, false);

        // 根据agentChannelId 分组所有邀请用户
        Map<Long, List<SohuAgentUserVo>> agentUserMap = agentUserVoList.stream()
                .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentChannelId));

        // 根据agentChannelId 分组成功绑定的用户
        Map<Long, List<SohuAgentUserVo>> agentUserSuccessMap = agentUserVoList.stream()
                .filter(sohuAgentUserVo -> InviteEnum.SUCCESS.getCode().equals(sohuAgentUserVo.getState()))
                .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentChannelId));

        ArrayList<SohuAgentChannelTopVo> topVoArrayList = new ArrayList<>();
        // 根据服务商/站长id获取客户收益明细
        List<SohuUserIncomeStatisticsInfoVo> sohuUserIncomeStatisticsInfoVoList = remoteIncomeStatisticsService.queryUserIncomeByTime(userId, DateUtil.parse(startDate, "yyyyMMdd"), DateUtil.parse(endDate, "yyyyMMdd"));

        // 获取每个渠道的收益
        Map<Long, BigDecimal> channelIncomeMap = sohuUserIncomeStatisticsInfoVoList.stream()
                .collect(Collectors.groupingBy(SohuUserIncomeStatisticsInfoVo::getConsumerChannelId, Collectors.reducing(BigDecimal.ZERO, SohuUserIncomeStatisticsInfoVo::getIncome, BigDecimal::add)));

        // 获取每个渠道的新增付费客户数
        Map<Long, Long> channelPayCountMap = sohuUserIncomeStatisticsInfoVoList.stream()
                .collect(Collectors.groupingBy(SohuUserIncomeStatisticsInfoVo::getConsumerChannelId, Collectors.counting()));

        for (SohuInviteChannelVo channelVo : sohuInviteChannelVoList) {
            SohuAgentChannelTopVo topVo = new SohuAgentChannelTopVo();
            Long channelId = channelVo.getId();

            topVo.setChannelId(channelId);
            topVo.setChannelName(allChannelMap.get(channelId));

            // 使用 Optional.ofNullable 或 getOrDefault 安全获取数据，没有则为0
            Long inviteCount = Long.valueOf(Optional.ofNullable(agentUserMap.get(channelId))
                    .map(List::size)
                    .orElse(0));
            topVo.setInviteCount(inviteCount);

            Long bindCount = Long.valueOf(Optional.ofNullable(agentUserSuccessMap.get(channelId))
                    .map(List::size)
                    .orElse(0));
            topVo.setBindCount(bindCount);

            Long payCount = channelPayCountMap.getOrDefault(channelId, 0L);
            topVo.setPayCount(payCount);

            BigDecimal perPayAmount = channelIncomeMap.getOrDefault(channelId, BigDecimal.ZERO);
            topVo.setPerPayAmount(perPayAmount);

            // 计算付费率，注意分母为0的情况
            BigDecimal payRate = BigDecimal.ZERO;
            if (inviteCount > 0) {
                payRate = CalUtils.calculateRate(payCount, inviteCount);
            }
            topVo.setPayRate(payRate);

            topVoArrayList.add(topVo);
        }

        return topVoArrayList.stream().sorted(Comparator.comparing(SohuAgentChannelTopVo::getInviteCount).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<SohuAgentRetentionVo> getRetentionDetail(String startDate, String endDate) {
        Long currentUserId = validateAndGetCurrentUserId();
        if (currentUserId == null) {
            return Collections.emptyList();
        }

        // 解析日期并查询数据
        Date start = parseDateSafely(startDate);
        Date end = parseDateSafely(endDate);

        // 初始化一个包含所有日期和默认（0）统计数据的列表
        List<SohuAgentRetentionVo> resultList = initializeRetentionVosForDateRange(start, end);

        // 使用 LinkedHashMap 保持日期顺序，或者在最后排序
        Map<String, SohuAgentRetentionVo> retentionVoMap = resultList.stream()
                .collect(Collectors.toMap(SohuAgentRetentionVo::getDate, Function.identity(),
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        // 查询实际的邀请用户数据
        List<SohuAgentUserVo> invitedUsers = queryAgentUsersByDateRange(currentUserId, start, end, true);

        // 如果有实际邀请数据，则进行计算并覆盖默认值
        if (!CollectionUtils.isEmpty(invitedUsers)) {
            // 按邀请完成日期分组用户
            Map<String, List<SohuAgentUserVo>> usersByInviteDate = groupUsersByInviteDate(invitedUsers);

            // 遍历有数据的日期，更新 resultList 中的对应项
            for (Map.Entry<String, List<SohuAgentUserVo>> entry : usersByInviteDate.entrySet()) {
                String inviteDate = entry.getKey();
                List<SohuAgentUserVo> usersInvitedOnDate = entry.getValue();

                SohuAgentRetentionVo retentionVo = retentionVoMap.get(inviteDate);
                if (retentionVo != null) {
                    retentionVo.setInvitedClientCount((long) usersInvitedOnDate.size());
                    // 计算并设置各个留存周期的数据
                    calculateRetentionForPeriods(retentionVo, inviteDate, usersInvitedOnDate);
                }
            }
        }

        // 计算每个日期的留存数据
        return retentionVoMap.values().stream()
                .sorted(Comparator.comparing(SohuAgentRetentionVo::getDate))
                .collect(Collectors.toList());
    }

    /**
     * 验证并获取当前用户ID
     */
    private Long validateAndGetCurrentUserId() {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            return null;
        }
        return userId;
    }

    /**
     * 安全解析日期字符串，支持多种格式
     */
    private Date parseDateSafely(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            throw new IllegalArgumentException("日期字符串不能为空");
        }

        try {
            // 如果是 yyyyMMdd 格式（8位数字）
            if (dateStr.matches("\\d{8}")) {
                return DateUtil.parse(dateStr, "yyyyMMdd");
            }
            // 如果是 yyyy-MM-dd 格式
            else if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return DateUtil.parse(dateStr, "yyyy-MM-dd");
            }
            // 其他格式使用 hutool 的默认解析
            else {
                return DateUtil.parse(dateStr);
            }
        } catch (Exception e) {
            log.error("日期解析失败，日期字符串: {}", dateStr, e);
            throw new IllegalArgumentException("无效的日期格式: " + dateStr + "，支持格式: yyyyMMdd 或 yyyy-MM-dd");
        }
    }

    /**
     * 初始化指定日期范围内的所有 RetentionVo 对象，并设置默认值0。
     *
     * @param start 开始日期
     * @param end 结束日期
     * @return 包含所有日期和默认值的 SohuAgentRetentionVo 列表
     */
    private List<SohuAgentRetentionVo> initializeRetentionVosForDateRange(Date start, Date end) {
        List<SohuAgentRetentionVo> initialList = new ArrayList<>();
        // 使用 Hutool 的 DateUtil.rangeToList 获取日期范围内的所有日期（包含开始和结束）
        List<DateTime> datesInRange = DateUtil.rangeToList(start, end, DateField.DAY_OF_MONTH);

        for (Date date : datesInRange) {
            SohuAgentRetentionVo vo = new SohuAgentRetentionVo();
            vo.setDate(DateUtil.formatDate(date));
            vo.setInvitedClientCount(0L);
            setDefaultRetentionValues(vo);
            initialList.add(vo);
        }
        return initialList;
    }

    /**
     * 查询指定时间范围内的代理用户数据（通用方法）
     *
     * @param userId      用户ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param onlySuccess 是否只查询成功状态的记录
     */
    private List<SohuAgentUserVo> queryAgentUsersByDateRange(Long userId, Date startDate, Date endDate, boolean onlySuccess) {
        LambdaQueryWrapper<SohuAgentUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuAgentUser::getAgentId, userId);

        if (onlySuccess) {
            wrapper.eq(SohuAgentUser::getState, InviteEnum.SUCCESS.getCode())
                    .between(SohuAgentUser::getFinishTime, startDate, endDate)
                    .orderByAsc(SohuAgentUser::getFinishTime);
        } else {
            wrapper.between(SohuAgentUser::getCreateTime, startDate, endDate)
                    .orderByAsc(SohuAgentUser::getCreateTime);
        }

        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 按邀请日期分组用户
     */
    private Map<String, List<SohuAgentUserVo>> groupUsersByInviteDate(List<SohuAgentUserVo> invitedUsers) {
        return invitedUsers.stream()
                .collect(Collectors.groupingBy(user -> {
                    // 将邀请完成时间格式化为日期字符串 yyyy-MM-dd
                    return DateUtil.formatDate(user.getFinishTime());
                }, LinkedHashMap::new, Collectors.toList()));
    }

    /**
     * 计算各个留存周期的留存数据
     */
    private void calculateRetentionForPeriods(SohuAgentRetentionVo retentionVo, String inviteDate, List<SohuAgentUserVo> users) {
        try {
            Date inviteDateParsed = DateUtil.parseDate(inviteDate);
            List<Long> userIds = users.stream().map(SohuAgentUserVo::getUserId).collect(Collectors.toList());

            // 遍历枚举定义的留存周期
            for (RetentionPeriodEnum periodEnum : RETENTION_PERIODS_ENUMS) {
                Date retentionDate = DateUtil.offsetDay(inviteDateParsed, periodEnum.getDays());
                // 结束日期是下一天的开始
                Date retentionEndDate = DateUtil.offsetDay(retentionDate, 1);

                // 查询在留存日期当天活跃的用户数量
                Long retainedCount = getRetainedUserCount(userIds, retentionDate, retentionEndDate);

                // 通过枚举的方法设置值，避免switch
                periodEnum.setRetentionValue(retentionVo, retainedCount);
            }
        } catch (Exception e) {
            log.error("计算留存数据失败，邀请日期: {}", inviteDate, e);
            // 设置默认值
            setDefaultRetentionValues(retentionVo);
        }
    }

    /**
     * 获取在指定时间段内活跃的用户数量
     */
    private Long getRetainedUserCount(List<Long> userIds, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(userIds)) {
            return 0L;
        }

        try {
            // 使用远程日志服务查询活跃用户数量
            return remoteLogService.getOnlineUserNumByUserIdsAndTime(userIds, startDate, endDate);
        } catch (Exception e) {
            log.error("查询用户活跃数据失败，用户ID列表: {}, 开始时间: {}, 结束时间: {}",
                    userIds, startDate, endDate, e);
            return 0L;
        }
    }

    /**
     * 设置默认留存值（当计算失败时使用）
     */
    private void setDefaultRetentionValues(SohuAgentRetentionVo retentionVo) {
        for (RetentionPeriodEnum periodEnum : RETENTION_PERIODS_ENUMS) {
            periodEnum.setRetentionValue(retentionVo, 0L);
        }
    }

    /**
     * 保存用户留存数据
     *
     * @param agentId 代理商/站长ID
     * @param retentionVos 留存数据列表
     */
    public void saveUserRetentionStats(Long agentId, List<SohuAgentRetentionVo> retentionVos) {
        if (CollUtil.isEmpty(retentionVos)) {
            log.info("无可保存的用户留存数据，agentId: {}", agentId);
            return;
        }

        List<SohuUserRetentionStatVo> statListToSave = new ArrayList<>();

        for (SohuAgentRetentionVo retentionVo : retentionVos) {
            // 将字符串日期转换为 LocalDate
            Date inviteDate = DateUtil.parse(retentionVo.getDate(), "yyyy-MM-dd");

            // 遍历所有留存周期，为每个周期创建一条记录
            for (RetentionPeriodEnum periodEnum : RETENTION_PERIODS_ENUMS) {
                Long retainedCount = periodEnum.getRetentionValue(retentionVo);

                SohuUserRetentionStatVo statVo = new SohuUserRetentionStatVo();
                statVo.setUserId(agentId);
                statVo.setInviteDate(inviteDate);
                statVo.setRetentionDay(periodEnum.getDays());
                statVo .setRetainedCount(retainedCount != null ? retainedCount : 0L);

                statListToSave.add(statVo);
            }
        }

        // 批量保存或更新到数据库
        remoteIncomeStatisticsService.saveOrUpdateBatchRetentionStats(statListToSave);
        log.info("成功保存/更新 {} 条用户留存统计数据，agentId: {}", statListToSave.size(), agentId);
    }

    @Override
    public void saveUserRetentionStats() {
        // 获取昨天的日期字符串
        String yesterdayStr = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");

        // 获取所有服务商id
        List<Long> allAgentIds = baseMapper.selectVoList(Wrappers.lambdaQuery(SohuAgentUser.class)).stream()
                .map(SohuAgentUserVo::getAgentId)
                .distinct()
                .collect(Collectors.toList());

        for (Long agentId : allAgentIds) {
            // 模拟调用 getRetentionDetail 内部的计算逻辑，或者直接调用 getRetentionDetail
            List<SohuAgentRetentionVo> calculatedRetentionData =
                    this.getRetentionDetailForStorage(agentId, yesterdayStr, yesterdayStr);

            this.saveUserRetentionStats(agentId, calculatedRetentionData);
        }
    }

    private List<SohuAgentRetentionVo> getRetentionDetailForStorage(Long agentId, String yesterdayStr, String yesterdayStr1) {
    }
}
