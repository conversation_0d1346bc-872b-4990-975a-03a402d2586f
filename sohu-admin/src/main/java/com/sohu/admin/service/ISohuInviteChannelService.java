package com.sohu.admin.service;


import com.sohu.admin.api.bo.SohuInviteChannelBo;
import com.sohu.admin.api.vo.SohuInviteChannelVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 平台拉新渠道Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ISohuInviteChannelService {

    /**
     * 查询平台拉新渠道
     */
    SohuInviteChannelVo queryById(Long id);

    /**
     * 查询平台拉新渠道列表
     */
    TableDataInfo<SohuInviteChannelVo> queryPageList(SohuInviteChannelBo bo, PageQuery pageQuery);

    /**
     * 查询平台拉新渠道列表
     */
    List<SohuInviteChannelVo> queryList(SohuInviteChannelBo bo);

    /**
     * 修改平台拉新渠道
     */
    Boolean insertByBo(SohuInviteChannelBo bo);

    /**
     * 修改平台拉新渠道
     */
    Boolean updateByBo(SohuInviteChannelBo bo);

    /**
     * 批量查询平台拉新渠道
     */
    Map<Long, String> queryMap(Set<Long> channelIds);
}
