package com.sohu.admin.dubbo;

import com.sohu.admin.api.RemoteAdminService;
import com.sohu.admin.api.bo.SohuQualificationListBo;
import com.sohu.admin.api.vo.SohuAgentUserVo;
import com.sohu.admin.api.vo.SohuCategoryBrandBusinessSettingsVo;
import com.sohu.admin.api.vo.SohuInviteChannelVo;
import com.sohu.admin.api.vo.SohuQualificationVo;
import com.sohu.admin.service.*;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.page.core.PageQuery;
import com.sohu.middle.api.vo.SohuVideoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteAdminServiceImpl implements RemoteAdminService {

    private final ISohuAgentUserService iSohuAgentUserService;
    private final ISohuQualificationService iSohuQualificationService;
    private final ISohuTopicService iSohuTopicService;
    private final ISohuCategoryBrandBusinessSettingsService iSohuCategoryBrandBusinessSettingsService;
    private final ISohuInviteChannelService sohuInviteChannelService;

    @Override
    public void bindAgentUser(Long agentId, String roleKey) {
        iSohuAgentUserService.bindAgentUser(agentId, roleKey);
    }

    @Override
    public void bindAgentUser(Long agentId, String roleKey, Long userId, Long siteId, String agentRole, Long agentChannelId) {
        iSohuAgentUserService.bindAgentUser(agentId, roleKey, userId, siteId, agentRole, agentChannelId);
    }

    @Override
    public void updateInviteState(Long userId, String merchantName, String accountType, String contactEmail) {
        iSohuAgentUserService.updateInviteState(userId, merchantName, accountType, contactEmail);
    }

    @Override
    public Boolean existsAgentUserOfJoin(Long userId, Long agentId) {
        return iSohuAgentUserService.existsAgentUserOfJoin(userId, agentId);
    }

    @Override
    public Boolean existsAgentUserOfWaitJoin(Long userId) {
        return iSohuAgentUserService.existsAgentUserOfWaitJoin(userId);
    }

    @Override
    public Boolean addRoleKey(Long userId, String roleKey) {
        return iSohuAgentUserService.addRoleKey(userId, roleKey);
    }

    @Override
    public List<SohuVideoVo> queryVideoListByTopicId(String topicIdent) {
        return iSohuTopicService.queryVideoListByTopicId(topicIdent);
    }

    @Override
    public List<SohuVideoVo> queryVideoListByTopicId(String topicIdent, PageQuery pageQuery) {
        return iSohuTopicService.queryVideoListByTopicId(topicIdent, pageQuery);
    }

    @Override
    public void refreshHotRecommendListJobHandler() {
        iSohuTopicService.refreshHotRecommendListJobHandler();
    }

    @Override
    public List<SohuQualificationVo> getQualificationList(SohuQualificationListBo bo) {
        return iSohuQualificationService.getQualificationList(bo);
    }

    @Override
    public SohuCategoryBrandBusinessSettingsVo getCategoryBrandBusinessSettings(Long businessId, String businessType) {
        return iSohuCategoryBrandBusinessSettingsService.getCategoryBrandBusinessSettings(businessId, businessType);
    }

    @Override
    public List<SohuAgentUserVo> queryInviteList() {
        return iSohuAgentUserService.queryInviteList();
    }

    @Override
    public SohuInviteChannelVo queryInviteChannel(Long inviteId, Long consumerUserId) {
        SohuAgentUserVo sohuAgentUserVo = iSohuAgentUserService.queryInviteInfo(inviteId, consumerUserId);
        Long channelId = 1L;
        if (Objects.nonNull(sohuAgentUserVo) && Objects.nonNull(sohuAgentUserVo.getAgentChannelId())){
            channelId = sohuAgentUserVo.getAgentChannelId();
        }
        return sohuInviteChannelService.queryById(channelId);
    }

    @Override
    public void saveUserRetentionStats() {
        iSohuAgentUserService.saveUserRetentionStats();
    }
}
