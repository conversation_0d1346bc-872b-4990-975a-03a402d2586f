package com.sohu.admin.controller;

import com.sohu.admin.api.bo.SohuInviteChannelBo;
import com.sohu.admin.api.vo.SohuInviteChannelVo;
import com.sohu.admin.service.ISohuInviteChannelService;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 平台拉新渠道控制器
 * 前端访问路由地址为:/system/inviteChannel
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/channel")
public class SohuInviteChannelController extends BaseController {

    private final ISohuInviteChannelService iSohuInviteChannelService;

    @Operation(summary = "查询渠道列表", description = "负责人：汪伟 查询列表")
    @GetMapping("/list")
    public TableDataInfo<SohuInviteChannelVo> list(@RequestBody SohuInviteChannelBo bo, PageQuery pageQuery) {
        return iSohuInviteChannelService.queryPageList(bo, pageQuery);
    }

    @Operation(summary = "新增渠道", description = "负责人：汪伟 新增渠道")
    @PostMapping()
    public R<Boolean> add(@RequestBody SohuInviteChannelBo bo) {
        return R.ok(iSohuInviteChannelService.insertByBo(bo));
    }

    @Operation(summary = "修改渠道", description = "负责人：汪伟 修改渠道")
    @PutMapping()
    public R<Boolean> edit(@RequestBody SohuInviteChannelBo bo) {
        return toAjax(iSohuInviteChannelService.updateByBo(bo));
    }
}
