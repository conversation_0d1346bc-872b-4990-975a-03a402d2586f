package com.sohu.admin.service;

import com.sohu.admin.service.impl.SohuAgentUserServiceImpl;
import com.sohu.report.api.vo.SohuAgentRetentionVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

/**
 * 邀请客户留存明细接口测试
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class SohuAgentUserServiceRetentionTest {

    @Resource
    private SohuAgentUserServiceImpl sohuAgentUserService;

    /**
     * 测试获取邀请客户留存明细
     */
    @Test
    public void testGetRetentionDetail() {
        try {
            // 测试日期格式：yyyyMMdd
            String startDate = "20250101";
            String endDate = "20250131";
            
            List<SohuAgentRetentionVo> result1 = sohuAgentUserService.getRetentionDetail(startDate, endDate);
            log.info("测试结果1 (yyyyMMdd格式): {}", result1);
            
            // 测试日期格式：yyyy-MM-dd
            String startDate2 = "2025-01-01";
            String endDate2 = "2025-01-31";
            
            List<SohuAgentRetentionVo> result2 = sohuAgentUserService.getRetentionDetail(startDate2, endDate2);
            log.info("测试结果2 (yyyy-MM-dd格式): {}", result2);
            
            // 验证结果结构
            if (!result1.isEmpty()) {
                SohuAgentRetentionVo firstResult = result1.get(0);
                log.info("留存数据示例:");
                log.info("日期: {}", firstResult.getDate());
                log.info("邀请客户数: {}", firstResult.getInvitedClientCount());
                log.info("1天后留存: {}", firstResult.getDay1After());
                log.info("7天后留存: {}", firstResult.getDay7After());
                log.info("30天后留存: {}", firstResult.getDay30After());
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    /**
     * 测试异常情况
     */
    @Test
    public void testGetRetentionDetailWithInvalidParams() {
        try {
            // 测试空参数
            List<SohuAgentRetentionVo> result1 = sohuAgentUserService.getRetentionDetail("", "");
            log.info("空参数测试结果: {}", result1);
            
            // 测试无效日期格式
            List<SohuAgentRetentionVo> result2 = sohuAgentUserService.getRetentionDetail("invalid", "invalid");
            log.info("无效日期格式测试结果: {}", result2);
            
        } catch (Exception e) {
            log.error("异常测试结果", e);
        }
    }
}
