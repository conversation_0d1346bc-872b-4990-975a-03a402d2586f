package com.sohu.system.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.system.api.domain.SysRole;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.domain.SysNotice;
import com.sohu.system.domain.SysNoticeUser;
import com.sohu.system.mapper.SysNoticeUserMapper;
import com.sohu.system.service.ISysRoleService;
import com.sohu.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 异步
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuSystemAsyncUtil {

    private final ISysUserService sysUserService;
    private final ISysRoleService sysRoleService;
    private final SysNoticeUserMapper sysNoticeUserMapper;

    @Async
    public void saveNoticeUser(SysNotice notice) {
        if (notice.getNoticeSendType() != 2) {
            return;
        }
        Long noticeId = notice.getNoticeId();
        String noticeRole = notice.getNoticeRole();
        List<String> split = StrUtil.split(noticeRole, StrPool.COMMA);
        List<SysRole> sysRoles = sysRoleService.queryByRoleKeys(split);
        if (CollUtil.isEmpty(sysRoles)) {
            log.info("发送公告，saveNoticeUser，角色未找到");
            return;
        }
        List<SysNoticeUser> batch = new ArrayList<>();
        for (SysRole sysRole : sysRoles) {
            SysUser user = new SysUser();
            user.setRoleId(sysRole.getRoleId());
            PageQuery pageQuery = new PageQuery(1, 999999);
            TableDataInfo<SysUser> dataInfo = sysUserService.selectAllocatedList(user, pageQuery);
            if (CollUtil.isEmpty(dataInfo.getData())) {
                log.info("发送公告，saveNoticeUser，该角色授权用户未空,{}", sysRole.getRoleKey());
                continue;
            }
            List<SysUser> data = dataInfo.getData();
            for (SysUser sysUser : data) {
                SysNoticeUser item = new SysNoticeUser();
                item.setNoticeId(noticeId);
                if (Objects.equals(sysUser.getUserId(), 1L)) {
                    continue;
                }
                item.setUserId(sysUser.getUserId());
                item.setCreateTime(new Date());
                item.setReadStatus(false);
                batch.add(item);
            }
        }
        sysNoticeUserMapper.insertBatch(batch);
        log.info("批量发送公告成功，{}条", batch.size());
    }

}
