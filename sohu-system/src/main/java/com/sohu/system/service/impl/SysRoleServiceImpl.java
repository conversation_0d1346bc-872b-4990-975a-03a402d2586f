package com.sohu.system.service.impl;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.CommonEnums;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.system.api.bo.SysRoleBo;
import com.sohu.system.api.domain.SysRole;
import com.sohu.system.api.vo.SysRoleVo;
import com.sohu.system.domain.*;
import com.sohu.system.mapper.*;
import com.sohu.system.service.ISysRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class SysRoleServiceImpl implements ISysRoleService {

    private final SysRoleMapper baseMapper;
    private final SysRoleMenuMapper roleMenuMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysRoleDeptMapper roleDeptMapper;
    private final SysPlatformFeatureMapper sysPlatformFeatureMapper;
    private final SysPlatformUserMapper sysPlatformUserMapper;
    private final SysPlatformRoleMapper sysPlatformRoleMapper;

    @Override
    public TableDataInfo<SysRole> selectPageRoleList(SysRoleBo role, PageQuery pageQuery) {
        //Page<SysRole> page = baseMapper.selectPageRoleList(PageQueryUtils.build(pageQuery), this.buildQueryWrapper(role));
        Page<SysRole> page = baseMapper.selectPage(PageQueryUtils.build(pageQuery), this.buildQueryWrapper(role));
        return TableDataInfoUtils.build(page);
    }

    /**
     * 根据条件分页查询角色数据
     *
     * @param bo 角色信息
     * @return 角色数据集合信息
     */
    @Override
    public List<SysRoleVo> selectRoleList(SysRoleBo bo) {
        //return baseMapper.selectRoleList(this.buildQueryWrapper(role));
        return baseMapper.selectVoList(this.buildQueryWrapper(bo));
    }

    @Deprecated
    private Wrapper<SysRole> buildQueryWrapper(SysRole role) {
        Map<String, Object> params = role.getParams();
        QueryWrapper<SysRole> wrapper = Wrappers.query();
        wrapper.eq("r.del_flag", UserConstants.ROLE_NORMAL)
                .eq(ObjectUtil.isNotNull(role.getRoleId()), "r.role_id", role.getRoleId())
                .like(StringUtils.isNotBlank(role.getRoleName()), "r.role_name", role.getRoleName())
                .eq(StringUtils.isNotBlank(role.getStatus()), "r.status", role.getStatus())
                .like(StringUtils.isNotBlank(role.getRoleKey()), "r.role_key", role.getRoleKey())
                .between(params.get("beginTime") != null && params.get("endTime") != null,
                        "r.create_time", params.get("beginTime"), params.get("endTime"))
                .orderByAsc("r.role_sort").orderByAsc("r.create_time");
        ;
        return wrapper;
    }

    private LambdaQueryWrapper<SysRole> buildQueryWrapper(SysRoleBo bo) {
        LambdaQueryWrapper<SysRole> lqw = Wrappers.lambdaQuery();
        lqw.eq(Objects.nonNull(bo.getRoleId()), SysRole::getRoleId, bo.getRoleId())
                .like(StrUtil.isNotBlank(bo.getRoleName()), SysRole::getRoleName, bo.getRoleName())
                .eq(StrUtil.isNotBlank(bo.getStatus()), SysRole::getStatus, bo.getStatus())
                .like(StrUtil.isNotBlank(bo.getRoleKey()), SysRole::getRoleKey, bo.getRoleKey())
                .between(StrUtil.isNotBlank(bo.getBeginTime()) && StrUtil.isNotBlank(bo.getEndTime()), BaseEntity::getCreateTime
                        , bo.getBeginTime(), bo.getEndTime())
                .orderByAsc(SysRole::getRoleSort).orderByAsc(BaseEntity::getCreateTime);
        return lqw;
    }

    //    private LambdaQueryWrapper<SysPlatformRole> buildQueryWrapper(SysPlatformRoleBo bo) {
    //        //Map<String, Object> params = bo.getParams();
    //        LambdaQueryWrapper<SysPlatformRole> lqw = Wrappers.lambdaQuery();
    //        if(Objects.isNull(bo)){return lqw;}
    //        lqw.like(StringUtils.isNotBlank(bo.getName()), SysPlatformRole::getName, bo.getName());
    //        lqw.like(StringUtils.isNotBlank(bo.getRoleName()), SysPlatformRole::getRoleName, bo.getRoleName());
    //        lqw.eq(StringUtils.isNotBlank(bo.getRoleKey()), SysPlatformRole::getRoleKey, bo.getRoleKey());
    //        //lqw.eq(StringUtils.isNotBlank(bo.getRoleSort()), SysPlatformRole::getRoleSort, bo.getRoleSort());
    //        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysPlatformRole::getStatus, bo.getStatus());
    //        return lqw;
    //    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRolesByUserId(Long userId) {
        //List<SysRole> userRoles = baseMapper.selectRolePermissionByUserId(userId);
        List<SysRoleVo> userRoles = baseMapper.selectListByUserId(userId);
        List<SysRole> roles = selectRoleAll();
        for (SysRole role : roles) {
            for (SysRoleVo userRole : userRoles) {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue()) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    @Override
    public List<SysRoleVo> selectListOfEnableByUserId(Long userId) {
        return this.baseMapper.selectListOfEnableByUserId(userId);
    }

    @Override
    public List<SysRoleVo> selectListByUserId(Long userId) {
        return this.baseMapper.selectListByUserId(userId);
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<SysRole> perms = baseMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms) {
            if (ObjectUtil.isNotNull(perm)) {
                permsSet.addAll(StringUtils.splitList(perm.getRoleKey().trim()));
            }
        }
        return permsSet;
    }

    @Override
    public Set<String> selectRoleKeyOfEnableByUserId(Long userId) {
        List<SysRoleVo> list = baseMapper.selectListOfEnableByUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return new HashSet<>();
        }
        Set<String> permsSet = new HashSet<>();
        for (SysRoleVo vo : list) {
            if (Objects.nonNull(vo)) {
                permsSet.addAll(StringUtils.splitList(vo.getRoleKey().trim()));
            }
        }
        return permsSet;
    }

    @Override
    public Set<String> selectRoleKeyByUserId(Long userId) {
        List<SysRoleVo> list = baseMapper.selectListByUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return new HashSet<>();
        }
        Set<String> permsSet = new HashSet<>();
        for (SysRoleVo vo : list) {
            if (Objects.nonNull(vo)) {
                permsSet.addAll(StringUtils.splitList(vo.getRoleKey().trim()));
            }
        }
        return permsSet;
    }

    @Override
    public List<SysRole> selectRolePermissionListByUserId(Long userId) {
        return baseMapper.selectRolePermissionByUserId(userId);
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll() {
        return this.baseMapper.selectList();
    }

    @Override
    public List<SysRoleVo> queryListOfEnable() {
        LambdaQueryWrapper<SysRole> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysRole::getStatus, 0);
        return this.baseMapper.selectVoList(lqw);
    }

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    @Override
    public List<Long> selectRoleListByUserId(Long userId) {
        return baseMapper.selectRoleListByUserId(userId);
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRole selectRoleById(Long roleId) {
        return baseMapper.selectById(roleId);
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleNameUnique(SysRole role) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleName, role.getRoleName())
                .ne(ObjectUtil.isNotNull(role.getRoleId()), SysRole::getRoleId, role.getRoleId()));
        return !exist;
    }

    /**
     * 校验角色权限是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleKeyUnique(SysRole role) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleKey, role.getRoleKey())
                .ne(ObjectUtil.isNotNull(role.getRoleId()), SysRole::getRoleId, role.getRoleId()));
        return !exist;
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    @Override
    public void checkRoleAllowed(SysRole role) {
        if (ObjectUtil.isNotNull(role.getRoleId()) && role.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员角色");
        }
        // 新增不允许使用 管理员标识符
        if (ObjectUtil.isNull(role.getRoleId())
                && StringUtils.equals(role.getRoleKey(), UserConstants.ADMIN_ROLE_KEY)) {
            throw new ServiceException("不允许使用系统内置管理员角色标识符!");
        }
        // 修改不允许修改 管理员标识符
        if (ObjectUtil.isNotNull(role.getRoleId())) {
            SysRole sysRole = baseMapper.selectById(role.getRoleId());
            // 如果标识符不相等 判断为修改了管理员标识符
            if (!StringUtils.equals(sysRole.getRoleKey(), role.getRoleKey())
                    && StringUtils.equals(sysRole.getRoleKey(), UserConstants.ADMIN_ROLE_KEY)) {
                throw new ServiceException("不允许修改系统内置管理员角色标识符!");
            }
        }
    }

    /**
     * 校验角色是否有数据权限
     *
     * @param roleId 角色id
     */
    @Override
    public void checkRoleDataScope(Long roleId) {
        if (!LoginHelper.isAdmin()) {
//            SysRole role = new SysRole();
//            role.setRoleId(roleId);
//            List<SysRole> roles = this.selectRoleList(role);
//            if (CollUtil.isEmpty(roles)) {
//                throw new ServiceException("没有权限访问角色数据！");
//            }
            LambdaQueryWrapper<SysRole> lqw = new LambdaQueryWrapper<>();
            lqw.eq(SysRole::getRoleId, roleId);
            if (!this.baseMapper.exists(lqw)) {
                throw new ServiceException("没有权限访问角色数据！");
            }
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public long countUserRoleByRoleId(Long roleId) {
        return userRoleMapper.selectCount(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getRoleId, roleId));
    }

    @Override
    public boolean existUserRoleByRoleId(Long roleId) {
        LambdaQueryWrapper<SysUserRole> lqw = new LambdaQueryWrapper();
        lqw.eq(SysUserRole::getRoleId, roleId);
        return userRoleMapper.exists(lqw);
    }

    private boolean existPlatformFeatureByRoleId(Long roleId) {
        LambdaQueryWrapper<SysPlatformFeature> lqw = new LambdaQueryWrapper();
        lqw.eq(SysPlatformFeature::getSysRoleId, roleId);
        return sysPlatformFeatureMapper.exists(lqw);
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertRole(SysRole role) {
        //this.checkRoleKeyUnique(role);
        this.validEntityBeforeSave(role);
        // 新增角色信息
        baseMapper.insert(role);
        insertRoleMenu(role);
        return true;
    }

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRole(SysRole role) {
        validEntityBeforeSave(role);
        // 修改角色信息
        baseMapper.updateById(role);
        // 删除角色与菜单关联
        roleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, role.getRoleId()));
        insertRoleMenu(role);
        //this.cleanOnlineUserByRole(role.getRoleId());
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysRole entity) {
        SysRole sysRole = null;
        if (Objects.nonNull(entity.getRoleId())) {
            sysRole = baseMapper.selectById(entity.getRoleId());
            if (Objects.isNull(sysRole)) {
                throw new ServiceException("数据不存在");
            }
            if (CommonEnums.DataTypeEnum.SYSTEM.name().equals(sysRole.getDataType())) {
                throw new ServiceException("系统内置角色，无权操作");
            }
            if (Objects.equals(entity.getStatus(), "1") && this.existUserRoleByRoleId(entity.getRoleId())) {
                throw new ServiceException(String.format("%s已分配，不能停用", sysRole.getRoleName()));
            }
        }
        sysRole = baseMapper.selectOne(SysRole::getRoleKey, entity.getRoleKey());
        if (Objects.nonNull(sysRole) && !Objects.equals(entity.getRoleId(), sysRole.getRoleId())) {
            throw new ServiceException("角色key不能重复");
        }
        sysRole = baseMapper.selectOne(SysRole::getRoleName, entity.getRoleName());
        if (Objects.nonNull(sysRole) && !Objects.equals(entity.getRoleId(), sysRole.getRoleId())) {
            throw new ServiceException("角色名称不能重复");
        }

    }

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public int updateRoleStatus(SysRole role) {
        validEntityBeforeSave(role);
        return baseMapper.updateById(role);
    }

    /**
     * 修改数据权限信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int authDataScope(SysRole role) {
        validEntityBeforeSave(role);
        // 修改角色信息
        baseMapper.updateById(role);
        // 删除角色与部门关联
        roleDeptMapper.delete(new LambdaQueryWrapper<SysRoleDept>().eq(SysRoleDept::getRoleId, role.getRoleId()));
        // 新增角色和部门信息（数据权限）
        return insertRoleDept(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int insertRoleMenu(SysRole role) {
        int rows = 1;
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
        for (Long menuId : role.getMenuIds()) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (list.size() > 0) {
            rows = roleMenuMapper.insertBatch(list) ? list.size() : 0;
        }
        return rows;
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleDept(SysRole role) {
        int rows = 1;
        // 新增角色与部门（数据权限）管理
        List<SysRoleDept> list = new ArrayList<SysRoleDept>();
        for (Long deptId : role.getDeptIds()) {
            SysRoleDept rd = new SysRoleDept();
            rd.setRoleId(role.getRoleId());
            rd.setDeptId(deptId);
            list.add(rd);
        }
        if (list.size() > 0) {
            rows = roleDeptMapper.insertBatch(list) ? list.size() : 0;
        }
        return rows;
    }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleById(Long roleId) {
        // 删除角色与菜单关联
        roleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, roleId));
        // 删除角色与部门关联
        roleDeptMapper.delete(new LambdaQueryWrapper<SysRoleDept>().eq(SysRoleDept::getRoleId, roleId));
        return baseMapper.deleteById(roleId);
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleByIds(Long[] roleIds) {
        for (Long roleId : roleIds) {
            SysRole role = selectRoleById(roleId);
            //checkRoleAllowed(role);
            //checkRoleDataScope(roleId);
//            if (countUserRoleByRoleId(roleId) > 0) {
//                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleName()));
//            }
            if (CommonEnums.DataTypeEnum.SYSTEM.name().equals(role.getDataType())) {
                throw new ServiceException("系统内置角色，无权操作");
            }
            if (this.existUserRoleByRoleId(roleId)) {
                throw new ServiceException(String.format("%s已分配用户，不能删除", role.getRoleName()));
            }
            if (this.existPlatformFeatureByRoleId(roleId)) {
                throw new ServiceException(String.format("%s已分配角色，不能删除", role.getRoleName()));
            }
        }
        List<Long> ids = Arrays.asList(roleIds);
        // 删除角色与菜单关联
        roleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().in(SysRoleMenu::getRoleId, ids));
        // 删除角色与部门关联
        roleDeptMapper.delete(new LambdaQueryWrapper<SysRoleDept>().in(SysRoleDept::getRoleId, ids));
//        //删除功能角色与平台角色关联
//        sysPlatformFeatureMapper.delete(new LambdaQueryWrapper<SysPlatformFeature>().in(SysPlatformFeature::getSysRoleId, ids));
        return baseMapper.deleteBatchIds(ids);
    }

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    @Override
    public int deleteAuthUser(SysUserRole userRole) {
        int rows = userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getRoleId, userRole.getRoleId())
                .eq(SysUserRole::getUserId, userRole.getUserId()));
//        if (rows > 0) {
//            cleanOnlineUserByRole(userRole.getRoleId());
//        }
        return rows;
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    public int deleteAuthUsers(Long roleId, Long[] userIds) {
        int rows = userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getRoleId, roleId)
                .in(SysUserRole::getUserId, Arrays.asList(userIds)));
//        if (rows > 0) {
//            cleanOnlineUserByRole(roleId);
//        }
        return rows;
    }

    @Override
    public int deleteAuthUsers(List<Long> roleIds, Long userId) {
        return userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>()
                .in(SysUserRole::getRoleId, roleIds)
                .eq(SysUserRole::getUserId, userId));
    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    @Override
    public int insertAuthUsers(Long roleId, Long[] userIds) {
        // 新增用户与角色管理
        int rows = 1;
        List<SysUserRole> list = new ArrayList<>();
        for (Long userId : userIds) {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        if (list.size() > 0) {
            rows = userRoleMapper.insertBatch(list) ? list.size() : 0;
        }
//        if (rows > 0) {
//            cleanOnlineUserByRole(roleId);
//        }
        return rows;
    }

    @Override
    public void cleanOnlineUserByRole(Long roleId) {
        // 如果角色未绑定用户 直接返回
        Long num = userRoleMapper.selectCount(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getRoleId, roleId));
        if (num == 0) {
            return;
        }
        List<String> keys = StpUtil.searchTokenValue("", 0, -1, false);
        if (CollUtil.isEmpty(keys)) {
            return;
        }
        for (String key : keys) {
            if (StrUtil.isEmptyIfStr(key)) {
                continue;
            }
            String token = StringUtils.substringAfterLast(key, ":");
            if (StrUtil.isEmptyIfStr(token)) {

            }
            // 如果已经过期则跳过
            if (StpUtil.stpLogic.getTokenActiveTimeoutByToken(token) < -1) {
                continue;
            }
            LoginUser loginUser = LoginHelper.getLoginUser(token);
            if (Objects.isNull(loginUser) || CollUtil.isEmpty(loginUser.getRoles())) {
                continue;
            }
            if (loginUser.getRoles().stream().anyMatch(r -> r.getRoleId().equals(roleId))) {
                try {
                    StpUtil.logoutByTokenValue(token);
                } catch (NotLoginException ignored) {
                }
            }
        }
        // 角色关联的在线用户量过大会导致redis阻塞卡顿 谨慎操作
//        keys.parallelStream().forEach(key -> {
//            String token = StringUtils.substringAfterLast(key, ":");
//            // 如果已经过期则跳过
//            if (StpUtil.stpLogic.getTokenActiveTimeoutByToken(token) < -1) {
//                return;
//            }
//            LoginUser loginUser = LoginHelper.getLoginUser(token);
//            if (loginUser.getRoles().stream().anyMatch(r -> r.getRoleId().equals(roleId))) {
//                try {
//                    StpUtil.logoutByTokenValue(token);
//                } catch (NotLoginException ignored) {
//                }
//            }
//        });
    }

    @Override
    public List<SysRole> queryByRoleKeys(List<String> roleKeys) {
        if (CollUtil.isEmpty(roleKeys)) {
            // roleKeys是否为空
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysRole::getRoleKey, roleKeys);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void insertList(List<SysRole> roleDOS, Long userId) {
        if (userId == null) {
            userId = LoginHelper.getUserId();
        }
        //Set<String> roleKeys = this.selectRolePermissionByUserId(userId);
        Set<String> roleKeys = this.selectRoleKeyByUserId(userId);
        // 用户已有的角色
        List<SysUserRole> insertBatch = new ArrayList<>();
        for (SysRole roleDO : roleDOS) {
            Long id = roleDO.getRoleId();
            if (roleKeys.contains(roleDO.getRoleKey())) {
                continue;
            }
            SysUserRole userRoleDO = new SysUserRole();
            userRoleDO.setRoleId(id);
            userRoleDO.setUserId(userId);
            insertBatch.add(userRoleDO);
        }
        // 保存用户角色关联
        userRoleMapper.insertBatch(insertBatch);
    }

    @Override
    public void insertUserRole(String roleKey, Long userId) {
        if (userId == null) {
            userId = LoginHelper.getUserId();
        }
        SysRole role = this.baseMapper.selectOne(SysRole::getRoleKey, roleKey);
        if (role == null) {
            return;
        }
        //排除已有角色
        SysUserRole userRole = this.userRoleMapper.selectOne(SysUserRole::getRoleId, role.getRoleId(), SysUserRole::getUserId, userId);
        if (userRole == null) {
            userRole = new SysUserRole();
            userRole.setRoleId(role.getRoleId());
            userRole.setUserId(userId);
            this.userRoleMapper.insert(userRole);
        }
    }

    @Override
    public void deleteUserRole(String roleKey, Long userId) {
        if (userId == null) {
            userId = LoginHelper.getUserId();
        }
        SysRole role = this.baseMapper.selectOne(SysRole::getRoleKey, roleKey);
        if (role == null) {
            return;
        }
        //存在则删除角色
        SysUserRole userRole = this.userRoleMapper.selectOne(SysUserRole::getRoleId, role.getRoleId(), SysUserRole::getUserId, userId);
        if (userRole != null) {
            this.userRoleMapper.delete(SysUserRole::getRoleId, role.getRoleId(), SysUserRole::getUserId, userId);
        }
        LambdaQueryWrapper<SysPlatformRole> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysPlatformRole::getRoleKey, roleKey).last("limit 1");
        SysPlatformRole sysPlatformRole = sysPlatformRoleMapper.selectOne(lqw);
        if (Objects.nonNull(sysPlatformRole)) {
            sysPlatformUserMapper.delete(SysPlatformUser::getPlatformRoleId, sysPlatformRole.getId(), SysPlatformUser::getUserId, userId);
        }
    }

    @Override
    public RoleCodeEnum getCurrentExclusiveRole(Long userId) {
        //取数据库的最新数据
        //Set<String> roleKeySet = this.selectRolePermissionByUserId(userId);
        Set<String> roleKeySet = this.selectRoleKeyByUserId(userId);
        if (roleKeySet != null) {
            if (roleKeySet.contains(RoleCodeEnum.Project.getCode())) {
                return RoleCodeEnum.Project;
            } else if (roleKeySet.contains(RoleCodeEnum.Conversion.getCode())) {
                return RoleCodeEnum.Conversion;
            } else if (roleKeySet.contains(RoleCodeEnum.Server.getCode())) {
                return RoleCodeEnum.Server;
            } else if (roleKeySet.contains(RoleCodeEnum.Agent.getCode())) {
                return RoleCodeEnum.Agent;
            } else if (roleKeySet.contains(RoleCodeEnum.MCN.getCode())) {
                return RoleCodeEnum.MCN;
            } else if (roleKeySet.contains(RoleCodeEnum.Professor.getCode())) {
                return RoleCodeEnum.Professor;
            }
        }
        return null;
    }

    @Override
    public List<SysRoleVo> selectListOfEnableByPlatformRoleId(Long platformRoleId) {
        return baseMapper.selectListOfEnableByPlatformRoleId(platformRoleId);
    }

    @Override
    public List<SysRoleVo> selectListOfEnableByPlatformRoleKey(String platformRoleKey) {
        return baseMapper.selectListOfEnableByPlatformRoleKey(platformRoleKey);
    }

    @Override
    public List<SysRoleVo> selectListByPlatformRoleId(Long platformRoleId) {
        return baseMapper.selectListByPlatformRoleId(platformRoleId);
    }

    @Override
    public List<String> selectKeysOfEnableByPlatformRoleId(Long platformRoleId) {
        List<SysRoleVo> sysRoleVoList = this.selectListOfEnableByPlatformRoleId(platformRoleId);
        if (CollUtil.isNotEmpty(sysRoleVoList)) {
            return sysRoleVoList.stream().map(p -> p.getRoleKey()).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<Long> selectIdsOfEnableByPlatformRoleId(Long platformRoleId) {
        List<SysRoleVo> sysRoleVoList = this.selectListOfEnableByPlatformRoleId(platformRoleId);
        if (CollUtil.isNotEmpty(sysRoleVoList)) {
            return sysRoleVoList.stream().map(p -> p.getRoleId()).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<Long> selectIdsByPlatformRoleId(Long platformRoleId) {
        List<SysRoleVo> sysRoleVoList = this.selectListByPlatformRoleId(platformRoleId);
        if (CollUtil.isNotEmpty(sysRoleVoList)) {
            return sysRoleVoList.stream().map(p -> p.getRoleId()).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public Boolean batchInsertList(List<String> roleKeys, Long userId) {
        if (CollUtil.isEmpty(roleKeys)) {
            return false;
        }
        List<SysRole> sysRoles = this.queryByRoleKeys(roleKeys);
        this.insertList(sysRoles, userId);
        return true;
    }

    @Override
    public Boolean batchInsertList(List<String> roleKeys, List<Long> userIdList) {
        if (CollUtil.isEmpty(roleKeys) || CollUtil.isEmpty(userIdList)) {
            return false;
        }
        List<SysRole> sysRoles = this.queryByRoleKeys(roleKeys);
        if (CollUtil.isEmpty(sysRoles)) {
            return false;
        }
        for (Long userId : userIdList) {
            this.insertList(sysRoles, userId);
        }
        return true;
    }

    @Override
    public List<Long> selectRoleIdsByPlatformRoleAndUserId(Long userId) {
        return this.baseMapper.selectRoleIdsByPlatformRoleAndUserId(userId);
    }
}
