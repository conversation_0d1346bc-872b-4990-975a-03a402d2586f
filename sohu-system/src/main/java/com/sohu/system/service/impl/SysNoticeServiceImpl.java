package com.sohu.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.system.api.bo.SysNoticeBo;
import com.sohu.system.api.vo.SohuNoticeRoleVo;
import com.sohu.system.api.vo.SysNoticeVo;
import com.sohu.system.domain.SysNotice;
import com.sohu.system.domain.SysNoticeUser;
import com.sohu.system.mapper.SysNoticeMapper;
import com.sohu.system.mapper.SysNoticeUserMapper;
import com.sohu.system.service.ISysNoticeService;
import com.sohu.system.util.SohuSystemAsyncUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公告 服务层实现
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SysNoticeServiceImpl implements ISysNoticeService {

    private final SysNoticeMapper baseMapper;
    private final SysNoticeUserMapper sysNoticeUserMapper;
    private final SohuSystemAsyncUtil sohuSystemAsyncUtil;

    @Override
    public TableDataInfo<SysNoticeVo> selectPageNoticeList(SysNoticeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysNotice> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(BaseEntity::getUpdateTime);
        Page<SysNoticeVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SysNoticeVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (SysNoticeVo record : records) {
                String noticeRole = record.getNoticeRole();
                if (StrUtil.isBlankIfStr(noticeRole)) {
                    continue;
                }
                List<String> split = StrUtil.split(noticeRole, StrPool.COMMA);
                List<String> roleNames = split.stream().map(RoleCodeEnum.subMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                record.setNoticeRole(String.join(",", roleNames));
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SysNoticeVo> indexList(SysNoticeBo notice, PageQuery pageQuery) {
        List<Integer> noticeTypeList = Arrays.asList(0, 1, 2);
        Page<SysNoticeVo> page = baseMapper.page(LoginHelper.getUserId(), notice.getNoticeRole(), noticeTypeList, PageQueryUtils.build(pageQuery));
        TableDataInfo<SysNoticeVo> result = TableDataInfoUtils.build(page);
        if (CollUtil.isEmpty(result.getData())) {
            return result;
        }
        List<SysNoticeVo> data = result.getData();
        for (SysNoticeVo noticeVo : data) {
            int noticeType = noticeVo.getNoticeType();
            if (!StrUtil.equalsAnyIgnoreCase(String.valueOf(noticeType), "3", "4", "5")) {
                continue;
            }
            String noticeDate = noticeVo.getNoticeDate(); // 格式如：20250101
            if (StrUtil.isBlank(noticeDate) || noticeDate.length() != 8) {
                continue;
            }

            try {
                LocalDate start = LocalDate.parse(noticeDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                LocalDate end = start;

                switch (noticeType) {
                    case 3: // 月度账单
                        end = start.withDayOfMonth(start.lengthOfMonth());
                        break;
                    case 4: // 季度账单
                        int quarter = (start.getMonthValue() - 1) / 3 + 1;
                        Month quarterEndMonth = Month.of(quarter * 3);
                        YearMonth quarterEndYm = YearMonth.of(start.getYear(), quarterEndMonth);
                        end = quarterEndYm.atEndOfMonth();
                        break;
                    case 5: // 年度账单
                        end = LocalDate.of(start.getYear(), 12, 31);
                        break;
                    default:
                        continue; // 其他类型跳过
                }

                noticeVo.setStartDate(start.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                noticeVo.setEndDate(end.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

            } catch (DateTimeParseException e) {
                // 忽略格式错误
                continue;
            }
        }
        return result;
    }

    @Override
    public TableDataInfo<SysNoticeVo> billList(SysNoticeBo notice, PageQuery pageQuery) {
        List<Integer> noticeTypeList = Arrays.asList(3, 4, 5);
        Page<SysNoticeVo> page = baseMapper.page(LoginHelper.getUserId(), notice.getNoticeRole(), noticeTypeList, PageQueryUtils.build(pageQuery));
        TableDataInfo<SysNoticeVo> result = TableDataInfoUtils.build(page);
        if (CollUtil.isEmpty(result.getData())) {
            return result;
        }
        List<SysNoticeVo> data = result.getData();
        for (SysNoticeVo noticeVo : data) {
            String noticeDate = noticeVo.getNoticeDate(); // 格式如：20250101
            if (StrUtil.isBlank(noticeDate) || noticeDate.length() != 8) {
                continue;
            }

            int noticeType = noticeVo.getNoticeType();
            try {
                LocalDate start = LocalDate.parse(noticeDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                LocalDate end = start;

                switch (noticeType) {
                    case 3: // 月度账单
                        end = start.withDayOfMonth(start.lengthOfMonth());
                        break;
                    case 4: // 季度账单
                        int quarter = (start.getMonthValue() - 1) / 3 + 1;
                        Month quarterEndMonth = Month.of(quarter * 3);
                        YearMonth quarterEndYm = YearMonth.of(start.getYear(), quarterEndMonth);
                        end = quarterEndYm.atEndOfMonth();
                        break;
                    case 5: // 年度账单
                        end = LocalDate.of(start.getYear(), 12, 31);
                        break;
                    default:
                        continue; // 其他类型跳过
                }

                noticeVo.setStartDate(start.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                noticeVo.setEndDate(end.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

            } catch (DateTimeParseException e) {
                // 忽略格式错误
                continue;
            }
        }
        return result;
    }

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNotice selectNoticeById(Long noticeId) {
        SysNotice sysNotice = baseMapper.selectById(noticeId);
        if (Objects.nonNull(sysNotice)) {
            LambdaUpdateWrapper<SysNoticeUser> luw = new LambdaUpdateWrapper<>();
            luw.eq(SysNoticeUser::getUserId, LoginHelper.getUserId());
            luw.eq(SysNoticeUser::getNoticeId, noticeId);
            luw.set(SysNoticeUser::getReadStatus, true);
            luw.set(SysNoticeUser::getReadTime, new Date());
            sysNoticeUserMapper.update(new SysNoticeUser(), luw);
        }
        return sysNotice;
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNoticeVo> selectNoticeList(SysNotice notice) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SysNotice>().like(StringUtils.isNotBlank(notice.getNoticeTitle()), SysNotice::getNoticeTitle, notice.getNoticeTitle()).eq(notice.getNoticeType() >= 0, SysNotice::getNoticeType, notice.getNoticeType()).like(StringUtils.isNotBlank(notice.getCreateBy()), SysNotice::getCreateBy, notice.getCreateBy()));
    }

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int insertNotice(SysNotice notice) {
        notice.setCreateTime(new Date());
        notice.setUpdateTime(new Date());
        int insert = baseMapper.insert(notice);
        sohuSystemAsyncUtil.saveNoticeUser(notice);
        return insert;
    }

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int updateNotice(SysNotice notice) {
        notice.setUpdateTime(new Date());
        return baseMapper.updateById(notice);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeById(Long noticeId) {
        return baseMapper.deleteById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeByIds(Long[] noticeIds) {
        return baseMapper.deleteBatchIds(Arrays.asList(noticeIds));
    }

    @Override
    public List<SohuNoticeRoleVo> noticeRoleList() {
        List<SohuNoticeRoleVo> result = new LinkedList<>();
        for (RoleCodeEnum roleCodeEnum : RoleCodeEnum.values()) {
            if (StrUtil.equalsAnyIgnoreCase(roleCodeEnum.getCode(), RoleCodeEnum.Agent.getCode(), RoleCodeEnum.MCN.getCode(), RoleCodeEnum.CityStationAgent.getCode())) {
                SohuNoticeRoleVo vo = new SohuNoticeRoleVo();
                vo.setCode(roleCodeEnum.getCode());
                vo.setName(roleCodeEnum.getName());
                vo.setSubName(roleCodeEnum.getSubName());
                if (StrUtil.equalsAnyIgnoreCase(roleCodeEnum.getCode(), RoleCodeEnum.CityStationAgent.getCode())) {
                    vo.setSubName("站长");
                }
                result.add(vo);
            }
        }
        return result;
    }

    @Override
    public Boolean sendNotice(Long receUserId, String noticeRole, Date noticeDate) {
        LambdaQueryWrapper<SysNotice> lqw = new LambdaQueryWrapper<>();
        lqw.like(SysNotice::getNoticeRole, noticeRole);
        lqw.eq(SysNotice::getNoticeSendType, 1);
        if (noticeDate != null) {
            lqw.le(BaseEntity::getCreateTime, noticeDate);
        }
        List<SysNotice> notices = this.baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(notices)) {
            log.warn("公告发送时机,首次进入,sendNotice,公告为空！");
            return Boolean.FALSE;
        }
        List<Long> noticeIds = notices.stream().map(SysNotice::getNoticeId).collect(Collectors.toList());
        LambdaQueryWrapper<SysNoticeUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysNoticeUser::getUserId, receUserId);
        queryWrapper.in(SysNoticeUser::getNoticeId, noticeIds);
        List<SysNoticeUser> sysNoticeUsers = sysNoticeUserMapper.selectList(queryWrapper);
        Map<Long, SysNoticeUser> sysNoticeUserMap = CollUtil.isEmpty(sysNoticeUsers) ? new HashMap<>() : sysNoticeUsers.stream().collect(Collectors.toMap(SysNoticeUser::getNoticeId, u -> u));
        for (SysNotice notice : notices) {
            SysNoticeUser noticeUser = sysNoticeUserMap.get(notice.getNoticeId());
            if (Objects.nonNull(noticeUser)) {
                continue;
            }
            SysNoticeUser item = new SysNoticeUser();
            item.setNoticeId(notice.getNoticeId());
            item.setUserId(receUserId);
            item.setReadStatus(false);
            item.setCreateTime(new Date());
            sysNoticeUserMapper.insert(item);
        }
        log.info("公告发送时机,首次进入,sendNotice,发送成功！");
        return Boolean.TRUE;
    }

    @Override
    public void sendUserIncomeStatisticsNotice() {
        LocalDate today = LocalDate.now();

        // 月账单：每月第3天发送上个月账单
        if (today.getDayOfMonth() == 3) {
            sendBillNotice(generateNoticeDate(today.minusMonths(1), 3), 3);
        }

        // 季度账单：每季度首月的第7天发送上季度账单
        if (today.getDayOfMonth() == 7 && Arrays.asList(1, 4, 7, 10).contains(today.getMonthValue())) {
            sendBillNotice(generateNoticeDate(today.minusMonths(3), 4), 4);
        }

        // 年账单：每年1月15日发送上一年账单
        if (today.getMonthValue() == 1 && today.getDayOfMonth() == 15) {
            sendBillNotice(generateNoticeDate(today.minusYears(1), 5), 5);
        }
    }

    private String generateNoticeDate(LocalDate date, int noticeType) {
        switch (noticeType) {
            case 3: // 月账单
                return date.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            case 4: // 季度账单
                int quarter = (date.getMonthValue() - 1) / 3 + 1;
                int startMonth = (quarter - 1) * 3 + 1;
                return LocalDate.of(date.getYear(), startMonth, 1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            case 5: // 年账单
                return LocalDate.of(date.getYear(), 1, 1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            default:
                throw new IllegalArgumentException("不支持的账单类型: " + noticeType);
        }
    }

    private void sendBillNotice(String noticeDateStr, int noticeType) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate noticeDate = LocalDate.parse(noticeDateStr, dateFormatter);
        String noticeTitle;

        switch (noticeType) {
            case 3: // 月账单
                noticeTitle = String.format("%d年%d月账单", noticeDate.getYear(), noticeDate.getMonthValue());
                break;

            case 4: // 季度账单
                int quarter = (noticeDate.getMonthValue() - 1) / 3 + 1;
                String quarterStr;
                switch (quarter) {
                    case 1:
                        quarterStr = "第一";
                        break;
                    case 2:
                        quarterStr = "第二";
                        break;
                    case 3:
                        quarterStr = "第三";
                        break;
                    case 4:
                        quarterStr = "第四";
                        break;
                    default:
                        quarterStr = "";
                        break;
                }
                noticeTitle = String.format("%d年%s季度账单", noticeDate.getYear(), quarterStr);
                break;

            case 5: // 年账单
                noticeTitle = String.format("%d年账单", noticeDate.getYear());
                break;

            default:
                noticeTitle = "账单通知";
        }

        SysNotice notice = new SysNotice();
        notice.setNoticeTitle(noticeTitle);
        notice.setNoticeType(noticeType);
        notice.setNoticeDate(noticeDateStr);
        notice.setNoticeContent("您的账单已生成，请及时查收。");
        notice.setNoticeRole(RoleCodeEnum.CityStationAgent.getCode());
        notice.setNoticeSendType(2); // 立即发送
        notice.setAsShow(true);
        notice.setAsTop(false);
        notice.setStatus("0");
        notice.setCreateTime(new Date());
        notice.setUpdateTime(new Date());

        baseMapper.insert(notice);
        log.info("定时任务发送账单公告: {} -> {}", noticeTitle, JSONUtil.toJsonStr(notice));
        sohuSystemAsyncUtil.saveNoticeUser(notice);
    }

    private LambdaQueryWrapper<SysNotice> buildQueryWrapper(SysNoticeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysNotice> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getNoticeTitle()), SysNotice::getNoticeTitle, bo.getNoticeTitle());
        lqw.eq(bo.getNoticeType() != null, SysNotice::getNoticeType, bo.getNoticeType());
        lqw.eq(StringUtils.isNotBlank(bo.getNoticeContent()), SysNotice::getNoticeContent, bo.getNoticeContent());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysNotice::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getNoticeRole()), SysNotice::getNoticeRole, bo.getNoticeRole());
        lqw.eq(bo.getNoticeSendType() != null, SysNotice::getNoticeSendType, bo.getNoticeSendType());
        return lqw;
    }
}
