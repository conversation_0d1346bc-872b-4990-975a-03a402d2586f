package com.sohu.system.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.system.api.bo.SysNoticeBo;
import com.sohu.system.api.vo.SohuNoticeRoleVo;
import com.sohu.system.api.vo.SysNoticeVo;
import com.sohu.system.domain.SysNotice;

import java.util.Date;
import java.util.List;

/**
 * 公告 服务层
 *
 * <AUTHOR>
 */
public interface ISysNoticeService {

    TableDataInfo<SysNoticeVo> selectPageNoticeList(SysNoticeBo notice, PageQuery pageQuery);

    TableDataInfo<SysNoticeVo> indexList(SysNoticeBo notice, PageQuery pageQuery);

    TableDataInfo<SysNoticeVo> billList(SysNoticeBo notice, PageQuery pageQuery);

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    SysNotice selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    List<SysNoticeVo> selectNoticeList(SysNotice notice);

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    int insertNotice(SysNotice notice);

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    int updateNotice(SysNotice notice);

    /**
     * 删除公告信息
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    int deleteNoticeById(Long noticeId);

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    int deleteNoticeByIds(Long[] noticeIds);

    /**
     * 获取系统通知角色列表
     *
     * @return {@link List}
     */
    List<SohuNoticeRoleVo> noticeRoleList();

    /**
     * 发送公告
     *
     * @param receUserId 接收人ID
     * @param noticeRole 接收人角色 {@link com.sohu.common.core.enums.RoleCodeEnum}
     * @param noticeDate 大于公告创建的时间
     * @return {@link Boolean}
     */
    Boolean sendNotice(Long receUserId, String noticeRole, Date noticeDate);

    /**
     * 发送系统账单公告
     */
    void sendUserIncomeStatisticsNotice();

}
