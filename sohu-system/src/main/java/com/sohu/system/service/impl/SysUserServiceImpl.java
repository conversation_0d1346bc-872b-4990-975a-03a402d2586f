package com.sohu.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheNames;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.UserConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.mybatis.helper.DataBaseHelper;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.SohuUserLabelRelationBo;
import com.sohu.middle.api.bo.SohuUserSiteRelationBo;
import com.sohu.middle.api.bo.airec.SohuAirecUserBo;
import com.sohu.middle.api.enums.AiRecTag;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecTagRelationService;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecUserService;
import com.sohu.middle.api.service.im.RemoteMiddleImTenantService;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.SohuUserSiteRelationVo;
import com.sohu.middle.api.vo.im.SohuImTenantVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.RemoteIndependentOrderService;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.system.api.bo.SysUserQueryBo;
import com.sohu.system.api.domain.SysDept;
import com.sohu.system.api.domain.SysRole;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.domain.UserVo;
import com.sohu.system.api.vo.*;
import com.sohu.system.domain.SysPlatformUser;
import com.sohu.system.domain.SysPost;
import com.sohu.system.domain.SysUserPost;
import com.sohu.system.domain.SysUserRole;
import com.sohu.system.mapper.*;
import com.sohu.system.service.ISysPlatformRoleService;
import com.sohu.system.service.ISysRoleService;
import com.sohu.system.service.ISysUserService;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.enums.AirecGenderEnum;
import com.sohu.third.aliyun.airec.enums.AirecUserIdTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserServiceImpl implements ISysUserService {

    private final SysUserMapper baseMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysPostMapper postMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysUserPostMapper userPostMapper;
    private final SysPlatformRoleMapper platformRoleMapper;
    private final SysPlatformUserMapper platformUserMapper;
    private final AsyncConfig asyncConfig;
    @DubboReference
    private RemoteMiddleUserSiteRelationService remoteMiddleUserSiteRelationService;
    @DubboReference
    private RemoteMiddleUserFollowService remoteMiddleUserFollowService;
    @DubboReference
    private RemoteMiddleAirecUserService remoteMiddleAirecUserService;
    @DubboReference
    private RemoteMiddleAirecTagRelationService remoteMiddleAirecTagRelationService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteMiddleDeleteService remoteMiddleDeleteService;
    @DubboReference
    private RemoteMiddleImTenantService imTenantService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;
    @DubboReference
    private RemoteIndependentOrderService remoteIndependentOrderService;
    @DubboReference
    private RemoteMiddleBillRecordService remoteMiddleBillRecordService;

    private final ISysPlatformRoleService platformRoleService;
    private final ISysRoleService sysRoleService;

    @Override
    public TableDataInfo<SysUser> selectPageUserList(SysUser user, PageQuery pageQuery) {
        Page<SysUser> page = baseMapper.selectPageUserList(PageQueryUtils.build(pageQuery), this.buildQueryWrapper(user));
        return TableDataInfoUtils.build(page);
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectUserList(SysUser user) {
        return baseMapper.selectUserList(this.buildQueryWrapper(user));
    }

    private Wrapper<SysUser> buildQueryWrapper(SysUser user) {
        Map<String, Object> params = user.getParams();
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        if (user.getCommonLabelIds() != null && !user.getCommonLabelIds().isEmpty()) {
            String labelIds = user.getCommonLabelIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            wrapper.and(w -> w.inSql("user_id",
                    "SELECT user_id FROM sohu_user_label_relation WHERE label_id IN (" + labelIds + ")"
            ));
        }
        //        if (user.getIndustryLabelIds() != null && !user.getIndustryLabelIds().isEmpty()) {
//            wrapper.and(w -> wrapper.inSql("user_id",
//                    "SELECT user_id FROM sohu_user_label_relation WHERE label_id IN (" +
//                            String.join(",", user.getIndustryLabelIds().stream().map(String::valueOf).collect(Collectors.toList())) + ")"
//            ));
//        }
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
                .eq(ObjectUtil.isNotNull(user.getUserId()), "u.user_id", user.getUserId())
                .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
                .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
                .like(StringUtils.isNotBlank(user.getPhoneNumber()), "u.phone_number", user.getPhoneNumber())
                .between(params.get("beginTime") != null && params.get("endTime") != null, "u.create_time", params.get("beginTime"), params.get("endTime"))
                .and(ObjectUtil.isNotNull(user.getDeptId()), w -> {
                    List<SysDept> deptList = deptMapper.selectList(new LambdaQueryWrapper<SysDept>().select(SysDept::getDeptId).apply(DataBaseHelper.findInSet(user.getDeptId(), "ancestors")));
                    List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                    ids.add(user.getDeptId());
                    w.in("u.dept_id", ids);
                });
        return wrapper;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUser> selectAllocatedList(SysUser user, PageQuery pageQuery) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL).eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId()).like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName()).eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus()).like(StringUtils.isNotBlank(user.getPhoneNumber()), "u.phone_number", user.getPhoneNumber());
        Page<SysUser> page = baseMapper.selectAllocatedList(PageQueryUtils.build(pageQuery), wrapper);
        return TableDataInfoUtils.build(page);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUser> selectUnallocatedList(SysUser user, PageQuery pageQuery) {
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL).and(w -> w.ne("r.role_id", user.getRoleId()).or().isNull("r.role_id")).notIn(CollUtil.isNotEmpty(userIds), "u.user_id", userIds).like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName()).like(StringUtils.isNotBlank(user.getPhoneNumber()), "u.phone_number", user.getPhoneNumber());
        Page<SysUser> page = baseMapper.selectUnallocatedList(PageQueryUtils.build(pageQuery), wrapper);
        return TableDataInfoUtils.build(page);
    }

    @Override
    public TableDataInfo<SysUser> selectAllocatedListOfPlatform(SysUserQueryBo bo, PageQuery pageQuery) {
        Page<SysUser> page = baseMapper.selectAllocatedListOfPlatform(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(page);
    }

    @Override
    public TableDataInfo<SysUser> selectUnallocatedListOfPlatform(SysUserQueryBo bo, PageQuery pageQuery) {
        Page<SysUser> page = baseMapper.selectUnallocatedListOfPlatform(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(page);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return baseMapper.selectUserByUserName(userName);
    }

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByPhonenumber(String phonenumber) {
        return baseMapper.selectUserByPhonenumber(phonenumber);
    }

    @Override
    public SysUser selectUserByEmail(String email) {
        return baseMapper.selectUserByEmail(email);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        SysUser user = baseMapper.selectUserById(userId);
        if (Objects.nonNull(user)) {
            List<SysPlatformRoleVo> platformRoleVos = platformRoleMapper.selectListByUserId(userId);
            user.setPlatformRoles(platformRoleVos);
        }
        return user;
    }

    @Override
    public SysUser selectAllUserById(Long userId) {
        return baseMapper.selectAllUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        //List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        List<SysRoleVo> list = roleMapper.selectListOfEnableByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysRoleVo::getRoleName);
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysPost::getPostName);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, user.getUserName()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhoneNumber, user.getPhoneNumber()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmail, user.getEmail()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (ObjectUtil.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!LoginHelper.isAdmin()) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = this.selectUserList(user);
            if (CollUtil.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        if (StrUtil.isEmpty(user.getAvatar())) {
            user.setAvatar(Constants.DEFAULT_AVATAR);
        }
        // 新增用户信息
        int rows = baseMapper.insert(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        insertUserPlatformRole(user.getUserId(), user.getRoleIds());
        //保存智能推荐信息
        this.saveAirecUser(user);
        return rows;
    }

    @Override
    public void saveUserAge(SysUser user) {
        //TODO 请重写
        long total = remoteAccountService.selectPassAccountCount();
        final int PAGE_SIZE = AliyunAirecConstant.BATCH_SIZE;
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int i = 1; i <= totalPages; i++) {
            //Page<SohuAccountVo> page = new Page<>(i, PAGE_SIZE);
            TableDataInfo<SohuAccountVo> pageResult = remoteAccountService.selectPassAccountList(new PageQuery(i, PAGE_SIZE));
            List<SohuAccountVo> accountList = pageResult.getData();
            //根据身份证号计算出年龄
            if (CollUtil.isNotEmpty(accountList)) {
                for (SohuAccountVo sohuAccountVo : accountList) {
                    if (sohuAccountVo.getIdentityType().equals("1")) {
                        String identityNo = sohuAccountVo.getIdentityNo();
                        if (StrUtil.isNotBlank(identityNo)) {
                            int age = getAgeByIdentityNo(identityNo);
                            //更新到智能推荐表和用户表
                            SysUser sysUser = baseMapper.selectUserById(sohuAccountVo.getUserId());
                            sysUser.setAge(age);
                            baseMapper.updateById(sysUser);
                            this.saveAirecUser(user);
                        }
                    }
                }
            }
        }
    }

    private int getAgeByIdentityNo(String identityNo) {
        String birthday = identityNo.substring(6, 14);
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            Date birthDate = sdf.parse(birthday);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return calendar.get(Calendar.YEAR) - Integer.parseInt(birthday.substring(0, 4));
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public Long registerUser(SysUser user) {
        user.setCreateBy(user.getUserName());
        user.setUpdateBy(user.getUserName());
        user.setUid(user.getUid());
        if (StrUtil.isBlankIfStr(user.getAvatar())) {
            user.setAvatar(Constants.DEFAULT_AVATAR);
        }
        int insert = baseMapper.insert(user);
        //用户初始站点
        SohuUserSiteRelationBo relation = new SohuUserSiteRelationBo();
        relation.setSiteId(user.getSiteId() != null ? user.getSiteId() : 11L);
        relation.setUserId(user.getUserId());
        remoteMiddleUserSiteRelationService.insertByBo(relation);
        //保存智能推荐信息
        this.saveAirecUser(user);
        return insert > 0 ? user.getUserId() : 0L;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        // 新增用户与岗位管理
        insertUserPost(user);
        if (user.getPlatformRoleIds() != null && user.getPlatformRoleIds().length > 0) {
            platformUserMapper.delete(new LambdaQueryWrapper<SysPlatformUser>().eq(SysPlatformUser::getUserId, userId));
            insertUserPlatformRole(userId, user.getPlatformRoleIds());
        }
        // 新增用户标签关联
        insertUserLabelRelation(user);
        CacheMgr.evict(LoginUser.REGION, String.valueOf(userId));
        return baseMapper.updateById(user);
    }

    /**
     * 批量新增用户标签关联
     *
     * @param user
     */
    public void insertUserLabelRelation(SysUser user) {
        // 删除用户与标签关联
        List<Long> commonLabelIds = user.getCommonLabelIds();
        List<Long> industryLabelIds = user.getIndustryLabelIds();
        List<Long> contentTagIds = user.getContentTagIds();
        if (CollUtil.isEmpty(commonLabelIds) && CollUtil.isEmpty(industryLabelIds) && CollUtil.isEmpty(contentTagIds)) {
            // 删除用户与指定类型标签的关联
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(user.getUserId(), null);
        }
        if (CollUtil.isEmpty(commonLabelIds)) {
            // 删除用户与指定类型标签的关联
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(user.getUserId(), LabelEnum.COMMON.getCode());
        }
        if (CollUtil.isEmpty(industryLabelIds)) {
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(user.getUserId(), LabelEnum.INDUSTRY.getCode());
        }
        if (CollUtil.isEmpty(commonLabelIds)) {
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(user.getUserId(), LabelEnum.CONTENT.getCode());
        }
        List<SohuUserLabelRelationBo> userLabelList = new ArrayList<>();
        // 处理通用标签
        processLabels(user.getUserId(), commonLabelIds, LabelEnum.COMMON, userLabelList);
        // 处理行业标签
        processLabels(user.getUserId(), industryLabelIds, LabelEnum.INDUSTRY, userLabelList);
        // 处理内容标签
        processLabels(user.getUserId(), contentTagIds, LabelEnum.CONTENT, userLabelList);
        // 批量插入新的标签关联
        if (CollUtil.isNotEmpty(userLabelList)) {
            remoteMiddleCommonLabelService.insertBatch(userLabelList);
        }
    }

    /**
     * 处理标签并添加到用户标签列表中
     *
     * @param userId        用户ID
     * @param labelIds      标签ID集合
     * @param labelType     标签类型
     * @param userLabelList 用户标签关联列表
     */
    private void processLabels(Long userId, List<Long> labelIds, LabelEnum labelType, List<SohuUserLabelRelationBo> userLabelList) {
        if (CollUtil.isNotEmpty(labelIds)) {
            // 删除用户与指定类型标签的关联
            remoteMiddleCommonLabelService.deleteUserLabelByUserId(userId, labelType.getCode());

            // 创建新的标签关联并添加到列表中
            labelIds.stream()
                    .map(labelId -> createUserLabelRelationBo(userId, labelId, labelType))
                    .forEach(userLabelList::add);
        }
    }

    /**
     * 创建用户标签关联对象
     *
     * @param userId    用户ID
     * @param labelId   标签ID
     * @param labelType 标签类型
     * @return SohuUserLabelRelationBo 对象
     */
    private SohuUserLabelRelationBo createUserLabelRelationBo(Long userId, Long labelId, LabelEnum labelType) {
        SohuUserLabelRelationBo bo = new SohuUserLabelRelationBo();
        bo.setUserId(userId);
        bo.setLabelId(labelId);
        bo.setLabelType(labelType.getCode());
        return bo;
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, String roleIds) {
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        insertUserRole(userId, Stream.of(roleIds.split(",")).map(Long::parseLong).toArray(Long[]::new));
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(user.getUserId()));
        return baseMapper.updateById(user);
    }

    @Override
    public void updateUserStatus(Long userId, String status) {
        SysUser user = this.baseMapper.selectById(userId);
        if (Objects.nonNull(user)) {
            if (Objects.equals(user.getStatus(), status)) {
                return;
            }
            SysUser entity = new SysUser();
            entity.setUserId(userId);
            entity.setStatus(status);
            baseMapper.updateById(entity);
            CacheMgr.evict(LoginUser.REGION, String.valueOf(userId));
        }
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(user.getUserId()));
        return baseMapper.updateById(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return baseMapper.update(new SysUser(), new LambdaUpdateWrapper<SysUser>().set(SysUser::getAvatar, avatar).eq(SysUser::getUserName, userName)) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(user.getUserId()));
        return baseMapper.updateById(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return baseMapper.update(new SysUser(), new LambdaUpdateWrapper<SysUser>().set(SysUser::getPassword, password).eq(SysUser::getUserName, userName));
    }

    /**
     * 手机号重置用户支付密码
     *
     * @param phoneNumber 手机号
     * @param payPassword 支付密码
     * @return
     */
    @Override
    public int resetUserPayPasswordByPhone(String phoneNumber, String payPassword) {
        return baseMapper.update(new SysUser(), new LambdaUpdateWrapper<SysUser>().set(SysUser::getPayPassword, payPassword).eq(SysUser::getPhoneNumber, phoneNumber));
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (ArrayUtil.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.insertBatch(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.insertBatch(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    private void insertUserPlatformRole(Long userId, Long[] roleIds) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysPlatformUser> list = new ArrayList<>(roleIds.length);
            for (Long roleId : roleIds) {
                SysPlatformUser ur = new SysPlatformUser();
                ur.setUserId(userId);
                ur.setPlatformRoleId(roleId);
                list.add(ur);
            }
            platformUserMapper.insertBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        return baseMapper.deleteById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
            remoteMiddleDeleteService.deleteByUserId(userId);
        }
        List<Long> ids = Arrays.asList(userIds);
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, ids));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getUserId, ids));
        platformUserMapper.delete(new LambdaQueryWrapper<SysPlatformUser>().in(SysPlatformUser::getUserId, ids));
        return baseMapper.deleteBatchIds(ids);
    }

    @Cacheable(cacheNames = CacheNames.SYS_USER_NAME, key = "#userId")
    @Override
    public String selectUserNameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>().select(SysUser::getUserName).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getUserName();
    }

    @Override
    public List<SysUser> selectUserList(Collection<Long> ids) {
        return this.baseMapper.selectBatchIds(ids);
    }

    @Override
    public Page<UserVo> page(Collection<Long> notInUserIds, String keyword, Page<UserVo> page) {
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysUser::getDelFlag, 0);
        if (CollUtil.isNotEmpty(notInUserIds)) {
            notInUserIds = notInUserIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(notInUserIds)) {
                lqw.notIn(SysUser::getUserId, notInUserIds);
            }
        }
        if (StrUtil.isNotBlank(keyword)) {
            lqw.and(i -> i.like(SysUser::getNickName, keyword).or().like(SysUser::getUserName, keyword)).or().like(SysUser::getPhoneNumber, keyword);
        }
        IPage<SysUser> result = this.baseMapper.selectVoPage(new Page<>(page.getCurrent(), page.getSize()), lqw);
        List<UserVo> list = new ArrayList<>();
        page.setRecords(list);
        if (result != null && CollUtil.isNotEmpty(result.getRecords())) {
            result.getRecords().forEach(sysUser -> {
                UserVo userVo = new UserVo();
                userVo.setNickName(sysUser.getNickName());
                userVo.setAvatar(sysUser.getAvatar());
                userVo.setId(sysUser.getUserId());
                // todo 待优化查询
                Long count = remoteMiddleUserFollowService.fansCount(userVo.getId());
                userVo.setFocusUserFans(count != null ? count : 0L);
                list.add(userVo);
            });
            page.setRecords(list);
            page.setTotal(result.getTotal());
        }
        return page;
    }

    @Override
    public List<Long> getUserBySiteId(List<Long> cityIds) {
        if (CollUtil.isEmpty(cityIds)) {
            return null;
        }
        SohuUserSiteRelationBo relationBo = new SohuUserSiteRelationBo();
        relationBo.setSiteIds(cityIds);
        List<SohuUserSiteRelationVo> userSiteRelationList = remoteMiddleUserSiteRelationService.queryList(relationBo);
        if (CollUtil.isEmpty(userSiteRelationList)) {
            return null;
        }
        return userSiteRelationList.stream()
                .map(SohuUserSiteRelationVo::getUserId)
                .collect(Collectors.toList());
    }

    /**
     * 保存智能推荐信息
     *
     * @param user
     */
    private void saveAirecUser(SysUser user) {
        SohuAirecUserBo model = buildAirecUserModel(user);
        model.setTags(remoteMiddleAirecTagRelationService.saveTagStr(user.getUserId(), AiRecTag.BizTypeEnum.USER.getCode(), model.getTags()));
        remoteMiddleAirecUserService.saveAirecUser(model);
    }

    /**
     * 创建智能推荐用户数据
     *
     * @param user
     * @return
     */
    private SohuAirecUserBo buildAirecUserModel(SysUser user) {
        SohuAirecUserBo model = new SohuAirecUserBo();
        model.setUserId(user.getUserId().toString());
        model.setUserIdType(AirecUserIdTypeEnum.PHONE.getCode());
        model.setThirdUserName(user.getUserName());
        model.setThirdUserType(user.getUserType());
        if (StringUtils.isNotEmpty(user.getPhoneNumber())) {
            model.setPhoneMd5(MD5Utils.stringToMD5(user.getPhoneNumber()));
        }
        model.setGender(transientGender(user.getSex()));
        return model;
    }

    /**
     * 转换性别
     *
     * @param sex
     * @return
     */
    public String transientGender(Integer sex) {
        String gender = AirecGenderEnum.AIREC_UNKNOWN.getCode();
        if (ObjectUtil.isNotNull(sex)) {
            if (Objects.equals(SexEnum.MALE.getSex(), sex)) {
                gender = AirecGenderEnum.AIREC_MALE.getCode();
            } else if (Objects.equals(SexEnum.FEMALE.getSex(), sex)) {
                gender = AirecGenderEnum.AIREC_FEMALE.getCode();
            }
        }
        return gender;
    }

    @Override
    public Boolean initAirecUsers() {
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysUser::getStatus, 0);
        lqw.eq(SysUser::getDelFlag, 0);
        //设置分页参数
        // 查询总数
        long total = baseMapper.selectCount(lqw);
        final int PAGE_SIZE = AliyunAirecConstant.BATCH_SIZE;
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int i = 1; i <= totalPages; i++) {
            // 分页查询
            Page<SysUser> page = new Page<>(i, PAGE_SIZE);
            IPage<SysUser> pageResult = baseMapper.selectPage(page, lqw);
            List<SysUser> list = pageResult.getRecords();
            // 处理查询结果
            if (CollUtil.isNotEmpty(list)) {
                //物料信息记录
                List<SohuAirecUserBo> modelList = new ArrayList<>();
                for (SysUser entity : list) {
                    SohuAirecUserBo model = buildAirecUserModel(entity);
                    model.setTags(remoteMiddleAirecTagRelationService.saveTagStr(entity.getUserId(), AiRecTag.BizTypeEnum.USER.getCode(), model.getTags()));
                    modelList.add(model);
                }
                //保存物料信息
                remoteMiddleAirecUserService.initAirecUsers(modelList);
            }
        }
        return true;
    }

    /**
     * 修改支付密码发送消息通知
     *
     * @param userId
     */
    @Override
    public void sendMsgOfUpdatePayPwd(Long userId) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.updatePayPwd);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.updatePayPwd.name());
        content.setDetailId(userId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(userId);
        detail.setDesc(SystemNoticeEnum.updatePayPwdDesc);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.updatePayPwd, contentJson, SystemNoticeEnum.Type.account);
    }

    /**
     * 修改登录密码发送消息通知
     *
     * @param userId
     */
    @Override
    public void sendMsgOfUpdatePwd(Long userId) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.updatePwd);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.updatePwd.name());
        content.setDetailId(userId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(userId);
        detail.setDesc(SystemNoticeEnum.updatePwdDesc);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.updatePwd, contentJson, SystemNoticeEnum.Type.account);
    }

    /**
     * 修改手机号发送消息通知
     *
     * @param userId
     * @param phone
     */
    @Override
    public void sendMsgOfUpdatePhone(Long userId, String phone) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.updatePhone);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.updatePhone.name());
        content.setDetailId(userId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(userId);
        phone = StrUtil.desensitized(phone, DesensitizedUtil.DesensitizedType.MOBILE_PHONE);
        detail.setDesc(String.format(SystemNoticeEnum.updatePhoneDesc, phone));
        detail.setKeyWord(new String[]{phone});
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.updatePhone, contentJson, SystemNoticeEnum.Type.account);
    }

    /**
     * 修改邮箱发送消息通知
     *
     * @param userId
     * @param email
     */
    @Override
    public void sendMsgOfUpdateEmail(Long userId, String email) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(SystemNoticeEnum.updateEmail);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.updateEmail.name());
        content.setDetailId(userId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(userId);
        email = DesensitizedUtil.desensitized(email, DesensitizedUtil.DesensitizedType.EMAIL);
        detail.setDesc(String.format(SystemNoticeEnum.updateEmailDesc, email));
        detail.setKeyWord(new String[]{email});
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.updateEmail, contentJson, SystemNoticeEnum.Type.account);
    }

    @Override
    public SysUserInfoVo getInfoByUserId(Long userId) {
        if (userId == null) {
            return new SysUserInfoVo();
        }
        SysUser sysUser = this.selectUserById(userId);
        if (Objects.isNull(sysUser)) {
            return new SysUserInfoVo();
        }
        SysUserInfoVo sysUserInfoVo = BeanCopyUtils.copy(sysUser, SysUserInfoVo.class);
        sysUserInfoVo.setDeptName(sysUser.getDept().getDeptName());
        // 暂时未启用
        sysUserInfoVo.setPostName(null);

        return sysUserInfoVo;
    }

    @Override
    public Boolean updatePhoneNumber(String newPhoneNumber) {
        SohuAccountVo sohuAccountVo = remoteAccountService.queryByUserId(LoginHelper.getUserId());
        if (Objects.nonNull(sohuAccountVo)) {
            throw new RuntimeException("当前手机号已经实名认证，不能修改绑定手机号");
        }
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysUser::getPhoneNumber, newPhoneNumber);
        if (this.baseMapper.exists(lqw)) {
            throw new RuntimeException("当前手机号已注册，可将当前手机号注销后再进行绑定");
        }
        SysUser updateEntity = new SysUser();
        updateEntity.setUserId(LoginHelper.getUserId());
        updateEntity.setPhoneNumber(newPhoneNumber);
        if (this.baseMapper.updateById(updateEntity) > 0) {
            CacheMgr.evict(LoginUser.REGION, String.valueOf(LoginHelper.getUserId()));
            CompletableFuture.runAsync(() -> this.sendMsgOfUpdatePhone(LoginHelper.getUserId(), newPhoneNumber), asyncConfig.getAsyncExecutor());
        }
        return true;
    }

    @Override
    public Long getUserNumByCreateTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.between(BaseEntity::getCreateTime, startTime, endTime);
        return this.baseMapper.getUserNum(lqw);
    }

    @Override
    public Long getUserTotalNum() {
        return this.baseMapper.selectCount();
    }

    @Override
    public Boolean registerImUser(String phone, String serverCode) {
        // 通过手机号查询用户是否存在
        SysUser sysUser = this.selectUserByPhonenumber(phone);
        if (Objects.nonNull(sysUser)) {
            return true;
        }
        //查询服务器id
        SohuImTenantVo sohuImTenantVo = imTenantService.queryByServerCode(serverCode);
        //新增用户
        sysUser = new SysUser();
        String name = RandomUtils.genUserName();
        sysUser.setUserName(name);
        sysUser.setNickName(name);
        sysUser.setPhoneNumber(phone);
        sysUser.setUserType(UserType.IM_USER.getUserType());
        sysUser.setTenantId(sohuImTenantVo.getId());
        this.baseMapper.insert(sysUser);
        // 添加用户角色
        this.giveRole(null, sysUser.getUserId());
        return true;
    }

    @Override
    public Map<Long, Integer> getTenantRegisterNum(List<Long> tenantIds) {
        List<TenantRegisterVo> list = this.baseMapper.getTenantRegisterNum(tenantIds);
        return list.stream().collect(Collectors.toMap(TenantRegisterVo::getTenantId, TenantRegisterVo::getRegisterNum));
    }

    @Override
    public List<Long> queryUserIdByBo(SysUserQueryBo bo) {
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper();
        lqw.like(StrUtil.isNotBlank(bo.getUserName()), SysUser::getUserName, bo.getUserName());
        lqw.like(StrUtil.isNotBlank(bo.getNickName()), SysUser::getNickName, bo.getNickName());
        lqw.like(!CalUtils.isNullOrZero(bo.getUserId()), SysUser::getUserId, bo.getUserId());
        List<SysUser> list = this.baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(SysUser::getUserId).collect(Collectors.toList());
    }

    @Override
    public void updateInviteUserId(Long userId, Long inviteUserId) {
        SysUser sysUser = this.baseMapper.selectById(userId);
        sysUser.setInviteUserId(inviteUserId);
        baseMapper.updateById(sysUser);
    }

    @Override
    public BigDecimal getUserIncome(Long userId, Integer siteType, Long siteId, String userRole, Integer independentStatus, Date startTime, Date endTime) {
        List<String> personRoles = Arrays.asList(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
        List<String> siteRoles = Arrays.asList(SohuIndependentObject.country.getKey(), SohuIndependentObject.city.getKey(), SohuIndependentObject.entrance.getKey(), SohuIndependentObject.industrysite.getKey(), SohuIndependentObject.invitecity.getKey());
        List<String> agencyRoles = Arrays.asList(SohuIndependentObject.agency.getKey());
        List<String> selectRoles = personRoles;
        if (UserRoleEnum.CITYSTATION.getType().equals(userRole)) {
            selectRoles = siteRoles;
        } else if (UserRoleEnum.AGENCY.getType().equals(userRole)) {
            selectRoles = agencyRoles;
        }
        BigDecimal distributeAmount = remoteIndependentOrderService.selectIndependentingByUserId(userId, siteType, siteId, selectRoles, null, independentStatus, startTime, endTime);
        return distributeAmount;
    }

    @Override
    public BigDecimal getUserWithdrawal(Long userId, Integer siteType, Long siteId, String userRole, Date startTime, Date endTime) {
        return remoteMiddleBillRecordService.sumWithdrawalByUserType(userId, siteType, siteId, userRole, startTime, endTime);
    }

    @Override
    public BigDecimal getUserInviteIncome(Long userId, Integer siteType, Long siteId, String independentObject, Date startTime, Date endTime) {
        BigDecimal inviteAmount = remoteIndependentOrderService.selectIndependentingByUserId(userId, siteType, siteId, null, independentObject, IndependentStatusEnum.DISTRIBUTED.getCode(), startTime, endTime);
        return inviteAmount;
    }

    @Override
    public SohuUserOrderInfoVo getUserOrderInfo(Long userId, Integer siteType, Long siteId, List<String> independentObject, String tradeType, Date startTime, Date endTime) {
        return new SohuUserOrderInfoVo();
    }

    /**
     * 赋予角色
     *
     * @param roleKey
     * @param userId
     */
    private void giveRole(PlatformRoleCodeEnum roleKey, Long userId) {
        // 赋予用户角色列表
        List<String> giveRoleList = new ArrayList<>();
        // 赋予用户平台角色列表
        List<String> givePlatformRoleList = new ArrayList<>();
        // 默认赋予 创作者功能 普通功能
        giveRoleList.add(RoleCodeEnum.Article.getCode());
        giveRoleList.add(RoleCodeEnum.COMMON.getCode());
        // 默认赋予 创作者服务平台角色
        givePlatformRoleList.add(PlatformRoleCodeEnum.Article.getCode());

        if (Objects.nonNull(roleKey)) {
            PlatformRoleCodeEnum currentRole = platformRoleService.getCurrentExclusiveRole(userId);
            if (currentRole != null && !StrUtil.equalsAnyIgnoreCase(roleKey.getCode(), currentRole.getCode())) {
                throw new RuntimeException("您已认证其他角色");
            }
        }
        log.info("给用户授予功能角色:{}", JSONObject.toJSON(giveRoleList));
        savePlatformRoles(userId, givePlatformRoleList);
        saveUserRoles(userId, giveRoleList);
    }

    /**
     * 保存平台角色信息
     *
     * @param userId
     * @param giveRoleList
     */
    private void savePlatformRoles(Long userId, List<String> giveRoleList) {
        List<SysPlatformRoleVo> roleVos = platformRoleService.queryOfEnableByRoleKeys(giveRoleList);
        if (CollUtil.isNotEmpty(roleVos)) {
            // 保存用户角色关联信息
            platformRoleService.insertList(roleVos, userId);
        }
    }

    /**
     * 保存用户角色信息
     *
     * @param userId       用户ID
     * @param giveRoleList 赋予角色列表
     */
    private void saveUserRoles(Long userId, List<String> giveRoleList) {
        // 查询用户拥有的角色列表
        List<SysRole> roleDOS = sysRoleService.queryByRoleKeys(giveRoleList);
        if (CollUtil.isNotEmpty(roleDOS)) {
            // 保存用户角色关联信息
            sysRoleService.insertList(roleDOS, userId);
        }
    }
}
