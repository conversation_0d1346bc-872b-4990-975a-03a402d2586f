package com.sohu.system.dubbo;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.CommonEnums;
import com.sohu.common.core.enums.PlatformEnum;
import com.sohu.common.core.enums.UserStatus;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.exception.user.UserException;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.utils.ValidatorUtil;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.config.RoleDTO;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuFriendsBo;
import com.sohu.middle.api.bo.SohuInviteBo;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.mcn.RemoteMiddleMcnUserService;
import com.sohu.middle.api.vo.SohuInviteVo;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.SohuUserSiteRelationVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.bo.SohuUserAppConfigBo;
import com.sohu.system.api.bo.SohuUserAppConfigSortBo;
import com.sohu.system.api.bo.SysUserQueryBo;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.domain.SysUserInfo;
import com.sohu.system.api.domain.UserVo;
import com.sohu.system.api.model.XcxLoginUser;
import com.sohu.system.api.vo.SohuUserAppConfigVo;
import com.sohu.system.api.vo.SohuUserOrderInfoVo;
import com.sohu.system.api.vo.SysPlatformRoleVo;
import com.sohu.system.domain.SysPlatformRole;
import com.sohu.system.domain.SysPlatformUser;
import com.sohu.system.mapper.SysPlatformRoleMapper;
import com.sohu.system.mapper.SysPlatformUserMapper;
import com.sohu.system.mapper.SysUserInfoMapper;
import com.sohu.system.mapper.SysUserMapper;
import com.sohu.system.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 用户服务
 * 建议迁移重构，已准备的位置com.sohu.system.service.ISysUserBizService
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteUserServiceImpl implements RemoteUserService {

    private final ISysUserService userService;
    private final ISysPermissionService permissionService;
    private final ISysConfigService configService;
    private final SysUserMapper userMapper;
    private final SysUserInfoMapper sysUserInfoMapper;

    private final SysPlatformUserMapper platformUserMapper;
    private final SysPlatformRoleMapper platformRoleMapper;

    private final ISysUserBizService sysUserBizService;

    @DubboReference
    private RemoteMiddleUserFollowService remoteMiddleUserFollowService;
    @DubboReference
    private RemoteMiddleMcnUserService remoteMcnUserService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemoteMiddleUserSiteRelationService remoteMiddleUserSiteRelationService;
    //private final ISohuAirecUserService sohuAirecUserService;
    @DubboReference
    private RemoteMiddleInviteService remoteMiddleInviteService;
    @DubboReference
    private RemoteMiddleArticleService remoteMiddleArticleService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;

    @DubboReference
    private RemoteAccountService remoteAccountService;

    private final ISohuUserAppConfigService sohuUserAppConfigService;

    private final AsyncConfig asyncConfig;

    /**
     * 邀请链接过期时间,有效期24小时,单位ms
     */
    private static final int DAYS_THRESHOLD = 7;

    /**
     * 账号状态 0--已启用 2--禁用
     */
    private static final int ACCOUNT_STATUS = 0;

    @Override
    public LoginUser getUserInfo(String username) throws UserException {
        if (StrUtil.isBlankIfStr(username)) {
            throw new UserException("user.username.not.blank");
        }
        if (ValidatorUtil.isMobile(username)) {
            return getUserInfoByPhonenumber(username);
        }
        if (ValidatorUtil.isEmail(username)) {
            return getUserInfoByEmail(username);
        }
        SysUser sysUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getUserName, SysUser::getStatus)
                .eq(SysUser::getUserName, username)
                .eq(SysUser::getDelFlag, ACCOUNT_STATUS)
                .last("limit 1")
        );
        if (ObjectUtil.isNull(sysUser)) {
            throw new UserException("user.not.exists", username);
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new UserException("user.blocked", username);
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        return buildLoginUser(userMapper.selectUserByUserName(username));
    }

    @Override
    public LoginUser getUserInfoByPhonenumber(String phonenumber) throws UserException {
        SysUser sysUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getPhoneNumber, SysUser::getStatus)
                .eq(SysUser::getPhoneNumber, phonenumber)
                .last("limit 1")
        );
        if (ObjectUtil.isNull(sysUser)) {
            throw new RuntimeException("您的账号未注册，请使用验证码登录");
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new UserException("user.blocked", phonenumber);
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        return buildLoginUser(userMapper.selectUserByPhonenumber(phonenumber));
    }

    @Override
    public LoginUser getUserInfoByPhone(String phonenumber) {
        SysUser sysUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getPhoneNumber, SysUser::getStatus)
                .eq(SysUser::getPhoneNumber, phonenumber)
                .eq(SysUser::getDelFlag, ACCOUNT_STATUS)
                .last("limit 1")
        );
        if (ObjectUtil.isNull(sysUser)) {
            return null;
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new UserException("user.blocked", phonenumber);
        }
        return buildLoginUser(userMapper.selectUserByPhonenumber(phonenumber));
    }

    @Override
    public LoginUser getUserInfoByEmail(String email) throws UserException {
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getPhoneNumber, SysUser::getStatus, SysUser::getEmail)
                .eq(SysUser::getEmail, email)
                .last("limit 1")
        );
        if (ObjectUtil.isNull(user)) {
            throw new RuntimeException("您的账号未注册，请使用验证码登录");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            throw new UserException("user.blocked", email);
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        return buildLoginUser(userMapper.selectUserByEmail(email));
    }

    @Override
    public LoginUser getUserInfoByMail(String email) throws UserException {
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getPhoneNumber, SysUser::getStatus, SysUser::getEmail)
                .eq(SysUser::getEmail, email)
                .eq(SysUser::getDelFlag, ACCOUNT_STATUS)
                .last("limit 1")
        );
        if (ObjectUtil.isNull(user)) {
            return null;
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            throw new UserException("user.blocked", email);
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        return buildLoginUser(userMapper.selectUserByEmail(email));
    }

    @Override
    public XcxLoginUser getUserInfoByOpenid(String openid) throws UserException {
        // todo 自行实现 userService.selectUserByOpenid(openid);
        SysUser sysUser = new SysUser();
        if (ObjectUtil.isNull(sysUser)) {
            // todo 用户不存在 业务逻辑自行实现
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            // todo 用户已被停用 业务逻辑自行实现
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        XcxLoginUser loginUser = new XcxLoginUser();
        loginUser.setUserId(sysUser.getUserId());
        loginUser.setUsername(sysUser.getUserName());
        loginUser.setUserType(sysUser.getUserType());
        loginUser.setOpenid(openid);
        return loginUser;
    }

    @Override
    public Long registerUserInfo(SysUser sysUser) {
        String username = sysUser.getUserName();
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
            throw new ServiceException("当前系统没有开启注册功能");
        }
        /*if (!userService.checkUserNameUnique(sysUser)) {
            throw new UserException("user.register.save.error", username);
        }*/
        Long userId = userService.registerUser(sysUser);
//        if (userId > 0L) {
//            //sohuAirecUserService.insertBySysUser(sysUser);
//        }
        return userId;
    }

    @Override
    public String selectUserNameById(Long userId) {
        return userService.selectUserNameById(userId);
    }

    @Override
    public LoginUser selectById(Long userId) {
        SysUser sysUser = userService.selectUserById(userId);
        return this.buildLoginUser(sysUser);
    }

    @Override
    public LoginUser selectAllUserById(Long userId) {
        SysUser sysUser = userService.selectAllUserById(userId);
        return this.buildLoginUser(sysUser);
    }

    @Override
    public LoginUser selectByUserName(String userName) {
        SysUser sysUser = userService.selectUserByUserName(userName);
        return this.buildLoginUser(sysUser);
    }

    @Override
    public LoginUser selectByPhonenumber(String phonenumber) {
        SysUser sysUser = userService.selectUserByPhonenumber(phonenumber);
        return this.buildLoginUser(sysUser);
    }

    @Override
    public Map<Long, LoginUser> queryMap(Collection<Long> userIds) {
        List<SysUser> sysUsers = userService.selectUserList(userIds);
        if (CollUtil.isEmpty(sysUsers)) {
            return new HashMap<>();
        }
        return sysUsers.stream().collect(Collectors.toMap(SysUser::getUserId, u -> this.buildLoginUser(u)));
    }

    @Override
    public void update(SysUser sysUser) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(sysUser.getUserId()));
        userMapper.updateById(sysUser);
        remoteMiddleUserFollowService.updateFocusUser(sysUser.getUserId(), sysUser.getUserName(), sysUser.getAvatar());
//        LoginUser loginUser = this.buildLoginUser(sysUser);
//        //LoginHelper.updateUser(loginUser);
//        LoginHelper.updateUserCache(loginUser);
        this.flushLoginCacheByUserId(sysUser.getUserId());
    }

    @Override
    public void deleteById(Long userId) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(userId));
        userMapper.deleteById(userId);
    }

    @Override
    public void updateInviteCode(Long userId, String inviteCode) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(userId));
        userMapper.updateInviteCode(userId, inviteCode);
    }

    @Override
    public TableDataInfo<UserVo> page(Collection<Long> notInUserIds, String keyword, Boolean queryFriend, Integer pageNum, Integer pageSize) {
        Page<UserVo> page = new Page<>(pageNum, pageSize);
        if (true) {
            return TableDataInfoUtils.build(userService.page(notInUserIds, keyword, page));
        }
        if (queryFriend == null || !queryFriend) {
            return TableDataInfoUtils.build(userMapper.page(notInUserIds, keyword, page));
        }
        return TableDataInfoUtils.build(userMapper.pageFriend(LoginHelper.getUserId(), notInUserIds, keyword, page));
    }


    /**
     * 构建登录用户
     */
    @Override
    public LoginUser buildLoginUser(SysUser user) {
        if (Objects.isNull(user) || (user.getUserId() == null || user.getUserId() <= 0L)) {
            return null;
        }
        LoginUser loginUser = new LoginUser();
        loginUser.setEmail(user.getEmail());
        loginUser.setPhoneNumber(user.getPhoneNumber());
        loginUser.setSex(user.getSex());
        loginUser.setBirthday(user.getBirthday());
        loginUser.setRemark(user.getRemark());
        loginUser.setInviteCode(user.getInviteCode());
        loginUser.setUserId(user.getUserId());
        loginUser.setDeptId(user.getDeptId());
        loginUser.setUsername(user.getUserName());
        loginUser.setNickname(user.getNickName());
        loginUser.setPassword(user.getPassword());
        loginUser.setPayPassword(user.getPayPassword());
        loginUser.setUserType(user.getUserType());
        loginUser.setAvatar(user.getAvatar());
        loginUser.setMenuPermission(permissionService.getMenuPermission(user));
        loginUser.setRolePermission(permissionService.getRolePermission(user));
        loginUser.setDeptName(ObjectUtil.isNull(user.getDept()) ? "" : user.getDept().getDeptName());
        List<RoleDTO> roles = BeanUtil.copyToList(user.getRoles(), RoleDTO.class);
        loginUser.setRoles(roles);
        loginUser.setCreateTime(user.getCreateTime());
        return loginUser;
    }

    /**
     * 构建登录用户-不查询权限和菜单
     */
    @Override
    public LoginUser buildUser(SysUser user) {
        if (Objects.isNull(user) || (user.getUserId() == null || user.getUserId() <= 0L)) {
            return null;
        }
        LoginUser loginUser = new LoginUser();
        loginUser.setEmail(user.getEmail());
        loginUser.setPhoneNumber(user.getPhoneNumber());
        loginUser.setSex(user.getSex());
        loginUser.setBirthday(user.getBirthday());
        loginUser.setRemark(user.getRemark());
        loginUser.setInviteCode(user.getInviteCode());
        loginUser.setUserId(user.getUserId());
        loginUser.setDeptId(user.getDeptId());
        loginUser.setUsername(user.getUserName());
        loginUser.setNickname(user.getNickName());
        loginUser.setPassword(user.getPassword());
        loginUser.setUserType(user.getUserType());
        loginUser.setAvatar(user.getAvatar());
        loginUser.setPayPassword(user.getPayPassword());
        loginUser.setCreateTime(user.getCreateTime());
        return loginUser;
    }

    @Override
    public List<LoginUser> selectList(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return null;
        }
        LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<>();
        lqw.in(SysUser::getUserId, userIds);
        List<SysUser> sysUsers = userMapper.selectList(lqw);
        if (CollUtil.isEmpty(sysUsers)) {
            return null;
        }
        List<LoginUser> result = new ArrayList<>();
        for (SysUser sysUser : sysUsers) {
            LoginUser loginUser = new LoginUser();
            loginUser.setUsername(sysUser.getUserName());
            loginUser.setNickname(sysUser.getNickName());
            loginUser.setUserId(sysUser.getUserId());
            loginUser.setAvatar(sysUser.getAvatar());
            loginUser.setPhoneNumber(sysUser.getPhoneNumber());
            loginUser.setSex(sysUser.getSex());
            loginUser.setBirthday(sysUser.getBirthday());
            result.add(loginUser);
        }
        return result;
    }

    @Override
    public List<LoginUser> idsByName(String name) {
        if (StrUtil.isEmptyIfStr(name)) {
            return null;
        }
        LambdaQueryWrapper<SysUser> query = new LambdaQueryWrapper<>();
        query.like(SysUser::getNickName, name);
        List<SysUser> sysUsers = userMapper.selectList(query);
        return build(sysUsers);
    }

    @Override
    public LoginUser queryById(Long userId) {
        SysUser sysUser = userMapper.selectUserById(userId);
        if (Objects.isNull(sysUser)) {
            return new LoginUser();
        }
        return buildUser(sysUser);
    }

    public List<LoginUser> build(List<SysUser> sysUsers) {
        if (CollUtil.isEmpty(sysUsers)) {
            return new ArrayList<>();
        }
        List<LoginUser> result = new ArrayList<>();
        for (SysUser sysUser : sysUsers) {
            LoginUser loginUser = new LoginUser();
            loginUser.setUsername(sysUser.getUserName());
            loginUser.setUserId(sysUser.getUserId());
            loginUser.setAvatar(sysUser.getAvatar());
            result.add(loginUser);
        }
        return result;
    }

    @Override
    public Map<Long, LoginUser> selectMapByName(String name) {
        if (StrUtil.isEmptyIfStr(name)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<SysUser> query = new LambdaQueryWrapper<>();
        query.likeRight(SysUser::getUserName, name).or().likeRight(SysUser::getNickName, name);
        List<SysUser> sysUsers = userMapper.selectList(query);
        if (CollUtil.isEmpty(sysUsers)) {
            return new HashMap<>();
        }
        List<LoginUser> build = build(sysUsers);
        return build.stream().collect(Collectors.toMap(LoginUser::getUserId, u -> u));
    }

    @Override
    public Map<Long, LoginUser> selectMap(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new HashMap<>();
        }
        Set<Long> collect = userIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(collect)) {
            return new HashMap<>();
        }
        List<LoginUser> sysUsers = this.selectList(collect);
        if (CollUtil.isEmpty(sysUsers)) {
            return new HashMap<>();
        }
        return sysUsers.stream().collect(Collectors.toMap(LoginUser::getUserId, u -> u));
    }

    @Override
    public Map<Long, String> selectNameMap(Collection<Long> userIds) {
        List<LoginUser> sysUsers = this.selectList(userIds);
        if (CollUtil.isEmpty(sysUsers)) {
            return new HashMap<>();
        }
        Map<Long, String> result = new HashMap<>();
        for (LoginUser sysUser : sysUsers) {
            result.put(sysUser.getUserId(), StringUtils.getValidString(sysUser.getNickname(), sysUser.getUsername()));
        }
        return result;
    }

    @Override
    public Integer updateUserPwd(Long userId, String password) {
        SysUser user = userService.selectUserById(userId);
        CacheMgr.evict(LoginUser.REGION, String.valueOf(userId));
        return userService.resetUserPwd(user.getUserName(), BCrypt.hashpw(password));
    }

    @Override
    public Integer updatePayPwd(Long userId, String password) {
        SysUser user = userService.selectUserById(userId);
        CacheMgr.evict(LoginUser.REGION, String.valueOf(userId));
        user.setPayPassword(BCrypt.hashpw(password));
        return userService.resetPwd(user);
    }

    @Override
    public Boolean isNewUser(Long userId) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUserId, userId)
                .apply("DATE_ADD(create_time, INTERVAL 7 DAY) > {0}", new Date());
        return userMapper.exists(queryWrapper);
    }

    @Override
    public Long selectByCode(String inviteCode) {
        return userMapper.selectByCode(inviteCode);
    }

    @Override
    public LoginUser selectByUid(Long uid) {
        SysUser sysUser = userMapper.selectOne(SysUser::getUid, uid);
        if (Objects.isNull(sysUser)) {
            return null;
        }
        LoginUser loginUser = new LoginUser();
        loginUser.setUsername(sysUser.getUserName());
        loginUser.setUserId(sysUser.getUserId());
        loginUser.setAvatar(sysUser.getAvatar());
        return loginUser;
    }

    @Override
    public void updateUser(SysUser sysUser) {
        CacheMgr.evict(LoginUser.REGION, String.valueOf(sysUser.getUserId()));
        userMapper.updateById(sysUser);
    }

    @Override
    public void logoutByUserId(Long userId) {
        SysUser sysUser = this.userService.selectUserById(userId);
        if (sysUser == null) {
            return;
        }
        LoginUser loginUser = this.buildUser(sysUser);
        LoginHelper.logoutByLoginId(loginUser.getLoginId());
    }

    @Override
    public void flushLoginCacheByUserId(Long userId) {
//        SysUser sysUser = this.userService.selectUserById(userId);
//        if (sysUser == null) {
//            return;
//        }
//        LoginUser loginUser = this.buildLoginUser(sysUser);
//        LoginHelper.updateUserCache(loginUser);
        this.sysUserBizService.asyncFlushLoginCacheByUserId(userId);
    }

    @Override
    public void bindMcnUser(Long id) {
        remoteMcnUserService.bindMcnUser(id);
    }

    @Override
    public void bindMcnUser(Long id, Long userId) {
        remoteMcnUserService.bindMcnUser(id, userId);
    }

    @Override
    public Boolean checkUserNameUnique(SysUser user) {
        return userService.checkUserNameUnique(user);
    }

    @Override
    public Boolean checkPhoneUnique(SysUser user) {
        return userService.checkPhoneUnique(user);
    }

    @Override
    public void updateUserStatus(Long userId, String status) {
        this.userService.updateUserStatus(userId, status);
    }

    @Override
    public UserVo search(String keyword) {
        if (StrUtil.isEmptyIfStr(keyword)) {
            return null;
        }
        LambdaQueryWrapper<SysUser> query = new LambdaQueryWrapper<>();
        query.eq(SysUser::getUserName, keyword)
                .or().eq(SysUser::getPhoneNumber, keyword);
        List<SysUser> sysUsers = userMapper.selectList(query);
        if (CollUtil.isEmpty(sysUsers)) {
            return null;
        }
        SysUser sysUser = sysUsers.get(0);

        UserVo vo = new UserVo();
        vo.setUserName(sysUser.getUserName());
        vo.setNickName(sysUser.getNickName());
        vo.setAvatar(sysUser.getAvatar());
        vo.setId(sysUser.getUserId());
        vo.setSex(sysUser.getSex());

        SohuUserSiteRelationVo userSiteRelationVo = remoteMiddleUserSiteRelationService.getByUserId(sysUser.getUserId());
        if (ObjectUtil.isNull(userSiteRelationVo)) {
            return vo;
        }
        vo.setSiteId(userSiteRelationVo.getSiteId());
        SohuSiteVo sohuSiteVo = remoteMiddleSiteService.queryById(userSiteRelationVo.getSiteId());
        if (ObjectUtil.isNull(sohuSiteVo)) {
            return vo;
        }
        vo.setSiteName(sohuSiteVo.getName());
        return vo;
    }

    @Override
    public List<Long> getUserBySiteId(List<Long> siteIds) {
        return userService.getUserBySiteId(siteIds);
    }

    @Override
    public void saveInviteInfo(String inviteType, String publishMediaId) {
        if (StrUtil.isBlank(publishMediaId)) {
            throw new RuntimeException("发布作品标识不能为空");
        }
        // 被邀请人用户id
        Long regUserId = LoginHelper.getUserId();
        LoginUser userInfo = this.selectById(regUserId);

        Date createTime = userInfo.getCreateTime();
        Duration duration = Duration.between(createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(), LocalDateTime.now());
        // 邀请人用户id
        Long inviteUserId = remoteMiddleArticleService.getUserIdByPublishMediaId(publishMediaId);
        // 记录邀请信息
        SohuInviteBo inviteBo = new SohuInviteBo();
        inviteBo.setInviteUser(inviteUserId);
        inviteBo.setRegUser(regUserId);
        inviteBo.setInviteType(inviteType);
        inviteBo.setPublishMediaId(publishMediaId);
        remoteMiddleInviteService.insertByArticle(inviteBo);

        // 绑定好友关系
        SohuFriendsBo friendsBo = new SohuFriendsBo();
        friendsBo.setUserId(inviteUserId);
        friendsBo.setFriendId(regUserId);
        friendsBo.setSource(inviteType);
        remoteMiddleFriendService.bindFriendByBo(friendsBo);
    }

    @Override
    public void saveInviteInfo(String inviteType, String publishMediaId, Long userId) {
        if (StrUtil.isBlank(publishMediaId)) {
            throw new RuntimeException("发布作品标识不能为空");
        }
        // 被邀请人用户id
        Long regUserId = userId;
        LoginUser userInfo = this.selectById(regUserId);

        Date createTime = userInfo.getCreateTime();
        Duration duration = Duration.between(createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(), LocalDateTime.now());
        // 邀请人用户id

        Long inviteUserId = remoteMiddleArticleService.getUserIdByPublishMediaId(publishMediaId);
        // 兼容原有拉新逻辑
        if (inviteUserId == null) {
            // 记录邀请信息
            SohuInviteBo inviteBo = new SohuInviteBo();
            inviteBo.setInviteUser(Long.valueOf(publishMediaId));
            inviteBo.setRegUser(regUserId);
            inviteBo.setInviteType(inviteType);
            remoteMiddleInviteService.insertByArticle(inviteBo);
            return;
        }
        // 记录邀请信息
        SohuInviteBo inviteBo = new SohuInviteBo();
        inviteBo.setInviteUser(inviteUserId);
        inviteBo.setRegUser(regUserId);
        inviteBo.setInviteType(inviteType);
        inviteBo.setPublishMediaId(publishMediaId);
        remoteMiddleInviteService.insertByArticle(inviteBo);

        // 绑定好友关系
        SohuFriendsBo friendsBo = new SohuFriendsBo();
        friendsBo.setUserId(inviteUserId);
        friendsBo.setFriendId(regUserId);
        friendsBo.setSource(inviteType);
        remoteMiddleFriendService.bindFriendByBo(friendsBo);
    }

    @Override
    public void saveInviteInfo(Long userId) {
        SohuInviteVo sohuInviteVo = remoteMiddleInviteService.queryByRegUser(userId);
        if (Objects.nonNull(sohuInviteVo)) {
            return;
        }
        SysUser sysUser = userMapper.selectById(userId);
        if (Objects.nonNull(sysUser) && Objects.nonNull(sysUser.getInviteUserId())) {
            // 记录邀请信息
            SohuInviteBo inviteBo = new SohuInviteBo();
            inviteBo.setInviteUser(sysUser.getInviteUserId());
            inviteBo.setRegUser(userId);
            remoteMiddleInviteService.insertByArticle(inviteBo);

            // 绑定好友关系
            SohuFriendsBo friendsBo = new SohuFriendsBo();
            friendsBo.setUserId(sysUser.getInviteUserId());
            friendsBo.setFriendId(userId);
            remoteMiddleFriendService.bindFriendByBo(friendsBo);
        }
    }

    @Override
    public void updateEmail(String phoneNumber, String email) {
        LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysUser::getPhoneNumber, phoneNumber)
                .set(SysUser::getEmail, email);
        if (userMapper.update(new SysUser(), updateWrapper) > 0) {
            SysUser sysUser = this.selectUserByEmail(email);
            if (sysUser != null) {
                this.deleteById(sysUser.getUserId());
                CacheMgr.evict(LoginUser.REGION, String.valueOf(sysUser.getUserId()));
            }
        }
    }

    @Override
    public List<Long> queryByUserName(String nickname) {
        return userMapper.queryByUserName(nickname);
    }

    @Override
    public List<Long> queryByLikePhonenumber(String phoneNumber) {
        return userMapper.queryByLikePhonenumber(phoneNumber);
    }

    public SysUser selectUserByEmail(String email) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getEmail, email);
        wrapper.eq(SysUser::getPhoneNumber, "")
                .last("limit 1");
        return userMapper.selectOne(wrapper);
    }

//    @Override
//    public SohuAccountModel selectAccountByUserId(Long userId) {
//        return remoteAccountService.selectAccountByUserId(userId);
//    }


    @Override
    public Boolean initAirecUsers() {
        return userService.initAirecUsers();
    }

    @Override
    public void sendMsgOfUpdatePayPwd(Long userId) {
        userService.sendMsgOfUpdatePayPwd(userId);
    }

    @Override
    public void updateUserInfo(SysUserInfo sysUserInfo) {
        if (Objects.nonNull(sysUserInfo.getUserId())) {
            CacheMgr.evict(LoginUser.REGION, String.valueOf(sysUserInfo.getUserId()));
        }
        sysUserInfoMapper.insertOrUpdate(sysUserInfo);
    }

    @Override
    public SysUserInfo getUserInfo(Long userId) {
        return sysUserInfoMapper.selectOne(SysUserInfo::getUserId, userId);
    }

    @Override
    public List<String> queryPlatformRole(Long userId) {
        // 用户和平台角色
        LambdaQueryWrapper<SysPlatformUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysPlatformUser::getUserId, userId);
        List<SysPlatformUser> platformUserList = platformUserMapper.selectVoList(lqw);
        List<Long> roleIds = platformUserList.stream().map(SysPlatformUser::getPlatformRoleId).collect(Collectors.toList());
        if (CollUtil.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        // 平台角色
        LambdaQueryWrapper<SysPlatformRole> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SysPlatformRole::getId, roleIds);
        wrapper.eq(SysPlatformRole::getStatus, CommonEnums.EnableEnum.ENABLE.name());
        wrapper.eq(SysPlatformRole::getDelFlag, ACCOUNT_STATUS);
        List<SysPlatformRoleVo> platformRoleList = platformRoleMapper.selectVoList(wrapper);
        return platformRoleList.stream().map(SysPlatformRoleVo::getName).collect(Collectors.toList());
    }

    @Override
    public Long getUserNumByCreateTime(Date startTime, Date endTime) {
        return userService.getUserNumByCreateTime(startTime, endTime);
    }

    @Override
    public Long getUserTotalNum() {
        return userService.getUserTotalNum();
    }

    @Override
    public Boolean registerImUser(String phone, String serverCode) {
        return userService.registerImUser(phone, serverCode);
    }

    @Override
    public Map<Long, Integer> getTenantRegisterNum(List<Long> tenantIds) {
        return userService.getTenantRegisterNum(tenantIds);
    }

    @Override
    public List<Long> queryUserIdByBo(SysUserQueryBo bo) {
        return userService.queryUserIdByBo(bo);
    }

    @Override
    public void updateInviteUserId(Long userId, Long inviteUserId) {
        userService.updateInviteUserId(userId, inviteUserId);
    }

    @Override
    public BigDecimal getUserIncome(Long userId, Integer siteType, Long siteId, String userRole, Integer independentStatus, Date startTime, Date endTime) {
        return userService.getUserIncome(userId, siteType, siteId, userRole, independentStatus, startTime, endTime);
    }

    @Override
    public BigDecimal getUserWithdrawal(Long userId, Integer siteType, Long siteId, String userRole, Date startTime, Date endTime) {
        return userService.getUserWithdrawal(userId, siteType, siteId, userRole, startTime, endTime);
    }

    @Override
    public BigDecimal getUserInviteIncome(Long userId, Integer siteType, Long siteId, String independentObject, Date startTime, Date endTime) {
        return userService.getUserInviteIncome(userId, siteType, siteId, independentObject, startTime, endTime);
    }

    @Override
    public SohuUserOrderInfoVo getUserOrderInfo(Long userId, Integer siteType, Long siteId, List<String> independentObject, String tradeType, Date startTime, Date endTime) {
        return userService.getUserOrderInfo(userId, siteType, siteId, independentObject, tradeType, startTime, endTime);
    }


    @Override
    public List<SohuUserAppConfigVo> getAppConfig(Long userId, Integer type, String platform, String version, String channel) {
        int platformType = PlatformEnum.mapPlatform.get(platform);
        //查询用户是否存在配置项,默认配置表并且新增配置项
        long count = sohuUserAppConfigService.getCount(userId, platformType);
        if (count > Constants.ZERO) {
            List<SohuUserAppConfigVo> list = sohuUserAppConfigService.queryList(userId, platformType, version, channel);
            if (type == Constants.ONE) {
                return list.stream().filter(item -> item.getEnable() == Constants.ONE).collect(Collectors.toList());
            }
            return list;
        }
        List<SohuUserAppConfigVo> list = sohuUserAppConfigService.getDefaultList(platformType, version, channel);
        //异步新增用户默认配置项
        CompletableFuture.supplyAsync(() -> {
            saveAppConfig(list, userId, platformType);
            return true;
        }, asyncConfig.getAsyncExecutor());
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean configSort(List<SohuUserAppConfigSortBo> list, Long userId, String platform) {
        if (CollUtil.isEmpty(list)) {
            throw new RuntimeException("参数错误");
        }
        int platformType = PlatformEnum.mapPlatform.get(platform);
        //删除已有的配置
        sohuUserAppConfigService.deleteByUserId(userId, platformType);
        //新增配置项
        List<SohuUserAppConfigBo> configBoList = new ArrayList<>();
        list.forEach(item -> {
            SohuUserAppConfigBo configBo = new SohuUserAppConfigBo(userId, item.getId(), item.getSort(), item.getEnable(), platformType);
            configBoList.add(configBo);
        });
        return sohuUserAppConfigService.batchSave(configBoList);
    }

    public void saveAppConfig(List<SohuUserAppConfigVo> list, Long userId, Integer platformType) {
        if (CollUtil.isNotEmpty(list) && userId != null) {
            List<SohuUserAppConfigBo> configBoList = new ArrayList<>();
            list.forEach(item -> {
                SohuUserAppConfigBo configBo = new SohuUserAppConfigBo(userId, item.getId(), item.getSort(), platformType);
                configBoList.add(configBo);
            });
            sohuUserAppConfigService.batchSave(configBoList);
        }
    }
}
