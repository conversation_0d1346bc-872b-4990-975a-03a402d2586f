package com.sohu.system.dubbo;

import com.sohu.system.api.RemoteLogService;
import com.sohu.system.api.domain.SysLogininfor;
import com.sohu.system.api.domain.SysOperLog;
import com.sohu.system.service.ISysLogininforService;
import com.sohu.system.service.ISysOperLogService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;

/**
 * 操作日志记录
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteLogServiceImpl implements RemoteLogService {

    private final ISysOperLogService operLogService;
    private final ISysLogininforService logininforService;

    @Override
    public Boolean saveLog(SysOperLog sysOperLog) {
        return operLogService.insertOperlog(sysOperLog) > 0;
    }

    @Override
    public Boolean saveLogininfor(SysLogininfor sysLogininfor) {
        return logininforService.insertLogininfor(sysLogininfor) > 0;
    }

    @Override
    public Long getOnlineUserNumByUserIdsAndTime(Collection<Long> userIds, Date startTime, Date endTime) {
        return logininforService.getOnlineUserNumByUserIdsAndTime(userIds, startTime, endTime);
    }
}
