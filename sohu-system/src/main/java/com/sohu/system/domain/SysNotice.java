package com.sohu.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.core.xss.Xss;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;


/**
 * 通知公告表 sys_notice
 *
 * <AUTHOR> Li
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("sys_notice")
public class SysNotice extends BaseEntity {

    /**
     * 公告ID
     */
    @TableId(value = "notice_id")
    private Long noticeId;

    /**
     * 公告标题
     */
    @Xss(message = "公告标题不能包含脚本字符")
    @NotBlank(message = "公告标题不能为空")
    @Size(min = 0, max = 50, message = "公告标题不能超过50个字符")
    private String noticeTitle;

    /**
     * 公告类型（1通知 2公告 3=月度账单 4=季度账单 5=年度账单）
     */
    private String noticeType;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 公告状态（0正常 1关闭）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
    /**
     * 公告查看角色类型,如站长，MCN机构，服务商，见枚举类RoleCodeEnum，默认=all ,多选以英文逗号间隔处理
     */
    private String noticeRole;
    /**
     * 公告发送时机，1：首次进入（拥有某个角色首次进入会收到的公告的用户）
     * 2：立即发送（公告添加后会立即发送）
     */
    private Integer noticeSendType;

    /**
     * 是否显示（0=否， 1=是，默认等于true）
     */
    private Boolean asShow;

    /**
     * 是否置顶（0=否， 1=是，默认不置顶false）
     */
    private Boolean asTop;

    /**
     * 年月日格式保存，20250501
     */
    private String noticeDate;

}
