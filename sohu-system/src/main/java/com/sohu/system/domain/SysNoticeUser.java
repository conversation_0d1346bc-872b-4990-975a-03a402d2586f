package com.sohu.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公告-用户关联对象 sys_notice_user
 *
 * <AUTHOR>
 * @date 2025-06-07
 */
@Data
@TableName("sys_notice_user")
public class SysNoticeUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 公告ID
     */
    private Long noticeId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 阅读状态（0=未读，1=已读）
     */
    private Boolean readStatus;
    /**
     * 阅读时间
     */
    private Date readTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
