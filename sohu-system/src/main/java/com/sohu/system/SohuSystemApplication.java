package com.sohu.system;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 系统模块
 *
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication(scanBasePackages = {"com.sohu"})
@EnableAsync
public class SohuSystemApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(SohuSystemApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  系统模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
