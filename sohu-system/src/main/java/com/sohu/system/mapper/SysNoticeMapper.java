package com.sohu.system.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.system.api.vo.SysNoticeVo;
import com.sohu.system.domain.SysNotice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知公告表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysNoticeMapper extends BaseMapperPlus<SysNoticeMapper, SysNotice, SysNoticeVo> {

    Page<SysNoticeVo> page(@Param("userId") Long userId, @Param("noticeRole") String noticeRole, @Param("noticeTypeList") List<Integer> noticeTypeList, @Param("page") Page page);

}
