<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.system.mapper.SysNoticeMapper">

    <resultMap type="com.sohu.system.domain.SysNotice" id="SysNoticeResult">
        <result property="noticeId" column="notice_id"/>
        <result property="noticeTitle" column="notice_title"/>
        <result property="noticeType" column="notice_type"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="noticeRole" column="notice_role"/>
        <result property="noticeSendType" column="notice_send_type"/>
        <result property="asShow" column="show"/>
        <result property="asTop" column="as_top"/>
    </resultMap>

    <select id="page" resultType="com.sohu.system.api.vo.SysNoticeVo">
        SELECT
        sn.*,snu.read_status as readType,snu.read_time as readTime
        FROM
        sys_notice sn
        INNER JOIN
        sys_notice_user snu ON sn.notice_id = snu.notice_id
        WHERE
        snu.user_id = #{userId}
        AND sn.status = '0'
        AND sn.as_show = 1
        <if test="noticeRole != null and noticeRole != ''">
            AND FIND_IN_SET(#{noticeRole}, sn.notice_role)
        </if>
        <if test="noticeTypeList != null and noticeTypeList.size() > 0">
            AND sn.notice_type IN
            <foreach item="item" collection="noticeTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY
        sn.as_top DESC,
        sn.update_time DESC

    </select>


</mapper>
