package com.sohu.shoporder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.middle.api.bo.SohuMerTradeRecordBo;
import com.sohu.middle.api.bo.SohuOperateBo;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.service.RemoteMiddleMerTradeRecordService;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.vo.SohuOrderIndependentPriceVo;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.RemoteIndependentOrderService;
import com.sohu.pay.api.model.SohuAccountBankModel;
import com.sohu.pay.api.model.SohuIndependentOrderModel;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.model.SohuProductWindowMcnModel;
import com.sohu.shopgoods.api.model.SohuProductWindowModel;
import com.sohu.shoporder.api.bo.SohuIndependentIdBo;
import com.sohu.shoporder.api.bo.SohuIndependentTempBo;
import com.sohu.shoporder.api.bo.SohuShopMasterOrderBo;
import com.sohu.shoporder.api.bo.SohuShopOrderInfoBo;
import com.sohu.shoporder.api.model.SohuShopOrderInfoModel;
import com.sohu.shoporder.api.model.SohuShopOrderModel;
import com.sohu.shoporder.api.vo.SohuShopMasterOrderVo;
import com.sohu.shoporder.api.vo.SohuShopOrderInfoVo;
import com.sohu.shoporder.domain.SohuShopOrder;
import com.sohu.shoporder.service.IPlayletShopMasterOrderService;
import com.sohu.shoporder.service.IPlayletShopOrderBizService;
import com.sohu.shoporder.service.IPlayletShopOrderInfoService;
import com.sohu.shoporder.service.IPlayletShopOrderService;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import com.wangcaio2o.ipossa.sdk.model.SplitList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sohu.common.core.enums.SohuTradeRecordEnum.Type.Good;

/**
 * 商城订单综合业务服务接口
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PlayletShopOrderBizServiceImpl implements IPlayletShopOrderBizService {

    private final IPlayletShopMasterOrderService masterOrderService;
    private final IPlayletShopOrderService orderService;
    private final IPlayletShopOrderInfoService infoService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    private final TransactionTemplate transactionTemplate;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteIndependentOrderService remoteIndependentOrderService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    private RemoteMiddleMerTradeRecordService remoteMiddleMerTradeRecordService;

    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean mqDelayConfirm(SohuShopOrderModel shopOrder) {
        /*
         * 先查询主订单
         * 在查询子订单集合
         * 在查询子订单的订单详情
         */
        // 获取主订单
        SohuShopMasterOrderVo masterOrder = masterOrderService.queryByMasterOrderNo(shopOrder.getMasterOrderNo());
        if (ObjectUtils.isNull(masterOrder)) {
            throw new RuntimeException("不存在当前主订单");
        }
        // 延时分账请求流水号
        String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
        shopOrder.setDelayTradeNo(delayOrderNo);
        log.warn("delayOrderNo 延时分账交易流水号：{}", delayOrderNo);
        SohuIndependentIdBo independentIdBo = new SohuIndependentIdBo();
        // 根据城市站点id获取用户id
        Long cityId = remoteMiddleSiteService.queryById(masterOrder.getSiteId()).getStationmasterId();
        independentIdBo.setCityId(cityId);
        // 根据城市站点id获取国家站点用户id
        Long countryId = remoteMiddleSiteService.selectSiteByPid(masterOrder.getSiteId()).getStationmasterId();
        independentIdBo.setCountryId(countryId);
        // todo 获取代理人用户id
        Long agencyId = remoteMiddleSiteService.selectSiteByPid(masterOrder.getSiteId()).getStationmasterId();
        independentIdBo.setAgencyId(agencyId);
        independentIdBo.setAdminId(2L);
        independentIdBo.setDistributorId(shopOrder.getIndependentUserId());
        independentIdBo.setInviteId(shopOrder.getInviteUserId());
        independentIdBo.setDistributorInviteId(shopOrder.getIndependentInviteUserId());
        // 获取当前的商户id
//        List<Long> merIds = shopOrderList.stream().map(SohuShopOrderModel::getMerId).collect(Collectors.toList());
        Long merId = shopOrder.getMerId();
        List<Long> merIds = Lists.newArrayList();
        merIds.add(merId);
        // 获取所有商户的userId
        Map<Long, SohuMerchantModel> merIdMapByIdList = remoteMerchantService.getMerIdMapByIdList(merIds);
        // 获取到这个店铺信息
        SohuMerchantModel sohuMerchantModel = merIdMapByIdList.get(shopOrder.getMerId());
        List<SohuIndependentOrderModel> independentOrderBoLists = Lists.newArrayList();
        BigDecimal zeroPrice = new BigDecimal("0.00");
        // 提取所有对象的 id 成为 List<Long>
        List<Long> idList = merIdMapByIdList.values().stream().map(SohuMerchantModel::getUserId).collect(Collectors.toList());
        // 查询所有分账人的翼码帐户信息
        List<Long> allUserIds = getAllIds(independentIdBo);
        allUserIds.addAll(idList);
        List<SohuAccountBankModel> accountBankVos = remoteAccountService.queryListByUserId(allUserIds);
        //用户id与翼码映射
        Map<Long, String> bankMap = accountBankVos.stream().collect(Collectors.toMap(SohuAccountBankModel::getUserId, SohuAccountBankModel::getMerchantId));
        //  增加分账订单记录
        BigDecimal platPrice = shopOrder.getAdminPrice();
        //查询分账角色是否处于冻结状态，处于冻结状态不进行分账，分账金额给平台
        Map<Long,Boolean> freezenMap = remoteAccountService.afterFreezeMap(
                Arrays.asList(countryId, cityId, agencyId, shopOrder.getIndependentUserId(),shopOrder.getInviteUserId(),shopOrder.getIndependentInviteUserId()),masterOrder.getCreateTime());
        if (shopOrder.getCountryPrice() != null && !shopOrder.getCountryPrice().equals(zeroPrice)
                && StrUtil.isNotEmpty(bankMap.get(countryId)) && Objects.isNull(freezenMap.get(countryId))) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setUserId(countryId);
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderBo.setTradeNo(delayOrderNo);
            independentOrderBo.setIndependentObject(SohuIndependentObject.country.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getCountryPrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        } else {
            platPrice = CalUtils.add(platPrice, shopOrder.getCountryPrice());
            shopOrder.setCountryPrice(BigDecimal.ZERO);
        }

        if (shopOrder.getCityPrice() != null && !shopOrder.getCityPrice().equals(zeroPrice)
                && StrUtil.isNotEmpty(bankMap.get(cityId)) && Objects.isNull(freezenMap.get(cityId))) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setUserId(cityId);
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderBo.setTradeNo(delayOrderNo);
            independentOrderBo.setIndependentObject(SohuIndependentObject.city.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getCityPrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        } else {
            platPrice = CalUtils.add(platPrice, shopOrder.getCityPrice());
            shopOrder.setCityPrice(BigDecimal.ZERO);
        }

        if (null != shopOrder.getIndependentUserId() && shopOrder.getDistributorPrice() != null && !shopOrder.getDistributorPrice().equals(zeroPrice)
                && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getDistributorId())) && Objects.isNull(freezenMap.get(independentIdBo.getDistributorId()))) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderBo.setTradeNo(delayOrderNo);
//            independentIdBo.setDistributorId(shopOrder.getIndependentUserId());
            independentOrderBo.setUserId(shopOrder.getIndependentUserId());
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.distribution.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getDistributorPrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        } else {
            platPrice = CalUtils.add(platPrice, shopOrder.getDistributorPrice());
            shopOrder.setDistributorPrice(BigDecimal.ZERO);
        }
        if (null != shopOrder.getInviteUserId() && shopOrder.getInvitePrice() != null && !shopOrder.getInvitePrice().equals(zeroPrice)
                && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getInviteId())) && Objects.isNull(freezenMap.get(independentIdBo.getInviteId()))) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderBo.setTradeNo(delayOrderNo);
//            independentIdBo.setInviteId(shopOrder.getInviteUserId());
            independentOrderBo.setUserId(shopOrder.getInviteUserId());
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.invite.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getInvitePrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        } else {
            platPrice = CalUtils.add(platPrice, shopOrder.getInvitePrice());
            shopOrder.setInvitePrice(BigDecimal.ZERO);
        }
        // 代理
        if (null != agencyId && shopOrder.getAgencyAdminPrice() != null && !shopOrder.getAgencyAdminPrice().equals(zeroPrice) && !shopOrder.getAgencyAdminPrice().toPlainString().equals("0")
                && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getAgencyId())) && Objects.isNull(freezenMap.get(independentIdBo.getAgencyId()))) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderBo.setTradeNo(delayOrderNo);
//            independentIdBo.setAgencyId(agencyId);
            independentOrderBo.setUserId(agencyId);
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.agency.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getAgencyAdminPrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        } else {
            platPrice = CalUtils.add(platPrice, shopOrder.getAgencyAdminPrice());
            shopOrder.setAgencyAdminPrice(BigDecimal.ZERO);
        }
        // 分销人的拉新人
        if (null != shopOrder.getIndependentInviteUserId() && shopOrder.getDistributorInvitePrice() != null && !shopOrder.getDistributorInvitePrice().equals(zeroPrice) && !shopOrder.getDistributorInvitePrice().toPlainString().equals("0")
                && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getDistributorInviteId())) && Objects.isNull(freezenMap.get(independentIdBo.getDistributorInviteId()))) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderBo.setTradeNo(delayOrderNo);
//            independentIdBo.setDistributorInviteId(shopOrder.getIndependentInviteUserId());
            independentOrderBo.setUserId(shopOrder.getIndependentInviteUserId());
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.distributionInvite.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getDistributorInvitePrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        } else {
            platPrice = CalUtils.add(platPrice, shopOrder.getDistributorInvitePrice());
            shopOrder.setDistributorInvitePrice(BigDecimal.ZERO);
        }

        if (shopOrder.getAdminPrice() != null && !shopOrder.getAdminPrice().equals(zeroPrice) && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getAdminId()))) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderBo.setTradeNo(delayOrderNo);
//            independentIdBo.setAdminId(2L);
            independentOrderBo.setUserId(2L);
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.platform.getKey());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBo.setIndependentPrice(platPrice);
            independentOrderBoLists.add(independentOrderBo);
        }
        shopOrder.setAdminPrice(platPrice);
//        // 获取翼码支付配置
//        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
//        // 延时分账请求参数对象
//        DelayConfirmRequest delayConfirmRequest = new DelayConfirmRequest();
//        // 固定参数
//        delayConfirmRequest.setPosId(yiMaPayConfig.getPosId());
//        delayConfirmRequest.setIsspid(yiMaPayConfig.getIssPid());
//        delayConfirmRequest.setSystemId(yiMaPayConfig.getSystemId());
//        delayConfirmRequest.setStoreId(yiMaPayConfig.getStoreId());
//        // 延时确认请求流水号
//        delayConfirmRequest.setPosSeq(delayOrderNo);
//        // 支付请求流水号及分账扩展参数
//        DelayConfirm confirm = new DelayConfirm();
//        confirm.setOrgPosSeq(masterOrder.getOrderNo());
//        // 分账信息相关扩展参数
//        ExtendParams params = new ExtendParams();
//        // 分账信息
//        SplitInfo splitInfo = new SplitInfo();
//        // 分账账号信息参数
//        List<SplitList> splitLists = Lists.newArrayList();
//        // 计算国家、城市站长、分销、拉新的钱
//        accountBankVos.forEach(bank -> {
//            // 分销人
//            if (null != independentIdBo.getDistributorId() && bank.getUserId().equals(independentIdBo.getDistributorId())) {
//                SplitList splitList = new SplitList();
//                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getDistributorPrice()).toString());
//                splitList.setMerchantId(bank.getMerchantId());
//                splitLists.add(splitList);
//            }
//            // 拉新人
//            if (null != independentIdBo.getInviteId() && bank.getUserId().equals(independentIdBo.getInviteId())) {
//                SplitList splitList = new SplitList();
//                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getInvitePrice()).toString());
//                splitList.setMerchantId(bank.getMerchantId());
//                splitLists.add(splitList);
//            }
//            // 国家站长
//            if (null != independentIdBo.getCountryId() && bank.getUserId().equals(independentIdBo.getCountryId())) {
//                SplitList splitList = new SplitList();
//                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getCountryPrice()).toString());
//                splitList.setMerchantId(bank.getMerchantId());
//                splitLists.add(splitList);
//            }
//            // 城市站长
//            if (null != independentIdBo.getCityId() && bank.getUserId().equals(independentIdBo.getCityId())) {
//                SplitList splitList = new SplitList();
//                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getCityPrice()).toString());
//                splitList.setMerchantId(bank.getMerchantId());
//                splitLists.add(splitList);
//            }
//            // 代理
//            if (null != independentIdBo.getAgencyId() && bank.getUserId().equals(independentIdBo.getAgencyId())) {
//                SplitList splitList = new SplitList();
//                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getAgencyAdminPrice()).toString());
//                splitList.setMerchantId(bank.getMerchantId());
//                splitLists.add(splitList);
//            }
//            // 分销人的拉新人
//            if (null != independentIdBo.getDistributorInviteId() && bank.getUserId().equals(independentIdBo.getDistributorInviteId())) {
//                SplitList splitList = new SplitList();
//                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getDistributorInvitePrice()).toString());
//                splitList.setMerchantId(bank.getMerchantId());
//                splitLists.add(splitList);
//            }
//        });
        // 计算商户的留存金额
        SplitList splitList = new SplitList();
        // 直接通过 Stream 找到符合条件的对象并获取 店铺的userId去获取店铺在翼码的分账id
        Optional<String> merIdOptional = accountBankVos.stream()
                .filter(accountBankVo -> sohuMerchantModel.getUserId().equals(accountBankVo.getUserId()))
                .map(SohuAccountBankModel::getMerchantId).findFirst();
        // 处理非空的情况
        merIdOptional.ifPresent(merchantId -> {
            // 如果存在值直接返回不存在传空
            splitList.setMerchantId(merIdOptional.orElse(""));
            // 每笔子单商户应该获得的钱
            BigDecimal shopPrice = CalUtils.sub(shopOrder.getPayPrice(), shopOrder.getDistributorPrice(), shopOrder.getInvitePrice(),
                    shopOrder.getAdminPrice(), shopOrder.getCountryPrice(), shopOrder.getCityPrice(), shopOrder.getChargePrice(),
                    shopOrder.getAgencyAdminPrice(), shopOrder.getDistributorInvitePrice());
//            splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopPrice).toString());
//            splitLists.add(splitList);
            // 商家分账信息
            SohuIndependentOrderModel independentOrderShop = new SohuIndependentOrderModel();
            independentOrderShop.setOrderNo(shopOrder.getOrderNo());
            independentOrderShop.setTradeType(BusyType.Goods.name());
            independentOrderShop.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderShop.setTradeNo(delayOrderNo);
            independentOrderShop.setUserId(sohuMerchantModel.getUserId());
            independentOrderShop.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderShop.setIndependentObject(SohuIndependentObject.shop.getKey());
            independentOrderShop.setIndependentPrice(shopPrice);
            independentOrderShop.setMerId(shopOrder.getMerId());
            log.warn("商家分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderShop));
            independentOrderBoLists.add(independentOrderShop);
        });
        log.warn("分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderBoLists));
//        // 去重从新计算之后的传给第三方的分账信息
//        Map<String, BigDecimal> result = splitLists.stream()
//                .collect(Collectors.groupingBy(SplitList::getMerchantId,
//                        Collectors.mapping(s -> new BigDecimal(s.getDivAmt()), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
//
//        List<SplitList> mergedList = Lists.newArrayList();
//        result.forEach((key, value) -> {
//            SplitList newSplitList = new SplitList();
//            newSplitList.setMerchantId(key);
//            newSplitList.setDivAmt(value.toString());
//            mergedList.add(newSplitList);
//        });
//        // 设置分账参数
//        splitInfo.setSplitList(mergedList);
//        // 平台总留存金额
//        splitInfo.setKeepAmt(BigDecimalUtils.yuanToFen(shopOrder.getAdminPrice()).toString());
//        // 分账信息
//        params.setSplitInfo(splitInfo);
//        // 添加分账扩展参数
//        confirm.setExtendParams(params);
//        // 延时分账请求添加分账信息
//        delayConfirmRequest.setDelayConfirmRequest(confirm);
//        log.warn("yi-ma延时分账请求：{}", JSONObject.toJSONString(delayConfirmRequest));
//        DelayConfirmResponse response = Client.getClient().execute(delayConfirmRequest);
//        log.warn("response 延时分账交易返回参数：{}", JSONUtil.toJsonStr(response));
//        // 判断yi-ma延时分账状态
//        List<String> resultList = Lists.newArrayList();
//        resultList.add("9998");
//        resultList.add("0000");
//        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
//            log.error("yi-ma延时分账异常");
//            throw new RuntimeException(response.getResult().getComment());
//        }

        //查询子订单获取产品Id集合
        List<SohuShopOrderInfoVo> orderInfoModels = infoService.getListByOrderNo(shopOrder.getOrderNo());
        Set<Long> productIds = orderInfoModels.stream().map(SohuShopOrderInfoVo::getProductId).collect(Collectors.toSet());

        List<SohuTradeRecordBo> tradeRecords = new ArrayList<>();
        List<SohuMerTradeRecordBo> merTradeRecordBos = new ArrayList<>();
        for (SohuIndependentOrderModel orderBo : independentOrderBoLists) {
            //商户计入商户流水表，非商户计入用户流水明细表
            if (orderBo.getIndependentObject().equals(SohuIndependentObject.shop.getKey())) {
                SohuMerTradeRecordBo tradeRecordBo = new SohuMerTradeRecordBo();
                tradeRecordBo.setUserId(orderBo.getUserId());
                tradeRecordBo.setMerId(shopOrder.getMerId());
                tradeRecordBo.setAmount(masterOrder.getTotalPrice());
                tradeRecordBo.setPrice(orderBo.getIndependentPrice());
                tradeRecordBo.setType(sohuMerchantModel.getIsSelf() ? "Self" : "Supply");
                tradeRecordBo.setProductId(productIds.stream().map(Object::toString).collect(Collectors.joining(",")));
//            tradeRecordBo.setVirtualCoin(); // 暂没有虚拟币扣除，后续扩展
                tradeRecordBo.setCouponId(masterOrder.getCouponId());
                tradeRecordBo.setCouponPrice(masterOrder.getCouponPrice());
                tradeRecordBo.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
                tradeRecordBo.setMsg("商品分账收入");
                tradeRecordBo.setAmountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
                tradeRecordBo.setEffectiveTime(DateUtil.offsetDay(new Date(), 7)); //售后有效期 默认收货后7天
                tradeRecordBo.setOperateChannel(masterOrder.getPayChannel());
                tradeRecordBo.setMasterOrderNo(shopOrder.getMasterOrderNo());
                tradeRecordBo.setOrderNo(orderBo.getOrderNo());
                tradeRecordBo.setPayStatus(PayStatus.Paid.name());
                tradeRecordBo.setTransactionId(masterOrder.getTransactionId());
                tradeRecordBo.setPayTime(shopOrder.getPayTime());
                tradeRecordBo.setIndependent(true);
                tradeRecordBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
                tradeRecordBo.setAccountType(SohuTradeRecordEnum.AccountType.Amount.name());
                tradeRecordBo.setEarningsType(1);
                tradeRecordBo.setOrderTime(masterOrder.getCreateTime());
                merTradeRecordBos.add(tradeRecordBo);
            }
            SohuTradeRecordBo tradeRecordBo = SohuTradeRecordBo.builder().
                    userId(orderBo.getUserId()).
                    type(Good.getCode()).
                    consumeType(Good.getCode()).
                    consumeCode(shopOrder.getMasterOrderNo()).
                    amount(orderBo.getIndependentPrice()).
                    amountType(SohuTradeRecordEnum.AmountType.InCome.getCode()).
                    payType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus()).
                    operateChannel(masterOrder.getPayChannel()).
                    payNumber(masterOrder.getOutTradeNo()).
                    payStatus(PayStatus.Paid.name()).
                    msg("商品分账收入").
                    independent(true).
                    independentObject(orderBo.getIndependentObject()).
                    independentStatus(orderBo.getIndependentStatus()).
                    accountType(SohuTradeRecordEnum.AccountType.Amount.name()).
                    unq(SohuTradeRecordBo.genUnq()).
                    transactionId(masterOrder.getTransactionId()).
                    build();
            tradeRecords.add(tradeRecordBo);
        }
        // 获取子订单
        List<SohuShopOrder> shopOrderListTemp = orderService.getListByMasterNo(shopOrder.getMasterOrderNo());
        List<SohuShopOrder> shopOrderList = shopOrderListTemp.stream().filter(s -> !s.getOrderNo().equals(shopOrder.getOrderNo())).collect(Collectors.toList());
        // 修改后的子单详情
        shopOrderList.add(BeanUtil.copyProperties(shopOrder, SohuShopOrder.class));
        // 计算主订单的总价
        SohuOrderIndependentPriceVo priceVo = extractedMasterOrderPrice(shopOrderList, masterOrder.getOrderNo());
        log.warn("SohuOrderIndependentPriceVo 计算主订单的总价：{}", priceVo);
        // 主订单分销详情
        BigDecimal adminPrice = Objects.requireNonNullElse(CalUtils.sub(priceVo.getAdminPrice(), masterOrder.getChargePrice()), BigDecimal.ZERO);
        masterOrder.setAdminPrice(adminPrice);
        masterOrder.setCountryPrice(Objects.requireNonNullElse(priceVo.getCountryPrice(), BigDecimal.ZERO));
        // 城市站长出平台服务费
        masterOrder.setCityPrice(Objects.requireNonNullElse(priceVo.getCityPrice(), BigDecimal.ZERO));
        masterOrder.setDistributorPrice(masterOrder.getIndependentUserId() != null ? priceVo.getDistributorPrice() : null);
        masterOrder.setInvitePrice(Objects.requireNonNullElse(priceVo.getInvitePrice(), BigDecimal.ZERO));
        masterOrder.setAgencyAdminPrice(Objects.requireNonNullElse(priceVo.getAgencyPrice(), BigDecimal.ZERO));
        return transactionTemplate.execute(e -> {
            orderService.updateById(BeanUtil.copyProperties(shopOrder, SohuShopOrder.class));
            log.warn("masterOrder 开始修改主订单：{}", JSONUtil.toJsonStr(masterOrder));
            masterOrderService.updateByBo(BeanUtil.copyProperties(masterOrder, SohuShopMasterOrderBo.class));
            log.warn("masterOrder 修改主订单完成：{}", JSONUtil.toJsonStr(masterOrder));
            remoteIndependentOrderService.batchInsert(independentOrderBoLists);
            //保存非商户流水明细
            remoteMiddleTradeRecordService.insertBatch(tradeRecords);
            //保存商户流水明细
            remoteMiddleMerTradeRecordService.insertBatch(merTradeRecordBos);
            // todo 延时队列5s后查询分账是否成功-分账成功修改分账订单记录表状态
            return Boolean.TRUE;
        });
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean mqDelayConfirmV2(SohuShopOrderModel shopOrder) {
        /*
         * 先查询主订单
         * 在查询子订单集合
         * 在查询子订单的订单详情
         */
        // 获取主订单
        SohuShopMasterOrderVo masterOrder = masterOrderService.queryByMasterOrderNo(shopOrder.getMasterOrderNo());
        if (ObjectUtils.isNull(masterOrder)) {
            throw new RuntimeException("不存在当前主订单");
        }
        // 延时分账请求流水号
        String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
        shopOrder.setDelayTradeNo(delayOrderNo);
        log.warn("delayOrderNo 延时分账交易流水号：{}", delayOrderNo);
//        SohuIndependentIdBo independentIdBo = new SohuIndependentIdBo();

        Long merId = shopOrder.getMerId();
        List<Long> merIds = Lists.newArrayList();
        merIds.add(merId);
        // 获取所有商户的userId
        Map<Long, SohuMerchantModel> merIdMapByIdList = remoteMerchantService.getMerIdMapByIdList(merIds);
        // 获取到这个店铺信息
        SohuMerchantModel sohuMerchantModel = merIdMapByIdList.get(shopOrder.getMerId());
        BigDecimal zeroPrice = new BigDecimal("0.00");
        // 提取所有对象的 id 成为 List<Long>
        List<Long> idList = merIdMapByIdList.values().stream().map(SohuMerchantModel::getUserId).collect(Collectors.toList());
        //查询商户翼码分账信息
        List<SohuAccountBankModel> merAccount = remoteAccountService.queryListByUserId(idList);
        BigDecimal countryPrice = BigDecimal.ZERO;
        BigDecimal entrancePrice = BigDecimal.ZERO;
        BigDecimal cityPrice = BigDecimal.ZERO;
        BigDecimal industryPrice = BigDecimal.ZERO;
        BigDecimal inviteSitePrice = BigDecimal.ZERO;
        BigDecimal distributorPrice = BigDecimal.ZERO;
        BigDecimal distributorInvitePrice = BigDecimal.ZERO;
        BigDecimal agencyAdminPrice = BigDecimal.ZERO;
        BigDecimal invitePrice = BigDecimal.ZERO;
        BigDecimal adminPrice = BigDecimal.ZERO;
        List<SohuIndependentOrderModel> independentOrderBoLists = Lists.newArrayList();
        List<SohuIndependentTempBo> tempBoList = Lists.newArrayList();
        String productName = "";
        //查询订单详情
        List<SohuShopOrderInfoVo> orderInfoList = infoService.getListByOrderNo(shopOrder.getOrderNo());
        if (CollUtil.isNotEmpty(orderInfoList)){
            productName = orderInfoList.get(0).getProductName();
            for (SohuShopOrderInfoVo item: orderInfoList
                 ) {
                //查询站长信息
//                Set<Long> siteIds = Set.of(item.getCountrySiteId(), item.getEntranceSiteId(), item.getCitySiteId(),item.getIndustrySiteId(), item.getInviteSiteId());
//                Map<Long, SohuSiteVo> siteMap = remoteMiddleSiteService.queryMap(siteIds);
                SohuIndependentIdBo independentIdBo = new SohuIndependentIdBo();
                independentIdBo.setCountryId(item.getCountrySiteUserId());
                independentIdBo.setEntranceSiteId(item.getEntranceSiteUserId());
                independentIdBo.setCityId(item.getCitySiteUserId());
                independentIdBo.setIndustrySiteId(item.getIndustrySiteUserId());
                independentIdBo.setInviteSiteId(item.getInviteSiteUserId());
                independentIdBo.setAgencyId(item.getAgencyUserId());
                independentIdBo.setAdminId(2L);
                independentIdBo.setDistributorId(item.getIndependentUserId());
                independentIdBo.setInviteId(item.getInviteUserId());
                independentIdBo.setDistributorInviteId(item.getIndependentInviteUserId());
                // 查询所有分账人的翼码帐户信息
                List<Long> allUserIds = getAllIds(independentIdBo);
                allUserIds.addAll(idList);
                List<SohuAccountBankModel> accountBankVos = remoteAccountService.queryListByUserId(allUserIds);
                //用户id与翼码映射
                Map<Long, String> bankMap = accountBankVos.stream().collect(Collectors.toMap(SohuAccountBankModel::getUserId, SohuAccountBankModel::getMerchantId));
                //  增加分账订单记录
                BigDecimal platPrice = item.getAdminPrice();
                // 国家站长
                if (independentIdBo.getCountryId() != null && !item.getCountryPrice().equals(zeroPrice) && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getCountryId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(independentIdBo.getCountryId());
                    tempBo.setIndependentObject(SohuIndependentObject.country.getKey());
                    tempBo.setIndependentPrice(item.getCountryPrice());
                    tempBo.setSiteId(item.getCountrySiteId());
                    tempBo.setSiteType(item.getSiteType());
                    tempBoList.add(tempBo);

                    countryPrice.add(item.getCountryPrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getCountryPrice());
                }
                //入口站长
                if (independentIdBo.getEntranceSiteId()!= null && !item.getEntranceSitePrice().equals(zeroPrice) && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getEntranceSiteId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(independentIdBo.getEntranceSiteId());
                    tempBo.setIndependentObject(SohuIndependentObject.entrance.getKey());
                    tempBo.setIndependentPrice(item.getEntranceSitePrice());
                    tempBo.setSiteId(item.getEntranceSiteId());
                    tempBo.setSiteType(item.getSiteType());
                    tempBoList.add(tempBo);

                    entrancePrice.add(item.getEntranceSitePrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getEntranceSitePrice());
                }
                // 城市站长
                if (independentIdBo.getCityId() != null && !item.getCityPrice().equals(zeroPrice) && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getCityId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(independentIdBo.getCityId());
                    tempBo.setIndependentObject(SohuIndependentObject.city.getKey());
                    tempBo.setIndependentPrice(item.getCityPrice());
                    tempBo.setSiteId(item.getCitySiteId());
                    tempBo.setSiteType(Constants.ONE);
                    tempBoList.add(tempBo);

                    cityPrice.add(item.getCityPrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getCityPrice());
                }
                // 行业站长
                if (independentIdBo.getIndustrySiteId() != null && !item.getIndustrySitePrice().equals(zeroPrice) && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getIndustrySiteId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(independentIdBo.getIndustrySiteId());
                    tempBo.setIndependentObject(SohuIndependentObject.industrysite.getKey());
                    tempBo.setIndependentPrice(item.getIndustrySitePrice());
                    tempBo.setSiteId(item.getIndustrySiteId());
                    tempBo.setSiteType(Constants.TWO);
                    tempBoList.add(tempBo);

                    industryPrice.add(item.getIndustrySitePrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getIndustrySitePrice());
                }
                //拉新站长
                if (independentIdBo.getInviteSiteId() != null && !item.getInviteSitePrice().equals(zeroPrice) && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getInviteSiteId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(independentIdBo.getInviteSiteId());
                    tempBo.setIndependentObject(SohuIndependentObject.invitecity.getKey());
                    tempBo.setIndependentPrice(item.getInviteSitePrice());
                    tempBo.setSiteId(item.getInviteSiteId());
                    tempBo.setSiteType(item.getInviteSiteType());
                    tempBoList.add(tempBo);

                    inviteSitePrice.add(item.getInviteSitePrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getInviteSitePrice());
                }
                //分销人
                if (null != item.getIndependentUserId() && item.getDistributorPrice() != null && !shopOrder.getDistributorPrice().equals(zeroPrice)
                        && StrUtil.isNotEmpty(bankMap.get(item.getIndependentUserId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(item.getIndependentUserId());
                    tempBo.setIndependentObject(SohuIndependentObject.distribution.getKey());
                    tempBo.setIndependentPrice(item.getDistributorPrice());
                    tempBo.setSiteId(item.getEntranceSiteId());
                    tempBoList.add(tempBo);

                    distributorPrice.add(item.getDistributorPrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getDistributorPrice());
                }
                //拉新人
                if (null != item.getInviteUserId() && item.getInvitePrice() != null && !item.getInvitePrice().equals(zeroPrice)
                        && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getInviteId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(item.getInviteUserId());
                    tempBo.setIndependentObject(SohuIndependentObject.invite.getKey());
                    tempBo.setIndependentPrice(item.getInvitePrice());
                    tempBo.setSiteId(item.getEntranceSiteId());
                    tempBoList.add(tempBo);

                    invitePrice.add(item.getInvitePrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getInvitePrice());
                }
                // 代理人
                if (independentIdBo.getAgencyId() != null&& item.getAgencyAdminPrice() != null && !item.getAgencyAdminPrice().equals(zeroPrice) && !item.getAgencyAdminPrice().toPlainString().equals("0")
                        && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getAgencyId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(independentIdBo.getAgencyId());
                    tempBo.setIndependentObject(SohuIndependentObject.agency.getKey());
                    tempBo.setIndependentPrice(item.getAgencyAdminPrice());
                    tempBo.setSiteId(item.getEntranceSiteId());
                    tempBoList.add(tempBo);

                    agencyAdminPrice.add(item.getAgencyAdminPrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getAgencyAdminPrice());
                }
                // 分销人的拉新人
                if (null != item.getIndependentInviteUserId() && item.getDistributorInvitePrice() != null && !item.getDistributorInvitePrice().equals(zeroPrice) && !item.getDistributorInvitePrice().toPlainString().equals("0")
                        && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getDistributorInviteId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(item.getIndependentInviteUserId());
                    tempBo.setIndependentObject(SohuIndependentObject.distributionInvite.getKey());
                    tempBo.setIndependentPrice(item.getDistributorInvitePrice());
                    tempBo.setSiteId(item.getEntranceSiteId());
                    tempBoList.add(tempBo);

                    distributorInvitePrice.add(item.getDistributorInvitePrice());
                } else {
                    platPrice = CalUtils.add(platPrice, item.getDistributorInvitePrice());
                }

                if (shopOrder.getAdminPrice() != null && !shopOrder.getAdminPrice().equals(zeroPrice) && StrUtil.isNotEmpty(bankMap.get(independentIdBo.getAdminId()))) {
                    SohuIndependentTempBo tempBo = new SohuIndependentTempBo();
                    tempBo.setUserId(2L);
                    tempBo.setIndependentObject(SohuIndependentObject.platform.getKey());
                    tempBo.setIndependentPrice(platPrice);
                    tempBo.setSiteId(item.getEntranceSiteId());
                    tempBoList.add(tempBo);

                    adminPrice.add(platPrice);
                }
            }
        }
        //整合分账信息，相同用户相同分账角色相同站点对象整合，分账金额累加
        if (CollUtil.isNotEmpty(tempBoList)){
            // 使用Java Stream进行分组和金额累加
            List<SohuIndependentTempBo> mergedList = tempBoList.stream()
                    .collect(Collectors.groupingBy(
                            tempBo -> new AbstractMap.SimpleEntry<>(
                                    new AbstractMap.SimpleEntry<>(tempBo.getUserId(), tempBo.getIndependentObject()),
                                    tempBo.getSiteId()
                            ),
                            Collectors.reducing(
                                    new SohuIndependentTempBo(), // 初始值
                                    (existing, replacement) -> {
                                        // 合并操作：如果存在相同的键，则累加金额
                                        existing.setIndependentPrice(
                                                existing.getIndependentPrice().add(replacement.getIndependentPrice())
                                        );
                                        return existing;
                                    }
                            )
                    ))
                    .values().stream()
                    .filter(mergedBo -> mergedBo.getUserId() != null && mergedBo.getIndependentPrice().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());
            for (SohuIndependentTempBo independentTempBo : mergedList){
                SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
                independentOrderBo.setOrderNo(shopOrder.getOrderNo());
                independentOrderBo.setTradeType(BusyType.Goods.name());
                independentOrderBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
                independentOrderBo.setTradeNo(delayOrderNo);
                independentOrderBo.setUserId(independentTempBo.getUserId());
                independentOrderBo.setSiteType(independentOrderBo.getSiteType());
                independentOrderBo.setSiteId(independentTempBo.getSiteId());
                independentOrderBo.setIndependentObject(independentTempBo.getIndependentObject());
                independentOrderBo.setIndependentPrice(independentTempBo.getIndependentPrice());
                independentOrderBo.setMerId(shopOrder.getMerId());
                independentOrderBo.setTaskFullAmount(shopOrder.getPayPrice());
                independentOrderBo.setConsumerUserId(shopOrder.getUserId());
                independentOrderBo.setTaskTitle(productName);
                independentOrderBoLists.add(independentOrderBo);
            }
        }
        // 计算商户的留存金额
        BigDecimal shopPrice = CalUtils.sub(shopOrder.getPayPrice(), shopOrder.getChargePrice(),countryPrice, entrancePrice,
                cityPrice, industryPrice, inviteSitePrice,distributorPrice,distributorInvitePrice,agencyAdminPrice,invitePrice, adminPrice);
        if (CollUtil.isNotEmpty(merAccount) && StrUtil.isNotEmpty(merAccount.get(0).getMerchantId())) {
            // 商家分账信息
            SohuIndependentOrderModel independentOrderShop = new SohuIndependentOrderModel();
            independentOrderShop.setOrderNo(shopOrder.getOrderNo());
            independentOrderShop.setTradeType(BusyType.Goods.name());
            independentOrderShop.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderShop.setTradeNo(delayOrderNo);
            independentOrderShop.setUserId(sohuMerchantModel.getUserId());
            independentOrderShop.setSiteType(Constants.ONE);
            independentOrderShop.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderShop.setIndependentObject(SohuIndependentObject.shop.getKey());
            independentOrderShop.setIndependentPrice(shopPrice);
            independentOrderShop.setMerId(shopOrder.getMerId());
            independentOrderShop.setTaskFullAmount(shopOrder.getPayPrice());
            independentOrderShop.setTaskTitle(productName);
            independentOrderShop.setConsumerUserId(shopOrder.getUserId());
            log.warn("商家分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderShop));
            independentOrderBoLists.add(independentOrderShop);
        }
        log.warn("分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderBoLists));

        //查询子订单获取产品Id集合
        List<SohuShopOrderInfoVo> orderInfoModels = infoService.getListByOrderNo(shopOrder.getOrderNo());
        Set<Long> productIds = orderInfoModels.stream().map(SohuShopOrderInfoVo::getProductId).collect(Collectors.toSet());

        List<SohuTradeRecordBo> tradeRecords = new ArrayList<>();
        List<SohuMerTradeRecordBo> merTradeRecordBos = new ArrayList<>();
        for (SohuIndependentOrderModel orderBo : independentOrderBoLists) {
            //商户计入商户流水表，非商户计入用户流水明细表
            if (orderBo.getIndependentObject().equals(SohuIndependentObject.shop.getKey())) {
                SohuMerTradeRecordBo tradeRecordBo = new SohuMerTradeRecordBo();
                tradeRecordBo.setUserId(orderBo.getUserId());
                tradeRecordBo.setMerId(shopOrder.getMerId());
                tradeRecordBo.setAmount(masterOrder.getTotalPrice());
                tradeRecordBo.setPrice(orderBo.getIndependentPrice());
                tradeRecordBo.setType(sohuMerchantModel.getIsSelf() ? "Self" : "Supply");
                tradeRecordBo.setProductId(productIds.stream().map(Object::toString).collect(Collectors.joining(",")));
//            tradeRecordBo.setVirtualCoin(); // 暂没有虚拟币扣除，后续扩展
                tradeRecordBo.setCouponId(masterOrder.getCouponId());
                tradeRecordBo.setCouponPrice(masterOrder.getCouponPrice());
                tradeRecordBo.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
                tradeRecordBo.setMsg("商品分账收入");
                tradeRecordBo.setAmountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
                tradeRecordBo.setEffectiveTime(DateUtil.offsetDay(new Date(), 7)); //售后有效期 默认收货后7天
                tradeRecordBo.setOperateChannel(masterOrder.getPayChannel());
                tradeRecordBo.setMasterOrderNo(shopOrder.getMasterOrderNo());
                tradeRecordBo.setOrderNo(orderBo.getOrderNo());
                tradeRecordBo.setPayStatus(PayStatus.Paid.name());
                tradeRecordBo.setTransactionId(masterOrder.getTransactionId());
                tradeRecordBo.setPayTime(shopOrder.getPayTime());
                tradeRecordBo.setIndependent(true);
                tradeRecordBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTING.getCode());
                tradeRecordBo.setAccountType(SohuTradeRecordEnum.AccountType.Amount.name());
                tradeRecordBo.setEarningsType(1);
                tradeRecordBo.setOrderTime(masterOrder.getCreateTime());
                merTradeRecordBos.add(tradeRecordBo);
            }
            SohuTradeRecordBo tradeRecordBo = SohuTradeRecordBo.builder().
                    userId(orderBo.getUserId()).
                    type(Good.getCode()).
                    consumeType(Good.getCode()).
                    consumeCode(shopOrder.getMasterOrderNo()).
                    amount(orderBo.getIndependentPrice()).
                    amountType(SohuTradeRecordEnum.AmountType.InCome.getCode()).
                    payType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus()).
                    operateChannel(masterOrder.getPayChannel()).
                    payNumber(masterOrder.getOutTradeNo()).
                    payStatus(PayStatus.Paid.name()).
                    msg("商品分账收入").
                    independent(true).
                    independentObject(orderBo.getIndependentObject()).
                    independentStatus(orderBo.getIndependentStatus()).
                    accountType(SohuTradeRecordEnum.AccountType.Amount.name()).
                    unq(SohuTradeRecordBo.genUnq()).
                    transactionId(masterOrder.getTransactionId()).
                    build();
            tradeRecords.add(tradeRecordBo);
        }
        // 获取子订单
        List<SohuShopOrder> shopOrderListTemp = orderService.getListByMasterNo(shopOrder.getMasterOrderNo());
        List<SohuShopOrder> shopOrderList = shopOrderListTemp.stream().filter(s -> !s.getOrderNo().equals(shopOrder.getOrderNo())).collect(Collectors.toList());
        // 修改后的子单详情
        shopOrderList.add(BeanUtil.copyProperties(shopOrder, SohuShopOrder.class));
        // 计算主订单的总价
        SohuOrderIndependentPriceVo priceVo = extractedMasterOrderPrice(shopOrderList, masterOrder.getOrderNo());
        log.warn("SohuOrderIndependentPriceVo 计算主订单的总价：{}", priceVo);
        // 主订单分销详情
        BigDecimal adminPrice1 = Objects.requireNonNullElse(CalUtils.sub(priceVo.getAdminPrice(), masterOrder.getChargePrice()), BigDecimal.ZERO);
        masterOrder.setAdminPrice(adminPrice1);
        masterOrder.setCountryPrice(Objects.requireNonNullElse(priceVo.getCountryPrice(), BigDecimal.ZERO));
        // 城市站长出平台服务费
        masterOrder.setCityPrice(Objects.requireNonNullElse(priceVo.getCityPrice(), BigDecimal.ZERO));
        masterOrder.setDistributorPrice(masterOrder.getIndependentUserId() != null ? priceVo.getDistributorPrice() : null);
        masterOrder.setInvitePrice(Objects.requireNonNullElse(priceVo.getInvitePrice(), BigDecimal.ZERO));
        masterOrder.setAgencyAdminPrice(Objects.requireNonNullElse(priceVo.getAgencyPrice(), BigDecimal.ZERO));
        return transactionTemplate.execute(e -> {
            orderService.updateById(BeanUtil.copyProperties(shopOrder, SohuShopOrder.class));
            log.warn("masterOrder 开始修改主订单：{}", JSONUtil.toJsonStr(masterOrder));
            masterOrderService.updateByBo(BeanUtil.copyProperties(masterOrder, SohuShopMasterOrderBo.class));
            log.warn("masterOrder 修改主订单完成：{}", JSONUtil.toJsonStr(masterOrder));
            remoteIndependentOrderService.batchInsert(independentOrderBoLists);
            //保存非商户流水明细
            remoteMiddleTradeRecordService.insertBatch(tradeRecords);
            //保存商户流水明细
            remoteMiddleMerTradeRecordService.insertBatch(merTradeRecordBos);
            // todo 延时队列5s后查询分账是否成功-分账成功修改分账订单记录表状态
            //同步收入统计
            MqMessaging mqCancelMessaging = new MqMessaging(shopOrder.getOrderNo(), MqKeyEnum.INCOME_INFO.getKey());
            remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);
            return Boolean.TRUE;
        });
    }

    /**
     * 计算主订单的总价
     *
     * @param orderModelList
     * @param masterOrderNo
     */
    private static SohuOrderIndependentPriceVo extractedMasterOrderPrice
    (List<SohuShopOrder> orderModelList, String masterOrderNo) {
        SohuOrderIndependentPriceVo priceVo = new SohuOrderIndependentPriceVo();
        // 定义字段名和对应提取器的映射
        Map<String, Function<SohuShopOrder, BigDecimal>> priceMappings = Map.of(
                "adminPrice", SohuShopOrder::getAdminPrice,
                "countryPrice", SohuShopOrder::getCountryPrice,
                "cityPrice", SohuShopOrder::getCityPrice,
                "invitePrice", SohuShopOrder::getInvitePrice,
                "distributorPrice", SohuShopOrder::getDistributorPrice,
                "distributorInvitePrice", SohuShopOrder::getDistributorInvitePrice,
                "agencyPrice", SohuShopOrder::getAgencyAdminPrice
        );
        // 循环处理每个字段
        for (Map.Entry<String, Function<SohuShopOrder, BigDecimal>> entry : priceMappings.entrySet()) {
            String fieldName = entry.getKey();
            Function<SohuShopOrder, BigDecimal> valueExtractor = entry.getValue();
            // 获取满足条件的值
            List<BigDecimal> matchingValues = orderModelList.stream()
                    .filter(item -> masterOrderNo.equals(item.getMasterOrderNo()))
                    .map(valueExtractor)
                    .filter(Objects::nonNull)  // 过滤掉为null的值
                    .collect(Collectors.toList());
            // 判断是否需要计算和
            if (!matchingValues.isEmpty()) {
                // 计算和
                BigDecimal sum = matchingValues.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                // 设置和到priceVo对象的相应字段
                switch (fieldName) {
                    case "adminPrice":
                        // 平台
                        priceVo.setAdminPrice(sum);
                        break;
                    case "countryPrice":
                        // 国家站长
                        priceVo.setCountryPrice(sum);
                        break;
                    case "cityPrice":
                        // 城市站长
                        priceVo.setCityPrice(sum);
                        break;
                    case "invitePrice":
                        // 拉新人
                        priceVo.setInvitePrice(sum);
                        break;
                    case "distributorPrice":
                        // 分销人
                        priceVo.setDistributorPrice(sum);
                        break;
                    case "distributorInvitePrice":
                        priceVo.setDistributorInvitePrice(sum);
                        break;
                    case "agencyPrice":
                        priceVo.setAgencyPrice(sum);
                        break;
                    // 可以添加其他字段的处理
                    default:
                        throw new IllegalArgumentException("Unknown field: " + fieldName);
                }
            }
        }
        return priceVo.setShopOrderNo(masterOrderNo);
    }

    /**
     * 根据对象获取id集合
     *
     * @param bo
     */
    public static List<Long> getAllIds(SohuIndependentIdBo bo) {
        return Stream.of(bo.getAdminId(), bo.getCountryId(), bo.getCityId(), bo.getDistributorId(), bo.getInviteId(),
                bo.getDistributorInviteId(), bo.getAgencyId(),bo.getEntranceSiteId(),bo.getIndustrySiteId(),bo.getInviteSiteId()).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取翼码支付配置
     *
     * @return
     */
    protected YiMaPayConfig getYiMaPayConfig() {
        SysDictData dictData = remoteDictService.getDictData(DictEnum.payConfig.getKey(), DictEnum.YMPayConfig.getKey());
        Objects.requireNonNull(dictData, "翼码支付配置为空");
        String dictValue = dictData.getDictValue();
        return JSONUtil.toBean(dictValue, YiMaPayConfig.class);
    }

    /**
     * 主订单号
     *
     * @param operateBo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mqUpdateBatchMerchants(SohuOperateBo operateBo, Boolean isRefund) {
        // 查询所有子订单
        List<SohuShopOrder> shopOrderModels = orderService.getListByMasterNo(operateBo.getOrderNo());
        // 修改店铺集合对象
        List<SohuMerchantModel> updateMerchantModels = Lists.newArrayList();
        // 不是退货就不修改店铺销售数量
        if (!isRefund) {
            // 获取所有商铺id
            List<Long> merIds = shopOrderModels.stream().distinct().map(SohuShopOrder::getMerId).collect(Collectors.toList());
            // 获取所有店铺信息
            Map<Long, SohuMerchantModel> merchantModels = remoteMerchantService.getMerIdMapByIdList(merIds);
            for (SohuShopOrder shopOrderModel : shopOrderModels) {
                SohuMerchantModel sohuMerchantModel = merchantModels.get(shopOrderModel.getMerId());
                sohuMerchantModel.setSaleNums(sohuMerchantModel.getSaleNums() + shopOrderModel.getTotalNum());
                updateMerchantModels.add(sohuMerchantModel);
            }
        }
        // mcn相关操作
        // 获取所有子订单号
        List<String> orderNos = shopOrderModels.stream().distinct().map(SohuShopOrder::getOrderNo).collect(Collectors.toList());
        List<SohuShopOrderInfoVo> orderInfoModels = infoService.getListByOrderNos(orderNos);
        // 取出所有的userId和productId
        List<Long> windowUserIds = new ArrayList<>();
        List<Long> windowProductIds = new ArrayList<>();
        List<Long> windowMcnUserIds = new ArrayList<>();
        List<Long> windowMcnProductIds = new ArrayList<>();
        extractedIds(orderInfoModels, windowUserIds, windowProductIds, windowMcnUserIds, windowMcnProductIds);
        // 个人商品橱窗
        List<SohuProductWindowModel> productWindowModels = remoteProductService.selectByUserIdsAndProIds(windowUserIds, windowProductIds);
        if (CollUtil.isNotEmpty(productWindowModels)) {
            productWindowModels.forEach(productWindowModel -> {
                orderInfoModels.stream()
                        .filter(orderInfoModel -> Objects.equals(productWindowModel.getProductId(), orderInfoModel.getProductId()))
                        .filter(orderInfoModel -> {
                            // 是否分销人=商品橱窗人-不带mcn机构
                            boolean isIndependent = Objects.equals(orderInfoModel.getIndependentUserId(), productWindowModel.getUserId());
                            // 是否是mcn机构
                            boolean isMcn = Objects.equals(orderInfoModel.getMcnId(), productWindowModel.getMcnId());
                            // 是否分销人=商品橱窗人-带mcn机构
                            boolean isOriginator = Objects.equals(orderInfoModel.getOriginatorUserId(), productWindowModel.getUserId());
                            // 返回不通选项
                            return (isMcn && isOriginator) || isIndependent;
                        })
                        .forEach(orderInfoModel ->
                                productWindowModel.setSaleCount(productWindowModel.getSaleCount() + (operateBo.getOperate() ?
                                        orderInfoModel.getPayNum() : -orderInfoModel.getPayNum())));
            });
        }

        // mcn商品橱窗
        List<SohuProductWindowMcnModel> productWindowMcnModels = remoteProductService.selectMcnByUserIdsAndProIds(windowMcnUserIds, windowMcnProductIds);
        if (CollUtil.isNotEmpty(productWindowMcnModels)) {
            productWindowMcnModels.forEach(productWindowMcnModel -> {
                orderInfoModels.stream()
                        .filter(orderInfoModel ->
                                Objects.equals(productWindowMcnModel.getProductId(), orderInfoModel.getProductId())
                                        && Objects.equals(orderInfoModel.getMcnId(), productWindowMcnModel.getUserId())
                        )
                        .forEach(orderInfoModel ->
                                productWindowMcnModel.setSaleCount(productWindowMcnModel.getSaleCount() + (operateBo.getOperate() ?
                                        orderInfoModel.getPayNum() : -orderInfoModel.getPayNum())));
            });
        }

        // todo 批量修改mcn商品橱窗
        Boolean execute = transactionTemplate.execute(e -> {
            if (!isRefund) {
                // 商铺数量修改
                if (CollUtil.isNotEmpty(updateMerchantModels)) {
                    remoteMerchantService.updateBatch(updateMerchantModels);
                }
            }
            // 个人商铺橱窗修改
            if (CollUtil.isNotEmpty(productWindowModels)) {
                remoteProductService.updateBatchWindow(productWindowModels);
            }
            // mcn商铺橱窗修改
            if (CollUtil.isNotEmpty(productWindowMcnModels)) {
                remoteProductService.updateBatchWindowMcn(productWindowMcnModels);
            }
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            throw new ServiceException("商品数量相关更新失败");
        }
    }

    @Override
    public Boolean updateReplyStatus(String orderNo, Long orderInfoId, Integer isReply) {
        this.orderService.updateReplyStatus(orderNo, isReply);
        this.infoService.updateReplyStatus(orderInfoId, isReply);
        return true;
    }

    /**
     * 组装ids
     *
     * @param orderInfoModels
     * @param windowUserIds
     * @param windowProductIds
     * @param windowMcnUserIds
     * @param windowMcnProductIds
     */
    private static void extractedIds(List<SohuShopOrderInfoVo> orderInfoModels, List<Long> windowUserIds, List<Long> windowProductIds, List<Long> windowMcnUserIds, List<Long> windowMcnProductIds) {
        for (SohuShopOrderInfoVo info : orderInfoModels) {
            if (null != info.getMcnId()) {
                windowMcnUserIds.add(info.getMcnId());
                windowMcnProductIds.add(info.getProductId());
                windowUserIds.add(info.getOriginatorUserId());
            } else {
                windowUserIds.add(info.getIndependentUserId());
            }
            windowProductIds.add(info.getProductId());
        }
    }

}
