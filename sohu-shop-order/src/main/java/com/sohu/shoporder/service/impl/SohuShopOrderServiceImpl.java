package com.sohu.shoporder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.kuaidi100.sdk.request.QueryTrackParam;
import com.sohu.admin.api.RemoteMerchantBondService;
import com.sohu.admin.api.RemoteMerchantSalesService;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.bo.SohuMerchantSalesReportBo;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.SohuMerchantSalesReportVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.*;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.noticeContent.NoticeShopBuyContent;
import com.sohu.im.api.noticeContent.NoticeShopBuyContentDetail;
import com.sohu.middle.api.bo.SohuOperateBo;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.mcn.RemoteMiddleMcnUserService;
import com.sohu.middle.api.service.notice.RemoteMiddleShopNoticeService;
import com.sohu.middle.api.service.shop.RemoteMiddleOpenClientMerchantService;
import com.sohu.middle.api.vo.SohuOrderIndependentPriceVo;
import com.sohu.middle.api.vo.SohuRegionVo;
import com.sohu.middle.api.vo.SohuUserAddressVo;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import com.sohu.middle.api.vo.shop.SohuOpenClientMerchantVo;
import com.sohu.pay.api.RemoteAccountProcessService;
import com.sohu.pay.api.RemoteIndependentLevelService;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.bo.AccountPlatformBo;
import com.sohu.pay.api.model.SohuIndependentLevelConfigModel;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pm.api.RemotePmSharePubService;
import com.sohu.pm.api.vo.SohuPmSharePubVo;
import com.sohu.resource.api.RemoteJiguangService;
import com.sohu.resource.api.domain.bo.SohuJiguangPush2UserReqBo;
import com.sohu.shopgoods.api.*;
import com.sohu.shopgoods.api.bo.SohuFreightPayBo;
import com.sohu.shopgoods.api.model.SohuProductAttrValueModel;
import com.sohu.shopgoods.api.model.SohuProductModel;
import com.sohu.shopgoods.api.model.SohuShopCartModel;
import com.sohu.shopgoods.api.vo.SohuFreightPayVo;
import com.sohu.shopgoods.api.vo.SohuFreightTemplateVo;
import com.sohu.shoporder.api.bo.*;
import com.sohu.shoporder.api.constants.RefundConstants;
import com.sohu.shoporder.api.constants.RefundTypeEnums;
import com.sohu.shoporder.api.domain.*;
import com.sohu.shoporder.api.model.*;
import com.sohu.shoporder.api.vo.*;
import com.sohu.shoporder.domain.*;
import com.sohu.shoporder.mapper.SohuShopOrderMapper;
import com.sohu.shoporder.mapper.SohuShopRefundOrderMapper;
import com.sohu.shoporder.service.*;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.third.wechat.pay.util.KuaiDiUtil;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商户订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuShopOrderServiceImpl implements ISohuShopOrderService {

    /**
     * 百分百 1.00
     */
    private static final BigDecimal RATION = new BigDecimal(1);
    //毫秒换算一周
    private final static long WEEK_MATH = 7 * 1000 * 60 * 60 * 24;
    //毫秒换算天数
    private final static long DAY_MATH = 1000 * 60 * 60 * 24;
    //毫秒换算小时
    private final static long HOUR_MATH = 1000 * 60 * 60;
    private final SohuShopOrderMapper baseMapper;
    private final SohuShopRefundOrderMapper shopRefundOrderMapper;
    private final AsyncConfig asyncConfig;

    @Resource
    private ISohuShopOrderStatusService statusService;
    @Resource
    private ISohuShopOrderLogisticsService logisticsService;
    @Resource
    private ISohuShopOrderInfoService infoService;
    @Resource
    private ISohuShopMasterOrderService masterOrderService;
    @Resource
    private IPlayletShopOrderService playletShopOrderService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private final SohuShopRefundOrderMapper refundOrderMapper;
    @Resource
    private ISohuShopOrderRefundStatusService refundStatusService;
    @Resource
    private SohuOrderSyncService orderSyncService;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteShopCartAttrService remoteShopCartAttrService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteProductAttrValueService remoteProductAttrValueService;
    @DubboReference
    private RemoteJiguangService remoteJiguangService;
    @DubboReference
    private RemoteProductAttrValueService productAttrValueService;
    @DubboReference
    private RemoteFreightTemplateService remoteFreightTemplateService;
    @DubboReference
    private RemoteFreightPayService remoteFreightPayService;
    @DubboReference
    private RemoteMiddleExpressService remoteExpressService;
    @DubboReference
    private RemoteUserService userService;
    @DubboReference
    private RemoteIndependentTemplateService remoteTemplateService;
    @DubboReference
    private RemoteIndependentLevelService remoteIndependentLevelService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteMiddleViewRecordService remoteMiddleViewRecordService;
    @DubboReference
    private RemoteMiddleInviteService remoteMiddleInviteService;
    @DubboReference
    private RemoteMiddleMcnUserService remoteMcnUserService;
    @DubboReference
    private RemoteMiddleShopNoticeService remoteMiddleShopNoticeService;
    @DubboReference
    private RemoteMiddleUserAddressService remoteMiddleUserAddressService;
    @DubboReference
    private RemoteMiddleRegionService remoteMiddleRegionService;
    @DubboReference
    private RemoteMiddleOpenClientMerchantService remoteMiddleOpenClientMerchantService;
    @DubboReference
    private RemotePmSharePubService remotePmSharePubService;
    @DubboReference
    private RemoteMerchantSalesService remoteMerchantSalesService;
    @DubboReference
    private RemoteProductCategoryPcService remoteProductCategoryPcService;
    @DubboReference
    private RemoteMerchantBondService remoteMerchantBondService;
    @DubboReference
    private RemotePlatformIndustryService remotePlatformIndustryService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;

    @DubboReference
    private RemoteAccountProcessService remoteAccountProcessService;

    /**
     * 统计默认值
     */
    private static final Long DEFAULT_STAT_VALUE = 0L;

    /**
     * 查询商户订单
     */
    @Override
    public SohuShopOrderVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商户订单列表
     */
    @Override
    public TableDataInfo<SohuShopOrderVo> queryPageList(SohuShopOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuShopOrder> lqw = buildQueryWrapper(bo);
        Page<SohuShopOrderVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商户订单列表
     */
    @Override
    public List<SohuShopOrderVo> queryList(SohuShopOrderBo bo) {
        LambdaQueryWrapper<SohuShopOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuShopOrder> buildQueryWrapper(SohuShopOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), SohuShopOrder::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getMasterOrderNo()), SohuShopOrder::getMasterOrderNo, bo.getMasterOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPayOrderNo()), SohuShopOrder::getPayOrderNo, bo.getPayOrderNo());
        lqw.eq(null != bo.getMerId(), SohuShopOrder::getMerId, bo.getMerId());
        lqw.eq(null != bo.getUserId(), SohuShopOrder::getUserId, bo.getUserId());
        lqw.eq(null != bo.getOrderSource(), SohuShopOrder::getOrderSource, bo.getOrderSource());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), SohuShopOrder::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiverName()), SohuShopOrder::getReceiverName, bo.getReceiverName());
        lqw.eq(StringUtils.isNotBlank(bo.getUserPhone()), SohuShopOrder::getUserPhone, bo.getUserPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getUserDetailAddress()), SohuShopOrder::getUserDetailAddress, bo.getUserDetailAddress());
        lqw.eq(null != bo.getTotalNum(), SohuShopOrder::getTotalNum, bo.getTotalNum());
        lqw.eq(bo.getProductTotalPrice() != null, SohuShopOrder::getProductTotalPrice, bo.getProductTotalPrice());
        lqw.eq(bo.getTotalPostage() != null, SohuShopOrder::getTotalPostage, bo.getTotalPostage());
        lqw.eq(bo.getTotalPrice() != null, SohuShopOrder::getTotalPrice, bo.getTotalPrice());
        lqw.eq(bo.getPayPrice() != null, SohuShopOrder::getPayPrice, bo.getPayPrice());
        lqw.eq(bo.getPayPostage() != null, SohuShopOrder::getPayPostage, bo.getPayPostage());
        lqw.eq(null != bo.getCouponId(), SohuShopOrder::getCouponId, bo.getCouponId());
        lqw.eq(bo.getCouponPrice() != null, SohuShopOrder::getCouponPrice, bo.getCouponPrice());
        lqw.eq(null != bo.getPaid(), SohuShopOrder::getPaid, bo.getPaid());
        lqw.eq(bo.getPayTime() != null, SohuShopOrder::getPayTime, bo.getPayTime());
        lqw.eq(StringUtils.isNotBlank(bo.getPayType()), SohuShopOrder::getPayType, bo.getPayType());
        lqw.eq(bo.getPayIntegral() != null, SohuShopOrder::getPayIntegral, bo.getPayIntegral());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SohuShopOrder::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundStatus()), SohuShopOrder::getRefundStatus, bo.getRefundStatus());
        lqw.eq(bo.getIsReply() != null, SohuShopOrder::getIsReply, bo.getIsReply());
        lqw.like(StringUtils.isNotBlank(bo.getDeliveryName()), SohuShopOrder::getDeliveryName, bo.getDeliveryName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeliveryCode()), SohuShopOrder::getDeliveryCode, bo.getDeliveryCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDeliveryNo()), SohuShopOrder::getDeliveryNo, bo.getDeliveryNo());
        lqw.eq(StringUtils.isNotBlank(bo.getUserRemark()), SohuShopOrder::getUserRemark, bo.getUserRemark());
        lqw.eq(StringUtils.isNotBlank(bo.getMerRemark()), SohuShopOrder::getMerRemark, bo.getMerRemark());
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformRemark()), SohuShopOrder::getPlatformRemark, bo.getPlatformRemark());
        lqw.eq(bo.getIsMerchantDel() != null, SohuShopOrder::getIsMerchantDel, bo.getIsMerchantDel());
        lqw.eq(bo.getIsUserDel() != null, SohuShopOrder::getIsUserDel, bo.getIsUserDel());
        lqw.eq(null != bo.getIsUserCancel(), SohuShopOrder::getIsUserCancel, bo.getIsUserCancel());
        if (StringUtils.isNotBlank(bo.getStartTime())) {
            lqw.ge(StringUtils.isNotBlank(bo.getStartTime()), SohuEntity::getCreateTime, DateUtils.beginOfTime(bo.getStartTime()));
        }
        if (StringUtils.isNotBlank(bo.getStartTime())) {
            lqw.le(StringUtils.isNotBlank(bo.getEndTime()), SohuEntity::getCreateTime, DateUtils.endOfTime(bo.getEndTime()));
        }
        return lqw;
    }

    /**
     * 新增商户订单
     */
    @Override
    public Boolean insertByBo(SohuShopOrderBo bo) {
        SohuShopOrder add = BeanUtil.toBean(bo, SohuShopOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商户订单
     */
    @Override
    public Boolean updateByBo(SohuShopOrderBo bo) {
        SohuShopOrder update = BeanUtil.toBean(bo, SohuShopOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuShopOrder entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商户订单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<SohuShopOrderVo> queryStorePageList(SohuStatusOrderBo request, PageQuery pageQuery) {
        QueryWrapper<SohuShopOrder> queryWrapper = new QueryWrapper<>();
        // 添加查询商户id
        queryWrapper.select("order_no", "master_order_no", "receiver_name", "pay_price", "pay_type", "paid", "status", "refund_status", "is_user_del", "user_remark", "create_time", "mer_remark");
        if (null == request.getMerId()) {
            // 没有传查所有
            List<Long> idList = remoteMerchantService.selectByUserId(LoginHelper.getUserId()).stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(idList)) {
                queryWrapper.in("mer_id", idList);
            }
        } else {
            // 根据商户id查询
            queryWrapper.eq("mer_id", request.getMerId());
        }
        // 是否有订单号
        if (StrUtil.isNotBlank(request.getOrderNo())) {
            queryWrapper.eq("order_no", request.getOrderNo());
        }
        // 时间
        if (ObjectUtil.isNotNull(request.getStartTime()) && ObjectUtil.isNotNull(request.getEndTime())) {
            queryWrapper.between("create_time", request.getStartTime(), request.getEndTime());
        }
        // 组装状态sql
        getMerchantStatusWhere(queryWrapper, request.getStatusType());
        queryWrapper.orderByDesc("id");
        Page<SohuShopOrderVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), queryWrapper);
        if (ObjectUtil.isNull(result)) {
            return TableDataInfoUtils.build();
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public SohuShopOrderNumVo queryStoreNum(Date startTime, Date endTime, Long merId) {
        SohuShopOrderNumVo response = new SohuShopOrderNumVo();
        // 全部订单
        response.setAll(getCount(startTime, endTime, OrderConstants.MERCHANT_ORDER_STATUS_ALL, merId));
        // 未支付订单
        response.setUnPaid(getCount(startTime, endTime, OrderConstants.MERCHANT_ORDER_STATUS_UNPAID, merId));
        // 未发货订单
        response.setNotShipped(getCount(startTime, endTime, OrderConstants.ORDER_STATUS_SHIPPING, merId));
        // 待收货订单
        response.setSpike(getCount(startTime, endTime, OrderConstants.ORDER_STATUS_AWAIT_RECEIVING, merId));
        // 交易完成订单
        response.setComplete(getCount(startTime, endTime, OrderConstants.ORDER_STATUS_OVER, merId));
        // 已退款订单
        response.setRefunded(getCount(startTime, endTime, OrderConstants.MERCHANT_ORDER_STATUS_REFUNDED, merId));
        // 已删除订单
        response.setDeleted(getCount(startTime, endTime, OrderConstants.MERCHANT_ORDER_STATUS_DELETED, merId));
        return response;
    }

    @Override
    public Boolean deleteById(String orderNo) {
        SohuShopOrder storeOrder = getInfoByMerIdAndOrderNo(orderNo);
        if (!storeOrder.getIsUserDel()) {
            throw new RuntimeException("无法删除用户未删除的订单！");
        }
        if (storeOrder.getIsMerchantDel()) {
            throw new RuntimeException("此订单已经被删除了!");
        }
        storeOrder.setIsMerchantDel(true);
        return this.baseMapper.updateById(storeOrder) > 0;
    }

    @Override
    public Boolean remark(SohuRemarkOrderBo bo) {
        SohuShopOrder storeOrder = getInfoByMerIdAndOrderNo(bo.getOrderNo());
        storeOrder.setMerRemark(bo.getRemark());
        return this.baseMapper.updateById(storeOrder) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean send(SohuSendOrderBo bo) {
        SohuShopOrder storeOrder = getInfoByMerIdAndOrderNo(bo.getOrderNo());
        if (storeOrder.getIsMerchantDel()) {
            throw new RuntimeException("订单已删除,不能发货!");
        }
        if (storeOrder.getRefundStatus().equals(OrderConstants.MERCHANT_REFUND_ORDER_STATUS_APPLY) || storeOrder.getStatus().equals(OrderConstants.ORDER_STATUS_OVER) || storeOrder.getStatus().equals(OrderConstants.ORDER_STATUS_AWAIT_REPLY)) {
            throw new RuntimeException("当前状态订单无法发货请勿重复操作!");
        }
        List<SohuShopOrderInfoVo> orderInfoVoList = infoService.getListByOrderNo(bo.getOrderNo());
        if (CollectionUtils.isNotEmpty(orderInfoVoList)) {
            Long productId = orderInfoVoList.get(0).getProductId();
            SohuProductModel sohuProductModel = remoteProductService.queryById(productId);
            if (ObjectUtil.isNotNull(sohuProductModel) && sohuProductModel.getProductType() == 2) {
                //快递公司信息
                storeOrder.setDeliveryNo("虚拟商品无物流单号");
                storeOrder.setDeliveryCode("虚拟商品无物流公司代码");
                storeOrder.setDeliveryName("虚拟商品无物流公司");
                storeOrder.setStatus(OrderConstants.ORDER_STATUS_AWAIT_RECEIVING);
                // 初始化订单物流
                SohuShopOrderLogistics orderLogistics = new SohuShopOrderLogistics();
                orderLogistics.setOrderNo(storeOrder.getOrderNo());
                orderLogistics.setExpName("虚拟商品无物流公司");
                orderLogistics.setExpCode("虚拟商品无物流公司代码");
                orderLogistics.setLogisticsInfo("虚拟商品无物流轨迹");
                orderLogistics.setState(0);
                QueryTrackParam queryTrackParam = new QueryTrackParam();
                queryTrackParam.setCom(orderLogistics.getExpCode().toLowerCase());
                queryTrackParam.setNum(orderLogistics.getExpNo());
                return getSendBoolean(storeOrder, orderLogistics);
            }
        }
        return expressOrder(bo, storeOrder);
    }

    /**
     * 发货组装
     *
     * @param storeOrder
     * @param orderLogistics
     */
    @Nullable
    private Boolean getSendBoolean(SohuShopOrder storeOrder, SohuShopOrderLogistics orderLogistics) {
        Boolean execute = transactionTemplate.execute(i -> {
            this.baseMapper.updateById(storeOrder);
            //订单记录增加
            statusService.createLog(storeOrder.getOrderNo(), OrderConstants.ORDER_LOG_EXPRESS, OrderConstants.ORDER_LOG_MESSAGE_EXPRESS.replace("{deliveryName}", orderLogistics.getExpName()).replace("{deliveryCode}", storeOrder.getDeliveryNo()));
            logisticsService.save(orderLogistics);
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            throw new RuntimeException("快递发货失败！");
        }
        //推送极光通知
        CompletableFuture.runAsync(
                () -> this.pushShopOderJiGuangNotice(storeOrder),
                asyncConfig.getAsyncExecutor()
        );
        // 发货通知
        sendGoodsNotify(storeOrder);
        return execute;
    }

    @Override
    public Boolean openSaveDeliverInfo(SohuOpenShopOrderDeliverBo bo) {
        SohuShopOrder storeOrder = getByOrderNo(bo.getOrderNo());
        if (storeOrder.getIsMerchantDel()) {
            throw new RuntimeException("订单已删除,不能发货!");
        }
        if (storeOrder.getRefundStatus().equals(OrderConstants.MERCHANT_REFUND_ORDER_STATUS_APPLY) || storeOrder.getStatus().equals(OrderConstants.ORDER_STATUS_OVER) || storeOrder.getStatus().equals(OrderConstants.ORDER_STATUS_AWAIT_REPLY)) {
            throw new RuntimeException("当前状态订单无法发货请勿重复操作!");
        }
        SohuOpenClientMerchantVo sohuOpenClientMerchantVo = remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantId(bo.getOpenClientId(), storeOrder.getMerId());
        if (Objects.isNull(sohuOpenClientMerchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        SohuSendOrderBo sendOrderBo = new SohuSendOrderBo();
        sendOrderBo.setExpressNumber(bo.getDeliveryNo());
        sendOrderBo.setExpressCode(bo.getDeliveryCode());
        return expressOrder(sendOrderBo, storeOrder);
    }

    @Override
    public SohuShopOrderNumVo queryPcNum(Date startTime, Date endTime) {
        SohuShopOrderNumVo response = new SohuShopOrderNumVo();
        // 全部订单
        response.setAll(getCount(startTime, endTime, OrderConstants.MERCHANT_ORDER_STATUS_ALL, null));
        // 未支付订单
        response.setUnPaid(getCount(startTime, endTime, OrderConstants.MERCHANT_ORDER_STATUS_UNPAID, null));
        // 未发货订单
        response.setNotShipped(getCount(startTime, endTime, OrderConstants.ORDER_STATUS_SHIPPING, null));
        // 待收货订单
        response.setSpike(getCount(startTime, endTime, OrderConstants.ORDER_STATUS_AWAIT_RECEIVING, null));
        // 交易完成订单
        response.setComplete(getCount(startTime, endTime, OrderConstants.ORDER_STATUS_OVER, null));
        // 已退款订单
        response.setRefunded(getCount(startTime, endTime, OrderConstants.MERCHANT_ORDER_STATUS_REFUNDED, null));
        // 已删除订单
        response.setDeleted(getCount(startTime, endTime, OrderConstants.MERCHANT_ORDER_STATUS_DELETED, null));
        return response;
    }

    @Override
    public Boolean remarkPc(SohuRemarkOrderBo bo) {
        SohuShopOrder storeOrder = getByOrderNo(bo.getOrderNo());
        storeOrder.setPlatformRemark(bo.getRemark());
        return this.baseMapper.updateById(storeOrder) > 0;
    }

    @Override
    public SohuShopOrderVo queryByOrderNo(String orderNo) {
        // 设置订单信息
        SohuShopOrder storeOrder = getByOrderNo(orderNo);
        SohuShopOrderVo orderInfoResponse = new SohuShopOrderVo();
        BeanUtils.copyProperties(storeOrder, orderInfoResponse);
        List<SohuShopOrderInfoVo> orderInfos = infoService.getListByOrderNo(storeOrder.getOrderNo());
        orderInfoResponse.setOrderInfo(orderInfos);

        // 设置退款信息
        LambdaQueryWrapper<SohuShopRefundOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuShopRefundOrder::getShopOrderNo, storeOrder.getOrderNo());
        List<SohuShopRefundOrderVo> refundOrders = refundOrderMapper.selectVoList(wrapper);
        if (CollectionUtils.isNotEmpty(refundOrders)) {
            orderInfoResponse.setStoreRefundOrderList(refundOrders);
        }
        // 获取用户信息
        LoginUser user = userService.selectById(storeOrder.getUserId());
        orderInfoResponse.setNikeName(user.getNickname());
        orderInfoResponse.setUserEmail(user.getPhoneNumber());

        // 商户信息
        SohuMerchantModel merchantModel = remoteMerchantService.selectById(storeOrder.getMerId());
        orderInfoResponse.setMerName(merchantModel.getName());
        //orderInfoResponse.setMerIsSelf(true);
        return orderInfoResponse;
    }

    /**
     * 根据订单号查询商户订单信息
     *
     * @param orderNo
     * @return
     */
    @Override
    public SohuShopOrder getByOrderNo(String orderNo) {
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getOrderNo, orderNo);
        lqw.last("limit 1");
        return this.baseMapper.selectOne(lqw);
    }

    @Override
    public Boolean updateById(SohuShopOrder storeOrder) {
        return this.baseMapper.updateById(storeOrder) > 0;
    }

    @Override
    public TableDataInfo<SohuShopOrderModel> getOrderListByType(SohuOrderReqBo bo, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        // 查询本人用户订单
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        // 如果status=100获取全部订单列表
        String orderType = bo.getOrderType();
        if (null != orderType) {
            statusApiByWhere(lqw, orderType);
        }
//        // 待支付
//        if (OrderConstants.MERCHANT_ORDER_STATUS_UNPAID.equals(orderType)) {
//            lqw.eq(SohuShopOrder::getPaid, 0);
//        }
        lqw.eq(null != userId, SohuShopOrder::getUserId, userId);
//        if (OrderConstants.ORDER_STATUS_OVER.equals(orderType)){
//            lqw.in(SohuShopOrder::getStatus, Arrays.asList(OrderConstants.ORDER_STATUS_RECEIVE, OrderConstants.ORDER_STATUS_OVER));
//        }else {
//            lqw.eq(null != orderType, SohuShopOrder::getStatus, orderType);
//        }
        lqw.eq(SohuShopOrder::getIsUserDel, Boolean.FALSE);
        lqw.orderByDesc(SohuShopOrder::getCreateTime);
        Page<SohuShopOrder> pageInfo = new Page<>();
        if (StrUtil.isNotBlank(bo.getProductName())) {
            bo.setUserId(userId);
            statusApi(bo, orderType);
            pageInfo = baseMapper.selectPageByProductName(bo, PageQueryUtils.build(pageQuery));
        } else {
            pageInfo = baseMapper.selectPage(PageQueryUtils.build(pageQuery), lqw);
        }
        List<SohuShopOrder> orderList = pageInfo.getRecords();
        if (CollUtil.isEmpty(orderList)) {
            return TableDataInfoUtils.build();
        }
        Page<SohuShopOrderModel> modelInfo = new Page<>();
        BeanUtils.copyProperties(pageInfo, modelInfo);
        List<Long> merIdList = orderList.stream().map(SohuShopOrder::getMerId).distinct().collect(Collectors.toList());
        // 店铺
        Map<Long, SohuMerchantModel> merchantMap = remoteMerchantService.getMerIdMapByIdList(merIdList);
//        for (SohuShopOrder storeOrder : orderList) {
//            SohuShopOrderModel orderModel = new SohuShopOrderModel();
//            BeanUtils.copyProperties(storeOrder, orderModel);
//            // 订单详情对象列表
//            List<SohuShopOrderInfo> orderInfoList = infoService.getListByOrderNo(storeOrder.getOrderNo());
//            List<SohuShopOrderInfoModel> infoResponseList = CollUtil.newArrayList();
//            orderInfoList.forEach(e -> {
//                SohuShopOrderInfoModel orderInfoResponse = new SohuShopOrderInfoModel();
//                BeanUtils.copyProperties(e, orderInfoResponse);
//                infoResponseList.add(orderInfoResponse);
//            });
//            orderModel.setOrderInfo(infoResponseList);
//            orderModel.setMerName(merchantMap.get(storeOrder.getMerId()).getName());
//            responseList.add(orderModel);
//        }
        // 数据转换
        List<SohuShopOrderModel> records = orderList.stream().map(shopOrder -> {
            SohuShopOrderModel orderModel = new SohuShopOrderModel();
            BeanUtils.copyProperties(shopOrder, orderModel);
            return orderModel;
        }).collect(Collectors.toList());

        records.forEach(storeOrder -> {
            // 订单详情对象列表
            List<SohuShopOrderInfoVo> orderInfoList = infoService.getListByOrderNo(storeOrder.getOrderNo());
            if (CollUtil.isNotEmpty(orderInfoList)) {
                List<SohuShopOrderInfoModel> infoResponseList = CollUtil.newArrayList();
                orderInfoList.forEach(e -> {
                    SohuShopOrderInfoModel orderInfoResponse = new SohuShopOrderInfoModel();
                    BeanUtils.copyProperties(e, orderInfoResponse);
                    infoResponseList.add(orderInfoResponse);
                });
                storeOrder.setOrderInfo(infoResponseList);
                storeOrder.setMerName(merchantMap.get(storeOrder.getMerId()).getName());
            }
        });
        modelInfo.setRecords(records);
        return TableDataInfoUtils.build(modelInfo);

    }

    @Override
    public TableDataInfo<SohuMasterOrderAwaitPayModel> getWaitOrderListByType(PageQuery pageQuery, SohuOrderReqBo bo) {
        Long userId = LoginHelper.getUserId();
        if (null == userId) {
            throw new RuntimeException("用户未登录");
        }
        SohuShopMasterOrderBo masterOrderBo = new SohuShopMasterOrderBo();
        masterOrderBo.setUserId(userId);
//        masterOrderBo.sets
        // 获取所有的主订单
        TableDataInfo<SohuShopMasterOrderVo> masterOrderVoPage = TableDataInfoUtils.build();
        if (StrUtil.isNotBlank(bo.getProductName())) {
            bo.setUserId(userId);
            masterOrderVoPage = masterOrderService.getWaitOrderListByProductName(bo, pageQuery);
        } else {
            masterOrderVoPage = masterOrderService.getAwaitPayList(pageQuery);
        }
        List<SohuShopMasterOrderVo> masterOrderList = masterOrderVoPage.getData();
        if (CollectionUtils.isEmpty(masterOrderList)) {
            return TableDataInfoUtils.build();
        }
        // 数据组装
        List<SohuMasterOrderAwaitPayModel> responseList = masterOrderList.stream().map(masterOrder -> {
            SohuMasterOrderAwaitPayModel awaitPayResponse = new SohuMasterOrderAwaitPayModel();
            BeanUtils.copyProperties(masterOrder, awaitPayResponse);
            // 根据主订单号获取所有子订单
            List<SohuShopOrder> storeOrderList = this.getListByMasterNo(masterOrder.getOrderNo());
            List<SohuPreStoreOrderModel> storeOrderVoList = storeOrderList.stream().map(storeOrder -> {
                // 获取商户信息
                SohuMerchantModel merchantModel = remoteMerchantService.selectById(storeOrder.getMerId());
                // 查询订单详细信息
                List<SohuShopOrderInfoVo> infoList = infoService.getListByOrderNo(storeOrder.getOrderNo());
                SohuPreStoreOrderModel orderVo = new SohuPreStoreOrderModel();
                orderVo.setMerId(storeOrder.getMerId());
                // 商户名称
                orderVo.setMerName(merchantModel.getName());
                List<SohuShopOrderInfoModel> infoVoList = infoList.stream().map(info -> {
                    SohuShopOrderInfoModel infoVo = new SohuShopOrderInfoModel();
                    BeanUtils.copyProperties(info, infoVo);
                    return infoVo;
                }).collect(Collectors.toList());
                orderVo.setOrderInfoList(infoVoList);
                return orderVo;
            }).collect(Collectors.toList());
            awaitPayResponse.setOrderList(storeOrderVoList);
            return awaitPayResponse;
        }).collect(Collectors.toList());
        return TableDataInfoUtils.build(responseList);
    }

    /**
     * 根据主订单号查询所有子订单
     *
     * @param masterOrderNo
     */
    @Override
    public List<SohuShopOrder> getListByMasterNo(String masterOrderNo) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<SohuShopOrder>().eq(SohuShopOrder::getMasterOrderNo, masterOrderNo));
    }

    @Override
    public Boolean updateBatchById(List<SohuShopOrderModel> storeOrderList) {
        return this.baseMapper.updateBatchById(BeanCopyUtils.copyList(storeOrderList, SohuShopOrder.class));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> createOrder(SohuCreateOrderReqBo createOrderReqBo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (ObjectUtil.isNull(user)) {
            throw new ServiceException("未登录");
        }
        // 通过缓存获取预下单对象
        String key = createOrderReqBo.getPreOrderNo();
        boolean exists = RedisUtils.isExistsObject(key);
        if (!exists) {
            throw new ServiceException("Pre-order order does not exist");
        }
        Object cacheObject = RedisUtils.getCacheObject(key);
        if (ObjectUtil.isNull(cacheObject)) {
            throw new ServiceException("Pre-order order does not exist");
        }
        SohuPreOrderModel sohuPreOrderModel = JSONObject.parseObject(JSON.toJSONString(cacheObject), SohuPreOrderModel.class);
        // 检测支付方式
        if (!checkPayType(createOrderReqBo.getPayType())) {
            throw new ServiceException("This payment method is not currently supported, please refresh the page or contact the administrator");
        }
        // 校验商品库存
        List<Map<String, Object>> productMap = validateProductStock(sohuPreOrderModel);
        // 校验收货信息
        if (createOrderReqBo.getAddressId() <= 0) {
            throw new ServiceException("Please select a delivery address");
        }
        SohuUserAddressVo userAddress = remoteMiddleUserAddressService.queryById(createOrderReqBo.getAddressId());
        if (ObjectUtil.isNull(userAddress)) {
            throw new ServiceException("Incorrect delivery address");
        }
        sohuPreOrderModel.setRegionId(Long.valueOf(userAddress.getProvinceCode()));

        // 计算订单各种价格
        SohuOrderComputedPriceResBo orderComputedPriceRequest = new SohuOrderComputedPriceResBo();
        orderComputedPriceRequest.setAddressId(createOrderReqBo.getAddressId());
        orderComputedPriceRequest.setCouponId(createOrderReqBo.getCouponId());
        SohuPreOrderModel computedOrderPriceResponse = computedPrice(orderComputedPriceRequest, sohuPreOrderModel, user);

        // 生成主订单号
        String orderNo = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_PLATFORM);
        // 主订单
        SohuShopMasterOrder masterOrder = new SohuShopMasterOrder();
        if (createOrderReqBo.getPayType().equals(PayTypeEnum.PAY_TYPE_INTEGRAL.getStatus())) {
            // 如果积分兑换订单就添加兑换总积分
            masterOrder.setTotalIntegral(sohuPreOrderModel.getProTotalIntegral());
        }
        masterOrder.setOrderNo(orderNo);
        masterOrder.setUserId(user.getUserId());
        masterOrder.setSex(user.getSex());
        // 地址姓名
        masterOrder.setEmail(createOrderReqBo.getEmail());
        masterOrder.setReceiverName(userAddress.getName());
        masterOrder.setUserPhone(userAddress.getPhone());
        // 详细地址：省+市+区+详细地址拼接
        StringBuilder addressBody = new StringBuilder();
        // todo 收货地址
        if (StringUtils.isNotBlank(userAddress.getProvinceName())) {
            addressBody.append(userAddress.getProvinceName());
        }
        if (StringUtils.isNotBlank(userAddress.getCityName())) {
            addressBody.append(userAddress.getCityName());
        }
        if (StringUtils.isNotBlank(userAddress.getDistrictName())) {
            addressBody.append(userAddress.getDistrictName());
        }
        if (StringUtils.isNotBlank(userAddress.getProvinceName())) {
            addressBody.append(userAddress.getDetail());
        }
        if (StringUtils.isBlank(addressBody)) {
            addressBody.append(userAddress.getDetail());
        }
        // 完整地址
        masterOrder.setUserDetailAddress(addressBody.toString());

        masterOrder.setTotalNum(sohuPreOrderModel.getOrderProNum());
        masterOrder.setProductTotalPrice(computedOrderPriceResponse.getProTotalFee());
        masterOrder.setTotalPostage(computedOrderPriceResponse.getFreightFee());
        BigDecimal totalPrice = computedOrderPriceResponse.getProTotalFee().add(computedOrderPriceResponse.getFreightFee());
        masterOrder.setTotalPrice(totalPrice);
        masterOrder.setPayPostage(computedOrderPriceResponse.getFreightFee());
        masterOrder.setPayPrice(computedOrderPriceResponse.getPayFee());
        masterOrder.setCouponId(Optional.ofNullable(createOrderReqBo.getCouponId()).orElse(0L));
        masterOrder.setCouponPrice(computedOrderPriceResponse.getCouponFee());
        masterOrder.setPaid(false);
        masterOrder.setPayType(createOrderReqBo.getPayType());
        masterOrder.setMark(StrUtil.isEmpty(createOrderReqBo.getMark()) ? "" : createOrderReqBo.getMark());
        masterOrder.setPayChannel(createOrderReqBo.getPayChannel());
        masterOrder.setSiteId(sohuPreOrderModel.getSiteId());
        // 商户订单
        List<SohuShopOrderInfo> storeOrderInfoList = CollUtil.newArrayList();
        // todo 个人优惠券
//        StoreCouponUser storeCouponUser = null;
//        if (masterOrder.getCouponId() > 0) {
//            storeCouponUser = storeCouponUserService.getById(createOrderReqBo.getCouponId());
//        }
//        StoreCouponUser finalStoreCouponUser = storeCouponUser;
        // 检测是否是分销商品
        // 通过查询分享记录表 分享人id + 商品id + 操作人id 查出来唯一记录 count = 1 返回 true 设置independentOrder = true
        // todo 待优化-如果可以多个购买的话
        if (sohuPreOrderModel.getOrderList().size() == 1) {
            Long productId = sohuPreOrderModel.getOrderList().get(0).getOrderInfoList().get(0).getProductId();
            boolean hasViewRecord = remoteMiddleViewRecordService.hasViewRecord(user.getUserId(), productId, BusyType.Goods.name());
            if (!hasViewRecord) {
                sohuPreOrderModel.setIsIndependent(Boolean.FALSE);
            }
        }
        // 先查询分拥等级
        SohuIndependentLevelConfigModel levelConfigModel = remoteIndependentLevelService.queryByStatusAndSiteId(0, 11L);
        log.warn("分销人的用户id: {}", sohuPreOrderModel.getIndependentUserId());
        Integer level = ObjectUtils.isNull(levelConfigModel) ? levelConfigModel.getLevel() : 2;
        // 查询消费者是否有拉新人-上上级拉新人
        AtomicReference<Long> regUserId = new AtomicReference<>(0L);
//                remoteMiddleInviteService.selectByInviteCount(user.getUserId(), level);
//        Boolean regUser = Boolean.FALSE;
//        if (null != regUserId) {
//            //判断上级拉新人是否存在非普通用户角色
//            Set<String> roles = remoteSysRoleService.selectRoleKeyByUserId(regUserId);
//            if (roles.size() == Constants.TWO) {
//                regUser = Boolean.TRUE;
//            }
//        }
//        Boolean finalRegUser = regUser;
//        // todo 查询是否有代理人
//        Boolean agencyId = Boolean.FALSE;
        // 分账金额详情集合
        List<SohuOrderIndependentPriceVo> independentPriceVoList = Lists.newArrayList();
        // 商家销售额集合
        List<SohuMerchantSalesReportBo> merchantSalesReportBoList = Lists.newArrayList();
        // 订单详情


        List<SohuShopOrder> storeOrderList = sohuPreOrderModel.getOrderList().stream().map(e -> {
            // 生成商户订单号
            String childOrderNo = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_MERCHANT);
            // 商户订单
            SohuShopOrder storeOrder = new SohuShopOrder();
            // 商家销售统计
            SohuMerchantSalesReportBo merchantSalesReportBo = new SohuMerchantSalesReportBo();
            merchantSalesReportBo.setMerId(e.getMerId());
            // 判断支付方式：积分兑换
            if (createOrderReqBo.getPayType().equals(PayTypeEnum.PAY_TYPE_INTEGRAL.getStatus())) {
                storeOrder.setPayIntegral(sohuPreOrderModel.getProTotalIntegral());
            }
            storeOrder.setMasterOrderNo(masterOrder.getOrderNo());
            storeOrder.setOrderNo(childOrderNo);
            storeOrder.setMerId(e.getMerId());
            storeOrder.setUserId(user.getUserId());
            // todo 收货地址
            storeOrder.setEmail(createOrderReqBo.getEmail());
            storeOrder.setReceiverName(userAddress.getName());
            storeOrder.setUserPhone(userAddress.getPhone());
            storeOrder.setUserDetailAddress(addressBody.toString());
            storeOrder.setPaid(false);
            storeOrder.setPayType(createOrderReqBo.getPayType());
            storeOrder.setUserRemark(masterOrder.getMark());
            storeOrder.setOpenClientId(e.getOpenClientId());
            storeOrder.setMerIsSelf(e.getMerIsSelf());
            storeOrder.setPmSharePubId(e.getPmSharePubId());
            // 分销人的拉新人
            Long independentRegUserId = null;
            Long mcnId = null;
            if (null != sohuPreOrderModel.getIndependentUserId() && sohuPreOrderModel.getIndependentUserId() != 0L) {
                // 查询分销人是否有拉新人-上上级拉新人
                independentRegUserId = remoteMiddleInviteService.selectByInviteCount(sohuPreOrderModel.getIndependentUserId(), level);
                // todo 判断分销人是否有mcn机构。如果有mcn机构的话钱入账到mcn机构的账号
                mcnId = null;
            }
            Long finalIndependentRegUserId = independentRegUserId;
            // 订单详情
            Long finalMcnId = mcnId;
            AtomicReference<BigDecimal> salesPrice = new AtomicReference<>(BigDecimal.ZERO);
            AtomicInteger salesNum = new AtomicInteger(0);
            List<SohuShopOrderInfo> list = e.getOrderInfoList().stream().map(info -> {
                SohuShopOrderInfo orderInfo = new SohuShopOrderInfo();
                orderInfo.setMerOrderNo(childOrderNo);
                orderInfo.setMerId(e.getMerId());
                orderInfo.setCategoryId(remoteProductService.getCategoryIdByProductId(info.getProductId(), 2));
                orderInfo.setProductId(info.getProductId());
                orderInfo.setProductName(info.getProductName());
                orderInfo.setImage(info.getImage());
                orderInfo.setProductAttrValueId(info.getProductAttrValueId());
                orderInfo.setSku(info.getSku());
                orderInfo.setPrice(info.getPrice());
                orderInfo.setPayNum(info.getPayNum());
                orderInfo.setWeight(info.getWeight());
                orderInfo.setVolume(info.getVolume());
                orderInfo.setOpenClientId(info.getOpenClientId());
                orderInfo.setThirdProductId(info.getThirdProductId());
                orderInfo.setSiteType(info.getSiteType());
                orderInfo.setSiteId(info.getSiteId());

                salesPrice.set(CalUtils.add(salesPrice.get(), info.getPrice()));
                salesNum.addAndGet(info.getPayNum());
                // 分销人总价
                BigDecimal allIndependentPrice = null;
                // 是否有分销人
                boolean haveIndependentUserId = null != sohuPreOrderModel.getIndependentUserId();
                if (haveIndependentUserId) {
                    sohuPreOrderModel.setIsIndependent(Boolean.TRUE);
                }
                // 查询产品所属的行业站与城市站
                SohuProductModel productModel = remoteProductService.queryById(info.getProductId());
//                // 通过入口站查询入口站长ID
//                Long entranceSiteId = info.getSiteId();
//                // 国家站id
//                Long countrySiteId = remoteMiddleSiteService.selectSiteByPid(info.getSiteId()).getId();
//                //通过行业ID查询行业站长
//                Long industrySiteId = remotePlatformIndustryService.queryIndustryIdByCategoryIdAndBusyType(productModel.getCategoryId(), BusyType.Goods.getType());
//                //获取城市站长
//                Long citySiteId = productModel.getSiteId();
//                //判断当前入口站是否等于商品所属站点,如果站长相同，则无拉新收益
//                Boolean inviteSiteUser = Boolean.FALSE;
//                if (Objects.nonNull(info.getSiteType()) && info.getSiteType() == 1) {
//                    inviteSiteUser = entranceSiteId.equals(industrySiteId);
//                }
//                if (Objects.nonNull(info.getSiteType()) && info.getSiteType() == 2) {
//                    inviteSiteUser = entranceSiteId.equals(citySiteId);
//                }
                // 子单总分销金额
                allIndependentPrice = info.getDistributorPrice().multiply(new BigDecimal(info.getPayNum()));

                // 分账各个人算价
                AccountPlatformBo bo = new AccountPlatformBo();
                bo.setBusyType(BusyType.Goods.getType());
                bo.setBusyCode(info.getProductId());
                bo.setSiteType(info.getSiteType());
                bo.setEntranceSiteId(info.getSiteId());
                bo.setCitySiteId(productModel.getSiteId());
                bo.setIndustryType(productModel.getCategoryId());
                bo.setPayPrice(CalUtils.sub(info.getPrice().multiply(new BigDecimal(info.getPayNum())), allIndependentPrice));
                bo.setChargeFee(BigDecimal.ZERO);
                //封装平台对象
                setPlatformObject(orderInfo, bo);
                log.info("订单详情分账-封装平台对象:{}", orderInfo);
                //封装分销对象
                setIndependentObject(sohuPreOrderModel.getIsIndependent(), info.getPrice().multiply(new BigDecimal(info.getPayNum())), info.getIndependentRatio(), allIndependentPrice, orderInfo, finalIndependentRegUserId);
                log.info("订单详情分账-封装分销对象:{}", orderInfo);
                SohuOrderIndependentPriceVo independentPrice = new SohuOrderIndependentPriceVo();
//                        getShopIndependentPrice(info.getSiteId(), info.getPrice().multiply(new BigDecimal(info.getPayNum())), CalUtils.sub(info.getPrice().multiply(new BigDecimal(info.getPayNum())), allIndependentPrice), info.getIndependentRatio(), allIndependentPrice, sohuPreOrderModel.getIsIndependent(), finalIndependentRegUserId, finalRegUser, agencyId, inviteSiteUser);
                // 如果是分销单并且有分销人id
                if (haveIndependentUserId && Boolean.TRUE.equals(sohuPreOrderModel.getIsIndependent())) {
                    // 分销人分账金额
                    independentPrice.setDistributorPrice(allIndependentPrice);
                    // 分销单设置分销人id
                    independentPrice.setIndependentUserId(sohuPreOrderModel.getIndependentUserId());
                    orderInfo.setDistributorPrice(allIndependentPrice);
                    // 分销人的拉新人id
                    orderInfo.setIndependentInviteUserId(finalIndependentRegUserId);
                    // 判断是否跟mcn绑定
                    if (null != finalMcnId) {
                        orderInfo.setIndependentUserId(finalMcnId);
                        orderInfo.setMcnId(finalMcnId);
                        orderInfo.setOriginatorUserId(sohuPreOrderModel.getIndependentUserId());
                    } else {
                        orderInfo.setIndependentUserId(sohuPreOrderModel.getIndependentUserId());
                    }
                }
                independentPrice.setShopOrderNo(childOrderNo);
                // 订单详情每笔订单的分账人所获得的金额详细
//                orderInfo.setInvitePrice(independentPrice.getInvitePrice());
//                orderInfo.setAdminPrice(independentPrice.getAdminPrice());
//                orderInfo.setCountryPrice(independentPrice.getCountryPrice());
//                orderInfo.setCityPrice(independentPrice.getCityPrice());
//                orderInfo.setEntranceSitePrice(independentPrice.getEntrancePrice());
//                orderInfo.setIndustrySitePrice(independentPrice.getIndustryPrice());
//                orderInfo.setCityPrice(independentPrice.getCityPrice());
//                orderInfo.setInviteSitePrice(independentPrice.getInviteCityPrice());
//                orderInfo.setCountrySiteId(countrySiteId);
//                orderInfo.setEntranceSiteId(entranceSiteId);
//                orderInfo.setCitySiteId(citySiteId);
//                orderInfo.setIndustrySiteId(industrySiteId);
//                orderInfo.setInviteSiteId(citySiteId);
                independentPrice.setInvitePrice(orderInfo.getInvitePrice());
                independentPrice.setAdminPrice(orderInfo.getAdminPrice());
                independentPrice.setCountryPrice(orderInfo.getCountryPrice());
                independentPrice.setCityPrice(orderInfo.getCityPrice());
                independentPrice.setEntrancePrice(orderInfo.getEntranceSitePrice());
                independentPrice.setIndustryPrice(orderInfo.getIndustrySitePrice());
                independentPrice.setCityPrice(orderInfo.getCityPrice());
                independentPrice.setInviteCityPrice(orderInfo.getInviteSitePrice());
                independentPrice.setAgencyPrice(orderInfo.getAgencyAdminPrice());
                // 商户订单号
                independentPrice.setShopOrderNo(childOrderNo);
                independentPriceVoList.add(independentPrice);
//                // 拉新人id
//                orderInfo.setInviteUserId(regUserId);
                regUserId.set(orderInfo.getInviteUserId());
                // 分销人的拉新人
                orderInfo.setIndependentInviteUserId(finalIndependentRegUserId);
                return orderInfo;
            }).collect(Collectors.toList());
            log.error("storeOrderInfoList:{}", JsonUtils.toJsonString(list));
            storeOrderInfoList.addAll(list);
            // 计算字订单所有分账信息
            SohuOrderIndependentPriceVo extractedPriceVo = extractedPriceVo(independentPriceVoList, childOrderNo);
//            // null处理
//            storeOrder.setAdminPrice(Objects.requireNonNullElse(extractedPriceVo.getAdminPrice(), BigDecimal.ZERO));
//            storeOrder.setCountryPrice(Objects.requireNonNullElse(extractedPriceVo.getCountryPrice(), BigDecimal.ZERO));
//            storeOrder.setCityPrice(Objects.requireNonNullElse(extractedPriceVo.getCityPrice(), BigDecimal.ZERO));
//            storeOrder.setAgencyAdminPrice(Objects.requireNonNullElse(extractedPriceVo.getAgencyPrice(), BigDecimal.ZERO));
            if (null != sohuPreOrderModel.getIndependentUserId() && sohuPreOrderModel.getIndependentUserId() != 0 && !extractedPriceVo.getDistributorPrice().equals(new BigDecimal("0.00"))) {
                storeOrder.setDistributorPrice(extractedPriceVo.getDistributorPrice());
                storeOrder.setDistributorInvitePrice(extractedPriceVo.getDistributorInvitePrice());
                // 判断是否跟mcn绑定
                if (null != finalMcnId) {
                    // 分销人id
                    storeOrder.setIndependentUserId(finalMcnId);
                    // mcn机构id
                    storeOrder.setMcnId(finalMcnId);
                    // mcn绑定的用户id
                    storeOrder.setOriginatorUserId(sohuPreOrderModel.getIndependentUserId());
                } else {
                    // 分销人id
                    storeOrder.setIndependentUserId(sohuPreOrderModel.getIndependentUserId());
                }
                // 分销人的拉新人
                storeOrder.setIndependentInviteUserId(finalIndependentRegUserId);
            }
            storeOrder.setInvitePrice(Objects.requireNonNullElse(extractedPriceVo.getInvitePrice(), BigDecimal.ZERO));
            // 假如有分销人id去最新的一个或者不取id
            masterOrder.setIndependentUserId(sohuPreOrderModel.getIndependentUserId());
            // 分销人的拉新人id
            storeOrder.setIndependentInviteUserId(finalIndependentRegUserId);
            masterOrder.setIndependentInviteUserId(finalIndependentRegUserId);
            // 拉新人id
            masterOrder.setInviteUserId(regUserId.get());
            storeOrder.setInviteUserId(regUserId.get());

            //组装商家销售额
            log.info("商户销售额：{}", salesPrice.get());
            merchantSalesReportBo.setSalesPrice(salesPrice.get());
            merchantSalesReportBo.setSalesNum(salesNum.get());
            log.info("商户销售额组装：{}", merchantSalesReportBo);
            merchantSalesReportBoList.add(merchantSalesReportBo);

            storeOrder.setTotalNum(list.stream().mapToInt(SohuShopOrderInfo::getPayNum).sum());
            BigDecimal proTotalPrice = list.stream().map(i -> i.getPrice().multiply(new BigDecimal(i.getPayNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            storeOrder.setProductTotalPrice(proTotalPrice);
            storeOrder.setTotalPostage(getTotalFreightFee(e.getOrderInfoList(), sohuPreOrderModel.getRegionId()));
            storeOrder.setTotalPrice(proTotalPrice.add(storeOrder.getTotalPostage()));
            storeOrder.setPayPrice(storeOrder.getTotalPrice());
            storeOrder.setPayPostage(storeOrder.getTotalPostage());
            // 优惠金额部分
            storeOrder.setCouponId(0L);
            storeOrder.setCouponPrice(BigDecimal.ZERO);
            // todo 个人优惠券
//            if (masterOrder.getCouponId() > 0 && storeOrder.getMerId().equals(finalStoreCouponUser.getMerId())) {
//                storeOrder.setCouponId(masterOrder.getCouponId());
//                if (storeOrder.getProductTotalPrice().compareTo(masterOrder.getCouponPrice()) > 0) {
//                    storeOrder.setPayPrice(storeOrder.getProductTotalPrice().add(storeOrder.getPayPostage()).subtract(masterOrder.getCouponPrice()));
//                } else {
//                    storeOrder.setPayPrice(storeOrder.getPayPostage());
//                }
//                storeOrder.setCouponPrice(masterOrder.getCouponPrice());
//            }
            return storeOrder;
        }).collect(Collectors.toList());

        Boolean execute = transactionTemplate.execute(e -> {
            // 扣减库存
            for (Map<String, Object> skuRecord : productMap) {
                // 普通商品扣库存
                Long productId = Long.parseLong(skuRecord.get("productId").toString());
                Integer num = Integer.parseInt(skuRecord.get("num").toString());
                remoteProductService.operationStock(productId, num, "sub");
                // 普通商品规格扣库存
                remoteProductAttrValueService.operationStock(Long.parseLong(skuRecord.get("attrValueId").toString()), Integer.parseInt(skuRecord.get("num").toString()), "sub", ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS, null);
            }
            masterOrder.setCreateBy(String.valueOf(user.getUserId()));
            masterOrder.setUpdateBy(String.valueOf(user.getUserId()));
            masterOrderService.insert(masterOrder);
            this.baseMapper.insertBatch(storeOrderList);
            infoService.insertBatch(storeOrderInfoList);
            // 优惠券修改
//            if (masterOrder.getCouponId() > 0) {
//                // todo 个人优惠券
////                finalStoreCouponUser.setStatus(CouponConstants.STORE_COUPON_USER_STATUS_USED);
////                storeCouponUserService.updateById(finalStoreCouponUser);
//            }
            // 生成订单日志
            storeOrderList.forEach(order -> {
                statusService.createLog(order.getOrderNo(), OrderConstants.ORDER_LOG_CREATE, OrderConstants.ORDER_LOG_MESSAGE_CREATE);
            });

            // 清除购物车数据
            if (CollUtil.isNotEmpty(sohuPreOrderModel.getCartIdList())) {
                remoteShopCartAttrService.deleteCartByIds(sohuPreOrderModel.getCartIdList());
            }
            // 添加店铺销售数量队列
            // 组装数据
            SohuOperateBo sohuOperateBo = new SohuOperateBo();
            sohuOperateBo.setOrderNo(masterOrder.getOrderNo()).setOperate(Boolean.TRUE);
            MqMessaging mqMessaging = new MqMessaging(JSONObject.toJSONString(sohuOperateBo), "task_merchant_count");
            remoteStreamMqService.sendDelayMsg(mqMessaging, 1L);

            // 加入自动未支付自动取消队列
            MqMessaging mqCancelMessaging = new MqMessaging(JSONObject.toJSONString(masterOrder), "cancel_shop_order");
            remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 16L);
            log.info("已发送延时取消队列-cancel_shop_order:{}", JSONObject.toJSONString(masterOrder));
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            throw new ServiceException("Order generation failed");
        }
        //异步同步商家销售统计
        remoteMerchantSalesService.syncSalesReport(merchantSalesReportBoList);

        // 删除缓存订单
        if (RedisUtils.isExistsObject(key)) {
            RedisUtils.deleteObject(key);
        }

        // todo 加入自动未支付自动取消队列
//        redisUtil.lPush(TaskConstants.ORDER_TASK_REDIS_KEY_AUTO_CANCEL_KEY, masterOrder.getOrderNo());

        Map<String, Object> record = Maps.newHashMap();
        record.put("orderNo", masterOrder.getOrderNo());
        record.put("independentPriceList", independentPriceVoList);
        return record;
    }

    /**
     * 计算每个子单的总价
     *
     * @param independentPriceVoList
     * @param childOrderNo
     */
    private static SohuOrderIndependentPriceVo extractedPriceVo(List<SohuOrderIndependentPriceVo> independentPriceVoList, String childOrderNo) {
        SohuOrderIndependentPriceVo priceVo = new SohuOrderIndependentPriceVo();
        Map<String, Function<SohuOrderIndependentPriceVo, BigDecimal>> priceMappings = Map.of(
                "adminPrice", SohuOrderIndependentPriceVo::getAdminPrice,
                "countryPrice", SohuOrderIndependentPriceVo::getCountryPrice,
                "cityPrice", SohuOrderIndependentPriceVo::getCityPrice,
                "invitePrice", SohuOrderIndependentPriceVo::getInvitePrice,
                "distributorPrice", SohuOrderIndependentPriceVo::getDistributorPrice,
                "distributorInvitePrice", SohuOrderIndependentPriceVo::getDistributorInvitePrice,
                "agencyPrice", SohuOrderIndependentPriceVo::getAgencyPrice,
                "entrancePrice", SohuOrderIndependentPriceVo::getEntrancePrice,
                "inviteCityPrice", SohuOrderIndependentPriceVo::getInviteCityPrice,
                "industryPrice", SohuOrderIndependentPriceVo::getIndustryPrice);
        for (Map.Entry<String, Function<SohuOrderIndependentPriceVo, BigDecimal>> entry : priceMappings.entrySet()) {
            String fieldName = entry.getKey();
            Function<SohuOrderIndependentPriceVo, BigDecimal> valueExtractor = entry.getValue();
            BigDecimal sum = independentPriceVoList.stream().filter(item -> childOrderNo.equals(item.getShopOrderNo())).map(valueExtractor).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 设置和到priceVo对象的相应字段
            switch (fieldName) {
                case "adminPrice":
                    priceVo.setAdminPrice(sum);
                    break;
                case "countryPrice":
                    priceVo.setCountryPrice(sum);
                    break;
                case "cityPrice":
                    priceVo.setCityPrice(sum);
                    break;
                case "invitePrice":
                    priceVo.setInvitePrice(sum);
                    break;
                case "distributorPrice":
                    priceVo.setDistributorPrice(sum);
                    break;
                case "distributorInvitePrice":
                    priceVo.setDistributorInvitePrice(sum);
                    break;
                case "agencyPrice":
                    priceVo.setAgencyPrice(sum);
                    break;
                case "entrancePrice":
                    priceVo.setEntrancePrice(sum);
                    break;
                case "inviteCityPrice":
                    priceVo.setInviteCityPrice(sum);
                    break;
                case "industryPrice":
                    priceVo.setIndustryPrice(sum);
                    break;
                // 可以添加其他字段的处理
                default:
                    throw new IllegalArgumentException("Unknown field: " + fieldName);
            }
        }
        return priceVo.setShopOrderNo(childOrderNo);
    }

//    public static void main(String[] args) {
//        // 订单分销总金额、参与手续费总金额
//        SohuOrderIndependentPriceVo independentPrice = getShopIndependentPriceTest(new BigDecimal("20"), new BigDecimal("16").multiply(new BigDecimal("1")), new BigDecimal("20"), new BigDecimal("3.2"), true, 424L, true, false);
//        System.out.println(independentPrice);
//    }
//
//    /**
//     * 分账算价
//     *
//     * @param price1
//     * @param price
//     * @param independentRatio
//     * @param productIndependentPrice
//     * @param independentOrder
//     * @param independentRegUserId
//     * @param consumerInviteUser
//     */
//    public static SohuOrderIndependentPriceVo getShopIndependentPriceTest(BigDecimal price1, BigDecimal price, BigDecimal independentRatio, BigDecimal productIndependentPrice, Boolean independentOrder, Long independentRegUserId, Boolean consumerInviteUser, Boolean agencyId) {
//        // 查询分账模版
//        SohuIndependentTemplateModel templateModel = new SohuIndependentTemplateModel();
//        templateModel.setConsumerInviteRatio(new BigDecimal("10"));
//        templateModel.setAgencyRatio(new BigDecimal("50"));
//        templateModel.setDistributorInviteRatio(new BigDecimal("20"));
//        templateModel.setDistributorRatio(new BigDecimal("80"));
//        templateModel.setCityRatio(new BigDecimal("30"));
//        templateModel.setCountryRatio(new BigDecimal("20"));
//        templateModel.setAdminRatio(new BigDecimal("50"));
//        templateModel.setPlatformRatio(new BigDecimal("90"));
//        // 分账对象
//        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
//        // 平台手续费百分比-3.9%
//        BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
//        // 平台百分比
//        BigDecimal adminDivide = BigDecimalUtils.divide(templateModel.getAdminRatio(), CalUtils.PERCENTAGE);
//        // 消费者拉新人百分比
//        BigDecimal consumerDivide = BigDecimalUtils.divide(templateModel.getConsumerInviteRatio(), CalUtils.PERCENTAGE);
//        // 扣除平台手续费比例后比例-96.1%
////        BigDecimal subtractPlatform = RATION.subtract(platformDivide);
//        // 平台总手续费 = 商品价格 * 平台手续费比例 不计分拥金额
//        BigDecimal independentPlatformPrice = price.multiply(platformDivide).setScale(2, RoundingMode.HALF_UP);
//        log.warn("平台总手续费：{}", independentPlatformPrice);
//        // 平台总分账金额 = 平台总手续费 * 平台分账比例
//        BigDecimal platPrice;
//        BigDecimal consumerInvitePrice;
//        if (consumerInviteUser) {
//            platPrice = independentPlatformPrice.multiply(adminDivide).setScale(2, RoundingMode.HALF_UP);
//            consumerInvitePrice = independentPlatformPrice.multiply(consumerDivide).setScale(2, RoundingMode.HALF_UP);
//        } else {
//            platPrice = independentPlatformPrice.multiply(CalUtils.add(adminDivide, consumerDivide)).setScale(2, RoundingMode.HALF_UP);
//            consumerInvitePrice = BigDecimal.ZERO;
//        }
//        // 消费者拉新人分账金额 = 平台总分账金额 - 平台分账金额
//        orderIndependentPrice.setInvitePrice(consumerInvitePrice);
//        // 分红总金额 =  平台总手续费 - 平台分账金额 - 消费者拉新人分账金额
//        BigDecimal sharePrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice);
//        // 国家站站长分账金额 = 分红总金额 * 国家站站长分账比例
//        BigDecimal countryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCountryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//        orderIndependentPrice.setCountryPrice(countryPrice);
//        BigDecimal agencyPrice = BigDecimal.ZERO;
//        // 城市站站长分账金额 = 分红总金额 * 城市站站长分账比例
//        BigDecimal cityPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//        // 代理算价
//        if (agencyId && null != templateModel.getAgencyRatio() && !BigDecimal.ZERO.equals(templateModel.getAgencyRatio())) {
//            // 代理分账金额 = 平台总手续费 -（平台分账金额+国家站长分账金额+城市站长分账金额）
//            agencyPrice = CalUtils.sub(independentPlatformPrice, platPrice, countryPrice);
//        } else {
//            // 剩下的钱
//            BigDecimal lastPrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice, countryPrice, cityPrice);
//            platPrice = CalUtils.add(platPrice, lastPrice);
//        }
//        // 平台分账金额
//        orderIndependentPrice.setAdminPrice(platPrice);
//        orderIndependentPrice.setCityPrice(cityPrice);
//        orderIndependentPrice.setAgencyPrice(agencyPrice);
//        // 如果是分销订单并且有拉新人就计算拉新人的费用
//        if (independentOrder) {
//            // 分销费用 = 商品总金额 *（1- 平台手续费比例） * 商户分佣比例
//            // 商户设置分佣比例
//            BigDecimal independentShop = BigDecimalUtils.divide(independentRatio, CalUtils.PERCENTAGE);
//            log.warn("商户设置分佣比例：{}", independentShop);
//            // 拉新人金额 = 商户扣除平台手续费后金额 * 商品分佣比例 - 分销人所得金额
//            // 分销总金额
//            BigDecimal independentPrice = price1.multiply(independentShop).setScale(2, RoundingMode.HALF_UP);
//            // 分销人获得总金额
//            orderIndependentPrice.setDistributorPrice(productIndependentPrice);
//            // 判断分销人是否有拉新人
//            if (null != independentRegUserId) {
//                BigDecimal independentInvitePrice = CalUtils.sub(independentPrice, productIndependentPrice);
//                // 分销人的拉新人的分账金额
//                orderIndependentPrice.setDistributorInvitePrice(independentInvitePrice);
//                log.warn("拉新人分账金额：{}", independentInvitePrice);
//            } else {
//                // 没有拉新人的情况下剩下钱都给平台
//                orderIndependentPrice.setAdminPrice(CalUtils.add(platPrice, CalUtils.sub(independentPrice, productIndependentPrice)));
//            }
//        }
//        // 分销单状态
//        orderIndependentPrice.setIndependentOrder(independentOrder);
//        return orderIndependentPrice;
//    }

    @Override
    public Map<String, Object> preorder(SohuPreOrderReqBo preOrderReqBo) {
        if (CollUtil.isEmpty(preOrderReqBo.getOrderDetails())) {
            throw new ServiceException("Pre-order details cannot be empty");
        }
        LoginUser user = LoginHelper.getLoginUser();
        if (ObjectUtil.isNull(user)) {
            throw new ServiceException("用户未登录");
        }
        // 校验预下单商品信息
        SohuPreOrderModel preOrderVo = validatePreOrderRequest(preOrderReqBo, user);
        List<SohuShopOrderInfoModel> orderInfoList = new ArrayList<>();
        for (SohuPreStoreOrderModel orderVo : preOrderVo.getOrderList()) {
            orderInfoList.addAll(orderVo.getOrderInfoList());
        }
        // 商品总计金额
        BigDecimal proTotalPrice = orderInfoList.stream().map(e -> e.getPrice().multiply(new BigDecimal(e.getPayNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        preOrderVo.setProTotalFee(proTotalPrice);
        // todo
        // 购买商品总数量
        long totalPayNum = orderInfoList.stream().mapToLong(SohuShopOrderInfoModel::getPayNum).sum();

        // 如果总和小于等于 Integer.MAX_VALUE，则转换为 int，否则使用 Integer.MAX_VALUE
        int orderProNum = totalPayNum <= Integer.MAX_VALUE ? (int) totalPayNum : Integer.MAX_VALUE;
//        int orderProNum = orderInfoList.stream().mapToInt(SohuShopOrderInfoModel::getPayNum).sum();
        preOrderVo.setOrderProNum(orderProNum);
        // 获取默认地址，没有默认地址则取最新地址
        //SohuUserAddressVo userAddress = remoteMiddleUserAddressService.getDefaultByUserId(user.getUserId());
        SohuUserAddressVo userAddress = remoteMiddleUserAddressService.getDefaultByUserId(user.getUserId(), preOrderReqBo.getSysSource());
        if (ObjectUtil.isNull(userAddress)) {
            //userAddress = remoteMiddleUserAddressService.getLatestAddressByUserId(user.getUserId());
            userAddress = remoteMiddleUserAddressService.getLatestAddressByUserId(user.getUserId(), preOrderReqBo.getSysSource());
        }
        if (ObjectUtil.isNotNull(userAddress)) {
            preOrderVo.setAddressId(userAddress.getId());
            preOrderVo.setRegionId(Long.valueOf(userAddress.getProvinceCode()));
            preOrderVo.setRealName(userAddress.getName());
            preOrderVo.setPhone(userAddress.getPhone());
            preOrderVo.setDetail(userAddress.getDetail());
            preOrderVo.setCountry("中国");
            // todo email没有
            preOrderVo.setEmail(userAddress.getPhone());

            // 运费
            preOrderVo.setFreightFee(getTotalFreightFee(orderInfoList, preOrderVo.getRegionId()));
        } else {
            // 没有地址，则运费为0
            preOrderVo.setAddressId(0L);
            preOrderVo.setFreightFee(BigDecimal.ZERO);
        }

        // 实际支付金额
        BigDecimal totalPrice = preOrderVo.getProTotalFee().add(preOrderVo.getFreightFee());
        preOrderVo.setTotalPrice(totalPrice);
        preOrderVo.setPayFee(totalPrice);
        // 站点id
        preOrderVo.setSiteId(preOrderReqBo.getSiteId());
        if (BooleanUtil.isTrue(preOrderReqBo.getIsProcess())) {
            // 需要过审时,站点默认是武汉站点
            preOrderVo.setSiteId(11L);
        }
        if (null != preOrderReqBo.getIndependentUserId()) {
            preOrderVo.setIndependentUserId(preOrderReqBo.getIndependentUserId());
        }
        // 缓存订单
        String key = user.getUserId() + String.valueOf(System.currentTimeMillis() / 1000) + NumberUtil.getUuid();
//        redisUtil.set(RedisConstants.USER_READY_ORDER_KEY + key, JSONObject.toJSONString(preOrderVo), Constants.ORDER_CASH_CONFIRM, TimeUnit.MINUTES);
        RedisUtils.setCacheObject(key, preOrderVo, Duration.ofMinutes(5));
        log.info("预下单缓存信息：{}", JSONObject.toJSONString(preOrderVo));
        Map<String, Object> map = Maps.newHashMap();
        map.put("preOrderNo", key);
        return map;
    }

    @Override
    public SohuPreOrderModel loadPreOrder(String preOrderNo) {
        // 通过缓存获取预下单对象
        boolean exists = RedisUtils.isExistsObject(preOrderNo);
        if (!exists) {
            throw new ServiceException("Pre-order order does not exist");
        }
        Object cacheObject = RedisUtils.getCacheObject(preOrderNo);
        if (ObjectUtil.isNotNull(cacheObject)) {
            return JSONObject.parseObject(JSON.toJSONString(cacheObject), SohuPreOrderModel.class);
        }
        return null;
    }

    @Override
    public Boolean cancelOrder(String orderNo) {
        SohuShopMasterOrderVo masterOrder = masterOrderService.queryByMasterOrderNo(orderNo);
        if (ObjectUtil.isNull(masterOrder)) {
            throw new ServiceException("order does not exist");
        }
        if (masterOrder.getPaid()) {
            throw new ServiceException("Paid orders cannot be cancelled");
        }
        Boolean execute = transactionTemplate.execute(e -> {
            // todo 判断是否是积分兑换订单
//            if (masterOrder.getPayType().equals(PayTypeEnum.PAY_TYPE_INTEGRAL.getStatus())) {
//                // 查询积分兑换明细
////                QueryWrapper<UserIntegralDetail> query = Wrappers.query();
////                query.eq("exchange_no", masterOrderService.getByOrderNo(masterOrder.getOutTradeNo()));
////                UserIntegralDetail integralDetail = userIntegralDetailService.getOne(query);
////                if (ObjectUtil.isNotNull(integralDetail) && integralDetail.getExchangeStatus() != 3) {
////                    integralDetail.setExchangeStatus(3);
////                    // 积分兑换明细-支付取消
////                    userIntegralDetailService.updateById(integralDetail);
////                    // 积分兑换账户-支付取消回退积分
////                    QueryWrapper<UserIntegral> queryIntegral = Wrappers.query();
////                    queryIntegral.eq("id", integralDetail.getUserIntegralId());
////                    UserIntegral userIntegral = userIntegralService.getById(queryIntegral);
////                    userIntegral.setCurrentIntegral(userIntegral.getCurrentIntegral() - userIntegral.getChangeIntegral());
////                    userIntegral.setChangeType(false);
////                    userIntegral.setUsedIntegral(userIntegral.getUsedIntegral() - userIntegral.getExpiredIntegral());
////                    userIntegralService.updateById(userIntegral);
////                }
//            }
            masterOrderService.cancel(masterOrder.getOrderNo());
            cancelByMasterNo(masterOrder.getOrderNo(), true);
            return Boolean.TRUE;
        });
        if (Boolean.TRUE.equals(execute)) {
            //后续操作放入redis
//            redisUtil.lPush(TaskConstants.ORDER_TASK_REDIS_KEY_AFTER_CANCEL_BY_USER, masterOrder.getOrderNo());
            // 取消订单相关操作 库存扣减
//            orderSyncService.cancel(masterOrder);
            // 延时队列
            MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(masterOrder), "cancel_shop_order");
            remoteStreamMqService.sendDelayMsg(mqMessaging, 1L);
        }
        return execute;
    }

    /**
     * 根据masterOrderNo取消订单
     *
     * @param masterOrderNo 主订单号
     * @param isUser        是否用户取消
     */
    @Override
    public Boolean cancelByMasterNo(String masterOrderNo, boolean isUser) {
        LambdaUpdateWrapper<SohuShopOrder> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_CANCEL);
        wrapper.set(SohuShopOrder::getIsUserCancel, isUser);
        wrapper.eq(SohuShopOrder::getMasterOrderNo, masterOrderNo);
        return this.baseMapper.update(new SohuShopOrder(), wrapper) > 0;
    }

    @Override
    public SohuApplyRefundOrderInfoModel refundApplyOrder(String orderNo) {
        SohuShopOrder storeOrder = getByOrderNo(orderNo);
        SohuApplyRefundOrderInfoModel response = new SohuApplyRefundOrderInfoModel();
        BeanUtils.copyProperties(storeOrder, response);
        // 订单详情对象列表
        List<SohuShopOrderInfoVo> infoVoList = infoService.getListByOrderNo(orderNo);
        List<SohuShopOrderInfoModel> infoResponseList = CollUtil.newArrayList();
        infoVoList.forEach(e -> {
            SohuShopOrderInfoModel orderInfoResponse = new SohuShopOrderInfoModel();
            BeanUtils.copyProperties(e, orderInfoResponse);
            infoResponseList.add(orderInfoResponse);
        });
        response.setOrderInfoList(infoResponseList);
        return response;
    }

    @Override
    public Boolean refundApply(SohuOrderRefundApplyReqBo refundApplyReqBo) {
        SohuShopOrder storeOrder = getByOrderNo(refundApplyReqBo.getOrderNo());
        if (ObjectUtil.isNull(storeOrder) || storeOrder.getIsUserDel() || storeOrder.getIsMerchantDel()) {
            throw new ServiceException("order does not exist");
        }
        if (!storeOrder.getPaid()) {
            throw new ServiceException("Unpaid orders cannot be refunded");
        }
       /* if (storeOrder.getStatus().equals(OrderConstants.ORDER_STATUS_AWAIT_RECEIVING)) {
            throw new ServiceException("Pending orders cannot be refunded");
        }*/
        if (storeOrder.getRefundStatus().equals(OrderConstants.ORDER_REFUND_STATUS_APPLY)) {
            throw new RuntimeException("已申请退款，请勿重复申请");
        }
        if (storeOrder.getRefundStatus().equals(OrderConstants.ORDER_REFUND_STATUS_REFUNDING)) {
            throw new ServiceException("Order refund process");
        }
        if (storeOrder.getRefundStatus().equals(OrderConstants.ORDER_REFUND_STATUS_REFUND)) {
            throw new ServiceException("Order has been refunded");
        }
        // storeOrder.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_APPLY);
        SohuShopRefundOrder refundOrder = new SohuShopRefundOrder();
        refundOrder.setRefundOrderNo(NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND));
        refundOrder.setShopOrderNo(storeOrder.getOrderNo());
        refundOrder.setMasterOrderNo(storeOrder.getMasterOrderNo());
        refundOrder.setMerId(storeOrder.getMerId());
        refundOrder.setUserId(storeOrder.getUserId());
        refundOrder.setEmail(storeOrder.getEmail());
        refundOrder.setReceiverName(storeOrder.getReceiverName());
        refundOrder.setUserPhone(storeOrder.getUserPhone());
        refundOrder.setUserDetailAddress(storeOrder.getUserDetailAddress());
        refundOrder.setRefundPrice(storeOrder.getPayPrice());
        refundOrder.setTotalNum(storeOrder.getTotalNum());
        refundOrder.setRefundReasonWap(refundApplyReqBo.getText());
        refundOrder.setRefundReasonWapImg(refundApplyReqBo.getRefundReasonWapImg());
        refundOrder.setRefundReasonWapExplain(refundApplyReqBo.getRefundReasonWapExplain());
        refundOrder.setRefundType(refundApplyReqBo.getRefundType());
        refundOrder.setOpenClientId(storeOrder.getOpenClientId());
        if (RefundTypeEnums.ONLY_REFUND.equals(refundApplyReqBo.getRefundType())) {
            refundOrder.setRefundStatus(RefundConstants.ONLY_REFUND);
            storeOrder.setRefundStatus(RefundConstants.ONLY_REFUND);
        } else if (RefundTypeEnums.RETURN_REFUND.equals(refundApplyReqBo.getRefundType())) {
            refundOrder.setRefundStatus(RefundConstants.RETURN_REFUND);
            storeOrder.setRefundStatus(RefundConstants.RETURN_REFUND);
        }
        Boolean execute = transactionTemplate.execute(e -> {
            // 判断是否是积分兑换订单
            if (storeOrder.getPayType().equals(PayTypeEnum.PAY_TYPE_INTEGRAL.getStatus())) {
                refundOrder.setRefundIntegral(storeOrder.getPayIntegral());
                // todo 查询积分兑换明细
//                QueryWrapper<UserIntegralDetail> query = Wrappers.query();
//                query.eq("exchange_no", masterOrderService.getByOrderNo(storeOrder.getMasterOrderNo()).getOutTradeNo());
//                UserIntegralDetail integralDetail = userIntegralDetailService.getOne(query);
//                if (ObjectUtil.isNotNull(integralDetail) && integralDetail.getExchangeStatus() == 1 || integralDetail.getExchangeStatus() == 2) {
//                    integralDetail.setExchangeStatus(3);
//                    // 积分兑换明细-支付取消
//                    userIntegralDetailService.updateById(integralDetail);
//                    // 积分兑换账户-支付取消回退积分
//                    QueryWrapper<UserIntegral> queryIntegral = Wrappers.query();
//                    queryIntegral.eq("id", integralDetail.getUserIntegralId());
//                    UserIntegral userIntegral = userIntegralService.getById(queryIntegral);
//                    userIntegral.setCurrentIntegral(userIntegral.getCurrentIntegral() - userIntegral.getChangeIntegral());
//                    userIntegral.setChangeType(false);
//                    userIntegral.setUsedIntegral(userIntegral.getUsedIntegral() - userIntegral.getExpiredIntegral());
//                    userIntegralService.updateById(userIntegral);
//                }
            }
            this.updateById(storeOrder);
            //发送申请退款消息通知
            sendRefundIngNotify(refundOrder);
            refundOrderMapper.insert(refundOrder);
            refundStatusService.createLog(refundOrder.getRefundOrderNo(), OrderConstants.REFUND_ORDER_LOG_TYPE_APPLY, StrUtil.format(OrderConstants.ORDER_LOG_MESSAGE_REFUND_APPLY, refundApplyReqBo.getExplain()));
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            throw new ServiceException("Failed to apply for refund");
        }
        return Boolean.TRUE;
    }

    /**
     * 申请退款通知
     *
     * @param model 订单
     */
    private void sendRefundIngNotify(SohuShopRefundOrder model) {
        // todo 根据订单id查询用户信息
        NoticeShopBuyContent content = new NoticeShopBuyContent();
        content.setTitle(ShopNoticeEnum.refundIngTitle);
        content.setNoticeTime(DateUtils.getTime());
        content.setDetailId(model.getId());
        List<SohuShopOrderInfoVo> shopOrderInfos = infoService.getListByOrderNo(model.getShopOrderNo());
        SohuShopOrderInfoVo orderInfo = shopOrderInfos.get(0);
        content.setShopId(orderInfo.getProductId());
        content.setOrderNo(model.getRefundOrderNo());
        content.setState(OrderConstants.ORDER_REFUND_STATUS_REFUNDING);
        NoticeShopBuyContentDetail detail = new NoticeShopBuyContentDetail();
        detail.setDesc(ShopNoticeEnum.refundIngDesc);
        detail.setShopTitle(orderInfo.getProductName());
        detail.setCoverImage(orderInfo.getImage());
        SohuProductAttrValueModel attrValue = productAttrValueService.queryById(orderInfo.getProductAttrValueId());
        detail.setAttrValue(attrValue.getAttrValue());
        detail.setPrice(orderInfo.getPrice());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleShopNoticeService.sendNotice(UserConstants.ADMIN_ID, model.getUserId(), ShopNoticeEnum.refundIngTitle, contentJson);

    }

    @Override
    public Boolean taskOrder(String orderNo) {
        SohuShopOrder storeOrder = getByOrderNo(orderNo);
        Long userId = LoginHelper.getUserId();
        if (ObjectUtil.isNull(storeOrder) || !Objects.equals(userId, storeOrder.getUserId())) {
            //订单号错误
            throw new ServiceException("No relevant order information found!");
        }
        if (!storeOrder.getStatus().equals(OrderConstants.ORDER_STATUS_AWAIT_RECEIVING)) {
            throw new ServiceException("wrong order status");
        }

        //已收货，待评价
        storeOrder.setStatus(OrderConstants.ORDER_STATUS_OVER);
        return transactionTemplate.execute(e -> {
            // todo 如果是积分兑换-修改积分兑换明细表
//            if (storeOrder.getPayType().equals(PayTypeEnum.PAY_TYPE_INTEGRAL.getStatus())) {
//                // 查询主订单
//                MasterOrder masterOrder = masterOrderService.getByOrderNo(storeOrder.getMasterOrderNo());
//                // 利用主订单的交易号来查询积分兑换明细
//                QueryWrapper<UserIntegralDetail> query = Wrappers.query();
//                query.eq("exchange_no", masterOrder.getOutTradeNo());
//                UserIntegralDetail integralDetail = userIntegralDetailService.getOne(query);
//                // 兑换状态 0-提交 1-支付完成 2-结单 3-退单
//                integralDetail.setExchangeStatus(2);
//                userIntegralDetailService.updateById(integralDetail);
//            }
            this.updateById(storeOrder);
            infoService.orderReceipt(storeOrder.getOrderNo());
            // 日志
            statusService.createLog(storeOrder.getOrderNo(), OrderConstants.ORDER_LOG_RECEIVING, OrderConstants.ORDER_LOG_MESSAGE_RECEIVING);
            // 确认收货队列分账
            MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(storeOrder), "task_delay_order_independent");
            remoteStreamMqService.sendDelayMsg(mqMessaging, 12L);
            return Boolean.TRUE;
        });
    }

    @Override
    public SohuMasterOrderInfoModel masterOrderInfo(String orderNo) {
        Long userId = LoginHelper.getUserId();
        if (ObjectUtil.isNull(userId)) {
            throw new ServiceException("未登录");
        }
        return getMasterOrderInfo(orderNo, userId);
    }

    @Override
    public Boolean deleteOrder(String orderNo) {
        SohuShopOrder storeOrder = getByOrderNo(orderNo);
        Long userId = LoginHelper.getUserId();
        if (ObjectUtil.isNull(storeOrder) || !Objects.equals(userId, storeOrder.getUserId())) {
            throw new RuntimeException("No relevant order information found!");
        }
        if (storeOrder.getIsUserDel() || storeOrder.getIsMerchantDel()) {
            throw new RuntimeException("Order deleted!");
        }
        if (!storeOrder.getStatus().equals(OrderConstants.ORDER_STATUS_OVER)) {
            throw new RuntimeException("Only completed orders can be deleted!");
        }
        if (storeOrder.getPaid()) {
            if (StringUtils.isNotBlank(storeOrder.getRefundStatus()) && !storeOrder.getRefundStatus().equals(OrderConstants.MERCHANT_REFUND_ORDER_STATUS_FALSE) && !storeOrder.getRefundStatus().equals(OrderConstants.ORDER_REFUND_STATUS_REFUND)) {
                throw new RuntimeException("Orders cannot be deleted during the refund process!");
            }
        } else {
            throw new RuntimeException("Unpaid orders cannot be deleted!");
        }

        //可以删除
        storeOrder.setIsUserDel(true);
        return transactionTemplate.execute(e -> {
            this.baseMapper.updateById(storeOrder);
            //日志
            statusService.createLog(storeOrder.getOrderNo(), OrderConstants.ORDER_LOG_REMOVE, OrderConstants.ORDER_LOG_MESSAGE_REMOVE);
            return Boolean.TRUE;
        });
    }

    @Override
    public KuaidiModel queryTrack(SohuQueryTrackBo param) {
        SohuShopOrderLogisticsVo orderLogistics = logisticsService.queryByExpNo(param.getNum());
        if (ObjectUtil.isNull(orderLogistics)) {
            return null;
        }
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom(orderLogistics.getExpCode().toLowerCase());
        queryTrackParam.setNum(orderLogistics.getExpNo());
        if (StringUtils.isNotBlank(param.getOrder())) {
            queryTrackParam.setOrder(param.getOrder());
        }
        if (StringUtils.isNotBlank(param.getTo())) {
            queryTrackParam.setTo(param.getTo());
        }
        if (StringUtils.isNotBlank(param.getPhone())) {
            queryTrackParam.setPhone(param.getPhone());
        }
        if (StringUtils.isNotBlank(param.getShow())) {
            queryTrackParam.setShow(param.getShow());
        }
        if (StringUtils.isNotBlank(param.getResultv2())) {
            queryTrackParam.setResultv2(param.getResultv2());
        }
        if (StringUtils.isNotBlank(param.getFrom())) {
            queryTrackParam.setFrom(param.getFrom());
        }
        String query = KuaiDiUtil.query(queryTrackParam);
        KuaidiModel model = JSONUtil.toBean(query, KuaidiModel.class);
        model.setName(orderLogistics.getExpName());
        return model;
    }

    @Override
    public KuaidiModel getLogisticsByOrderNo(String orderNo, Long clientId) {
        SohuShopOrder entity = this.getByOrderNo(orderNo);
        if (Objects.isNull(entity)) {
            return null;
        }
        SohuOpenClientMerchantVo sohuOpenClientMerchantVo = remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantId(clientId, entity.getMerId());
        if (Objects.isNull(sohuOpenClientMerchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        SohuShopOrderLogistics orderLogistics = logisticsService.queryByOrderNo(orderNo);
        if (ObjectUtil.isNull(orderLogistics)) {
            return null;
        }
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom(orderLogistics.getExpCode().toLowerCase());
        queryTrackParam.setNum(orderLogistics.getExpNo());
        String query = KuaiDiUtil.query(queryTrackParam);
        KuaidiModel model = JSONUtil.toBean(query, KuaidiModel.class);
        model.setName(orderLogistics.getExpName());
        return model;
    }

    /**
     * 查询物流信息
     *
     * @param
     * @return
     */
    @Override
    public KuaidiModel findLogisticsByExpNo(SohuShopOrderLogistics orderLogistics) {
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom(orderLogistics.getExpCode().toLowerCase());
        queryTrackParam.setNum(orderLogistics.getExpNo());
        queryTrackParam.setPhone(orderLogistics.getUserPhone());
        String query = KuaiDiUtil.query(queryTrackParam);
        KuaidiModel model = JSONUtil.toBean(query, KuaidiModel.class);
        return model;
    }

    @Override
    public Map<String, List<SohuShopOrderVo>> queryMap(List<String> masterOrderNos) {
        LambdaQueryWrapper<SohuShopOrder> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuShopOrder::getMasterOrderNo, masterOrderNos);
        List<SohuShopOrderVo> shopOrders = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(shopOrders)) {
            return new HashMap<>();
        }
        return shopOrders.stream().collect(Collectors.groupingBy(SohuShopOrderVo::getMasterOrderNo));
    }

    /**
     * 查询某主单号的所有的子订单号
     *
     * @param masterOrderNo 主订单号
     * @return
     */
    @Override
    public List<String> querySubOrderNos(String masterOrderNo) {
        return baseMapper.querySubOrderNos(masterOrderNo);
    }

    @Override
    public List<String> querySubOrderNos(List<String> masterOrderNos) {
        LambdaQueryWrapper<SohuShopOrder> lqw = new LambdaQueryWrapper<>();
        lqw.select(SohuShopOrder::getOrderNo);
        lqw.in(SohuShopOrder::getMasterOrderNo, masterOrderNos);
        List<SohuShopOrderVo> shopOrders = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(shopOrders)) {
            return null;
        }
        return shopOrders.stream().map(SohuShopOrderVo::getOrderNo).collect(Collectors.toList());
    }

    /**
     * APP 订单查询 where status 封装
     *
     * @param queryWrapper 查询条件
     * @param status       状态
     */
    private void statusApiByWhere(LambdaQueryWrapper<SohuShopOrder> queryWrapper, String status) {
        switch (status) {
            case OrderConstants.MERCHANT_ORDER_STATUS_UNPAID: // 未支付
                queryWrapper.eq(SohuShopOrder::getPaid, false);
                queryWrapper.eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_SHIPPING);
                queryWrapper.eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
                break;
            case OrderConstants.ORDER_STATUS_SHIPPING: // 待发货
                queryWrapper.eq(SohuShopOrder::getPaid, true);
                queryWrapper.eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_SHIPPING);
                queryWrapper.eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
                break;
            case OrderConstants.ORDER_STATUS_AWAIT_RECEIVING: // 待收货
                queryWrapper.eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_AWAIT_RECEIVING);
                queryWrapper.eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
                break;
            case OrderConstants.ORDER_STATUS_OVER: // 已收货/已完成
                queryWrapper.eq(SohuShopOrder::getPaid, true);
                queryWrapper.in(SohuShopOrder::getStatus, List.of(OrderConstants.ORDER_STATUS_OVER, OrderConstants.ORDER_STATUS_RECEIVE));
                queryWrapper.eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
                break;
            default:
                break;
        }
        queryWrapper.eq(SohuShopOrder::getIsUserDel, false);
        queryWrapper.eq(SohuShopOrder::getIsMerchantDel, false);
    }

    /**
     * APP 订单查询 where status 封装
     *
     * @param bo     查询条件
     * @param status 状态
     */
    private void statusApi(SohuOrderReqBo bo, String status) {
        switch (status) {
            case OrderConstants.MERCHANT_ORDER_STATUS_UNPAID: // 未支付
                bo.setPaid(false);
                bo.setStatus(OrderConstants.ORDER_STATUS_SHIPPING);
                bo.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
                break;
            case OrderConstants.ORDER_STATUS_SHIPPING: // 待发货
                bo.setPaid(true);
                bo.setStatus(OrderConstants.ORDER_STATUS_SHIPPING);
                bo.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
                break;
            case OrderConstants.ORDER_STATUS_AWAIT_RECEIVING: // 待收货
                bo.setStatus(OrderConstants.ORDER_STATUS_AWAIT_RECEIVING);
                bo.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
                ;
                break;
            case OrderConstants.ORDER_STATUS_OVER: // 已收货/已完成
                bo.setPaid(true);
                bo.setStatus(OrderConstants.ORDER_STATUS_OVER);
                bo.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
                break;
            default:
                break;
        }
    }

    /**
     * 获取主订单信息
     *
     * @param orderNo
     * @param userId
     */
    private SohuMasterOrderInfoModel getMasterOrderInfo(String orderNo, Long userId) {
        SohuShopMasterOrderVo masterOrder = masterOrderService.queryByMasterOrderNo(orderNo);
        // 获取子订单
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getMasterOrderNo, orderNo);
        List<SohuShopOrderVo> orderList = baseMapper.selectVoList(lqw);
        List<Long> merIdList = orderList.stream().map(SohuShopOrderVo::getMerId).distinct().collect(Collectors.toList());

        // 获取用户的店铺ID(店铺商家查看订单详情)
        List<SohuMerchantModel> merchantList = remoteMerchantService.selectByUserId(userId);
        List<Long> merchantIds = merchantList.stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
        boolean contains = merchantIds.stream().anyMatch(merIdList::contains);
        if ((ObjectUtil.isNull(masterOrder) || !masterOrder.getUserId().equals(userId) || masterOrder.getIsCancel()) && !contains) {
            throw new ServiceException("order does not exist");
        }
        SohuMasterOrderInfoModel response = new SohuMasterOrderInfoModel();
        BeanUtils.copyProperties(masterOrder, response);
        List<SohuShopOrder> storeOrderList = this.getListByMasterNo(masterOrder.getOrderNo());
        List<SohuStoreOrderInfoModel> orderResponseList = storeOrderList.stream().map(storeOrder -> {
            SohuMerchantModel merchant = remoteMerchantService.selectById(storeOrder.getMerId());
            List<SohuShopOrderInfoVo> infoList = infoService.getListByOrderNo(storeOrder.getOrderNo());
            SohuStoreOrderInfoModel orderResponse = new SohuStoreOrderInfoModel();
            BeanUtils.copyProperties(storeOrder, orderResponse);
            orderResponse.setMerName(merchant.getName());
            List<SohuOrderInfoModel> infoResponseList = infoList.stream().map(info -> {
                SohuOrderInfoModel orderInfoResponse = new SohuOrderInfoModel();
                BeanUtils.copyProperties(info, orderInfoResponse);
                return orderInfoResponse;
            }).collect(Collectors.toList());
            orderResponse.setOrderInfoList(infoResponseList);
            return orderResponse;
        }).collect(Collectors.toList());
        response.setOrderList(orderResponseList);
        return response;
    }

    @Override
    public SohuStoreOrderInfoResModel shopOrderInfo(String orderNo) {
        Long userId = LoginHelper.getUserId();

        // 查询订单
        SohuShopOrder storeOrder = this.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(storeOrder) || storeOrder.getIsUserDel() || storeOrder.getIsMerchantDel()) {
            throw new ServiceException("order does not exist");
        }
        // 获取用户的店铺ID(店铺商家查看订单详情)
        List<SohuMerchantModel> merchantList = remoteMerchantService.selectByUserId(userId);
        List<Long> merchantIds = merchantList.stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
        boolean contains = merchantIds.contains(storeOrder.getMerId());
        if (!storeOrder.getUserId().equals(userId) && !contains) {
            throw new ServiceException("order does not exist");
        }
        // 组装商户订单详情信息
        SohuStoreOrderInfoResModel storeOrderDetailResponse = new SohuStoreOrderInfoResModel();
        BeanUtils.copyProperties(storeOrder, storeOrderDetailResponse);
        // 如果有拒绝退款，展示全部拒绝退款原因
        LambdaQueryWrapper<SohuShopRefundOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopRefundOrder::getShopOrderNo, orderNo);
        List<SohuShopRefundOrder> refundOrderLists = refundOrderMapper.selectList(lqw);
        if (CollectionUtils.isNotEmpty(refundOrderLists)) {
            List<SohuShopRefundOrderModel> refundOrderModels = Lists.newArrayList();
            for (SohuShopRefundOrder refundOrderList : refundOrderLists) {
                SohuShopRefundOrderModel refundOrderModel = new SohuShopRefundOrderModel();
                BeanUtils.copyProperties(refundOrderList, refundOrderModel);
                refundOrderModels.add(refundOrderModel);
            }
            storeOrderDetailResponse.setStoreRefundOrderList(refundOrderModels);
        }
        // 订单详情对象列表
        List<SohuShopOrderInfoModel> infoResponseList = CollUtil.newArrayList();
        List<SohuShopOrderInfoVo> infoList = infoService.getListByOrderNo(storeOrder.getOrderNo());
        infoList.forEach(e -> {
            SohuShopOrderInfoModel orderInfoResponse = new SohuShopOrderInfoModel();
            BeanUtils.copyProperties(e, orderInfoResponse);
            infoResponseList.add(orderInfoResponse);
        });
        storeOrderDetailResponse.setOrderInfoList(infoResponseList);
        // 商户名称
        SohuMerchantModel merchant = remoteMerchantService.selectById(storeOrder.getMerId());
        storeOrderDetailResponse.setMerName(merchant.getName());
        return storeOrderDetailResponse;
    }

    @Override
    public SohuRefundOrderInfoModel refundOrderInfo(String refundOrderNo) {
        LambdaQueryWrapper<SohuShopRefundOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopRefundOrder::getRefundOrderNo, refundOrderNo);
        SohuShopRefundOrder refundOrder = refundOrderMapper.selectOne(lqw);
        if (ObjectUtil.isNull(refundOrder)) {
            throw new ServiceException("Refund order does not exist");
        }
        SohuRefundOrderInfoModel infoResponse = new SohuRefundOrderInfoModel();
        BeanUtils.copyProperties(refundOrder, infoResponse);
        SohuMerchantModel merchant = remoteMerchantService.selectById(refundOrder.getMerId());
        infoResponse.setMerName(merchant.getName());
        List<SohuShopOrderInfoVo> orderInfoList = infoService.getListByOrderNo(refundOrder.getShopOrderNo());
        List<SohuShopOrderInfoModel> infoResponseList = orderInfoList.stream().map(e -> {
            SohuShopOrderInfoModel orderInfoResponse = new SohuShopOrderInfoModel();
            BeanUtils.copyProperties(e, orderInfoResponse);
            return orderInfoResponse;
        }).collect(Collectors.toList());
        infoResponse.setOrderInfoList(infoResponseList);
        return infoResponse;
    }

    /**
     * 校验商品库存
     *
     * @param sohuPreOrderModel
     * @return Map<String, Object>
     */
    private List<Map<String, Object>> validateProductStock(SohuPreOrderModel sohuPreOrderModel) {
        List<Map<String, Object>> recordList = CollUtil.newArrayList();
        List<SohuPreStoreOrderModel> orderList = sohuPreOrderModel.getOrderList();
        List<SohuShopOrderInfoModel> orderInfoList = CollUtil.newArrayList();
        for (SohuPreStoreOrderModel storeOrderVo : orderList) {
            orderInfoList.addAll(storeOrderVo.getOrderInfoList());
        }
        orderInfoList.forEach(e -> {
            // 查询商品信息
            SohuProductModel storeProduct = remoteProductService.queryById(e.getProductId());
            if (ObjectUtil.isNull(storeProduct)) {
                throw new ServiceException("Purchased product information does not exist");
            }
            if (storeProduct.getIsDel()) {
                throw new ServiceException("Purchased item deleted");
            }
            if (!storeProduct.getIsShow() || storeProduct.getIsForced()) {
                throw new ServiceException("The purchased item has been discontinued");
            }
            if (storeProduct.getStock().equals(0) || e.getPayNum() > storeProduct.getStock()) {
                throw new ServiceException("The purchased item is out of stock");
            }
            // 查询商品规格属性值信息
            SohuProductAttrValueModel attrValue = getStoreProductAttrValue(e.getProductAttrValueId(), e.getProductId(), "The purchased product specification information does not exist", e.getPayNum(), "The purchased item is out of stock");
            Map<String, Object> record = Maps.newHashMap();
            record.put("productId", e.getProductId());
            record.put("num", e.getPayNum());
            record.put("attrValueId", e.getProductAttrValueId());
            // todo 暂时不要-并发版本控制
//            record.put("attrValueVersion", attrValue.getVersion());
            recordList.add(record);
        });
        return recordList;
    }

    /**
     * 计算订单各种价格
     *
     * @param request
     * @param preOrderVo
     * @param user
     * @return SohuPreOrderModel
     */
    private SohuPreOrderModel computedPrice(SohuOrderComputedPriceResBo request, SohuPreOrderModel preOrderVo, LoginUser user) {
        // 计算各种价格
        SohuPreOrderModel priceResponse = new SohuPreOrderModel();
        priceResponse.setProTotalFee(preOrderVo.getProTotalFee());
        // 计算运费
        List<SohuShopOrderInfoModel> orderInfoList = new ArrayList<>();
        for (SohuPreStoreOrderModel orderVo : preOrderVo.getOrderList()) {
            orderInfoList.addAll(orderVo.getOrderInfoList());
        }
        preOrderVo.setFreightFee(getTotalFreightFee(orderInfoList, preOrderVo.getRegionId()));
        priceResponse.setFreightFee(preOrderVo.getFreightFee());
        // 计算优惠券金额
        if (ObjectUtil.isNull(request.getCouponId()) || request.getCouponId() <= 0) {
            priceResponse.setCouponFee(BigDecimal.ZERO);
        }
//        else {
//            // 判断优惠券是否可以使用
//            StoreCouponUser storeCouponUser = storeCouponUserService.getById(request.getCouponId());
//            if (ObjectUtil.isNull(storeCouponUser) || !storeCouponUser.getUid().equals(user.getUid())) {
//                throw new ServiceException("Coupon claim record does not exist！");
//            }
//            if (storeCouponUser.getStatus().equals(CouponConstants.STORE_COUPON_USER_STATUS_USED)) {
//                throw new ServiceException("This coupon has already been used！");
//            }
//
//            if (storeCouponUser.getStatus().equals(CouponConstants.STORE_COUPON_USER_STATUS_LAPSED)) {
//                throw new ServiceException("This coupon has expired！");
//            }
//            //判断是否在使用时间内
//            Date date = DateUtil.nowDateTime();
//            if (storeCouponUser.getStartTime().compareTo(date) > 0) {
//                throw new ServiceException("This coupon has not expired within the validity period！");
//            }
//            if (date.compareTo(storeCouponUser.getEndTime()) > 0) {
//                throw new ServiceException("This coupon has expired");
//            }
//            //检测优惠券信息
//            if (storeCouponUser.getUseType().equals(CouponConstants.COUPON_USE_TYPE_PRODUCT)) {
//                // 商品券
//                List<Integer> productIdList = orderInfoList.stream().map(SohuShopOrderInfoModel::getProductId).collect(Collectors.toList());
//                if (productIdList.size() < 1) {
//                    throw new ServiceException("No item found product");
//                }
//                //设置优惠券所提供的集合
//                List<Integer> primaryKeyIdList = CrmebUtil.stringToArray(storeCouponUser.getPrimaryKey());
//                //取两个集合的交集，如果是false则证明没有相同的值
//                //oldList.retainAll(newList)返回值代表oldList是否保持原样，如果old和new完全相同，那old保持原样并返回false。
//                //交集：listA.retainAll(listB) ——listA内容变为listA和listB都存在的对象；listB不变
//                primaryKeyIdList.retainAll(productIdList);
//                if (CollUtil.isEmpty(primaryKeyIdList)) {
//                    throw new ServiceException("This coupon is a commodity coupon, please use it after purchasing related products！");
//                }
//                List<SohuShopOrderInfoModel> infoList = orderInfoList.stream().filter(info -> primaryKeyIdList.contains(info.getProductId())).collect(Collectors.toList());
//                if (CollUtil.isEmpty(infoList)) {
//                    throw new ServiceException("This coupon is a commodity coupon, please use it after purchasing related products！");
//                }
//                BigDecimal proTotalPrice = infoList.stream().map(e -> e.getPrice().multiply(new BigDecimal(e.getPayNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
//                if (storeCouponUser.getMinPrice().compareTo(proTotalPrice) > 0) {
//                    throw new ServiceException("The total amount is less than the minimum coupon usage amount");
//                }
//                if (proTotalPrice.compareTo(storeCouponUser.getMoney()) > 0) {
//                    priceResponse.setCouponFee(storeCouponUser.getMoney());
//                } else {
//                    priceResponse.setCouponFee(proTotalPrice);
//                }
//            }
//            if (storeCouponUser.getUseType().equals(CouponConstants.COUPON_USE_TYPE_MERCHANT)) {
//                // 商家券
//                List<Integer> merIdList = preOrderVo.getOrderList().stream().map(SohuPreStoreOrderModel::getMerId).collect(Collectors.toList());
//                if (!merIdList.contains(storeCouponUser.getMerId())) {
//                    throw new ServiceException("This coupon is a merchant coupon, please use it after purchasing related products！");
//                }
//                preOrderVo.getOrderList().forEach(e -> {
//                    if (e.getMerId().equals(storeCouponUser.getMerId())) {
//                        List<SohuShopOrderInfoModel> infoList = e.getOrderInfoList();
//                        BigDecimal proTotalPrice = infoList.stream().map(i -> i.getPrice().multiply(new BigDecimal(i.getPayNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        if (storeCouponUser.getMinPrice().compareTo(proTotalPrice) > 0) {
//                            throw new ServiceException("The total amount is less than the minimum coupon usage amount");
//                        }
//                        if (proTotalPrice.compareTo(storeCouponUser.getMoney()) > 0) {
//                            priceResponse.setCouponFee(storeCouponUser.getMoney());
//                        } else {
//                            priceResponse.setCouponFee(proTotalPrice);
//                        }
//                    }
//                });
//            }
//        }

        BigDecimal payPrice;
        if (priceResponse.getProTotalFee().compareTo(priceResponse.getCouponFee()) > 0) {
            payPrice = priceResponse.getProTotalFee().add(priceResponse.getFreightFee()).subtract(priceResponse.getCouponFee());
        } else {
            priceResponse.setCouponFee(priceResponse.getProTotalFee());
            payPrice = priceResponse.getFreightFee();
        }
        priceResponse.setPayFee(payPrice);
        return priceResponse;
    }

    /**
     * 检测支付方式
     *
     * @param payType
     */
    private Boolean checkPayType(String payType) {
        ArrayList<String> list = CollUtil.newArrayList();
        list.add(PayTypeEnum.PAY_TYPE_PAYPAL.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_STRIPE.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_INTEGRAL.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_WECHAT_NATIVE.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_WECHAT_H5.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_WECHAT_JSAPI.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_WECHAT_APP.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_TIKTOK.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_BALANCE.getStatus());
        list.add(PayTypeEnum.PAY_TYPE_OFFLINE_PAY.getStatus());
        return list.contains(payType);
    }

    /**
     * 校验预下单商品信息
     *
     * @param request
     * @param user
     * @return SohuPreOrderModel
     */
    private SohuPreOrderModel validatePreOrderRequest(SohuPreOrderReqBo request, LoginUser user) {
        SohuPreOrderModel orderVo = new SohuPreOrderModel();
        List<SohuPreStoreOrderModel> storeOrderVoList = CollUtil.newArrayList();

        // todo 判断是否事积分支付。并且校验积分余额是否充足
//        if (request.getPreOrderType().equals("integral")) {
//            QueryWrapper<UserIntegral> query = Wrappers.query();
//            query.eq("yudao_user", user.getYudaoUser());
//            UserIntegral userIntegral = userIntegralDao.selectOne(query);
//            if (ObjectUtil.isNull(userIntegral)) {
//                throw new ServiceException("This user has no integral");
//            }
//            // 立即兑换只会有一条详情
//            PreOrderDetailRequest detailRequest = request.getOrderDetails().get(0);
//            if (ObjectUtil.isNull(detailRequest.getProductId())) {
//                throw new ServiceException("Product ID cannot be empty");
//            }
//            // 查询商品信息
//            StoreProduct storeProduct = getStoreProduct(detailRequest.getProductId(), detailRequest.getProductNum());
//            // 查询商品规格属性值信息
//            StoreProductAttrValue attrValue = getStoreProductAttrValue(detailRequest.getAttrValueId(), detailRequest.getProductId(), "The product specification information does not exist, please refresh and select again", detailRequest.getProductNum(), "Insufficient stock of product specifications, please refresh and select again");
//            // 计算兑换商品总积分并且校验积分是否充足
//            // 兑换商品数量
//            Integer productNum = request.getOrderDetails().get(0).getProductNum();
//            // 兑换单个商品所需积分
//            Integer wantIntegral = attrValue.getWantIntegral();
//            // 计算兑换商品总积分  单个商品积分*兑换商品数量
//            Integer totalIntegral = productNum * wantIntegral;
//            // 判断兑换积分
//            if (userIntegral.getCurrentIntegral() < totalIntegral) {
//                // 兑换积分不足
//                throw new ServiceException("Insufficient points for exchanging goods");
//            }
//            Merchant merchant = getMerchant(storeProduct);
//            PreStoreOrderVo storeOrderVo = new PreStoreOrderVo();
//            storeOrderVo.setMerId(merchant.getId());
//            storeOrderVo.setMerName(merchant.getName());
//            PreStoreOrderInfoVo infoVo = new PreStoreOrderInfoVo();
//            infoVo.setProductId(storeProduct.getId());
//            infoVo.setProductName(storeProduct.getStoreName());
//            infoVo.setAttrValueId(attrValue.getId());
//            infoVo.setImage(StrUtil.isNotBlank(attrValue.getImage()) ? attrValue.getImage() : storeProduct.getImage());
//            infoVo.setSku(attrValue.getSku());
//            // 暂时不考虑 积分+钱兑换
//            infoVo.setPrice(attrValue.getPrice());
//            infoVo.setPayNum(detailRequest.getProductNum());
//            infoVo.setVolume(attrValue.getVolume());
//            infoVo.setWeight(attrValue.getWeight());
//            infoVo.setPostage(storeProduct.getPostage());
//            // 添加商品积分
//            infoVo.setIntegral(attrValue.getWantIntegral());
//            // 添加商品兑换总积分
//            orderVo.setProTotalIntegral(totalIntegral);
//
//            List<PreStoreOrderInfoVo> infoList = CollUtil.newArrayList();
//            infoList.add(infoVo);
//            storeOrderVo.setOrderInfoList(infoList);
//            storeOrderVoList.add(storeOrderVo);
//        }

        // 购物车购买
        if (request.getPreOrderType().equals("shoppingCart")) {
            storeOrderVoList = validatePreOrderShopping(request, user);
            List<Long> cartIdList = request.getOrderDetails().stream().map(SohuPreOrderDetailReqBo::getShoppingCartId).distinct().collect(Collectors.toList());
            orderVo.setCartIdList(cartIdList);
        }

        // 立即购买
        if (request.getPreOrderType().equals("buyNow")) {
            // 立即购买只会有一条详情
            SohuPreOrderDetailReqBo detailRequest = request.getOrderDetails().get(0);
            // 普通商品
            if (ObjectUtil.isNull(detailRequest.getProductId())) {
                throw new ServiceException("Product ID cannot be empty");
            }
            if (ObjectUtil.isNull(detailRequest.getAttrValueId())) {
                throw new ServiceException("Product specification attribute value cannot be empty");
            }
            if (ObjectUtil.isNull(detailRequest.getProductNum()) || detailRequest.getProductNum() < 0) {
                throw new ServiceException("Purchase quantity must be greater than 0");
            }
            //分销商单
            SohuPmSharePubVo sohuPmSharePubVo = remotePmSharePubService.queryByIdOfEnable(request.getPmSharePubId());
            // 查询商品信息
            SohuProductModel storeProduct = getStoreProduct(detailRequest.getProductId(), detailRequest.getProductNum());
            SohuProductAttrValueModel attrValue = getStoreProductAttrValue(detailRequest.getAttrValueId(), detailRequest.getProductId(), "The product specification information does not exist, please refresh and select again", detailRequest.getProductNum(), "Insufficient stock of product specifications, please refresh and select again");
            SohuMerchantModel merchant = getMerchant(storeProduct);
            SohuPreStoreOrderModel storeOrderVo = new SohuPreStoreOrderModel();
            storeOrderVo.setMerId(merchant.getId());
            storeOrderVo.setMerName(merchant.getName());
            storeOrderVo.setOpenClientId(storeProduct.getOpenClientId());
            storeOrderVo.setMerIsSelf(merchant.getIsSelf());
            if (Objects.nonNull(sohuPmSharePubVo)) {
                storeOrderVo.setPmSharePubId(request.getPmSharePubId());
            }
            SohuShopOrderInfoModel infoVo = new SohuShopOrderInfoModel();
            infoVo.setProductId(storeProduct.getId());
            infoVo.setProductName(storeProduct.getStoreName());
            infoVo.setProductAttrValueId(attrValue.getId());
            infoVo.setImage(StrUtil.isNotBlank(attrValue.getImage()) ? attrValue.getImage() : storeProduct.getImage());
            infoVo.setSku(attrValue.getSku());
            infoVo.setPrice(attrValue.getPrice());
            // 入口站封装
//            infoVo.setSiteId(storeProduct.getsiteId())
            infoVo.setSiteId(detailRequest.getSiteId());
            infoVo.setSiteType(detailRequest.getSiteType());
            if (BooleanUtil.isTrue(request.getIsProcess())) {
                // 需要过审时,站点默认是武汉站点
                infoVo.setSiteId(11L);
                infoVo.setSiteType(1);
            }
            infoVo.setPayNum(detailRequest.getProductNum());
            infoVo.setVolume(attrValue.getVolume());
            infoVo.setWeight(attrValue.getWeight());
            infoVo.setPostage(storeProduct.getPostage());
            if (Objects.isNull(sohuPmSharePubVo)) {
                // 商品分销相关
                infoVo.setIndependentRatio(storeProduct.getIndependentRatio());
                infoVo.setDistributorPrice(storeProduct.getIndependentPrice());
            } else {
                //分享商单分销
                infoVo.setIndependentRatio(sohuPmSharePubVo.getIndependentRatio());
                infoVo.setDistributorPrice(sohuPmSharePubVo.getIndependentPrice());
            }
            infoVo.setThirdProductId(storeProduct.getThirdProductId());
            infoVo.setOpenClientId(storeProduct.getOpenClientId());
            List<SohuShopOrderInfoModel> infoList = CollUtil.newArrayList();
            infoList.add(infoVo);
            storeOrderVo.setOrderInfoList(infoList);
            storeOrderVoList.add(storeOrderVo);
        }
        orderVo.setOrderList(storeOrderVoList);
        return orderVo;
    }

//    /**
//     * 分账算价
//     *
//     * @param siteId
//     * @param price
//     * @param independentRatio
//     * @param productIndependentPrice
//     * @param independentOrder
//     * @param inviteUser
//     */
//    private SohuOrderIndependentPriceVo getShopIndependentPrice(Long siteId, BigDecimal price, BigDecimal independentRatio,
//                                                                BigDecimal productIndependentPrice, Boolean independentOrder,
//                                                                Boolean inviteUser) {
//        // 查询分账模版
//        SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(siteId, 1);
//        // 分账对象
//        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
//        // 平台手续费百分比-3.9%
//        BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), PERCENTAGE);
//        // 扣除平台手续费比例后比例-96.1%
//        BigDecimal subtractPlatform = RATION.subtract(platformDivide);
//        // 商户扣除平台手续费后的金额 = 商品价格 * （1-平台手续费比例）
//        BigDecimal independentShopPrice = price.multiply(subtractPlatform).setScale(2, RoundingMode.HALF_UP);
//        // 平台总手续费
//        BigDecimal independentPlatformPrice = CalUtils.sub(pri0ce, independentShopPrice);
//        log.warn("平台总手续费：{}", independentPlatformPrice);
//        // 平台分账金额 = 平台总手续费 * 平台分账比例
//        BigDecimal platPrice = independentPlatformPrice.multiply(BigDecimalUtils.divide(templateModel.getAdminRatio(), PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//        orderIndependentPrice.setAdminPrice(platPrice);
//        // 国家站站长分账金额 = 平台总手续费 * 国家站站长分账比例
//        BigDecimal countryPrice = independentPlatformPrice.multiply(BigDecimalUtils.divide(templateModel.getCountryRatio(), PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//        orderIndependentPrice.setCountryPrice(countryPrice);
//        // 城市站站长分账金额 = 平台总手续费 -（平台分账金额+国家站长分账金额）
//        BigDecimal cityPrice = CalUtils.sub(independentPlatformPrice, platPrice, countryPrice);
//        orderIndependentPrice.setCityPrice(cityPrice);
//        // 如果是分销订单并且有拉新人就计算拉新人的费用
//        if (independentOrder) {
//            // 分销费用 = 商品总金额 *（1- 平台手续费比例） * 商户分佣比例
//            // 商户设置分佣比例
//            BigDecimal independentShop = BigDecimalUtils.divide(independentRatio, PERCENTAGE);
//            log.warn("商户设置分佣比例：{}", independentShop);
//            // 拉新人金额 = 商户扣除平台手续费后金额 * 商品分佣比例 - 分销人所得金额
//            BigDecimal independentPrice = independentShopPrice.multiply(independentShop).setScale(2, RoundingMode.HALF_UP);
//            if (inviteUser) {
//                BigDecimal independentInvitePrice = CalUtils.sub(independentPrice, productIndependentPrice);
//                orderIndependentPrice.setInvitePrice(independentInvitePrice);
//                log.warn("拉新人分账金额：{}", independentInvitePrice);
//                // 分销人获得总金额
//                orderIndependentPrice.setDistributorPrice(productIndependentPrice);
//            } else {
//                // 分销人获得总金额-所有商品分拥金额
//                orderIndependentPrice.setDistributorPrice(independentPrice);
//            }
//        }
//        // 分销单状态
//        orderIndependentPrice.setIndependentOrder(independentOrder);
//        return orderIndependentPrice;
//    }

    /**
     * 封装平台对象
     *
     * @param orderInfo
     * @param bo
     */
    private void setPlatformObject(SohuShopOrderInfo orderInfo, AccountPlatformBo bo) {
        // 查询分账模版
        SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(bo.getEntranceSiteId(), 1);
        bo.setTemplateModel(templateModel);
        List<SohuIndependentTempBo> boList = remoteAccountProcessService.accountPlatformObjects(bo);
        // 订单详情每笔订单的分账人所获得的金额详细
        orderInfo.setInvitePrice(BigDecimal.ZERO);
        orderInfo.setAdminPrice(BigDecimal.ZERO);
        orderInfo.setCountryPrice(BigDecimal.ZERO);
        orderInfo.setCityPrice(BigDecimal.ZERO);
        orderInfo.setEntranceSitePrice(BigDecimal.ZERO);
        orderInfo.setIndustrySitePrice(BigDecimal.ZERO);
        orderInfo.setInviteSitePrice(BigDecimal.ZERO);
        orderInfo.setAgencyAdminPrice(BigDecimal.ZERO);
        if (CollUtil.isNotEmpty(boList)) {
            for (SohuIndependentTempBo item : boList) {
                switch (item.getIndependentObject()) {
                    case "PLATFORM":
                        orderInfo.setAdminPrice(item.getIndependentPrice());
                        break;
                    case "COUNTRY":
                        orderInfo.setCountryPrice(item.getIndependentPrice());
                        orderInfo.setCountrySiteId(item.getSiteId());
                        orderInfo.setCountrySiteUserId(item.getUserId());
                        break;
                    case "CITY":
                        orderInfo.setCityPrice(item.getIndependentPrice());
                        orderInfo.setCitySiteId(item.getSiteId());
                        orderInfo.setCitySiteUserId(item.getUserId());
                        break;
                    case "ENTRANCESITE":
                        orderInfo.setEntranceSitePrice(item.getIndependentPrice());
                        orderInfo.setEntranceSiteId(item.getSiteId());
                        orderInfo.setEntranceSiteUserId(item.getUserId());
                        break;
                    case "INDUSTRYSITE":
                        orderInfo.setIndustrySitePrice(item.getIndependentPrice());
                        orderInfo.setIndustrySiteId(item.getSiteId());
                        orderInfo.setIndustrySiteUserId(item.getUserId());
                        break;
                    case "INVITESITE":
                        orderInfo.setInviteSitePrice(item.getIndependentPrice());
                        orderInfo.setInviteSiteId(item.getSiteId());
                        orderInfo.setInviteSiteUserId(item.getUserId());
                        orderInfo.setInviteSiteType(item.getSiteType());
                        break;
                    case "INVITE":
                        orderInfo.setInvitePrice(item.getIndependentPrice());
                        orderInfo.setInviteUserId(item.getUserId());
                        break;
                    case "AGENCY":
                        orderInfo.setAgencyAdminPrice(item.getIndependentPrice());
                        orderInfo.setAgencyUserId(item.getUserId());
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 封装分销对象
     *
     * @param orderInfo
     */
    private void setIndependentObject(Boolean independentOrder, BigDecimal allPrice, BigDecimal independentRatio, BigDecimal productIndependentPrice, SohuShopOrderInfo orderInfo, Long independentRegUserId) {
        // 如果是分销订单并且有拉新人就计算拉新人的费用
        if (independentOrder) {
            // 分销费用 = 商品总金额 *（1- 平台手续费比例） * 商户分佣比例
            // 商户设置分佣比例
            BigDecimal independentShop = BigDecimalUtils.divide(independentRatio, CalUtils.PERCENTAGE);
            log.warn("商户设置分佣比例：{}", independentShop);
            // 拉新人金额 = 商户扣除平台手续费后金额 * 商品分佣比例 - 分销人所得金额
            // 分销总金额
            BigDecimal independentPrice = allPrice.multiply(independentShop).setScale(2, RoundingMode.HALF_UP);
            // 分销人获得总金额
            orderInfo.setDistributorPrice(productIndependentPrice);
            // 判断分销人是否有拉新人
            if (null != independentRegUserId) {
                BigDecimal independentInvitePrice = CalUtils.sub(independentPrice, productIndependentPrice);
                // 分销人的拉新人的分账金额
                log.warn("拉新人分账金额：{}", independentInvitePrice);
                orderInfo.setDistributorInvitePrice(independentInvitePrice);
            } else {
                // 没有拉新人的情况下剩下钱都给平台
                orderInfo.setAdminPrice(CalUtils.add(orderInfo.getAdminPrice(), CalUtils.sub(independentPrice, productIndependentPrice)));
            }
        }
    }

    /**
     * 分账算价
     *
     * @param siteId
     * @param allPrice                子单总支付金额
     * @param price                   子单总参与分红金额
     * @param independentRatio
     * @param productIndependentPrice 子单总分销人金额
     * @param independentOrder
     * @param independentRegUserId
     * @param consumerInviteUser
     */
    private SohuOrderIndependentPriceVo getShopIndependentPrice(Long siteId, BigDecimal allPrice, BigDecimal price, BigDecimal independentRatio, BigDecimal productIndependentPrice, Boolean independentOrder, Long independentRegUserId, Boolean consumerInviteUser, Boolean agencyId, Boolean inviteSiteUser) {
        // 查询分账模版
        SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(siteId, 1);
        // 分账对象
        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
        // 平台手续费百分比-3.9%
        BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
        // 平台百分比
        BigDecimal adminDivide = BigDecimalUtils.divide(templateModel.getAdminRatio(), CalUtils.PERCENTAGE);
        // 消费者拉新人百分比
        BigDecimal consumerDivide = BigDecimalUtils.divide(templateModel.getConsumerInviteRatio(), CalUtils.PERCENTAGE);
        // 扣除平台手续费比例后比例-96.1%
//        BigDecimal subtractPlatform = RATION.subtract(platformDivide);
        // 平台总手续费 = 商品价格 * 平台手续费比例 不计分拥金额
        BigDecimal independentPlatformPrice = price.multiply(platformDivide).setScale(2, RoundingMode.HALF_UP);
        log.warn("平台总手续费：{}", independentPlatformPrice);
        // 平台总分账金额 = 平台总手续费 * 平台分账比例
        BigDecimal platPrice;
        BigDecimal consumerInvitePrice;
        if (consumerInviteUser) {
            platPrice = independentPlatformPrice.multiply(adminDivide).setScale(2, RoundingMode.HALF_UP);
            consumerInvitePrice = independentPlatformPrice.multiply(consumerDivide).setScale(2, RoundingMode.HALF_UP);
        } else {
            platPrice = independentPlatformPrice.multiply(CalUtils.add(adminDivide, consumerDivide)).setScale(2, RoundingMode.HALF_UP);
            consumerInvitePrice = BigDecimal.ZERO;
        }
        // 消费者拉新人分账金额 = 平台总分账金额 - 平台分账金额
        orderIndependentPrice.setInvitePrice(consumerInvitePrice);
        // 分红总金额 =  平台总手续费 - 平台分账金额 - 消费者拉新人分账金额
        BigDecimal sharePrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice);
        // 国家站站长分账金额 = 分红总金额 * 国家站站长分账比例
        BigDecimal countryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCountryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        orderIndependentPrice.setCountryPrice(countryPrice);
        BigDecimal agencyPrice = BigDecimal.ZERO;
        // 城市站站长分账金额 = 分红总金额 * 城市站站长分账比例
        BigDecimal cityPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        // 行业站站长分账金额 = 分红总金额 * 行业站站长分账比例
        BigDecimal industryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getIndustryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        // 入口站站长分账金额 = 分红总金额 * 入口站站长分账比例
        BigDecimal entrancePrice = BigDecimal.ZERO;
        // 拉新站长分账金额 = 入口站站长分账金额 * 拉新站长分账比例
        BigDecimal stationInvitePrice = BigDecimal.ZERO;
        if (Objects.nonNull(siteId)) {
            entrancePrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getEntranceRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            if (!inviteSiteUser) {
                stationInvitePrice = entrancePrice.multiply(BigDecimalUtils.divide(templateModel.getInviteCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            }
        }
        //todo 拉新分账
        // 代理算价
        if (agencyId && null != templateModel.getAgencyRatio() && !BigDecimal.ZERO.equals(templateModel.getAgencyRatio())) {
            // 代理分账金额 = 平台总手续费 -（平台分账金额+国家站长分账金额+城市站长分账金额）
            agencyPrice = CalUtils.sub(independentPlatformPrice, platPrice, countryPrice);
        } else {
            // 剩下的钱
            BigDecimal lastPrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice, countryPrice, cityPrice);
            platPrice = CalUtils.add(platPrice, lastPrice);
        }
        // 平台分账金额
        orderIndependentPrice.setAdminPrice(platPrice);
        orderIndependentPrice.setCityPrice(cityPrice);
        orderIndependentPrice.setIndustryPrice(industryPrice);
        orderIndependentPrice.setAgencyPrice(agencyPrice);
        orderIndependentPrice.setEntrancePrice(entrancePrice.subtract(stationInvitePrice));
        orderIndependentPrice.setInviteCityPrice(stationInvitePrice);
        // 如果是分销订单并且有拉新人就计算拉新人的费用
        if (independentOrder) {
            // 分销费用 = 商品总金额 *（1- 平台手续费比例） * 商户分佣比例
            // 商户设置分佣比例
            BigDecimal independentShop = BigDecimalUtils.divide(independentRatio, CalUtils.PERCENTAGE);
            log.warn("商户设置分佣比例：{}", independentShop);
            // 拉新人金额 = 商户扣除平台手续费后金额 * 商品分佣比例 - 分销人所得金额
            // 分销总金额
            BigDecimal independentPrice = allPrice.multiply(independentShop).setScale(2, RoundingMode.HALF_UP);
            // 分销人获得总金额
            orderIndependentPrice.setDistributorPrice(productIndependentPrice);
            // 判断分销人是否有拉新人
            if (null != independentRegUserId) {
                BigDecimal independentInvitePrice = CalUtils.sub(independentPrice, productIndependentPrice);
                // 分销人的拉新人的分账金额
                log.warn("拉新人分账金额：{}", independentInvitePrice);
            } else {
                // 没有拉新人的情况下剩下钱都给平台
                orderIndependentPrice.setAdminPrice(CalUtils.add(platPrice, CalUtils.sub(independentPrice, productIndependentPrice)));
            }
        }
        // 分销单状态
        orderIndependentPrice.setIndependentOrder(independentOrder);
        return orderIndependentPrice;
    }


    /**
     * 获取商户信息并筛选
     *
     * @param storeProduct
     * @return SohuMerchantModel
     */
    private SohuMerchantModel getMerchant(SohuProductModel storeProduct) {
        SohuMerchantModel merchant = remoteMerchantService.selectById(storeProduct.getMerId());
        if (!merchant.getIsSwitch()) {
            throw new RuntimeException("当前商品的店铺已关闭、敬请等待店铺重启开启");
        }
        return merchant;
    }

    /**
     * 购物车预下单校验
     *
     * @param request 请求参数
     * @param user    用户
     * @return List<OrderInfoDetailVo>
     */
    private List<SohuPreStoreOrderModel> validatePreOrderShopping(SohuPreOrderReqBo request, LoginUser user) {
        List<SohuPreStoreOrderModel> storeOrderVoList = CollUtil.newArrayList();
        //分销商单
        SohuPmSharePubVo sohuPmSharePubVo = remotePmSharePubService.queryByIdOfEnable(request.getPmSharePubId());
        request.getOrderDetails().forEach(e -> {
            if (ObjectUtil.isNull(e.getShoppingCartId())) {
                throw new ServiceException("Cart ID cannot be empty");
            }
            SohuShopCartModel storeCart = remoteShopCartAttrService.getByIdAndUserId(e.getShoppingCartId(), user.getUserId());
            if (ObjectUtil.isNull(storeCart)) {
                throw new ServiceException("No corresponding shopping cart information found");
            }
            // 查询商品信息
            SohuProductModel storeProduct = getStoreProduct(storeCart.getProductId(), storeCart.getCartNum());
            // 判断门店信息
            SohuMerchantModel merchant = remoteMerchantService.selectById(storeProduct.getMerId());
            if (!merchant.getIsSwitch()) {
                throw new ServiceException("当前购买门店已关闭，请刷新并重新选择");
            }
            // 查询商品规格属性值信息
            SohuProductAttrValueModel attrValue = getStoreProductAttrValue(storeCart.getProductAttrId(), storeCart.getProductId(), "The product specification information does not exist, please refresh and select again", storeCart.getCartNum(), "Insufficient stock of product specifications, please refresh and select again");

            if (storeOrderVoList.stream().anyMatch(o -> o.getMerId().equals(merchant.getId()))) {
                for (SohuPreStoreOrderModel orderVo : storeOrderVoList) {
                    orderVo.setOpenClientId(storeProduct.getOpenClientId());
                    orderVo.setMerIsSelf(merchant.getIsSelf());
                    if (Objects.nonNull(sohuPmSharePubVo)) {
                        orderVo.setPmSharePubId(request.getPmSharePubId());
                    }
                    if (orderVo.getMerId().equals(merchant.getId())) {
                        SohuShopOrderInfoModel infoVo = new SohuShopOrderInfoModel();
                        infoVo.setProductId(storeProduct.getId());
                        //封装入口站信息
//                        infoVo.setSiteId(storeProduct.getSiteId());
                        infoVo.setSiteId(storeCart.getSiteId());
                        infoVo.setSiteType(storeCart.getSiteType());
                        if (BooleanUtil.isTrue(request.getIsProcess())) {
                            // 需要过审时,站点默认是武汉站点
                            infoVo.setSiteId(11L);
                            infoVo.setSiteType(1);
                        }
                        infoVo.setProductName(storeProduct.getStoreName());
                        infoVo.setThirdProductId(storeProduct.getThirdProductId());
                        infoVo.setOpenClientId(storeProduct.getOpenClientId());
                        if (ObjectUtil.isNotNull(attrValue)) {
                            infoVo.setProductAttrValueId(attrValue.getId());
                            infoVo.setImage(StrUtil.isNotBlank(attrValue.getImage()) ? attrValue.getImage() : storeProduct.getImage());
                            infoVo.setSku(attrValue.getSku());
                            infoVo.setPrice(attrValue.getPrice());
                            infoVo.setPayNum(storeCart.getCartNum());
                            infoVo.setVolume(attrValue.getVolume());
                            infoVo.setWeight(attrValue.getWeight());
                        }
                        infoVo.setPostage(storeProduct.getPostage());
                        if (Objects.isNull(sohuPmSharePubVo)) {
                            // 商品分销相关
                            infoVo.setIndependentRatio(storeProduct.getIndependentRatio());
                            infoVo.setDistributorPrice(storeProduct.getIndependentPrice());
                        } else {
                            //分享商单分销
                            infoVo.setIndependentRatio(sohuPmSharePubVo.getIndependentRatio());
                            infoVo.setDistributorPrice(sohuPmSharePubVo.getIndependentPrice());
                            orderVo.setPmSharePubId(request.getPmSharePubId());
                        }
                        orderVo.getOrderInfoList().add(infoVo);
                        break;
                    }
                }
            } else {
                SohuPreStoreOrderModel storeOrderVo = new SohuPreStoreOrderModel();
                storeOrderVo.setMerId(merchant.getId());
                storeOrderVo.setMerName(merchant.getName());
                storeOrderVo.setOpenClientId(storeProduct.getOpenClientId());
                storeOrderVo.setMerIsSelf(merchant.getIsSelf());
                if (Objects.nonNull(sohuPmSharePubVo)) {
                    storeOrderVo.setPmSharePubId(request.getPmSharePubId());
                }
                SohuShopOrderInfoModel infoVo = new SohuShopOrderInfoModel();
                infoVo.setProductId(storeProduct.getId());
                //封装入口站信息
//                        infoVo.setSiteId(storeProduct.getSiteId());
                infoVo.setSiteId(storeCart.getSiteId());
                infoVo.setSiteType(storeCart.getSiteType());
                if (BooleanUtil.isTrue(request.getIsProcess())) {
                    // 需要过审时,站点默认是武汉站点
                    infoVo.setSiteId(11L);
                    infoVo.setSiteType(1);
                }
                infoVo.setProductName(storeProduct.getStoreName());
                infoVo.setProductAttrValueId(attrValue.getId());
                infoVo.setImage(StrUtil.isNotBlank(attrValue.getImage()) ? attrValue.getImage() : storeProduct.getImage());
                infoVo.setSku(attrValue.getSku());
                infoVo.setPrice(attrValue.getPrice());
                infoVo.setPayNum(storeCart.getCartNum());
                infoVo.setVolume(attrValue.getVolume());
                infoVo.setWeight(attrValue.getWeight());
                infoVo.setPostage(storeProduct.getPostage());
                if (Objects.isNull(sohuPmSharePubVo)) {
                    // 商品分销相关
                    infoVo.setIndependentRatio(storeProduct.getIndependentRatio());
                    infoVo.setDistributorPrice(storeProduct.getIndependentPrice());
                } else {
                    //分享商单分销
                    infoVo.setIndependentRatio(sohuPmSharePubVo.getIndependentRatio());
                    infoVo.setDistributorPrice(sohuPmSharePubVo.getIndependentPrice());
                }
                infoVo.setOpenClientId(storeProduct.getOpenClientId());
                infoVo.setThirdProductId(storeProduct.getThirdProductId());

                List<SohuShopOrderInfoModel> infoList = CollUtil.newArrayList();
                infoList.add(infoVo);
                storeOrderVo.setOrderInfoList(infoList);
                storeOrderVoList.add(storeOrderVo);
            }
        });
        return storeOrderVoList;
    }

    /**
     * 查询商品规格属性值信息
     *
     * @param productAttrId
     * @param productId
     * @param nullMessage
     * @param cartNum
     * @param sizeMessage
     * @return SohuProductAttrValueModel
     */
    private SohuProductAttrValueModel getStoreProductAttrValue(Long productAttrId, Long productId, String nullMessage, Integer cartNum, String sizeMessage) {
        // 查询商品规格属性值信息
        SohuProductAttrValueModel attrValue = remoteProductAttrValueService.getByIdAndProductIdAndType(productAttrId, productId, ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        if (ObjectUtil.isNull(attrValue)) {
            throw new ServiceException(nullMessage);
        }
        if (attrValue.getStock() < cartNum) {
            throw new ServiceException(sizeMessage);
        }
        return attrValue;
    }

    /**
     * 查询商品信息
     *
     * @param productId
     * @param cartNum
     * @return SohuProductModel
     */
    private SohuProductModel getStoreProduct(Long productId, Integer cartNum) {
        SohuProductModel storeProduct = remoteProductService.queryById(productId);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new ServiceException("Product information does not exist, please refresh and select again");
        }
        if (storeProduct.getIsDel()) {
            throw new ServiceException("The item has been deleted, please refresh and select again");
        }
        if (!storeProduct.getIsShow() || storeProduct.getIsForced()) {
            throw new ServiceException("The product has been removed, please refresh and select again");
        }
        if (storeProduct.getStock() < cartNum) {
            throw new ServiceException("Insufficient product stock, please refresh and select again");
        }
        //校验商品是否可售
        long secondCateId = remoteProductCategoryPcService.getSecondCateByCateId(storeProduct.getCategoryId());
        boolean isSale = remoteMerchantBondService.checkMerchantBondPayStatus(storeProduct.getMerId(), secondCateId);
        if (!isSale) {
            throw new ServiceException("The product is not for sale, please refresh and select again");
        }
        return storeProduct;
    }

    /**
     * 计算订单运费
     */
    private BigDecimal getFreightFee(List<SohuShopOrderInfoModel> orderInfoList) {
        // 运费 = 单件商品运费 * 商品数量
        BigDecimal storePostage = BigDecimal.ZERO;
        for (SohuShopOrderInfoModel detailVo : orderInfoList) {
            storePostage = storePostage.add(detailVo.getPostage().multiply(new BigDecimal(detailVo.getPayNum())));
        }
        return storePostage;
    }

    /**
     * 根据运费模板计算商品运费
     */
    private BigDecimal getTotalFreightFee(List<SohuShopOrderInfoModel> orderInfoList, Long regionId) {
        BigDecimal postage = BigDecimal.ZERO;
        for (SohuShopOrderInfoModel orderInfo : orderInfoList) {
            SohuProductModel storeProduct = remoteProductService.queryById(orderInfo.getProductId());
            // 根据商品ID获取运费模板
            if (storeProduct.getFreightTemplateId() == null || storeProduct.getFreightTemplateId() == 0) {
                throw new ServiceException("该商品未配置运费模板，无法购买");
            }
            // 根据运费模板计算运费
            SohuFreightTemplateVo freightTemplateVo = remoteFreightTemplateService.queryById(storeProduct.getFreightTemplateId());
            SohuRegionVo sohuRegionVo = remoteMiddleRegionService.queryById(regionId);
            // 包邮区域String productName = bo.getProductName();
            String[] freeArea = freightTemplateVo.getFreeArea().split(",");
            boolean result = Arrays.stream(freeArea).anyMatch(a -> a.equals(sohuRegionVo.getAgencyCode()));
            if (!result) {
                // 不配送区域
                String notDeliveryArea = freightTemplateVo.getNotDeliveryArea();
                JSONArray array = JSONArray.parseArray(notDeliveryArea);
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    String regionCode = String.valueOf(jsonObject.get("regionCode"));
                    if (StrUtil.equalsAnyIgnoreCase(regionCode, sohuRegionVo.getAgencyCode())) {
                        throw new ServiceException("该商品暂不支持配送该地址，如需购买请更换收货地址。");
                    }
                }
                // 买家付运费区域
                SohuFreightPayBo bo = new SohuFreightPayBo();
                bo.setFreightId(freightTemplateVo.getId());
                bo.setAgencyCode(sohuRegionVo.getAgencyCode());
                List<SohuFreightPayVo> freightPayList = remoteFreightPayService.queryList(bo);
                if (CollUtil.isNotEmpty(freightPayList)) {
                    Integer payNum = orderInfo.getPayNum();
                    BigDecimal freight = freightPayList.get(0).getFreight();
                    if (payNum > 1) {
                        BigDecimal addNum = new BigDecimal(payNum - 1);
                        BigDecimal addFreight = addNum.multiply(freightPayList.get(0).getAddFreight());
                        BigDecimal totalFreight = freight.add(addFreight);
                        postage = postage.add(totalFreight);
                    } else {
                        postage = postage.add(freight);
                    }
                }
            }
        }
        return postage;
    }

    /**
     * 快递发货
     *
     * @param request
     * @param storeOrder
     */
    private Boolean expressOrder(SohuSendOrderBo request, SohuShopOrder storeOrder) {
        //快递公司信息
//        SohuExpressVo express = remoteExpressService.getByCode(request.getExpressCode());
        storeOrder.setDeliveryNo(request.getExpressNumber());
        storeOrder.setDeliveryCode(request.getExpressCode());
        storeOrder.setDeliveryName(request.getExpressName());
        storeOrder.setStatus(OrderConstants.ORDER_STATUS_AWAIT_RECEIVING);
        // 初始化订单物流
        SohuShopOrderLogistics orderLogistics = new SohuShopOrderLogistics();
        orderLogistics.setOrderNo(storeOrder.getOrderNo());
        orderLogistics.setExpNo(request.getExpressNumber());
        orderLogistics.setExpCode(request.getExpressCode());
        orderLogistics.setExpName(request.getExpressName());
        orderLogistics.setUserPhone(storeOrder.getUserPhone());
        orderLogistics.setLogisticsInfo("");
        orderLogistics.setState(0);
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom(orderLogistics.getExpCode().toLowerCase());
        queryTrackParam.setNum(orderLogistics.getExpNo());
        if (StringUtils.isBlank(findLogisticsByExpNo(orderLogistics).getState())) {
            throw new ServiceException(MessageUtils.message("ERROR_LOGISTICS_IS_EMPTY"));
        }
        return getSendBoolean(storeOrder, orderLogistics);
    }

    /**
     * 推送商品已发货结果通知
     *
     * @param storeOrder
     */
    private void pushShopOderJiGuangNotice(SohuShopOrder storeOrder) {
        SohuJiguangPush2UserReqBo reqBo = new SohuJiguangPush2UserReqBo();
        reqBo.setUserIds(com.google.common.collect.Lists.newArrayList(storeOrder.getUserId()));
        reqBo.setJumpPathType(AppPathTypeEnum.SHOP_ORDER_DETAIL);
        reqBo.setBizType(PushJiGuangBizTypeEnum.ORDER_LOGISTIC);
        String title = "您的商品已发货";
        String alert = "您有商品已发货，点击查看";
        Map<String, Object> params = Maps.newHashMap();
        params.put("orderNo", storeOrder.getMasterOrderNo());
        params.put("orderType", storeOrder.getStatus());
        reqBo.setParameters(params);
        reqBo.setTitle(title);
        reqBo.setAlert(alert);
        log.info("极光入参={}", JSONObject.toJSONString(reqBo));
        try {
            remoteJiguangService.push2User(reqBo);
        } catch (Exception e) {
            log.warn("商品已发货结极光推送异常，原因: {}", e.getMessage());
        }
    }

    /**
     * 发货通知
     *
     * @param model 订单
     */
    private void sendGoodsNotify(SohuShopOrder model) {
        // todo 根据订单id查询用户信息
        NoticeShopBuyContent content = new NoticeShopBuyContent();
        content.setTitle(ShopNoticeEnum.waitReceiveTitle);
        content.setNoticeTime(DateUtils.getTime());
        content.setDetailId(model.getId());
        content.setState(OrderConstants.ORDER_STATUS_AWAIT_RECEIVING);
        List<SohuShopOrderInfoVo> shopOrderInfos = infoService.getListByOrderNo(model.getOrderNo());
        SohuShopOrderInfoVo orderInfo = shopOrderInfos.get(0);
        content.setShopId(orderInfo.getProductId());
        content.setOrderNo(model.getMasterOrderNo());
        NoticeShopBuyContentDetail detail = new NoticeShopBuyContentDetail();
        detail.setDesc(ShopNoticeEnum.waitReceiveDesc);
        detail.setShopTitle(orderInfo.getProductName());
        SohuProductAttrValueModel attrValue = productAttrValueService.queryById(orderInfo.getProductAttrValueId());
        detail.setAttrValue(attrValue.getAttrValue());
        detail.setPrice(orderInfo.getPrice());
        detail.setCoverImage(orderInfo.getImage());
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleShopNoticeService.sendNotice(UserConstants.ADMIN_ID, model.getUserId(), ShopNoticeEnum.waitReceiveTitle, contentJson);
//        User user = userService.getById(storeOrder.getUserId());
//        if (ObjectUtil.isNull(user)) {
//            return;
//        }
//        if (user.getUserType().equals(UserConstants.USER_LOGIN_TYPE_PHONE)) {
//            // 发送短信通知
//            smsService.sendOrderDeliverNotice(user.getCountryCode(), user.getPhone(), storeOrder.getOrderNo());
//            return;
//        }
//        if (StrUtil.isNotBlank(user.getEmail())) {
//            emailService.sendOrderDeliver(user.getEmail(), storeOrder.getOrderNo());
//        }
    }

    /**
     * 订单状态（UNPAID：待支付 WAIT_SENT：待发货：WAIT_RECEIVE 待收货,2：RECEIVE 已收货,COMPLETED：已完成，CANCEL：已取消）
     * 根据商户号、商户订单号查询订单信息
     *
     * @param orderNo
     */
    private SohuShopOrder getInfoByMerIdAndOrderNo(String orderNo) {
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getOrderNo, orderNo);
        lqw.last(" limit 1");
        SohuShopOrder storeOrder = this.baseMapper.selectOne(lqw);
        if (ObjectUtil.isNull(storeOrder)) {
            throw new RuntimeException("没有找到订单信息");
        }
        return storeOrder;
    }

    /**
     * 获取订单总数
     *
     * @param startTime
     * @param endTime
     * @param statusType
     * @param merId
     * @return
     */
    private Integer getCount(Date startTime, Date endTime, String statusType, Long merId) {
        QueryWrapper<SohuShopOrder> lqw = new QueryWrapper<>();
        if (null != merId && merId > 0) {
            lqw.eq("mer_id", merId);
        }
        if (ObjectUtils.isNotNull(startTime) && ObjectUtils.isNotNull(endTime)) {
            getRequestTimeWhere(lqw, startTime, endTime);
        }
        getMerchantStatusWhere(lqw, statusType);
        return Math.toIntExact(this.baseMapper.selectCount(lqw));
    }

    /**
     * 组装查询时间
     *
     * @param lqw
     * @param startTime
     * @param endTime
     */
    private void getRequestTimeWhere(QueryWrapper<SohuShopOrder> lqw, Date startTime, Date endTime) {
        lqw.between("create_time", startTime, endTime);
    }

    @Override
    public TableDataInfo<SohuShopOrderVo> queryPcPageList(SohuAdminQueryOrderBo request, PageQuery pageQuery) {

        QueryWrapper<SohuShopOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "mer_id", "order_no", "master_order_no", "receiver_name", "pay_price", "pay_type", "paid", "status", "refund_status", "is_user_del", "is_merchant_del", "user_remark", "mer_remark", "platform_remark", "create_time");
        if (ObjectUtil.isNotNull(request.getMerId())) {
            queryWrapper.eq("mer_id", request.getMerId());
        }
        // 主订单号
        if (StrUtil.isNotBlank(request.getMasterOrderNo())) {
            queryWrapper.eq("master_order_no", request.getMasterOrderNo());
        }
        // 订单号
        if (StrUtil.isNotBlank(request.getOrderNo())) {
            queryWrapper.eq("order_no", request.getOrderNo());
        }
        // 时间
        if (ObjectUtil.isNotNull(request.getStartTime()) && ObjectUtil.isNotNull(request.getEndTime())) {
            queryWrapper.between("create_time", request.getStartTime(), request.getEndTime());
        }
        getMerchantStatusWhere(queryWrapper, request.getStatusType());
        queryWrapper.orderByDesc("id");
        Page<SohuShopOrderVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), queryWrapper);
        if (ObjectUtil.isNull(result)) {
            return TableDataInfoUtils.build();
        }
        List<SohuShopOrderVo> records = result.getRecords();
        // 获取商户ids
        List<Long> merIdList = records.stream().map(SohuShopOrderVo::getMerId).collect(Collectors.toList());
        // 商户信息
        Map<Long, SohuMerchantModel> merchantMap = remoteMerchantService.getMerIdMapByIdList(merIdList);
        result.getRecords().forEach(e -> {
            SohuShopOrderVo pageResponse = new SohuShopOrderVo();
            BeanUtils.copyProperties(e, pageResponse);
            // 商户信息
            pageResponse.setMerName(merchantMap.get(e.getMerId()).getName());
        });
        return TableDataInfoUtils.build(result);
    }

    /**
     * 根据订单状态获取where条件(商户端)
     *
     * @param queryWrapper QueryWrapper<StoreOrder> 表达式
     * @param status       String 类型
     */
    private void getMerchantStatusWhere(QueryWrapper<SohuShopOrder> queryWrapper, String status) {
        if (StrUtil.isBlank(status)) {
            return;
        }
        switch (status) {
            case OrderConstants.MERCHANT_ORDER_STATUS_ALL: //全部
                break;
            case OrderConstants.MERCHANT_ORDER_STATUS_UNPAID: //未支付
                queryWrapper.eq("paid", 0);//支付状态n
                queryWrapper.eq("status", "UNPAID"); //订单状态
                queryWrapper.eq("is_user_del", 0);//删除状态
                break;
            case OrderConstants.ORDER_STATUS_SHIPPING: //未发货
                queryWrapper.eq("paid", 1);
                queryWrapper.eq("status", "WAIT_SENT");
                queryWrapper.ne("refund_status", 3);
                queryWrapper.eq("is_user_del", 0);
                break;
            case OrderConstants.ORDER_STATUS_AWAIT_RECEIVING: //待收货
                queryWrapper.eq("paid", 1);
                queryWrapper.eq("status", "WAIT_RECEIVE");
                queryWrapper.ne("refund_status", 3);
                queryWrapper.eq("is_user_del", 0);
                break;
            case OrderConstants.ORDER_STATUS_OVER: //交易完成
                queryWrapper.eq("paid", 1);
                queryWrapper.eq("status", "COMPLETED");
                queryWrapper.ne("refund_status", 3);
                queryWrapper.eq("is_user_del", 0);
                break;
            case OrderConstants.MERCHANT_ORDER_STATUS_REFUNDED: //已退款
                queryWrapper.eq("paid", 1);
                queryWrapper.eq("refund_status", 3);
                queryWrapper.eq("is_user_del", 0);
                break;
            case OrderConstants.MERCHANT_ORDER_STATUS_DELETED: //已删除
                queryWrapper.eq("is_user_del", 1);
                break;
        }
        queryWrapper.eq("is_merchant_del", 0);
    }

//    @Override
//    public TableDataInfo<SohuShopOrderStatModel> queryShopOrderTop(Long mcnId, Long articleUserId, PageQuery pageQuery) {
//        Page<SohuShopOrderStatModel> result = null;//this.baseMapper.queryShopOrderTop(mcnId, articleUserId, PageQueryUtils.build(pageQuery));
//        if (CollectionUtil.isNotEmpty(result.getRecords())) {
//            Set<Long> userIdSet = result.getRecords().stream().map(p -> p.getUserId()).collect(Collectors.toSet());
//            Map<Long, LoginUser> userMap = userService.selectMap(userIdSet);
//            for (SohuShopOrderStatModel model : result.getRecords()) {
//                LoginUser user = userMap.get(model.getUserId());
//                if (Objects.nonNull(user)) {
//                    model.setNickName(user.getNickname());
//                    model.setUserAvatar(user.getAvatar());
//                }
//            }
//        }
//        return TableDataInfoUtils.build(result);
//    }

    @Override
    public TableDataInfo<SohuShopOrderStatVo> queryShopOrderTop(SohuShopOrderMcnBo bo, PageQuery pageQuery) {
        IPage<SohuShopOrderStatVo> result = this.baseMapper.queryShopOrderTop(bo, PageQueryUtils.build(pageQuery));
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            Set<Long> userIdSet = result.getRecords().stream().map(p -> p.getUserId()).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = userService.selectMap(userIdSet);
            for (SohuShopOrderStatVo model : result.getRecords()) {
                LoginUser user = userMap.get(model.getUserId());
                if (Objects.nonNull(user)) {
                    model.setNickName(user.getNickname());
                    model.setUserAvatar(user.getAvatar());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuShopOrderModel> getOrderListByOrderNo(List<String> orderNoList) {
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.in(SohuShopOrder::getOrderNo, orderNoList);
        List<SohuShopOrder> shopOrdersList = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(shopOrdersList)) {
            return null;
        }
        List<SohuShopOrderModel> result = Lists.newArrayList();
        for (SohuShopOrder shopOrder : shopOrdersList) {
            SohuShopOrderModel orderModel = new SohuShopOrderModel();
            BeanUtils.copyProperties(shopOrder, orderModel);
            result.add(orderModel);
        }
        return result;
    }

    @Override
    public Long getMcnOrderStat(Long mcnId) {
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getMcnId, mcnId);
        return baseMapper.selectCount(lqw);
    }

    @Override
    public Long getMcnBuyStat(Long mcnId) {
        // 通过mcnId获取所有成员
        List<SohuMcnUserVo> mcnUserList = remoteMcnUserService.getListByMcnId(mcnId);
        // 通过userId和mcn获取购买人数并汇总 getProductBuyStatistics
        Long totalMcnBuyCount = mcnUserList.stream().mapToLong(mcnUser -> remoteProductService.productBuyStatistics(mcnUser.getUserId(), mcnUser.getMcnUserId())).sum();
        return Optional.of(totalMcnBuyCount).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public BigDecimal getMcnPerCustomerTransaction(Long mcnId) {
        // 客单价 = 成功付款金额/成交人数
        // 获取成交人数
        Long mcnBuyStat = this.getMcnBuyStat(mcnId);
        // 获取机构订单总数
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getMcnId, mcnId);
        List<SohuShopOrder> shopOrderList = baseMapper.selectList(lqw);
        // 计算成功付款金额
        BigDecimal totalPaymentAmount = shopOrderList.stream().map(SohuShopOrder::getPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算客单价 TODO 优化分母不为零
        BigDecimal averageOrderValue = shopOrderList.isEmpty() ? BigDecimal.ZERO : BigDecimalUtils.divide(totalPaymentAmount, new BigDecimal(mcnBuyStat));

        return Optional.of(averageOrderValue).orElse(BigDecimal.ZERO);
    }

    @Override
    public BigDecimal getMcnTransactionRate(Long mcnId) {
        // 获取成交订单数
        Long mcnOrderStat = this.getMcnOrderStat(mcnId);
        // 获取机构所有成员
        List<SohuMcnUserVo> mcnUserList = remoteMcnUserService.getListByMcnId(mcnId);
        // 获取mcn机构所有创作者橱窗所有商品的浏览量总和
        long totalMcnViewCount = mcnUserList.stream().mapToLong(mcnUser -> remoteProductService.productViewStatistics(mcnUser.getMcnUserId(), mcnUser.getMcnUserId())).sum();
        // 成交转换率 = 成交订单数/所有创作者的所有商品的浏览量总和 TODO 优化分母不为零
        BigDecimal transactionRate = BigDecimalUtils.divide(new BigDecimal(mcnOrderStat), new BigDecimal(totalMcnViewCount));

        return Optional.of(transactionRate).orElse(BigDecimal.ZERO);
    }

    @Override
    public Long getMcnBeforeDeliveryRefundStat(Long mcnId) {
        // 获取mcn机构待发货的退款人数
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getMcnId, mcnId);
        lqw.eq(SohuShopOrder::getPaid, true);
        lqw.eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_SHIPPING);
        lqw.eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);
        lqw.groupBy(SohuShopOrder::getUserId);

        Long beforeDeliveryRefundStat = baseMapper.selectCount(lqw);
        return Optional.ofNullable(beforeDeliveryRefundStat).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public Long getMcnBeforeDeliveryRefundOrderStat(Long mcnId) {
        // 获取mcn机构待发货的退款订单数
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getMcnId, mcnId);
        lqw.eq(SohuShopOrder::getPaid, true);
        lqw.eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_SHIPPING);
        lqw.eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_NO_REFUND);

        Long beforeDeliveryRefundOrderStat = baseMapper.selectCount(lqw);
        return Optional.ofNullable(beforeDeliveryRefundOrderStat).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public BigDecimal getMcnBeforeDeliveryRefundAmountStat(Long mcnId) {
        // 获取退款主订单
        List<SohuShopOrder> orderNoList = new ArrayList<>(baseMapper.selectList(Wrappers.<SohuShopOrder>lambdaQuery().eq(SohuShopOrder::getMcnId, mcnId).eq(SohuShopOrder::getPaid, true).eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_SHIPPING).eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_NO_REFUND).select(SohuShopOrder::getOrderNo)));
        if (CollUtil.isEmpty(orderNoList)) {
            return BigDecimal.ZERO;
        }
        // 获取退款子订单
        List<BigDecimal> refundAmountList = shopRefundOrderMapper.selectList(Wrappers.<SohuShopRefundOrder>lambdaQuery().in(SohuShopRefundOrder::getShopOrderNo, orderNoList).select(SohuShopRefundOrder::getRefundPrice)).stream().map(SohuShopRefundOrder::getRefundPrice).collect(Collectors.toList());

        // 计算退款总金额
        BigDecimal beforeDeliveryRefundAmountStat = refundAmountList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        return Optional.of(beforeDeliveryRefundAmountStat).orElse(BigDecimal.ZERO);
    }

    @Override
    public Long getMcnAfterDeliveryRefundStat(Long mcnId) {
        // 获取mcn机构已发货的退款人数
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getMcnId, mcnId);
        lqw.eq(SohuShopOrder::getPaid, true);
        lqw.eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_OVER);
        lqw.eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_REFUND);
        lqw.groupBy(SohuShopOrder::getUserId);

        Long afterDeliveryRefundStat = baseMapper.selectCount(lqw);
        return Optional.ofNullable(afterDeliveryRefundStat).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public Long getMcnAfterDeliveryRefundOrderStat(Long mcnId) {
        // 获取mcn机构待发货的退款订单数
        LambdaQueryWrapper<SohuShopOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopOrder::getMcnId, mcnId);
        lqw.eq(SohuShopOrder::getPaid, true);
        lqw.eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_OVER);
        lqw.eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_REFUND);

        Long afterDeliveryRefundOrderStat = baseMapper.selectCount(lqw);
        return Optional.ofNullable(afterDeliveryRefundOrderStat).orElse(DEFAULT_STAT_VALUE);
    }

    @Override
    public BigDecimal getMcnAfterDeliveryRefundAmountStat(Long mcnId) {
        // 获取退款主订单
        List<SohuShopOrder> orderNoList = new ArrayList<>(baseMapper.selectList(Wrappers.<SohuShopOrder>lambdaQuery().eq(SohuShopOrder::getMcnId, mcnId).eq(SohuShopOrder::getPaid, true).eq(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_OVER).eq(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_REFUND).select(SohuShopOrder::getOrderNo)));
        if (CollUtil.isEmpty(orderNoList)) {
            return BigDecimal.ZERO;
        }
        // 获取退款子订单
        List<BigDecimal> refundAmountList = shopRefundOrderMapper.selectList(Wrappers.<SohuShopRefundOrder>lambdaQuery().in(SohuShopRefundOrder::getShopOrderNo, orderNoList).select(SohuShopRefundOrder::getRefundPrice)).stream().map(SohuShopRefundOrder::getRefundPrice).collect(Collectors.toList());

        // 计算退款总金额
        BigDecimal afterDeliveryRefundAmountStat = refundAmountList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        return Optional.of(afterDeliveryRefundAmountStat).orElse(BigDecimal.ZERO);
    }

    @Override
    public SohuShopOrderStatVo getTradeStat(Long mcnId) {
        // 构造对象
        SohuShopOrderStatVo shopOrderStatVo = new SohuShopOrderStatVo();
        // 成交订单数
        shopOrderStatVo.setOrderCount(this.getMcnOrderStat(mcnId));
        // 成交人数
        shopOrderStatVo.setBuyCount(this.getMcnBuyStat(mcnId));
        // 客单价
        shopOrderStatVo.setPerCustomerTransaction(this.getMcnPerCustomerTransaction(mcnId));
        // 成交转化率
        shopOrderStatVo.setTransactionRate(this.getMcnTransactionRate(mcnId));

        return shopOrderStatVo;
    }

    @Override
    public SohuShopOrderStatVo getAfterSaleStat(Long mcnId) {
        // 构造对象
        SohuShopOrderStatVo shopOrderStatVo = new SohuShopOrderStatVo();
        // 发货前--退款人数
        shopOrderStatVo.setBeforeDeliveryRefundCount(this.getMcnBeforeDeliveryRefundStat(mcnId));
        // 发货前--退款订单数
        shopOrderStatVo.setBeforeDeliveryRefundOrderCount(this.getMcnBeforeDeliveryRefundOrderStat(mcnId));
        // 发货前--退款金额
        shopOrderStatVo.setBeforeDeliveryRefundAmountCount(this.getMcnBeforeDeliveryRefundAmountStat(mcnId));
        // 发货后--退款人数
        shopOrderStatVo.setAfterDeliveryRefundCount(this.getMcnAfterDeliveryRefundStat(mcnId));
        // 发货后--退款订单数
        shopOrderStatVo.setAfterDeliveryRefundOrderCount(this.getMcnAfterDeliveryRefundOrderStat(mcnId));
        // 发货后--退款金额
        shopOrderStatVo.setAfterDeliveryRefundAmountCount(this.getMcnAfterDeliveryRefundAmountStat(mcnId));

        return shopOrderStatVo;
    }

    @Override
    public void updateOrderListWaitReceive() {
        QueryWrapper<SohuShopOrder> wrapper = new QueryWrapper<SohuShopOrder>().eq("status", OrderConstants.ORDER_STATUS_AWAIT_RECEIVING);
        List<SohuShopOrder> sohuShopOrders = baseMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(sohuShopOrders)) {
            for (SohuShopOrder sohuShopOrder : sohuShopOrders) {
                //通过订单编号先去redis查 有可能是已经签收了 还在七天内 没变成已完成状态
                Date returnGoodsTime = RedisUtils.getCacheObject(CacheConstants.IS_RECEIVE_TIME_CODE_KEY + sohuShopOrder.getOrderNo());
                if (Objects.nonNull(returnGoodsTime)) {
                    //判断是否超过七天
                    List<String> timeInterval = getTimeInterval(DateUtils.getNowDate(), returnGoodsTime);
                    if (Integer.parseInt(timeInterval.get(0)) > 7) {
                        //将订单状态改为已完成 并清除redis
                        sohuShopOrder.setStatus(OrderConstants.ORDER_STATUS_OVER);
                        baseMapper.updateById(sohuShopOrder);
                        RedisUtils.deleteObject(CacheConstants.IS_RECEIVE_TIME_CODE_KEY + sohuShopOrder.getOrderNo());
                    }
                } else {
                    SohuQueryTrackBo trackBo = new SohuQueryTrackBo();
                    trackBo.setNum(sohuShopOrder.getOrderNo());
                    KuaidiModel kuaidiModel = queryTrack(trackBo);
                    if (Objects.nonNull(kuaidiModel)) {
                        List<KuaidiModel.DataDTO> data = kuaidiModel.getData();
                        if (CollUtil.isNotEmpty(data)) {
                            String context = data.get(0).getContext();
                            if (context.contains("已签收")) {
                                //不存在 判断物流是否已签收 设置进redis
                                RedisUtils.setCacheObject(CacheConstants.IS_RECEIVE_TIME_CODE_KEY + sohuShopOrder.getOrderNo(), DateUtils.getNowDate());
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取时间差方法,返回时分秒 HH:mm:ss
     *
     * @param currentTime
     * @param firstTime
     * @return
     */
    @Override
    public List<String> getTimeInterval(Date currentTime, Date firstTime) {
        DecimalFormat decimalFormat = new DecimalFormat("00");
        long diff = WEEK_MATH + firstTime.getTime() - currentTime.getTime();//得到的差值
        long days = diff / DAY_MATH; //获取天
        long hours = (diff - days * DAY_MATH) / HOUR_MATH;  //获取时
        List<String> countTimes = new ArrayList<>();
        countTimes.add(decimalFormat.format(days).substring(1, 2));
        countTimes.add(decimalFormat.format(hours));
        return countTimes;
    }

    @Override
    public Boolean updateReplyStatus(String orderNo, Integer isReply) {
        LambdaUpdateWrapper<SohuShopOrder> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuShopOrder::getOrderNo, orderNo)
                .set(SohuShopOrder::getIsReply, isReply);
        this.baseMapper.update(new SohuShopOrder(), luw);
        return true;
    }

    /**
     * 查询用户店铺订单
     */
    @Override
    public TableDataInfo<SohuShopOrderVo> getStoreOrderList(SohuStoreOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuShopOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuShopOrder::getMerId, bo.getMerId());
        lqw.eq(SohuShopOrder::getUserId, bo.getUserId());
        lqw.eq(SohuShopOrder::getIsUserDel, Boolean.FALSE);
        lqw.orderByDesc(SohuShopOrder::getCreateTime);
        Page<SohuShopOrderVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        result.getRecords().forEach(f -> {
            // 校验是否是售后订单
            LambdaQueryWrapper<SohuShopRefundOrder> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuShopRefundOrder::getShopOrderNo, f.getOrderNo());
            wrapper.orderByDesc(SohuShopRefundOrder::getId);
            List<SohuShopRefundOrderVo> refundOrderList = refundOrderMapper.selectVoList(wrapper);
            if (CollUtil.isNotEmpty(refundOrderList)) {
                String refundOrderNo = refundOrderList.get(0).getRefundOrderNo();
                f.setRefundOrderNo(refundOrderNo);
            }

            // 订单详情
            List<SohuShopOrderInfoVo> orderInfoList = infoService.getListByOrderNo(f.getOrderNo());
            if (CollUtil.isNotEmpty(orderInfoList)) {
                f.setOrderInfoList(orderInfoList);
            }
        });
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询用户分销商单列表
     */
    @Override
    public TableDataInfo<SohuShopOrderVo> queryIndependentList(Long userId, Long pmSharePubId, PageQuery pageQuery) {
        LambdaUpdateWrapper<SohuShopOrder> lqw = new LambdaUpdateWrapper<>();
        lqw.like(pmSharePubId != null, SohuShopOrder::getPmSharePubId, pmSharePubId);
        lqw.eq(SohuShopOrder::getIndependentUserId, userId);
        lqw.in(SohuShopOrder::getStatus, OrderConstants.ORDER_STATUS_SHIPPING, OrderConstants.ORDER_STATUS_AWAIT_RECEIVING, OrderConstants.ORDER_STATUS_OVER);
        lqw.isNotNull(SohuShopOrder::getPmSharePubId);
        lqw.orderByDesc(SohuShopOrder::getCreateTime);
        Page<SohuShopOrderVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public Boolean updateOrderStatus(String orderNo, String sourceStatus, String targetStatus) {
        LambdaUpdateWrapper<SohuShopOrder> luw = new LambdaUpdateWrapper();
        luw.eq(SohuShopOrder::getOrderNo, orderNo);
        luw.eq(SohuShopOrder::getStatus, sourceStatus);
        luw.set(SohuShopOrder::getStatus, targetStatus);
        return baseMapper.update(null, luw) > 0;
    }

    @Override
    public void merchantSalesExcute(String day) {
        String startTime = day + " 00:00:00";
        String endTime = day + " 23:59:59";
        List<SohuMerchantSalesReportVo> salesReportVos = baseMapper.merchantSales(startTime, endTime);
        remoteMerchantSalesService.excuteSalesReport(salesReportVos, day);
    }

    @Override
    public Long inTransitOrder(List<Long> merId, List<String> states) {
        LambdaQueryWrapper<SohuShopOrder> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuShopOrder::getMerId, merId);
        lqw.in(SohuShopOrder::getStatus, states);
        lqw.ne(SohuShopOrder::getRefundStatus, OrderConstants.ORDER_REFUND_STATUS_REFUND);
        return baseMapper.selectCount(lqw);
    }

    /**
     * 根据用户地址获取运费
     */
    @Override
    public Map<String, Object> getFreight(SohuShopOrderFreightBo bo) {
        Map<String, Object> map = Maps.newHashMap();
        BigDecimal freightFee = BigDecimal.ZERO;
        SohuUserAddressVo userAddress = remoteMiddleUserAddressService.queryById(bo.getAddressId());

        List<SohuPreOrderDetailReqBo> orderDetails = bo.getOrderDetails();
        List<SohuShopOrderInfoModel> orderInfoList = new ArrayList<>();
        orderDetails.forEach(f -> {
            SohuProductModel storeProduct = getStoreProduct(f.getProductId(), f.getProductNum());
            SohuShopOrderInfoModel model = new SohuShopOrderInfoModel();
            model.setProductId(f.getProductId());
            model.setProductName(storeProduct.getStoreName());
            model.setPayNum(f.getProductNum());
            model.setPrice(storeProduct.getPrice());
            model.setProductAttrValueId(f.getAttrValueId());
            orderInfoList.add(model);
        });
        // 获取订单实际支付金额
        BigDecimal proTotalPrice = orderInfoList.stream().map(e -> e.getPrice().multiply(new BigDecimal(e.getPayNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (Objects.isNull(userAddress)) {
            map.put("freightFee", freightFee);
            map.put("payFee", proTotalPrice);
            return map;
        }
        // 获取运费
        if (StrUtil.equalsAnyIgnoreCase(Constants.SOHUGLOBAL, bo.getSysSource())) {
            freightFee = getTotalFreightFee(orderInfoList, Long.valueOf(userAddress.getProvinceCode()));
        } else {
            freightFee = playletShopOrderService.getTotalOverseasFreightFee(orderInfoList, Long.valueOf(userAddress.getProvinceCode()));
        }
        BigDecimal payFee = proTotalPrice.add(freightFee);
        map.put("freightFee", freightFee);
        map.put("payFee", payFee);
        return map;
    }

    @Override
    public TableDataInfo<SohuOpenShopOrderVo> openList(SohuOpenShopOrderPageQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuShopOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuShopOrder::getOpenClientId, bo.getOpenClientId());
        lqw.ge(Objects.nonNull(bo.getStartTime()), SohuEntity::getCreateTime, bo.getStartTime());
        lqw.le(Objects.nonNull(bo.getEndTime()), SohuEntity::getCreateTime, bo.getEndTime());
        //lqw.between(SohuShopOrder::getCreateTime, bo.getStartTime(), bo.getEndTime());
        Page<SohuShopOrder> result = baseMapper.selectPage(PageQueryUtils.build(pageQuery), lqw);
        TableDataInfo<SohuOpenShopOrderVo> tableDataInfo = TableDataInfoUtils.copyInfo(TableDataInfoUtils.build(result), SohuOpenShopOrderVo.class);
        List<Long> merIdList = tableDataInfo.getData().stream().map(p -> p.getMerId()).collect(Collectors.toList());
        Map<Long, SohuMerchantModel> merMap = remoteMerchantService.getMerIdMapByIdList(merIdList);
        for (SohuOpenShopOrderVo vo : tableDataInfo.getData()) {
            SohuMerchantModel sohuMerchantModel = merMap.get(vo.getMerId());
            if (Objects.nonNull(sohuMerchantModel)) {
                vo.setMerchantName(sohuMerchantModel.getName());
            }
            //vo.setMerId(null);
            List<SohuShopOrderInfoVo> orderInfoListVo = this.infoService.getListByOrderNo(vo.getOrderNo());
            vo.setOrderInfoList(BeanCopyUtils.copyList(orderInfoListVo, SohuOpenShopOrderInfoVo.class));
        }
        return tableDataInfo;
    }

    @Override
    public SohuOpenShopOrderVo getByOrderNo(String orderNo, Long openClientId) {
        LambdaQueryWrapper<SohuShopOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuShopOrder::getOpenClientId, openClientId);
        lqw.eq(SohuShopOrder::getOrderNo, orderNo);
        lqw.last("limit 1");
        SohuShopOrder shopOrder = this.baseMapper.selectOne(lqw);
        if (Objects.isNull(shopOrder)) {
            return null;
        }
        SohuOpenShopOrderVo vo = BeanUtil.copyProperties(shopOrder, SohuOpenShopOrderVo.class);
        SohuMerchantModel sohuMerchantModel = remoteMerchantService.selectById(shopOrder.getMerId());
        if (Objects.nonNull(sohuMerchantModel)) {
            vo.setMerchantName(sohuMerchantModel.getName());
        }
        List<SohuShopOrderInfoVo> orderInfoListVo = this.infoService.getListByOrderNo(vo.getOrderNo());
        vo.setOrderInfoList(BeanCopyUtils.copyList(orderInfoListVo, SohuOpenShopOrderInfoVo.class));
        return vo;
    }
}
