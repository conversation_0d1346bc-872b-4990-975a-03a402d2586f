package com.sohu.common.core.enums;

import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SohuDateEnum implements Serializable {

    DAY("day", "日"),
    WEEK("week", "周"),
    MONTH("month", "月"),
    QUARTER("quarter", "季"),
    YEAR("year", "年");

    private String code;
    private String desc;

    // 新增的重载方法，接受 String 类型的日期
    public static SohuDateEnum getGranularityEnum(String startDate, String endDate) {
        // 解析 String -> Date
        Date start = DateUtil.parse(startDate, "yyyyMMdd");
        Date end = DateUtil.parse(endDate, "yyyyMMdd");

        // 调用现有的 getGranularityEnum(Date, Date) 方法
        return getGranularityEnum(start, end);
    }

    /**
     * 根据产品需求返回对应的日期枚举
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return {@link SohuDateEnum}
     */
    public static SohuDateEnum getGranularityEnum(Date startDate, Date endDate) {
        // 转换 Date -> LocalDate
        LocalDate startLocalDate = convertToLocalDate(startDate);
        LocalDate endLocalDate = convertToLocalDate(endDate);

        long daysBetween = ChronoUnit.DAYS.between(startLocalDate, endLocalDate);

        if (daysBetween >= 7 && daysBetween <= 90) {
            return SohuDateEnum.DAY;
        } else if (daysBetween > 90 && daysBetween <= 365) {
            return SohuDateEnum.WEEK;
        } else if (daysBetween > 365) {
            return SohuDateEnum.MONTH;
        } else {
            return SohuDateEnum.DAY; // 默认最小粒度
        }
    }

    // 支持 LocalDate 直接调用的重载方法
    public static SohuDateEnum getGranularityEnum(LocalDate startDate, LocalDate endDate) {
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);

        if (daysBetween >= 7 && daysBetween <= 90) {
            return SohuDateEnum.DAY;
        } else if (daysBetween > 90 && daysBetween <= 365) {
            return SohuDateEnum.WEEK;
        } else if (daysBetween > 365) {
            return SohuDateEnum.MONTH;
        } else {
            return SohuDateEnum.DAY;
        }
    }

    private static LocalDate convertToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }


    public static void main(String[] args) {
        // 示例 1：使用 java.util.Date
        Date start = new Date(); // 当前时间
        Date end = new Date(System.currentTimeMillis() + 100L * 24 * 3600 * 1000); // 未来 100 天
        SohuDateEnum result1 = getGranularityEnum(start, end);
        System.out.println(result1);

        // 示例 2：使用 LocalDate
        LocalDate start2 = LocalDate.of(2023, 1, 1);
        LocalDate end2 = LocalDate.of(2024, 1, 10);
        SohuDateEnum result2 = getGranularityEnum(start2, end2);
        System.out.println(result2);
    }
}
