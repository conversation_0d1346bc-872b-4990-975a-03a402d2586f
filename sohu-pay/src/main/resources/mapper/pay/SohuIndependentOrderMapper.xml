<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.pay.mapper.SohuIndependentOrderMapper">

    <resultMap type="com.sohu.pay.domain.SohuIndependentOrder" id="SohuIndependentOrderResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="siteId" column="site_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="independentObject" column="independent_object"/>
        <result property="independentPrice" column="independent_price"/>
        <result property="independentTotalPrice" column="independent_total_price"/>
        <result property="chargePrice" column="charge_price"/>
        <result property="independentStatus" column="independent_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="tradeType" column="trade_type"/>
        <result property="merId" column="mer_id"/>
    </resultMap>

    <update id="updateIndependentStatus">
        UPDATE sohu_independent_order
        SET independent_status = #{independentStatus}
        WHERE order_no = #{orderNo}
          AND trade_type = #{tradeType}
    </update>

    <update id="updateIndependentStatusByOrderNo">
        UPDATE sohu_independent_order
        SET independent_status = #{independentStatus}
        WHERE order_no = #{orderNo}
    </update>

    <select id="selectSumDistributionAmount" resultType="java.math.BigDecimal">
        SELECT SUM(independent_price)
        FROM sohu_independent_order WHERE user_id =#{userId} AND trade_type =#{tradeType} AND del_flag = 0
        <if test="independentStatus!=null and independentStatus!=''">
            AND independent_status =#{independentStatus}
        </if>
        <if test="startTime!=null and startTime!=''">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            AND create_time &lt;= #{endTime}
        </if>

    </select>

    <select id="selectCountOrder" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sohu_independent_order WHERE user_id =#{userId} AND trade_type =#{tradeType} AND del_flag = 0
        <if test="independentStatus!=null and independentStatus!=''">
            AND independent_status =#{independentStatus}
        </if>
        <if test="startTime!=null and startTime!=''">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <select id="selectAggrBySharePerson" resultType="com.sohu.pay.api.vo.SohuIndependentOrderAggrVo">
        SELECT sum(independent_price) as independentAmount, count(1) as orderCount
        FROM sohu_independent_order
        WHERE user_id =#{userId} AND trade_type =#{tradeType} AND del_flag = 0
        <if test="independentStatus!=null and independentStatus!=''">
            AND independent_status =#{independentStatus}
        </if>
        <if test="startTime!=null and startTime!=''">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <select id="listOrderNo" resultType="java.lang.String">
        SELECT order_no
        FROM sohu_independent_order WHERE user_id =#{userId} AND trade_type =#{tradeType} AND del_flag = 0
        <if test="independentStatus!=null and independentStatus!=''">
            AND independent_status =#{independentStatus}
        </if>
        <if test="startTime!=null and startTime!=''">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <update id="updateIndependentStatusByTradeNo">
        UPDATE sohu_independent_order
        SET independent_status = #{independentStatus}
        WHERE trade_no = #{trade_no}
    </update>

</mapper>
