package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.bo.SohuBusyTaskPayBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskRefundBo;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.busyorder.api.vo.SohuBusyTaskPayVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.system.api.RemoteUserService;
import com.sohu.third.wechat.pay.constant.WechatPayConstants;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 任务商单支付酬金
 */
@Component
@Slf4j
public class YiMaBusyTaskFlowStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    private RemoteIndependentTemplateService remoteIndependentTemplateService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @Autowired
    private AsyncConfig asyncConfig;

    private static final String BUSY_TASK_PAY_PREFIX = "BUSY_TASK_FLOW:";

    @Override
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 任务商单 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        if (!RedisUtils.setObjectIfAbsent(BUSY_TASK_PAY_PREFIX + payBo.getMasterId(), 1, Duration.ofSeconds(1))) {
            throw new ServiceException("请勿重复提交");
        }
        return getPay(payBo);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - 任务商单 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        String taskNumber = refundPayBo.getMasterOrderNo();
        //查询支付记录
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.Paid.name());
        if (CollUtil.isEmpty(payList)) {
            throw new ServiceException("任务商单支付记录不存在");
        }
        SohuBusyTaskPayVo sohuBusyTaskPayVo = payList.get(0);
        // 转换价格y-f
        int totalPrice = CalUtils.yuanToCent(sohuBusyTaskPayVo.getPayAmount()).intValue();
        //退款流水号
        String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
        //翼码退款
        BarcodeReverseRequest barcodeReverseRequest = getBarcodeReverseRequest();
        barcodeReverseRequest.setPosSeq(refundNumber);
        BarcodeReverse barcodeReverse = new BarcodeReverse();
        barcodeReverse.setTxAmt(totalPrice);
        barcodeReverse.setOrgPosSeq(sohuBusyTaskPayVo.getPayNumber());
        barcodeReverseRequest.setBarcodeReverseRequest(barcodeReverse);
        log.info("退款请求request={}", JSONObject.toJSONString(barcodeReverseRequest));
        BarcodeReverseResponse barcodeReverseResponse = Client.getClient().execute(barcodeReverseRequest);
        log.info("退款请求response返回={}", JSONObject.toJSONString(barcodeReverseResponse));
        List<String> resultList = Lists.newArrayList("9998", "0000");
        if (ObjectUtils.isNull(barcodeReverseResponse) || !resultList.contains(barcodeReverseResponse.getResult().getId())) {
            log.error("流量商单退款异常：{}", JSONUtil.toJsonStr(barcodeReverseResponse));
            throw new RuntimeException(barcodeReverseResponse.getResult().getComment());
        }
        //新增退款记录
        SohuBusyTaskRefundBo refundBo = new SohuBusyTaskRefundBo();
        refundBo.setTaskNumber(taskNumber);
        refundBo.setRefundOrderNo(refundNumber);
        refundBo.setRefundNumber(refundNumber);
        refundBo.setUserId(sohuBusyTaskPayVo.getUserId());
        refundBo.setTransactionId(sohuBusyTaskPayVo.getTransactionId());
        refundBo.setRefundAmount(sohuBusyTaskPayVo.getPayAmount());
        refundBo.setRefundStatus(WechatPayConstants.RefundStatus.SUCCESS);
        remoteBusyTaskService.saveBusyTaskRefund(refundBo);
        //更新支付表中退款信息
        SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
        payBo.setId(sohuBusyTaskPayVo.getId());
        payBo.setRefundAmount(sohuBusyTaskPayVo.getPayAmount());
        payBo.setRefundId(refundNumber);
        payBo.setPayStatus(PayStatus.Refund.name());
        remoteBusyTaskService.updateBusyTaskPay(payBo);
        //更新流水记录状态 待支付->已支付
        remoteMiddleTradeRecordService.updatePayStatus(taskNumber, null, PayStatus.Paid.name(), PayStatus.Refund.name());
        //新增退款流水
        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = SohuTradeRecordBo.builder();
        recordBo.userId(sohuBusyTaskPayVo.getUserId());
        recordBo.amount(sohuBusyTaskPayVo.getPayAmount());
        recordBo.payType(sohuBusyTaskPayVo.getPayType());
        recordBo.payStatus(PayStatus.Refund.name());
        recordBo.payTime(new Date());
        recordBo.operateChannel(sohuBusyTaskPayVo.getPayChannel());
        recordBo.type(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        recordBo.consumeCode("0");
        recordBo.msg("流量商单分账余额退款");
        recordBo.amountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
        recordBo.payNumber(sohuBusyTaskPayVo.getPayNumber());
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        // 保存流水记录
        saveTradeRecord(recordBo.build());
        return true;
    }

    /**
     * 平台服务费：支付金额*10%
     * 第三方手续费：支付金额*千分之三
     * <p>
     * 如果平台服务费-第三方手续费>平台自留1%的金额
     * 用户分账金额 = 支付金额-平台服务费    公司账户分账 = 平台服务费-手续费
     * 如果平台服务费-第三方手续费<平台自留1%的金额
     * 用户分账金额 = 支付金额-平台服务费-自留金额(支付金额*1%)    公司账户分账= 支付金额*1%
     * <p>
     * ------------------------------------------------------------------------------------------------------------------------
     * 发单方支付：10000
     * 平台服务费：10000*10% = 1000
     * 三方手续费：10000*0.3% = 30 （支付回调返回）
     * 公司账户自留金额：10000*1% = 100 (至少自留1%)
     * <p>
     * if 平台服务费 - 三方手续费 > 自留金额    1000-30 >100
     * 用户分账金额 = （10000-1000 = 9000）  公司账户分账 = （1000-30=970）
     * if  平台服务费 - 三方手续费 < 自留金额    1000-30 < 100
     * 用户分账金额 = （10000-30-100 = 9870)  公司账户分账 = 100
     *
     * @param callbackResponse 三方回调信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 任务商单回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        String transactionId = response.getTradeNo();
        String chargeAmount = response.getChargeAmount();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        SohuBusyTaskPayVo sohuBusyTaskPayVo = remoteBusyTaskService.queryBusyTaskPay(outTradeNo, PayStatus.WaitPay.name());
        Objects.requireNonNull(sohuBusyTaskPayVo, "任务商单支付记录不存在");
        //平台服务费比例
        BigDecimal platformRatio = sohuBusyTaskPayVo.getPlatformRatio();
        //三方手续费 f->y
        BigDecimal chargeFee = CalUtils.divide(new BigDecimal(chargeAmount), CalUtils.PERCENTAGE);
        //手续费承担方 0 分账方 1 平台
        int chargeUndertake = sohuBusyTaskPayVo.getChargeUndertake();
        // 平台自留金额 1% 支付金额*1%
        BigDecimal platformAmt = sohuBusyTaskPayVo.getPayAmount().multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        /******************************************************************************************/
//        //平台服务费 支付金额 * 分账平台设置比例
//        BigDecimal platformCharge = CalUtils.multiply(sohuBusyTaskPayVo.getPayAmount(), CalUtils.divide(platformRatio, CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//        // 平台分账金额
//        BigDecimal platformChargeFee = BigDecimal.ZERO;
//        // 可分账总金额
//        BigDecimal distributorAmount = BigDecimal.ZERO;
//        if (platformCharge.subtract(chargeFee).compareTo(platformAmt) > 0) {
//            platformChargeFee = platformCharge.subtract(chargeFee);
//            distributorAmount = sohuBusyTaskPayVo.getPayAmount().subtract(platformCharge);
//        } else {
//            platformChargeFee = platformAmt;
//            distributorAmount = sohuBusyTaskPayVo.getPayAmount().subtract(platformCharge).subtract(chargeFee);
//        }
//        // 分销人可分账金额
//        BigDecimal shareRadio = CalUtils.divide(sohuBusyTaskPayVo.getShareAmount(), sohuBusyTaskPayVo.getPayAmount());
//        BigDecimal shareDistributorAmount = distributorAmount.multiply(shareRadio).setScale(2, RoundingMode.HALF_UP);
//        //接单人分销金额
//        BigDecimal receiveDistributorAmount = CalUtils.sub(distributorAmount, shareDistributorAmount);
        /*************************************************************************************************/
        BigDecimal payAmount = sohuBusyTaskPayVo.getPayAmount();
        BigDecimal busyAmount = sohuBusyTaskPayVo.getBusyAmount();
        BigDecimal shareAmount = sohuBusyTaskPayVo.getShareAmount();
        //平台服务费 支付金额*分账平台设置比例
        BigDecimal platformCharge = CalUtils.multiply(payAmount, CalUtils.divide(platformRatio, CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        // 商单金额占比
        BigDecimal distributorRadio = CalUtils.divide(busyAmount, payAmount);
        // 平台商单服务费
        BigDecimal platformChargeFee = CalUtils.multiply(platformCharge, distributorRadio).setScale(2, RoundingMode.HALF_UP);
        // 平台分销服务费
        BigDecimal platformShareAmount = platformCharge.subtract(platformChargeFee);
        //商单分账总金额
        BigDecimal distributorAmount = BigDecimal.ZERO;
        //商单分销总金额
        BigDecimal shareDistributorAmount = BigDecimal.ZERO;
        //承担手续费 0 分账方 1 平台
        if (chargeUndertake == Constants.ONE) {
            distributorAmount = busyAmount.subtract(platformChargeFee);
            platformChargeFee = platformChargeFee.subtract(chargeFee);
            shareDistributorAmount = shareAmount.subtract(platformShareAmount);
        } else if (chargeUndertake == Constants.ZERO) {
            BigDecimal distributorChargeAmount = CalUtils.multiply(chargeFee, distributorRadio).setScale(2, RoundingMode.HALF_UP);
            distributorAmount = busyAmount.subtract(platformChargeFee).subtract(distributorChargeAmount);
            shareDistributorAmount = shareAmount.subtract(platformShareAmount).subtract(CalUtils.sub(chargeFee, distributorChargeAmount));
        }

        Date now = new Date();
        //更新支付状态 待支付->已支付
        SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
        payBo.setId(sohuBusyTaskPayVo.getId());
        payBo.setTransactionId(transactionId);
        payBo.setChargeAmount(chargeFee);
        payBo.setPayTime(now);
        payBo.setReceiveDistributorAmount(distributorAmount);
        payBo.setPlatformAmount(platformChargeFee);
        payBo.setShareDistributorAmount(shareDistributorAmount);
        payBo.setPlatformShareAmount(platformShareAmount);
        payBo.setPayStatus(PayStatus.Paid.name());
        remoteBusyTaskService.updateBusyTaskPay(payBo);
        //更新商单状态 待支付->待接单
        remoteBusyTaskService.updateTaskState(sohuBusyTaskPayVo.getTaskNumber(), SohuBusyTaskState.WaitReceive.name());
        // 发送系统通知
        SohuBusyTaskVo busyTaskVo = remoteBusyTaskService.getByTaskNo(sohuBusyTaskPayVo.getTaskNumber());
        if (Objects.nonNull(busyTaskVo)) {
            remoteBusyTaskNoticeService.sendTaskNotice(busyTaskVo.getId(), TaskNoticeEnum.TASK_PUBLISH_SUCCESS,
                    busyTaskVo.getUserId(), null, null, Boolean.TRUE);
        }
        //新增交易流水
        SohuTradeRecordBo recordBo = SohuTradeRecordBo.builder().build();
        recordBo.setUserId(sohuBusyTaskPayVo.getUserId());
        recordBo.setType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        recordBo.setConsumeType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        recordBo.setConsumeCode(sohuBusyTaskPayVo.getTaskNumber());
        recordBo.setMsg(SohuTradeRecordEnum.Type.BusyTaskFlow.getMsg());
        recordBo.setAmountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordBo.setPayNumber(outTradeNo);
        recordBo.setAccountType(SohuTradeRecordEnum.AccountType.Amount.name());
        recordBo.setPayStatus(PayStatus.Paid.name());
        recordBo.setPayTime(now);
        recordBo.setAmount(sohuBusyTaskPayVo.getPayAmount());
        recordBo.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
        recordBo.setOperateChannel(sohuBusyTaskPayVo.getPayChannel());
        recordBo.setUnq(getUnq());
        // 保存流水记录 - 钱 - 支出
        saveTradeRecord(recordBo);
        //异步新增埋点数据
        CompletableFuture.runAsync(() -> {
            LoginUser loginUser = remoteUserService.queryById(sohuBusyTaskPayVo.getUserId());
            SohuBusyTaskVo sohuBusyTaskModel = remoteBusyTaskService.getByTaskNo(sohuBusyTaskPayVo.getTaskNumber());
            SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
            eventAttribute.setContentNo(sohuBusyTaskModel.getTaskNumber());
            eventAttribute.setContentName(sohuBusyTaskModel.getTitle());
            eventAttribute.setAmount(sohuBusyTaskModel.getFullAmount());
            eventAttribute.setContentType("通用");
            syncPayUserBehavior(loginUser.getUserId(), loginUser.getNickname(), "sd_pay_success", "愿望赏金支付成功", eventAttribute);
        }, asyncConfig.getAsyncExecutor());
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        Long masterId = payBo.getMasterId();
        Long userId = LoginHelper.getUserId();
//        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
//                masterId + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + userId;
//        if (StrUtil.isNotEmpty(payBo.getPayChannel()) && payBo.getPayChannel().equals("mobile")) {
//            boolean exists = RedisUtils.isExistsObject(key);
//            // 如果存在直接唤醒
//            if (exists) {
//                log.info("任务保证金支付订单存在，直接唤醒支付，订单号：{}，缓存值：{}", payBo.getMasterOrderNo(), RedisUtils.getCacheObject(key));
//                return RedisUtils.getCacheObject(key);
//            }
//        }
        // 查询商单
        List<SohuBusyTaskModel> taskList = remoteBusyTaskService.queryList(Arrays.asList(masterId));
        if (CollUtil.isEmpty(taskList)) {
            throw new ServiceException("任务商单不存在");
        }
        SohuBusyTaskModel sohuBusyTaskModel = taskList.get(0);
        // 判断是否未支付记录，如果存在删除撤销之前的交易
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(sohuBusyTaskModel.getTaskNumber(), PayStatus.WaitPay.name());
        if (CollUtil.isNotEmpty(payList)) {
            SohuBusyTaskPayVo sohuBusyTaskPayVo = payList.get(0);
            barcodeCancelPay(sohuBusyTaskPayVo.getPayNumber(), sohuBusyTaskPayVo.getId(), masterId, sohuBusyTaskPayVo.getUserId(), sohuBusyTaskPayVo.getPayChannel());
        }
        // 查询分账模板
        SohuIndependentTemplateModel templateModel = remoteIndependentTemplateService.queryTemplateInfo(null, null, 2);
        if (Objects.isNull(templateModel)) {
            throw new ServiceException("商单分账模板为空,请联系系统管理员");
        }
        // 支付酬金金额 开启分销 支付酬金金额 = 商单金额 + 分销金额  未开启分销 支付酬金金额 = 商单金额
        BigDecimal allAmount = sohuBusyTaskModel.getSingleAmount().multiply(BigDecimal.valueOf(sohuBusyTaskModel.getDeliveryStandard()));
        BigDecimal disAmount = getDisAmount(allAmount, sohuBusyTaskModel.getKickbackValue(), sohuBusyTaskModel.getKickbackType());
        BigDecimal payAmount = allAmount.add(disAmount);
        if (sohuBusyTaskModel.getFullAmount().compareTo(allAmount) != 0) {
            throw new ServiceException("商单金额异常");
        }
        String outTradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_TASK_FLOW);
        //封装支付请求参数
        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        ScanpayRequest request = getScanpayRequest();
        // 备注
        unifiedorderRequest.setMemo("流量商单支付酬金");
        request.setMemo("流量商单支付酬金");
        // 唯一订单号
        unifiedorderRequest.setPosSeq(outTradeNo);
        request.setPosSeq(outTradeNo);
        // 转换价格y-f
        int totalPrice = CalUtils.yuanToCent(payAmount).intValue();
        // 商单价格对象
        // 总金额
        Unifiedorder unifiedOrder = getUnifiedorder(payBo.getUserId());
        Scanpay scanpay = getScanpay();
        unifiedOrder.setTxAmt(totalPrice);
        scanpay.setTxAmt(totalPrice);
        // 是否分账配置
        ExtendParams extendParams = new ExtendParams();
        // 不分账 -- R实时分账 --D延时分账
        extendParams.setSplitFlag(ExtendParams.D);
        unifiedOrder.setExtendParams(extendParams);
        scanpay.setExtendParams(extendParams);
        // 设置请求参数
        unifiedorderRequest.setUnifiedorderRequest(unifiedOrder);
        request.setScanpayRequest(scanpay);
        //新增商单支付记录
        SohuBusyTaskPayBo sohuBusyTaskPayBo = new SohuBusyTaskPayBo();
        sohuBusyTaskPayBo.setUserId(userId);
        sohuBusyTaskPayBo.setPayAmount(payAmount);
        sohuBusyTaskPayBo.setPayNumber(outTradeNo);
        sohuBusyTaskPayBo.setPayStatus(PayStatus.WaitPay.name());
        sohuBusyTaskPayBo.setBusyType(BusyType.BusyTaskFlow.name());
        sohuBusyTaskPayBo.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
        sohuBusyTaskPayBo.setPayChannel(getOperateChannel(payBo.getPayChannel()));
        sohuBusyTaskPayBo.setOrderNo(outTradeNo);
        sohuBusyTaskPayBo.setTaskNumber(sohuBusyTaskModel.getTaskNumber());
        sohuBusyTaskPayBo.setPlatformRatio(templateModel.getPlatformRatio());
        sohuBusyTaskPayBo.setAdminRatio(templateModel.getAdminRatio());
        sohuBusyTaskPayBo.setConsumerInviteRatio(templateModel.getConsumerInviteRatio());
        sohuBusyTaskPayBo.setDistributorRatio(templateModel.getDistributorRatio());
        sohuBusyTaskPayBo.setDistributorInviteRatio(templateModel.getDistributorInviteRatio());
        sohuBusyTaskPayBo.setBusyAmount(allAmount);
        sohuBusyTaskPayBo.setShareAmount(disAmount);
        sohuBusyTaskPayBo.setChargeUndertake(templateModel.getChargeUndertake());
        remoteBusyTaskService.saveBusyTaskPay(sohuBusyTaskPayBo);

        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-流量商单支付请求request：{}", JSONUtil.toJsonStr(request));
            ScanpayResponse response = Client.getClient().execute(request);
            log.info("翼码支付 - PC-流量商单支付请求response返回：{}", JSONUtil.toJsonStr(response));
//            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- 流量商单支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        response.setTaskNumber(sohuBusyTaskModel.getTaskNumber());
        log.info("翼码支付 -MOBILE- 流量商单支付请求response返回：{}", JSONUtil.toJsonStr(response));
//        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);
    }

    private BigDecimal getDisAmount(BigDecimal fullAmount, BigDecimal kickbackValue, String kickbackType) {
        switch (kickbackType) {
            case "percentage":
                BigDecimal percentage = BigDecimalUtils.divide(kickbackValue, CalUtils.PERCENTAGE);
                return fullAmount.multiply(percentage).setScale(2, RoundingMode.HALF_UP);
            case "price":
                return kickbackValue;
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 撤销交易
     */
    private void barcodeCancelPay(String payNumber, Long payId, Long masterId, Long userId, String payChannel) {
//        BarcodeCancelRequest cancelRequest = getBarcodeCancelRequest();
//        cancelRequest.setPosSeq("C" + System.nanoTime());
//        BarcodeCancel cancel = new BarcodeCancel();
//        cancel.setOrgPosSeq(payNumber);
//        cancelRequest.setBarcodeCancelRequest(cancel);
//        System.out.println("撤销交易request=" + JSONObject.toJSONString(cancelRequest));
//        BarcodeCancelResponse barcodeCancelResponse = Client.getClient().execute(cancelRequest);
//        System.out.println("撤销交易请求response返回=" + JSONObject.toJSONString(barcodeCancelResponse));
        //更新支付状态 待支付->取消支付
        SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
        payBo.setId(payId);
        payBo.setPayStatus(PayStatus.Cancel.name());
        remoteBusyTaskService.updateBusyTaskPay(payBo);
        //删除缓存
//        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
//                masterId + StrPool.COLON + payChannel + StrPool.COLON + userId;
//        RedisUtils.deleteObject(key);
    }

    public static void main(String[] args) {
        BigDecimal platformRatio = new BigDecimal("10.00");
        BigDecimal chargeAmount = new BigDecimal("300");
        BigDecimal payAmount = new BigDecimal("1000");
        BigDecimal shareAmount = new BigDecimal("200");
        BigDecimal busyAmount = new BigDecimal("800");
        int chargeUndertake = Constants.ONE;
        //三方手续费 f->y
        BigDecimal chargeFee = CalUtils.divide(chargeAmount, CalUtils.PERCENTAGE);
        // 平台自留金额 1% 支付金额*1%
        BigDecimal platformAmt = payAmount.multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        //平台服务费 支付金额 * 分账平台设置比例
        BigDecimal platformCharge = CalUtils.multiply(payAmount, CalUtils.divide(platformRatio, CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        // 平台商单服务费
        BigDecimal platformChargeFee = CalUtils.multiply(platformCharge, CalUtils.divide(busyAmount, payAmount)).setScale(2, RoundingMode.HALF_UP);
        // 平台分销服务费
        BigDecimal platformShareAmount = platformCharge.subtract(platformChargeFee);
        // 可分账总金额
        BigDecimal distributorAmount = BigDecimal.ZERO;
        BigDecimal shareDistributorAmount = BigDecimal.ZERO;
        //平台承担手续费
        if (chargeUndertake == Constants.ONE) {
            distributorAmount = busyAmount.subtract(platformChargeFee);
            platformChargeFee = platformChargeFee.subtract(chargeFee);
            shareDistributorAmount = shareAmount.subtract(platformShareAmount);
        } else if (chargeUndertake == Constants.ZERO) {
            BigDecimal distributorRadio = CalUtils.divide(busyAmount, payAmount);
            BigDecimal distributorChargeAmount = CalUtils.multiply(chargeFee, distributorRadio).setScale(2, RoundingMode.HALF_UP);
            distributorAmount = busyAmount.subtract(platformChargeFee).subtract(distributorChargeAmount);
            shareDistributorAmount = shareAmount.subtract(platformShareAmount).subtract(CalUtils.sub(chargeFee, distributorChargeAmount));
        }
        System.out.println("三方手续费=" + chargeFee);
        System.out.println("商单平台手续费=" + platformChargeFee);
        System.out.println("分销平台手续费=" + platformShareAmount);
        System.out.println("用户分账金额" + distributorAmount);
        System.out.println("分销分账金额" + shareDistributorAmount);
    }

}
