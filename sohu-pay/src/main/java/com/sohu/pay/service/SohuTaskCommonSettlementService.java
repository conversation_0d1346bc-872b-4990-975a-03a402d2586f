package com.sohu.pay.service;

import com.sohu.busyorder.api.vo.SohuBusyTaskPayVo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/4 12:29
 */
public interface SohuTaskCommonSettlementService extends SohuBaseSettlementService {

    /**
     * 取消商单结算
     *
     * @param taskNumber
     * @return
     */
    Boolean cancelTaskSettle(String taskNumber, Integer paySceneType);

    /**
     * 保证金退款
     *
     * @param payVo        支付记录
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return
     */
    Boolean operRefund(SohuBusyTaskPayVo payVo, BigDecimal refundAmount, String refundReason);


    /**
     * 延时修改商单状态
     * @param payNumber
     * @return
     */
    Boolean delayTaskUpdate(String payNumber);

    /**
     * 管理员审核结算商单
     * @param taskNumber
     * @return
     */
    Boolean applySettle(String taskNumber, String reason);

    /**
     * 通用商单待入账处理
     * @param busyType
     * @param busyCode
     * @return
     */
    Boolean waitIncomeProcess(String busyType, Long busyCode);

}
