package com.sohu.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.pay.api.bo.SohuIndependentPlayetTemplateBo;
import com.sohu.pay.api.bo.SohuIndependentTemplateBo;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.vo.SohuIndependentTemplateVo;
import com.sohu.pay.domain.SohuIndependentTemplate;
import com.sohu.pay.domain.SohuOperationRecord;
import com.sohu.pay.mapper.SohuIndependentTemplateMapper;
import com.sohu.pay.mapper.SohuOperationRecordMapper;
import com.sohu.pay.service.ISohuIndependentTemplateService;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分账模版Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-11
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuIndependentTemplateServiceImpl implements ISohuIndependentTemplateService {

    private final SohuIndependentTemplateMapper baseMapper;
    private final SohuOperationRecordMapper recordMapper;


    /**
     * 查询分账模版
     */
    @Override
    public SohuIndependentTemplateVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询分账模版列表
     */
    @Override
    public TableDataInfo<SohuIndependentTemplateVo> queryPageList(SohuIndependentTemplateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuIndependentTemplate> lqw = buildQueryWrapper(bo);
        Page<SohuIndependentTemplateVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuIndependentTemplateVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfoUtils.build();
        }
        for (SohuIndependentTemplateVo record : records) {
            record.setTemplateCommon(record.getTemplateCommon() == null ? Boolean.FALSE : record.getTemplateCommon());
        }
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询分账模版列表
     */
    @Override
    public List<SohuIndependentTemplateVo> queryList(SohuIndependentTemplateBo bo) {
        LambdaQueryWrapper<SohuIndependentTemplate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuIndependentTemplate> buildQueryWrapper(SohuIndependentTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuIndependentTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTemplateType() != null, SohuIndependentTemplate::getTemplateType, bo.getTemplateType());
        lqw.eq(bo.getDistributorRatio() != null, SohuIndependentTemplate::getDistributorRatio, bo.getDistributorRatio());
        lqw.eq(bo.getConsumerInviteRatio() != null, SohuIndependentTemplate::getConsumerInviteRatio, bo.getConsumerInviteRatio());
        lqw.eq(bo.getDistributorInviteRatio() != null, SohuIndependentTemplate::getDistributorInviteRatio, bo.getDistributorInviteRatio());
        lqw.eq(bo.getAgencyRatio() != null, SohuIndependentTemplate::getAgencyRatio, bo.getAgencyRatio());
        lqw.eq(bo.getCityRatio() != null, SohuIndependentTemplate::getCityRatio, bo.getCityRatio());
        lqw.eq(bo.getCountryRatio() != null, SohuIndependentTemplate::getCountryRatio, bo.getCountryRatio());
        lqw.eq(bo.getAdminRatio() != null, SohuIndependentTemplate::getAdminRatio, bo.getAdminRatio());
        lqw.eq(null != bo.getSiteId(), SohuIndependentTemplate::getSiteId, bo.getSiteId());
        lqw.eq(bo.getIsValid() != null, SohuIndependentTemplate::getIsValid, bo.getIsValid());
        lqw.eq(bo.getSiteType() != null, SohuIndependentTemplate::getSiteType, bo.getSiteType());
        lqw.orderByDesc(SohuIndependentTemplate::getCreateTime);
        return lqw;
    }

    /**
     * 新增分账模版
     */
    @Override
    public Boolean insertByBo(SohuIndependentTemplateBo bo) {
        if (bo.getTemplateCommon()) {
            if (this.exitCommonData(bo.getTemplateType())) {
                throw new ServiceException("通用平台模板已存在");
            }
            SohuIndependentTemplate add = BeanCopyUtils.copy(bo, SohuIndependentTemplate.class);
            return baseMapper.insert(add) > 0;
        }
        List<SohuIndependentTemplate> templateList = bo.getSiteClassList().stream()
                .map(siteClass -> {
                    SohuIndependentTemplate sohuIndependentTemplate = BeanUtil.toBean(bo, SohuIndependentTemplate.class);
                    sohuIndependentTemplate.setSiteId(siteClass.getSiteId());
                    sohuIndependentTemplate.setSiteName(siteClass.getSiteName());
                    return sohuIndependentTemplate;
                }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(templateList)) {
            List<Long> siteIds = templateList.stream().map(SohuIndependentTemplate::getSiteId).collect(Collectors.toList());
            LambdaQueryWrapper<SohuIndependentTemplate> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(SohuIndependentTemplate::getSiteId, siteIds).eq(SohuIndependentTemplate::getTemplateType, bo.getTemplateType());
            // 查询所有的要插入的站点信息
            List<SohuIndependentTemplate> existingRecords = this.baseMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(existingRecords)) {
                throw new ServiceException("新增失败,站点已存在分账模板");
            }
//            Map<Long, SohuIndependentTemplate> existingRecordMap = existingRecords.stream()
//                    .collect(Collectors.toMap(SohuIndependentTemplate::getSiteId, Function.identity()));
//            // 筛选
//            List<SohuIndependentTemplate> templatesToInsert = templateList.stream()
//                    .filter(record -> !existingRecordMap.containsKey(record.getSiteId()))
//                    .collect(Collectors.toList());

            return baseMapper.insertBatch(templateList);
        }
        return false;
    }

    /**
     * 修改分账模版
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SohuIndependentTemplateBo bo) {
        SohuIndependentTemplate update = BeanUtil.toBean(bo, SohuIndependentTemplate.class);
        validEntityBeforeSave(update);
        boolean exit = baseMapper.updateById(update) > 0;
        if (exit && ObjectUtil.isNotNull(bo.getTypes())) {
            List<SohuOperationRecord> operationRecordList = Lists.newArrayList();
            return extractedJsonTemplate(bo, operationRecordList);
        }
        return false;
    }

    /**
     * 添加操作记录
     *
     * @param bo
     * @param operationRecordList
     */
    private boolean extractedJsonTemplate(SohuIndependentTemplateBo bo, List<SohuOperationRecord> operationRecordList) {
        if (ObjectUtil.isNotNull(bo.getTypes())) {
            SohuIndependentTemplateBo jsonTemplate = bo.getTypes();
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (ObjectUtil.isNull(loginUser)) {
                throw new RuntimeException("未登录");
            }
            // 是否有效
            if (ObjectUtil.isNotNull(jsonTemplate.getIsValid())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
//                String isValid = (jsonTemplate.getIsValid()) ? "有效" : "无效";
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【有效性】改为：" + "【" + ((jsonTemplate.getIsValid()) ? "有效" : "无效") + "】");
                operationRecordList.add(operationRecord);
            }
            // 分销人
            if (ObjectUtil.isNotNull(jsonTemplate.getDistributorRatio())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【分销人】的【分账系数】改为：" + "【" + jsonTemplate.getDistributorRatio() + "%】");
                operationRecordList.add(operationRecord);
            }
            // 分销人的拉新人
            if (ObjectUtil.isNotNull(jsonTemplate.getDistributorInviteRatio())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【分销人的拉新人】的【分账系数】改为：" + "【" + jsonTemplate.getDistributorInviteRatio() + "%】");
                operationRecordList.add(operationRecord);
            }
            // 消费者用户的拉新人
            if (ObjectUtil.isNotNull(jsonTemplate.getConsumerInviteRatio())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【消费者用户的拉新人】的【分账系数】改为：" + "【" + jsonTemplate.getConsumerInviteRatio() + "%】");
                operationRecordList.add(operationRecord);
            }
            // 平台系数
            if (ObjectUtil.isNotNull(jsonTemplate.getPlatformRatio())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【平台系数】改为：" + "【" + jsonTemplate.getPlatformRatio() + "%】");
                operationRecordList.add(operationRecord);
            }
            // 城市站长
            if (ObjectUtil.isNotNull(jsonTemplate.getCityRatio())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【城市站】的【分账系数】改为：" + "【" + jsonTemplate.getCityRatio() + "%】");
                operationRecordList.add(operationRecord);
            }
            // 国家站长
            if (ObjectUtil.isNotNull(jsonTemplate.getCountryRatio())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【国家站】的【分账系数】改为：" + "【" + jsonTemplate.getCountryRatio() + "%】");
                operationRecordList.add(operationRecord);
            }
            // 代理人
            if (ObjectUtil.isNotNull(jsonTemplate.getAgencyRatio())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【代理】的【分账系数】改为：" + "【" + jsonTemplate.getAgencyRatio() + "%】");
                operationRecordList.add(operationRecord);
            }
            // 平台
            if (ObjectUtil.isNotNull(jsonTemplate.getAdminRatio())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【平台】的【分账系数】改为：" + "【" + jsonTemplate.getAdminRatio() + "%】");
                operationRecordList.add(operationRecord);
            }
            // 站点
            if (ObjectUtil.isNotNull(jsonTemplate.getSiteName())) {
                SohuOperationRecord operationRecord = new SohuOperationRecord();
                operationRecord.setIndependentId(bo.getId()).setUserId(LoginHelper.getUserId());
                operationRecord.setOperationType("用户：" + loginUser.getNickname() + "将【站点】改为：" + "【" + jsonTemplate.getSiteName() + "】");
                operationRecordList.add(operationRecord);
            }
        }
        return recordMapper.insertBatch(operationRecordList);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuIndependentTemplate entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除分账模版
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
//        List<SohuIndependentTemplate> independentTemplates = this.baseMapper.selectBatchIds(ids);
        boolean flag = baseMapper.deleteBatchIds(ids) > 0;
//        if (flag) {
//            List<SohuOperationRecord> operationRecordList = Lists.newArrayList();
//            for (SohuIndependentTemplate independentTemplate : independentTemplates) {
//                SohuOperationRecord operationRecord = new SohuOperationRecord();
//                operationRecord.setIndependentId(independentTemplate.getId()).setOperationType("DELETE").setUserId(LoginHelper.getUserId());
//                operationRecordList.add(operationRecord);
//            }
//            return recordMapper.insertBatch(operationRecordList);
//        }

        return flag;
    }

    @Override
    public SohuIndependentTemplateVo exitData(Long siteId, Integer templateType) {
        LambdaQueryWrapper<SohuIndependentTemplate> wrapper = new LambdaQueryWrapper<>();
        if (siteId != null) {
            wrapper.eq(SohuIndependentTemplate::getSiteId, siteId);
        }else {
            wrapper.eq(SohuIndependentTemplate::getTemplateCommon, true);
        }
        wrapper.eq(SohuIndependentTemplate::getTemplateType, templateType);
        wrapper.eq(SohuIndependentTemplate::getIsValid, true).last(" limit 1");
        return this.baseMapper.selectVoOne(wrapper);
    }

    @Override
    public SohuIndependentTemplateVo queryByIdAndType(Long siteId, Integer type) {
        LambdaQueryWrapper<SohuIndependentTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentTemplate::getSiteId, siteId).eq(SohuIndependentTemplate::getTemplateType, type).eq(SohuIndependentTemplate::getIsValid, true);
        return this.baseMapper.selectVoOne(wrapper);
    }

    @Override
    public SohuIndependentTemplateVo queryByIdAndType(Integer type) {
        LambdaQueryWrapper<SohuIndependentTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentTemplate::getTemplateType, type).eq(SohuIndependentTemplate::getIsValid, true).last(" limit 1");
        return this.baseMapper.selectVoOne(wrapper);
    }

    @Override
    public Boolean exitCommonData(Integer type) {
        LambdaQueryWrapper<SohuIndependentTemplate> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentTemplate::getTemplateType, type).eq(SohuIndependentTemplate::getTemplateCommon, true);

        return this.baseMapper.exists(lqw);
    }

    @Override
    public List<SohuIndependentTemplateVo> listByIdAndType(List<Long> addSiteIds, int i) {
        LambdaQueryWrapper<SohuIndependentTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SohuIndependentTemplate::getSiteId, addSiteIds).eq(SohuIndependentTemplate::getTemplateType, i).eq(SohuIndependentTemplate::getIsValid, true);
        return this.baseMapper.selectVoList(wrapper);
    }

    @Override
    public SohuIndependentTemplateVo getPlayletInfo() {
        LambdaQueryWrapper<SohuIndependentTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentTemplate::getTemplateType, 3).eq(SohuIndependentTemplate::getIsValid, true);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public Boolean addPlaylet(SohuIndependentPlayetTemplateBo bo) {
        SohuIndependentTemplate sohuIndependentTemplate = BeanUtil.toBean(bo, SohuIndependentTemplate.class);
        sohuIndependentTemplate.setTemplateType(3);
        sohuIndependentTemplate.setIsValid(true);
        return baseMapper.insert(sohuIndependentTemplate) > 0;
    }

    @Override
    public SohuIndependentTemplateVo queryCommonTemp(Integer type) {
        LambdaQueryWrapper<SohuIndependentTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentTemplate::getTemplateType, type)
                .eq(SohuIndependentTemplate::getTemplateCommon, true)
                .eq(SohuIndependentTemplate::getIsValid, true).last(" limit 1");
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public Boolean queryByIndustryId(Long industryId) {
        LambdaQueryWrapper<SohuIndependentTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentTemplate::getSiteId, industryId)
                .eq(SohuIndependentTemplate::getIsValid, true);
        return this.baseMapper.selectCount(wrapper) > 0;
    }

    @Override
    public SohuIndependentTemplateModel queryTemplateInfo(Long siteId, Integer type) {
        // 查询通用模板
        if (siteId == null) {
            SohuIndependentTemplateVo sohuIndependentTemplateVo = queryCommonTemp(type);
            Objects.requireNonNull(sohuIndependentTemplateVo, "分账模板为空,请联系管理员");
            return BeanCopyUtils.copy(sohuIndependentTemplateVo, SohuIndependentTemplateModel.class);
        }
        SohuIndependentTemplateVo independentTemplateVo = queryByIdAndType(siteId, type);
        //如果站点模版不存在，取通用模版
        if (Objects.isNull(independentTemplateVo)) {
            independentTemplateVo = queryCommonTemp(type);
        }
        Objects.requireNonNull(independentTemplateVo, "当前站点的分账模版为空请联系管理员");
        return BeanCopyUtils.copy(independentTemplateVo, SohuIndependentTemplateModel.class);
    }

}
