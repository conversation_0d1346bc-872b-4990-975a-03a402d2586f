//package com.sohu.pay.service.impl;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
//import com.google.common.collect.Lists;
//import com.sohu.admin.api.RemoteMcnOrderService;
//import com.sohu.admin.api.RemoteMerchantService;
//import com.sohu.admin.api.model.SohuMcnMasterOrderModel;
//import com.sohu.admin.api.model.SohuMcnOrderModel;
//import com.sohu.admin.api.model.SohuMerchantModel;
//import com.sohu.busyorder.api.RemoteBusyOrderService;
//import com.sohu.busyorder.api.model.SohuBusyOrderModel;
//import com.sohu.busyorder.api.model.SohuBusyOrderPayModel;
//import com.sohu.busyorder.api.model.SohuBusyTaskReceiveModel;
//import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
//import com.sohu.common.core.constant.CacheConstants;
//import com.sohu.common.core.constant.Constants;
//import com.sohu.common.core.constant.OrderConstants;
//import com.sohu.common.core.enums.*;
//import com.sohu.common.core.exception.ServiceException;
//import com.sohu.common.core.utils.*;
//import com.sohu.common.redis.utils.RedisUtils;
//import com.sohu.common.satoken.config.LoginUser;
//import com.sohu.common.satoken.utils.LoginHelper;
//import com.sohu.dao.domain.bo.SohuTradeRecordBo;
//import com.sohu.dao.domain.vo.*;
//import com.sohu.dao.service.*;
//import com.sohu.pay.api.RemoteTemplateService;
//import com.sohu.pay.api.domain.SohuPayQueryBo;
//import com.sohu.pay.api.domain.SohuPrePayBo;
//import com.sohu.pay.api.model.SohuIndependentTemplateModel;
//import com.sohu.pay.api.model.SohuOrderRefundModel;
//import com.sohu.pay.api.model.SohuPayResultModel;
//import com.sohu.pay.domain.SohuIndependentLevelConfig;
//import com.sohu.pay.domain.SohuIndependentOrder;
//import com.sohu.pay.domain.bo.SohuIndependentIdPayBo;
//import com.sohu.pay.domain.bo.SohuIndependentOrderBo;
//import com.sohu.pay.domain.vo.SohuAccountBankVo;
//import com.sohu.pay.service.AbstractPayService;
//import com.sohu.pay.service.ISohuAccountBankService;
//import com.sohu.pay.service.ISohuIndependentLevelConfigService;
//import com.sohu.pay.service.ISohuIndependentOrderService;
//import com.sohu.pay.service.strategy.PaymentProcessor;
//import com.sohu.pay.service.strategy.PaymentStrategy;
//import com.sohu.shopgoods.api.RemoteProductService;
//import com.sohu.shopgoods.api.model.SohuProductModel;
//import com.sohu.shoporder.api.*;
//import com.sohu.shoporder.api.model.SohuShopMasterOrderModel;
//import com.sohu.shoporder.api.model.SohuShopOrderInfoModel;
//import com.sohu.shoporder.api.model.SohuShopOrderModel;
//import com.sohu.shoporder.api.model.SohuShopRefundOrderModel;
//import com.sohu.streamrocketmq.api.RemoteStreamMqService;
//import com.sohu.streamrocketmq.api.model.MqMessaging;
//import com.sohu.system.api.RemoteDictService;
//import com.sohu.system.api.RemoteSocialUserService;
//import com.sohu.system.api.RemoteUserService;
//import com.sohu.system.api.domain.SocialUserDomain;
//import com.sohu.system.api.domain.SysDictData;
//import com.sohu.third.wechat.pay.bean.WechatPayConfig;
//import com.sohu.third.wechat.pay.constant.WechatPayConstants;
//import com.sohu.third.wechat.pay.exception.WechatPayException;
//import com.sohu.third.wechat.pay.request.WechatPayOrderQueryRequest;
//import com.sohu.third.wechat.pay.request.WechatPayRefundQueryRequest;
//import com.sohu.third.wechat.pay.response.WechatPayQueryResponse;
//import com.sohu.third.wechat.pay.response.WechatPayRefundQueryResponse;
//import com.sohu.third.wechat.pay.service.WechatPayService;
//import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
//import com.wangcaio2o.ipossa.sdk.model.GoodsDetail;
//import com.wangcaio2o.ipossa.sdk.model.SplitInfo;
//import com.wangcaio2o.ipossa.sdk.model.SplitList;
//import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
//import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
//import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
//import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
//import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
//import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
//import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
//import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
//import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
//import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
//import com.wangcaio2o.ipossa.sdk.test.Client;
//import io.seata.common.util.CollectionUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.beans.BeanUtils;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.transaction.support.TransactionTemplate;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.time.Duration;
//import java.util.*;
//import java.util.stream.Collectors;
//import java.util.stream.Stream;
//
///**
// * 微信小程序支付
// *
// * @author: zc
// * @date: 2023/9/11 20:14
// * @version: 1.0.0
// */
//@Service
//@Slf4j
//public class YiMaJsapiServiceImplBak extends AbstractPayService {
//
//    @DubboReference
//    private RemoteMasterOrderService remoteMasterOrderService;
//    @DubboReference
//    private RemoteShopOrderService remoteShopOrderService;
//    @DubboReference
//    private RemoteShopOrderInfoService remoteShopOrderInfoService;
//    @DubboReference
//    private RemoteShopOrderStatusService remoteShopOrderStatusService;
//    @DubboReference
//    private RemoteProductService remoteProductService;
//    @DubboReference
//    private RemoteMerchantService remoteMerchantService;
//    @DubboReference
//    private RemoteSocialUserService socialUserService;
//    @DubboReference
//    private RemoteShopRefundOrderService remoteRefundOrderService;
//    @DubboReference
//    private RemoteShopRefundOrderStatusService remoteRefundOrderStatusService;
//    @DubboReference
//    private RemoteStreamMqService remoteStreamMqService;
//    @Resource
//    private TransactionTemplate transactionTemplate;
//    @DubboReference
//    private RemoteDictService remoteDictService;
//    @DubboReference
//    private RemoteBusyOrderService remoteBusyOrderService;
//    @DubboReference
//    private RemoteUserService remoteUserService;
//    @DubboReference
//    private RemoteTemplateService remoteTemplateService;
//    @DubboReference
//    private RemoteMcnOrderService remoteMcnOrderService;
//
//    @Resource
//    private ISohuInviteService inviteService;
//
//    @Resource
//    private ISohuSiteService siteService;
//
//    @Resource
//    private ISohuIndependentOrderService independentOrderService;
//    @Resource
//    private ISohuAccountBankService accountBankService;
//    @Resource
//    private ISohuIndependentLevelConfigService levelConfigService;
//    @Resource
//    private ISohuVideoService sohuVideoService;
//    @Resource
//    private ISohuPlayletService sohuPlayletService;
//    @Resource
//    private ISohuPlayletPayService sohuPlayletPayService;
//    @Resource
//    private ISohuRechargeListService sohuRechargeListService;
//    @Resource
//    private ISohuTradeRecordService sohuTradeRecordService;
//    @Resource
//    private ISohuUserVirtualService sohuUserVirtualService;
//
//    /**
//     * 测试openId
//     */
//    public static final String TEST_OPENID = "o0Dsn5MRN7GRKcnh4gncL8yc8A_0";
//
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public String payment(SohuPrePayBo payModel) {
//        // 进入微信小程序支付
//        try {
//            // 用户信息
//            LoginUser user = LoginHelper.getLoginUser();
//            Objects.requireNonNull(user, "未登录");
//            // 查询微信openId
//            SocialUserDomain userDomain = socialUserService.getByUserId(user.getUserId(), SocialSourceEnum.wechat_miniapp.getSource());
//            String openId = Objects.nonNull(userDomain) ? userDomain.getOpenId() : TEST_OPENID;
//            // 备注
//            StringBuilder body = new StringBuilder();
//            // 查询翼码支付配置
//            YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
//            log.info("yima payment request:{}", JSONUtil.toJsonStr(payModel));
//            return StrUtil.equalsAnyIgnoreCase(payModel.getPayChannel(), Constants.CHANNEL_PC) ? getPcCodePay(payModel, user, body, yiMaPayConfig) : getMobilePay(payModel, user, openId, body, yiMaPayConfig);
//        } catch (Exception e) {
//            throw new RuntimeException(e.getMessage());
//        }
//    }
//
//    /**
//     * 线上支付
//     *
//     * @param payModel
//     * @param user
//     * @param openId
//     * @param body
//     * @param yiMaPayConfig
//     */
//    private String getMobilePay(SohuPrePayBo payModel, LoginUser user, String openId, StringBuilder body, YiMaPayConfig yiMaPayConfig) {
//        // 翼码支付请求参数组装-公共信息
//        UnifiedorderRequest request = new UnifiedorderRequest();
//        request.setPosId(yiMaPayConfig.getPosId());
//        request.setIsspid(yiMaPayConfig.getIssPid());
//        request.setSystemId(yiMaPayConfig.getSystemId());
//        request.setStoreId(yiMaPayConfig.getStoreId());
//        // 支付参数组装
//        Unifiedorder unifiedorder = new Unifiedorder();
//        unifiedorder.setPayType(yiMaPayConfig.getPayType());
//        unifiedorder.setTradeType(yiMaPayConfig.getTradeType());
//        unifiedorder.setNotifyUrl(yiMaPayConfig.getNotifyUrl());
//        unifiedorder.setBuyerId(openId);
//        // 商品信息
//        List<GoodsDetail> goodsDetailList;
//
//        // 返回对象
//        UnifiedorderResponse response = null;
//        /*
//         * paySource = 1 商城商品
//         * paySource = 2 商单-支付预付款
//         * paySource = 3 商户付款码扫码支付
//         * paySource = 4 发单方-支付接单酬金
//         */
//        // 主订单号
//        String masterOrderNo = payModel.getMasterOrderNo();
//        // 是否分账配置
//        ExtendParams extendParams = new ExtendParams();
//        // 通过缓存获取预下单对象
//        String key = CacheConstants.ORDER_PAY_TWO + masterOrderNo;
//        boolean exists = RedisUtils.isExistsObject(key);
//        // 如果存在直接唤醒
//        if (exists && payModel.getPaySource() == 1) {
//            return RedisUtils.getCacheObject(key);
//        }
//        PaySourceEnum sourceEnum = PaySourceEnum.MAP.get(payModel.getPaySource());
//        Objects.requireNonNull(sourceEnum, MessageUtils.message("WRONG_PARAMS"));
//        switch (sourceEnum) {
//            case SHOP_ORDER_PAY:
//                if (true) {
//                    // 创建 PaymentProcessor 实例
//                    PaymentProcessor paymentProcessor = new PaymentProcessor();
//
//                    // 定义支付方式和支付来源
//                    PayTypeEnum payType = PayTypeEnum.PAY_TYPE_YI_MA;
//                    PaySourceEnum paySource = PaySourceEnum.SHOP_ORDER_PAY;
//
//                    // 获取支付策略
//                    PaymentStrategy paymentStrategy = paymentProcessor.getPaymentStrategy(payType, paySource);
//                    paymentStrategy.payment(null, null, null);
//                }
//                SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(masterOrderNo);
//                // 状态判断
//                if (ObjectUtil.isNull(masterOrder)) {
//                    throw new ServiceException("order does not exist");
//                }
//                if (masterOrder.getIsCancel()) {
//                    throw new ServiceException("order is cancelled");
//                }
//                if (masterOrder.getPaid()) {
//                    throw new ServiceException("order not paid");
//                }
//                // 剩余组装信息
//                Long merId = remoteShopOrderService.getListByMasterNo(payModel.getMasterOrderNo()).get(0).getMerId();
//                SohuMerchantModel merchantModel = remoteMerchantService.selectById(merId);
//                body.append(merchantModel.getName());
//                if (StringUtils.isBlank(body)) {
//                    body.append("用户下单");
//                }
//                // 备注
//                request.setMemo(body.toString());
//                // 唯一订单号
//                request.setPosSeq(masterOrder.getOrderNo());
//                // 总金额
//                unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getPayPrice()));
//                // 组装商品信息
//                goodsDetailList = exchangeGoodsDetailList(masterOrder.getOrderNo());
//                unifiedorder.setGoodsDetail(goodsDetailList);
//                // 延时分账
//                extendParams.setSplitFlag("D");
//                extendParams.setPlanSplitDate(DateUtils.getDate().replaceAll("-", ""));
//                unifiedorder.setExtendParams(extendParams);
//                // 设置请求参数
//                request.setUnifiedorderRequest(unifiedorder);
//                log.info("微信支付请求返回：{}" + JSONUtil.toJsonStr(request));
//                // 请求第三方-返回微信支付下单的响应数据
//                response = Client.getClient().execute(request);
//                break;
//            case BUSY_ORDER_PREPAY:
//                // 查询商单
//                SohuBusyOrderModel sohuBusyOrder = remoteBusyOrderService.queryBusyOrder(Long.valueOf(masterOrderNo));
//                if (Objects.isNull(sohuBusyOrder)) {
//                    throw new ServiceException(MessageUtils.message("BUSY_ORDER_NOT_FOUNT"));
//                }
//                if (!Objects.equals(sohuBusyOrder.getUserId(), user.getUserId())) {
//                    throw new ServiceException(MessageUtils.message("BUSY_ORDER_OPERATE_NO_AUTH"));
//                }
//                if (!Objects.equals(sohuBusyOrder.getPrepayState(), PayStatus.WaitPay.name()) && !Objects.equals(sohuBusyOrder.getPrepayState(), BusyOrderStatus.Prepay.name())) {
//                    throw new ServiceException(MessageUtils.message("BUSY_ORDER_WRONG_STATUS"));
//                }
//                String outTradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_ORDER_PREFIX);
//                request.setMemo("商单预付款支付");
//                request.setPosSeq(outTradeNo);
//                // 转换价格y-f
//                int busyOrderTotalPrice = CalUtils.yuanToCent(sohuBusyOrder.getPrepayAmount()).intValue();
//                // 商单价格对象
//                unifiedorder.setTxAmt(busyOrderTotalPrice);
//                // 不分账 -- R实时分账 --D延时分账
//                extendParams.setSplitFlag("N");
//                unifiedorder.setExtendParams(extendParams);
//                // 设置请求参数
//                request.setUnifiedorderRequest(unifiedorder);
//                log.info("yi-ma-pay busyorder request：{}" + JSONUtil.toJsonStr(request));
//                // 请求第三方-返回微信支付下单的响应数据
//                response = Client.getClient().execute(request);
//                // 保存待付款记录
//                remoteBusyOrderService.saveBusyOrderPay("sohuBusyOrder.getId()", outTradeNo, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), sohuBusyOrder.getPrepayAmount(), PayObject.BusyOrder.name(), 0L, user.getUserId(), 0L);
//                break;
//            case MERCHANT_CODE_PAY:
//
//                break;
//            case TASK_PARTY_PAY:
//                BigDecimal amount = payModel.getAmount();
//                if (amount == null || CalUtils.isLessEqualZero(amount)) {
//                    throw new ServiceException(MessageUtils.message("付款金额不能小于等于0"));
//                }
//                // 根据子订单号+已接单查询唯一订单
//                SohuBusyTaskReceiveModel busyOrderReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(payModel.getMasterOrderNo(), Boolean.TRUE);
//                if (Objects.isNull(busyOrderReceiveModel)) {
//                    throw new ServiceException(MessageUtils.message("order.rece.not.exist"));
//                }
//                // 查询商单子单
//                SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(payModel.getMasterOrderNo());
//                if (Objects.isNull(taskSiteModel)) {
//                    throw new ServiceException(MessageUtils.message("BUSY_ORDER_NOT_FOUNT"));
//                }
//                // 用户信息
//                Map<Long, LoginUser> userMap = remoteUserService.selectMap(Collections.singletonList(busyOrderReceiveModel.getUserId()));
//                LoginUser recipient = userMap.get(busyOrderReceiveModel.getUserId());
//                request.setMemo("支付任务商单酬金-" + recipient.getNickname());
//                // 分账信息
//                SplitInfo splitInfo = new SplitInfo();
//                // 先查询分拥等级
//                SohuIndependentLevelConfig levelConfig = levelConfigService.queryByStatusAndSiteId(0, 11L);
//                Integer level = ObjectUtils.isNull(levelConfig) ? levelConfig.getLevel() : 2;
//                // 查询接单人是否有拉新人-上上级拉新人
//                Long regUserId = inviteService.selectByInviteCount(busyOrderReceiveModel.getUserId(), level);
//                Boolean regUser = Boolean.FALSE;
//                if (null != regUserId) {
//                    regUser = Boolean.TRUE;
//                }
//                Boolean finalRegUser = regUser;
//                // 组装id
//                SohuIndependentIdPayBo independentIdBo = new SohuIndependentIdPayBo();
//                // 根据城市站点id获取城市站长id
//                Long cityId = siteService.queryById(taskSiteModel.getSiteId()).getStationmasterId();
//                // 根据城市站点id获取国家站点用户id
//                Long countryId = siteService.selectSiteByPid(taskSiteModel.getSiteId()).getStationmasterId();
//                // todo 查询是否有代理人
//                Boolean agencyId = Boolean.FALSE;
//                // 分销人的拉新人
//                Long distributionInviteUserId = null;
//                log.warn("busyOrderReceModel : {}", JsonUtils.toJsonString(busyOrderReceiveModel));
//                if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() != 0L) {
//                    log.warn("distributionUserId : {}", busyOrderReceiveModel.getSharePerson());
////                    sohuBusyOrderQuery.setIsRecommend(Boolean.TRUE);
//                    distributionInviteUserId = inviteService.selectByInviteCount(busyOrderReceiveModel.getSharePerson(), level);
//                }
//                log.warn("distributionInviteUserId : {}", distributionInviteUserId);
//                // 查询分账模板
//                // 根据城市站点、商品分账模板类型 查询分账模板
//                SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(taskSiteModel.getSiteId(), 2);
//                if (Objects.isNull(templateModel)) {
//                    throw new ServiceException("商单分账模板为空,请联系系统管理员");
//                }
//                // 分账金额
//                BigDecimal independentPrice = taskSiteModel.getDistributionAmount();
//                if (payModel.getAllAmount() || payModel.getIsDelivery()) {
//                    independentPrice = BigDecimal.ZERO;
//                }
////                // 一口价
////                if (taskSiteModel.getKickbackType().equals(KickbackType.price.getCode())) {
////                    independentPrice = taskSiteModel.getKickbackValue();
////                } else if (taskSiteModel.getKickbackType().equals(KickbackType.percentage.getCode())) {
////                    // 任务分销总金额
////                    independentPrice = taskSiteModel.getDistributionAmount();
////                    log.warn("任务按照百分比分销金额：{}", independentPrice);
////                }
//                // 分账方组装
//                Map<String, BigDecimal> calculateMap;
//                // 分账请求流水号
//                String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
//                // 分销单组装
//                List<SohuIndependentOrderBo> independentOrderBoLists = new ArrayList<>();
//                // 如果是佣金结算
//                if (null != payModel.getIsIndependentAmount() && payModel.getIsIndependentAmount()) {
//                    request.setMemo("支付商单接佣金-" + user.getNickname());
//                    String tradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_RECE_ORDER_PREFIX);
//                    request.setPosSeq(tradeNo);
//                    // 总金额
//                    unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(independentPrice));
//                    // 商单接单分账算价
//                    calculateMap = this.calculateBusyOrderDistribution(templateModel, taskSiteModel.getFullAmount(), independentPrice, busyOrderReceiveModel.getSharePerson() != null, finalRegUser, distributionInviteUserId, agencyId, Boolean.TRUE);
//                    for (String independentObject : calculateMap.keySet()) {
//                        // 分账金额
//                        BigDecimal itemPrice = calculateMap.get(independentObject);
//                        SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
//                        independentOrderBo.setOrderNo(tradeNo);
//                        independentOrderBo.setTradeType(BusyType.BusyOrder.name());
//                        independentOrderBo.setIndependentStatus(2);
//                        independentOrderBo.setTradeNo(delayOrderNo);
//                        // todo
//                        if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(2L);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.country.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(countryId);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.city.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(cityId);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.invite.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(regUserId);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distributionInvite.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(distributionInviteUserId);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
//                            // todo 代理人id
//                            independentOrderBo.setUserId(0L);
//                        }
//                        if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
//                            log.info("{} 用户ID为空", independentObject);
//                            continue;
//                        }
//                        if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
//                            log.info("分账用户-{} 所得金额为空", independentObject);
//                            continue;
//                        }
//                        independentOrderBo.setIndependentObject(independentObject);
//                        independentOrderBo.setMerId(0L);
//                        independentOrderBo.setSiteId(taskSiteModel.getSiteId());
//                        independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
//                        independentOrderBoLists.add(independentOrderBo);
//                    }
//                    // 平台手续费百分比
////                    BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), PERCENTAGE);
////                    log.warn("平台手续费百分比：{}", platformDivide);
//                    independentIdBo.setAdminId(2L);
//                    independentIdBo.setCountryId(countryId);
//                    independentIdBo.setCityId(cityId);
//                    // todo 代理id
////                    if (null != agencyId)
//                    // 分销人id
//                    if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() > 0L) {
//                        independentIdBo.setDistributorId(busyOrderReceiveModel.getSharePerson());
//                    }
//                    // 拉新人的上上级id
//                    if (regUserId != null && regUserId > 0L) {
//                        independentIdBo.setInviteId(regUserId);
//                    }
//                    // 接单人id
//                    independentIdBo.setReceiveId(busyOrderReceiveModel.getUserId());
//                    // 分销人的拉新人id
//                    if (null != distributionInviteUserId && distributionInviteUserId != 0L) {
//                        independentIdBo.setDistributorInviteId(distributionInviteUserId);
//                    }
//                    // 查询所有分账人的翼码帐户信息
//                    List<Long> allUserIds = getAllIds(independentIdBo);
//                    log.warn("allUserIds : {}", allUserIds);
//                    // 查询所有参与分账人员的翼码账户信息
//                    Map<Long, SohuAccountBankVo> accountBankVoMap = accountBankService.queryMapByUserIds(allUserIds);
//                    log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
//                    Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
//                    // 分账账号信息参数
//                    List<SplitList> splitLists = new ArrayList<>();
//                    // 累计的没有分出去的金额
//                    BigDecimal newPlatePrice = BigDecimal.ZERO;
//                    for (Long userId : independentOrderMap.keySet()) {
//                        SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
//                        // todo
//                        if (userId != 2L) {
//                            if (Objects.isNull(sohuAccountBankVo)) {
//                                log.warn("翼码账户为空,{}", userId);
//                                List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
//                                newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
//                                continue;
//                            }
//                            List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
//                            if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
//                                continue;
//                            }
//                            SplitList item = new SplitList();
//                            BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                            if (CalUtils.isLessEqualZero(divAmt)) {
//                                continue;
//                            }
//                            if (sohuIndependentOrderBos.size() > 1) {
//                                log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
//                            }
//                            item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
//                            item.setMerchantId(sohuAccountBankVo.getMerchantId());
//                            splitLists.add(item);
//                        }
//                    }
//                    // 去重从新计算之后的传给第三方的分账信息
//                    List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId, Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
//                        SplitList newSplitList = new SplitList();
//                        newSplitList.setMerchantId(entry.getKey());
//                        newSplitList.setDivAmt(entry.getValue().toString());
//                        return newSplitList;
//                    }).collect(Collectors.toList());
//                    // 设置分账参数
//                    splitInfo.setSplitList(splitListsNew);
//                    // 平台总金额
//                    BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);
//                    // 平台总留存金额
//                    splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
//                    // 分账信息
//                    extendParams.setSplitInfo(splitInfo);
//                    // 不分账 -- R实时分账 --D延时分账
//                    extendParams.setSplitFlag("R");
//                    // 添加分账扩展参数
//                    unifiedorder.setExtendParams(extendParams);
//                    // 设置请求参数
//                    request.setUnifiedorderRequest(unifiedorder);
//                    log.info("yi-ma-pay busy-order-independent request：{}", JSONUtil.toJsonStr(request));
//                    // 请求第三方-返回微信支付下单的响应数据
//                    response = Client.getClient().execute(request);
//                    // todo 是否一次性支付商单酬金  一次性支付的话就直接完成任务 改掉所有阶段性状态为已完成
//                    if (payModel.getIsIndependentAmount()) {
//                        // 如果是佣金结算 将支付单号+isIndependentAmount 12423432432isIndependentAmount 作为key存入
//                        RedisUtils.setCacheObject(tradeNo + "isIndependentAmount", Boolean.TRUE, Duration.ofMinutes(5));
//                    }
//                    // 保存待付款记录
//                    remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), independentPrice, PayObject.BusyOrderReceive.name(), user.getUserId(), busyOrderReceiveModel.getUserId(), null);
//                    // 保存分销单记录
//                    independentOrderBoLists.stream().filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject())).findFirst().ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
//                    independentOrderService.insertByBoList(independentOrderBoLists);
//                    break;
//                }
//                // 是否分销
//                Boolean isRecommend = Boolean.TRUE;
//                if (taskSiteModel.getKickbackType().equals(KickbackType.none.getCode())) isRecommend = Boolean.FALSE;
//                String tradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_RECE_ORDER_PREFIX);
//                request.setPosSeq(tradeNo);
//                // 商单总金额
//                BigDecimal busyPrice = BigDecimal.ZERO;
//                // 根据条件计算busyPrice和independentPrice 如果没有分销人-商单总金额为商单价值金额
//                if (busyOrderReceiveModel.getSharePerson() == null || busyOrderReceiveModel.getSharePerson().equals(0L)) {
//                    // 没有分销人时的逻辑
//                    if (payModel.getIsDelivery() || payModel.getAllAmount()) {
//                        busyPrice = amount;
//                        independentPrice = BigDecimal.ZERO; // 确保independentPrice也被设置为0
//                    } else {
//                        busyPrice = taskSiteModel.getFullAmount();
//                    }
//                } else {
//                    // 有分销人时的逻辑  是否是提前结算、阶段性支付
//                    if (!payModel.getAllAmount() && !payModel.getIsDelivery()) {
//                        busyPrice = CalUtils.add(taskSiteModel.getFullAmount(), independentPrice);
//                        // log.warn("busyPrice-child: {}", JsonUtils.toJsonString(busyPrice));
//                    } else {
//                        busyPrice = CalUtils.add(amount, independentPrice);
//                        // log.warn("busyPrice-all: {}", JsonUtils.toJsonString(busyPrice));
//                    }
//                }
//                // 总金额
//                unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(busyPrice));
//                // 阶段性商单接单分账算价
//                calculateMap = this.calculateBusyOrderDistribution(templateModel, amount, independentPrice, isRecommend, finalRegUser, distributionInviteUserId, agencyId, Boolean.FALSE);
//                for (String independentObject : calculateMap.keySet()) {
//                    // 分账金额
//                    BigDecimal itemPrice = calculateMap.get(independentObject);
//                    SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
//                    independentOrderBo.setOrderNo(tradeNo);
//                    independentOrderBo.setTradeType(BusyType.BusyOrder.name());
//                    independentOrderBo.setIndependentStatus(2);
//                    independentOrderBo.setTradeNo(delayOrderNo);
//                    if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
//                        // todo
//                        independentOrderBo.setUserId(2L);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.country.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(countryId);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.city.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(cityId);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.rece.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.invite.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(regUserId);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distributionInvite.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(distributionInviteUserId);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
//                        // todo 代理人id
//                        independentOrderBo.setUserId(0L);
//                    }
//                    if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
//                        log.info("{} 用户ID为空", independentObject);
//                        continue;
//                    }
//                    if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
//                        log.info("分账用户-{} 所得金额为空", independentObject);
//                        continue;
//                    }
//                    independentOrderBo.setIndependentObject(independentObject);
//                    independentOrderBo.setMerId(0L);
//                    independentOrderBo.setSiteId(taskSiteModel.getSiteId());
//                    independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
//                    independentOrderBoLists.add(independentOrderBo);
//                }
//                // 平台手续费百分比
////                    BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), PERCENTAGE);
////                    log.warn("平台手续费百分比：{}", platformDivide);
//                // todo
//                independentIdBo.setAdminId(2L);
//                independentIdBo.setCountryId(countryId);
//                independentIdBo.setCityId(cityId);
//                // todo 代理id
////                    if (null != agencyId)
//                // 分销人id
//                if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() > 0L) {
//                    independentIdBo.setDistributorId(busyOrderReceiveModel.getSharePerson());
//                }
//                // 拉新人的上上级id
//                if (regUserId != null && regUserId > 0L) {
//                    independentIdBo.setInviteId(regUserId);
//                }
//                // 接单人id
//                independentIdBo.setReceiveId(busyOrderReceiveModel.getUserId());
//                // 分销人的拉新人id
//                if (null != distributionInviteUserId && distributionInviteUserId != 0L) {
//                    independentIdBo.setDistributorInviteId(distributionInviteUserId);
//                }
//                // 查询所有分账人的翼码帐户信息
//                List<Long> allUserIds = getAllIds(independentIdBo);
//                log.warn("allUserIds : {}", allUserIds);
//                // 查询所有参与分账人员的翼码账户信息
//                Map<Long, SohuAccountBankVo> accountBankVoMap = accountBankService.queryMapByUserIds(allUserIds);
//                log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
//                Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
//                // 分账账号信息参数
//                List<SplitList> splitLists = new ArrayList<>();
//                // 累计的没有分出去的金额
//                BigDecimal newPlatePrice = BigDecimal.ZERO;
//                for (Long userId : independentOrderMap.keySet()) {
//                    SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
//                    // todo
//                    if (userId != 2L) {
//                        if (Objects.isNull(sohuAccountBankVo)) {
//                            log.warn("翼码账户为空,{}", userId);
//                            List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
//                            newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
//                            continue;
//                        }
//                        List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
//                        if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
//                            continue;
//                        }
//                        SplitList item = new SplitList();
//                        BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        if (CalUtils.isLessEqualZero(divAmt)) {
//                            continue;
//                        }
//                        if (sohuIndependentOrderBos.size() > 1) {
//                            log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
//                        }
//                        item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
//                        item.setMerchantId(sohuAccountBankVo.getMerchantId());
//                        splitLists.add(item);
//                    }
//                }
////                    BigDecimal totalDivAmt = splitLists.stream()
////                        .map(splitList -> new BigDecimal(splitList.getDivAmt()))
////                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                // 接单人金额 = 商单总价值 - 平台总手续费
//                BigDecimal receivePrice = CalUtils.sub(busyPrice, calculateMap.get(SohuIndependentObject.platform.getKey()), calculateMap.get(SohuIndependentObject.invite.getKey()), calculateMap.get(SohuIndependentObject.country.getKey()), calculateMap.get(SohuIndependentObject.city.getKey()), calculateMap.get(SohuIndependentObject.agency.getKey()), calculateMap.get(SohuIndependentObject.distribution.getKey()), calculateMap.get(SohuIndependentObject.distributionInvite.getKey()));
//                SohuIndependentOrderBo independentOrderBoReceive = new SohuIndependentOrderBo();
//                independentOrderBoReceive.setOrderNo(tradeNo);
//                independentOrderBoReceive.setTradeType(BusyType.BusyOrder.name());
//                independentOrderBoReceive.setIndependentStatus(2);
//                independentOrderBoReceive.setTradeNo(delayOrderNo);
//                independentOrderBoReceive.setUserId(busyOrderReceiveModel.getUserId());
//                independentOrderBoReceive.setIndependentObject(SohuIndependentObject.rece.getKey());
//                independentOrderBoReceive.setMerId(0L);
//                independentOrderBoReceive.setSiteId(taskSiteModel.getSiteId());
//                independentOrderBoReceive.setIndependentPrice(receivePrice);
//                independentOrderBoLists.add(independentOrderBoReceive);
//                // 接单人翼码账户信息
//                SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(busyOrderReceiveModel.getUserId());
//                // 接单人
//                SplitList itemReceive = new SplitList();
//                itemReceive.setDivAmt(BigDecimalUtils.yuanToFen(receivePrice).toString());
//                itemReceive.setMerchantId(sohuAccountBankVo.getMerchantId());
//                log.warn("itemReceive: {}", JsonUtils.toJsonString(itemReceive));
//                splitLists.add(itemReceive);
//                // 去重从新计算之后的传给第三方的分账信息
//                List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId, Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
//                    SplitList newSplitList = new SplitList();
//                    newSplitList.setMerchantId(entry.getKey());
//                    newSplitList.setDivAmt(entry.getValue().toString());
//                    return newSplitList;
//                }).collect(Collectors.toList());
//                // 设置分账参数
//                splitInfo.setSplitList(splitListsNew);
//                // 平台总金额
//                BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);
//                // 平台总留存金额
//                splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
//                // 分账信息
//                extendParams.setSplitInfo(splitInfo);
//                // 不分账 -- R实时分账 --D延时分账
//                extendParams.setSplitFlag("R");
//                // 添加分账扩展参数
//                unifiedorder.setExtendParams(extendParams);
//                // 设置请求参数
//                request.setUnifiedorderRequest(unifiedorder);
//                log.info("wxpay busyorderrece request：{}", JSONUtil.toJsonStr(request));
//                // 请求第三方-返回微信支付下单的响应数据
//                response = Client.getClient().execute(request);
//                // todo 是否一次性支付商单酬金  一次性支付的话就直接完成任务 改掉所有阶段性状态为已完成
//                if (payModel.getAllAmount()) {
////                    SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(masterOrderNo, Boolean.TRUE);
//                    // 如果是一次性支付商单酬金 将支付单号+allAmount 12423432432allAmount 作为key存入
//                    RedisUtils.setCacheObject(tradeNo + "allAmount", Boolean.TRUE, Duration.ofMinutes(5));
//                }
////                // 保存待付款记录
////                remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo,
////                        PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(),
////                        busyPrice, PayObject.BusyOrderReceive.name(), Long.valueOf(masterOrderNo),
////                        user.getUserId(), payModel.getMasterId());
//                // 保存待付款记录
//                remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), busyPrice, PayObject.BusyTaskPromise.name(), user.getUserId(), busyOrderReceiveModel.getUserId(), payModel.getMasterId());
//                // 保存分销单记录
//                independentOrderBoLists.stream().filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject())).findFirst().ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
//                independentOrderService.insertByBoList(independentOrderBoLists);
//                break;
//            //售后单支付报酬
//            case TASK_AFTER_SALES_PAY:
//                this.getBusyAfterSalesPayMobile(payModel, request, unifiedorder, extendParams, response, user);
//                break;
//            // 商单支付保证金
//            case BUSY_TASK_PROMISE_PAY:
//                response = this.getBusyTaskPromisePayMobile(payModel.getMasterId(), request, unifiedorder, extendParams, user.getUserId());
//                break;
//            // 单个视频付费
//            case VIDEO_PAY:
//                response = this.getVideoPayMobile(payModel.getMasterId(), request, unifiedorder, extendParams, user.getUserId());
//                break;
//            // 整部剧付费
//            case PLAYLET_PAY:
//                response = this.getPlayletPayMobile(payModel.getMasterId(), request, unifiedorder, extendParams, user.getUserId());
//                break;
//            // 虚拟币支付
//            case SOHU_VIRTUAL_CURRENCY_RECHARGE:
//                response = this.getCoinPayMobile(payModel.getMasterId(), request, unifiedorder, extendParams, user.getUserId());
//                break;
//            default:
//        }
//        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(5));
//        // 转换第三方请求响应参数
//        String payment = JSONUtil.toJsonStr(response);
//        log.warn("翼码支付请求返回：{}", payment);
//        return payment;
//    }
//
//    /**
//     * 售后支付报酬
//     *
//     * @param payModel
//     * @param request
//     * @param unifiedorder
//     * @param extendParams
//     * @param response
//     * @param user
//     */
//    private void getBusyAfterSalesPayMobile(SohuPrePayBo payModel, UnifiedorderRequest request, Unifiedorder unifiedorder, ExtendParams extendParams, UnifiedorderResponse response, LoginUser user) {
//        BigDecimal amount = payModel.getAmount();
//        if (amount == null || CalUtils.isLessEqualZero(amount)) {
//            throw new ServiceException(MessageUtils.message("付款金额不能小于等于0"));
//        }
//        // 根据子订单号+已接单查询唯一订单
//        SohuBusyTaskReceiveModel busyOrderReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(payModel.getMasterOrderNo(), Boolean.TRUE);
//        if (Objects.isNull(busyOrderReceiveModel)) {
//            throw new ServiceException(MessageUtils.message("order.rece.not.exist"));
//        }
//        // 查询商单子单
//        SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(payModel.getMasterOrderNo());
//        if (Objects.isNull(taskSiteModel)) {
//            throw new ServiceException(MessageUtils.message("BUSY_ORDER_NOT_FOUNT"));
//        }
//        // 用户信息
//        Map<Long, LoginUser> userMap = remoteUserService.selectMap(Collections.singletonList(busyOrderReceiveModel.getUserId()));
//        LoginUser recipient = userMap.get(busyOrderReceiveModel.getUserId());
//        request.setMemo("支付任务商单酬金-" + recipient.getNickname());
//        // 分账信息
//        SplitInfo splitInfo = new SplitInfo();
////        // 先查询分拥等级
////        SohuIndependentLevelConfig levelConfig = levelConfigService.queryByStatusAndSiteId(0, 11L);
////        Integer level = ObjectUtils.isNull(levelConfig) ? levelConfig.getLevel() : 2;
//        // 查询接单人是否有拉新人-上上级拉新人
////        Long regUserId = inviteService.selectByInviteCount(busyOrderReceiveModel.getUserId(), level);
////        Boolean regUser = Boolean.FALSE;
////        if (null != regUserId) {
////            regUser = Boolean.TRUE;
////        }
////        Boolean finalRegUser = regUser;
//        // 组装id
//        SohuIndependentIdPayBo independentIdBo = new SohuIndependentIdPayBo();
////        // 根据城市站点id获取城市站长id
////        Long cityId = siteService.queryById(taskSiteModel.getSiteId()).getStationmasterId();
////        // 根据城市站点id获取国家站点用户id
////        Long countryId = siteService.selectSiteByPid(taskSiteModel.getSiteId()).getStationmasterId();
////        // todo 查询是否有代理人
////        Boolean agencyId = Boolean.FALSE;
////        // 分销人的拉新人
////        Long distributionInviteUserId = null;
//        log.warn("busyOrderReceModel : {}", JsonUtils.toJsonString(busyOrderReceiveModel));
////        if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() != 0L) {
////            log.warn("distributionUserId : {}", busyOrderReceiveModel.getSharePerson());
//////                    sohuBusyOrderQuery.setIsRecommend(Boolean.TRUE);
////            distributionInviteUserId = inviteService.selectByInviteCount(busyOrderReceiveModel.getSharePerson(), level);
////        }
////        log.warn("distributionInviteUserId : {}", distributionInviteUserId);
//        // 查询分账模板
//        // 根据城市站点、商品分账模板类型 查询分账模板
//        SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(taskSiteModel.getSiteId(), 2);
//        if (Objects.isNull(templateModel)) {
//            throw new ServiceException("商单分账模板为空,请联系系统管理员");
//        }
//        // 分账金额
////        BigDecimal independentPrice = taskSiteModel.getDistributionAmount();
////        if (payModel.getAllAmount() || payModel.getIsDelivery()) {
////            independentPrice = BigDecimal.ZERO;
////        }
////                // 一口价
////                if (taskSiteModel.getKickbackType().equals(KickbackType.price.getCode())) {
////                    independentPrice = taskSiteModel.getKickbackValue();
////                } else if (taskSiteModel.getKickbackType().equals(KickbackType.percentage.getCode())) {
////                    // 任务分销总金额
////                    independentPrice = taskSiteModel.getDistributionAmount();
////                    log.warn("任务按照百分比分销金额：{}", independentPrice);
////                }
//        // 分账方组装
//        Map<String, BigDecimal> calculateMap;
//        // 分账请求流水号
//        String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
//        // 分销单组装
//        List<SohuIndependentOrderBo> independentOrderBoLists = new ArrayList<>();
////        // 如果是佣金结算
////        if (null != payModel.getIsIndependentAmount() && payModel.getIsIndependentAmount()) {
////            request.setMemo("支付商单接佣金-" + user.getNickname());
////            String tradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_RECE_ORDER_PREFIX);
////            request.setPosSeq(tradeNo);
////            // 总金额
////            unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(independentPrice));
////            // 商单接单分账算价
////            calculateMap = this.calculateBusyOrderDistribution(templateModel, taskSiteModel.getFullAmount(),
////                    independentPrice, busyOrderReceiveModel.getSharePerson() != null, finalRegUser, distributionInviteUserId, agencyId, Boolean.TRUE);
////            for (String independentObject : calculateMap.keySet()) {
////                // 分账金额
////                BigDecimal itemPrice = calculateMap.get(independentObject);
////                SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
////                independentOrderBo.setOrderNo(tradeNo);
////                independentOrderBo.setTradeType(BusyType.BusyOrder.name());
////                independentOrderBo.setIndependentStatus(2);
////                independentOrderBo.setTradeNo(delayOrderNo);
////                if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
////                    independentOrderBo.setUserId(1L);
////                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.country.getKey(), independentObject)) {
////                    independentOrderBo.setUserId(countryId);
////                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.city.getKey(), independentObject)) {
////                    independentOrderBo.setUserId(cityId);
////                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.invite.getKey(), independentObject)) {
////                    independentOrderBo.setUserId(regUserId);
////                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
////                    independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
////                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distributionInvite.getKey(), independentObject)) {
////                    independentOrderBo.setUserId(distributionInviteUserId);
////                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
////                    // todo 代理人id
////                    independentOrderBo.setUserId(0L);
////                }
////                if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
////                    log.info("{} 用户ID为空", independentObject);
////                    continue;
////                }
////                if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
////                    log.info("分账用户-{} 所得金额为空", independentObject);
////                    continue;
////                }
////                independentOrderBo.setIndependentObject(independentObject);
////                independentOrderBo.setMerId(0L);
////                independentOrderBo.setSiteId(taskSiteModel.getSiteId());
////                independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
////                independentOrderBoLists.add(independentOrderBo);
////            }
////            // 平台手续费百分比
//////                    BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), PERCENTAGE);
//////                    log.warn("平台手续费百分比：{}", platformDivide);
////            independentIdBo.setAdminId(UserConstants.ADMIN_ID);
////            independentIdBo.setCountryId(countryId);
////            independentIdBo.setCityId(cityId);
////            // todo 代理id
//////                    if (null != agencyId)
////            // 分销人id
////            if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() > 0L) {
////                independentIdBo.setDistributorId(busyOrderReceiveModel.getSharePerson());
////            }
////            // 拉新人的上上级id
////            if (regUserId != null && regUserId > 0L) {
////                independentIdBo.setInviteId(regUserId);
////            }
////            // 接单人id
////            independentIdBo.setReceiveId(busyOrderReceiveModel.getUserId());
////            // 分销人的拉新人id
////            if (null != distributionInviteUserId && distributionInviteUserId != 0L) {
////                independentIdBo.setDistributorInviteId(distributionInviteUserId);
////            }
////            // 查询所有分账人的翼码帐户信息
////            List<Long> allUserIds = getAllIds(independentIdBo);
////            log.warn("allUserIds : {}", allUserIds);
////            // 查询所有参与分账人员的翼码账户信息
////            Map<Long, SohuAccountBankVo> accountBankVoMap = accountBankService.queryMapByUserIds(allUserIds);
////            log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
////            Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
////            // 分账账号信息参数
////            List<SplitList> splitLists = new ArrayList<>();
////            // 累计的没有分出去的金额
////            BigDecimal newPlatePrice = BigDecimal.ZERO;
////            for (Long userId : independentOrderMap.keySet()) {
////                SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
////                if (userId != 1L) {
////                    if (Objects.isNull(sohuAccountBankVo)) {
////                        log.warn("翼码账户为空,{}", userId);
////                        List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
////                        newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream()
////                                .map(SohuIndependentOrderBo::getIndependentPrice)
////                                .reduce(BigDecimal.ZERO, BigDecimal::add));
////                        continue;
////                    }
////                    List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
////                    if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
////                        continue;
////                    }
////                    SplitList item = new SplitList();
////                    BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
////                    if (CalUtils.isLessEqualZero(divAmt)) {
////                        continue;
////                    }
////                    if (sohuIndependentOrderBos.size() > 1) {
////                        log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
////                    }
////                    item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
////                    item.setMerchantId(sohuAccountBankVo.getMerchantId());
////                    splitLists.add(item);
////                }
////            }
////            // 去重从新计算之后的传给第三方的分账信息
////            List<SplitList> splitListsNew = splitLists.stream()
////                    .collect(Collectors.groupingBy(SplitList::getMerchantId, Collectors.reducing(BigDecimal.ZERO,
////                            s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
////                        SplitList newSplitList = new SplitList();
////                        newSplitList.setMerchantId(entry.getKey());
////                        newSplitList.setDivAmt(entry.getValue().toString());
////                        return newSplitList;
////                    })
////                    .collect(Collectors.toList());
////            // 设置分账参数
////            splitInfo.setSplitList(splitListsNew);
////            // 平台总金额
////            BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);
////            // 平台总留存金额
////            splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
////            // 分账信息
////            extendParams.setSplitInfo(splitInfo);
////            // 不分账 -- R实时分账 --D延时分账
////            extendParams.setSplitFlag("R");
////            // 添加分账扩展参数
////            unifiedorder.setExtendParams(extendParams);
////            // 设置请求参数
////            request.setUnifiedorderRequest(unifiedorder);
////            log.info("yi-ma-pay busy-order-independent request：{}", JSONUtil.toJsonStr(request));
////            // 请求第三方-返回微信支付下单的响应数据
////            response = Client.getClient().execute(request);
////            // todo 是否一次性支付商单酬金  一次性支付的话就直接完成任务 改掉所有阶段性状态为已完成
////            if (payModel.getIsIndependentAmount()) {
////                // 如果是佣金结算 将支付单号+isIndependentAmount 12423432432isIndependentAmount 作为key存入
////                RedisUtils.setCacheObject(tradeNo + "isIndependentAmount", Boolean.TRUE, Duration.ofMinutes(5));
////            }
////            // 保存待付款记录
////            remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo,
////                    PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(),
////                    independentPrice, PayObject.BusyOrderReceive.name(), user.getUserId(),
////                    busyOrderReceiveModel.getUserId(), null);
////            // 保存分销单记录
////            independentOrderBoLists.stream()
////                    .filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject()))
////                    .findFirst()
////                    .ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
////            independentOrderService.insertByBoList(independentOrderBoLists);
////            return ;
////        }
//        // 是否分销
//        Boolean isRecommend = Boolean.FALSE;
////        if (taskSiteModel.getKickbackType().equals(KickbackType.none.getCode())) isRecommend = Boolean.FALSE;
//        String tradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_RECE_AFTER_SALES_PREFIX);
//        request.setPosSeq(tradeNo);
//        // 商单总金额
//        BigDecimal busyPrice = amount;
////        // 根据条件计算busyPrice和independentPrice 如果没有分销人-商单总金额为商单价值金额
////        if (busyOrderReceiveModel.getSharePerson() == null || busyOrderReceiveModel.getSharePerson().equals(0L)) {
////            // 没有分销人时的逻辑
////            if (payModel.getIsDelivery() || payModel.getAllAmount()) {
////                busyPrice = amount;
////                independentPrice = BigDecimal.ZERO; // 确保independentPrice也被设置为0
////            } else {
////                busyPrice = taskSiteModel.getFullAmount();
////            }
////        } else {
////            // 有分销人时的逻辑  是否是提前结算、阶段性支付
////            if (!payModel.getAllAmount() && !payModel.getIsDelivery()) {
////                busyPrice = CalUtils.add(taskSiteModel.getFullAmount(), independentPrice);
////                // log.warn("busyPrice-child: {}", JsonUtils.toJsonString(busyPrice));
////            } else {
////                busyPrice = CalUtils.add(amount, independentPrice);
////                // log.warn("busyPrice-all: {}", JsonUtils.toJsonString(busyPrice));
////            }
////        }
//        // 总金额
//        unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(busyPrice));
//        // 阶段性商单接单分账算价 todo 只跟平台分账-单独写
//        calculateMap = this.calculateBusyOrderDistribution(templateModel, amount, null, isRecommend, Boolean.FALSE, null, Boolean.FALSE, Boolean.FALSE);
//        for (String independentObject : calculateMap.keySet()) {
//            // 分账金额
//            BigDecimal itemPrice = calculateMap.get(independentObject);
//            SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
//            independentOrderBo.setOrderNo(tradeNo);
//            independentOrderBo.setTradeType(BusyType.BusyOrder.name());
//            independentOrderBo.setIndependentStatus(2);
//            independentOrderBo.setTradeNo(delayOrderNo);
//            if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
//                // todo
//                independentOrderBo.setUserId(2L);
//            }
////            else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.country.getKey(), independentObject)) {
////                independentOrderBo.setUserId(countryId);
////            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.city.getKey(), independentObject)) {
////                independentOrderBo.setUserId(cityId);
////            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.rece.getKey(), independentObject)) {
////                independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
////            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.invite.getKey(), independentObject)) {
////                independentOrderBo.setUserId(regUserId);
////            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
////                independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
////            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distributionInvite.getKey(), independentObject)) {
////                independentOrderBo.setUserId(distributionInviteUserId);
////            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
////                // todo 代理人id
////                independentOrderBo.setUserId(0L);
////            }
//            if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
//                log.info("{} 用户ID为空", independentObject);
//                continue;
//            }
//            if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
//                log.info("分账用户-{} 所得金额为空", independentObject);
//                continue;
//            }
//            independentOrderBo.setIndependentObject(independentObject);
//            independentOrderBo.setMerId(0L);
//            independentOrderBo.setSiteId(taskSiteModel.getSiteId());
//            independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
//            independentOrderBoLists.add(independentOrderBo);
//        }
//        // 平台手续费百分比
////                    BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), PERCENTAGE);
////                    log.warn("平台手续费百分比：{}", platformDivide);
//        // todo
//        independentIdBo.setAdminId(2L);
////        independentIdBo.setCountryId(countryId);
////        independentIdBo.setCityId(cityId);
//        // todo 代理id
////                    if (null != agencyId)
////        // 分销人id
////        if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() > 0L) {
////            independentIdBo.setDistributorId(busyOrderReceiveModel.getSharePerson());
////        }
////        // 拉新人的上上级id
////        if (regUserId != null && regUserId > 0L) {
////            independentIdBo.setInviteId(regUserId);
////        }
//        // 接单人id
//        independentIdBo.setReceiveId(busyOrderReceiveModel.getUserId());
////        // 分销人的拉新人id
////        if (null != distributionInviteUserId && distributionInviteUserId != 0L) {
////            independentIdBo.setDistributorInviteId(distributionInviteUserId);
////        }
//        // 查询所有分账人的翼码帐户信息
//        List<Long> allUserIds = getAllIds(independentIdBo);
//        log.warn("allUserIds : {}", allUserIds);
//        // 查询所有参与分账人员的翼码账户信息
//        Map<Long, SohuAccountBankVo> accountBankVoMap = accountBankService.queryMapByUserIds(allUserIds);
//        log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
//        Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
//        // 分账账号信息参数
//        List<SplitList> splitLists = new ArrayList<>();
//        // 累计的没有分出去的金额
//        BigDecimal newPlatePrice = BigDecimal.ZERO;
//        for (Long userId : independentOrderMap.keySet()) {
//            SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
//            // todo
//            if (userId != 2L) {
//                if (Objects.isNull(sohuAccountBankVo)) {
//                    log.warn("翼码账户为空,{}", userId);
//                    List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
//                    newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
//                    continue;
//                }
//                List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
//                if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
//                    continue;
//                }
//                SplitList item = new SplitList();
//                BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                if (CalUtils.isLessEqualZero(divAmt)) {
//                    continue;
//                }
//                if (sohuIndependentOrderBos.size() > 1) {
//                    log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
//                }
//                item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
//                item.setMerchantId(sohuAccountBankVo.getMerchantId());
//                splitLists.add(item);
//            }
//        }
////                    BigDecimal totalDivAmt = splitLists.stream()
////                        .map(splitList -> new BigDecimal(splitList.getDivAmt()))
////                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//        // 接单人金额 = 商单总价值 - 平台总手续费
//        BigDecimal receivePrice = CalUtils.sub(busyPrice, calculateMap.get(SohuIndependentObject.platform.getKey()), calculateMap.get(SohuIndependentObject.invite.getKey()), calculateMap.get(SohuIndependentObject.country.getKey()), calculateMap.get(SohuIndependentObject.city.getKey()), calculateMap.get(SohuIndependentObject.agency.getKey()), calculateMap.get(SohuIndependentObject.distribution.getKey()), calculateMap.get(SohuIndependentObject.distributionInvite.getKey()));
//        SohuIndependentOrderBo independentOrderBoReceive = new SohuIndependentOrderBo();
//        independentOrderBoReceive.setOrderNo(tradeNo);
//        independentOrderBoReceive.setTradeType(BusyType.BusyOrder.name());
//        independentOrderBoReceive.setIndependentStatus(2);
//        independentOrderBoReceive.setTradeNo(delayOrderNo);
//        independentOrderBoReceive.setUserId(busyOrderReceiveModel.getUserId());
//        independentOrderBoReceive.setIndependentObject(SohuIndependentObject.rece.getKey());
//        independentOrderBoReceive.setMerId(0L);
//        independentOrderBoReceive.setSiteId(taskSiteModel.getSiteId());
//        independentOrderBoReceive.setIndependentPrice(receivePrice);
//        independentOrderBoLists.add(independentOrderBoReceive);
//        // 接单人翼码账户信息
//        SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(busyOrderReceiveModel.getUserId());
//        // 接单人
//        SplitList itemReceive = new SplitList();
//        itemReceive.setDivAmt(BigDecimalUtils.yuanToFen(receivePrice).toString());
//        itemReceive.setMerchantId(sohuAccountBankVo.getMerchantId());
//        log.warn("itemReceive: {}", JsonUtils.toJsonString(itemReceive));
//        splitLists.add(itemReceive);
//        // 去重从新计算之后的传给第三方的分账信息
//        List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId, Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
//            SplitList newSplitList = new SplitList();
//            newSplitList.setMerchantId(entry.getKey());
//            newSplitList.setDivAmt(entry.getValue().toString());
//            return newSplitList;
//        }).collect(Collectors.toList());
//        // 设置分账参数
//        splitInfo.setSplitList(splitListsNew);
//        // 平台总金额
//        BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);
//        // 平台总留存金额
//        splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
//        // 分账信息
//        extendParams.setSplitInfo(splitInfo);
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("R");
//        // 添加分账扩展参数
//        unifiedorder.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setUnifiedorderRequest(unifiedorder);
//        log.info("wxpay busyorderrece request：{}", JSONUtil.toJsonStr(request));
//        // 请求第三方-返回微信支付下单的响应数据
//        response = Client.getClient().execute(request);
//        // todo 是否一次性支付商单酬金  一次性支付的话就直接完成任务 改掉所有阶段性状态为已完成
//        if (payModel.getAllAmount()) {
////                    SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(masterOrderNo, Boolean.TRUE);
//            // 如果是一次性支付商单酬金 将支付单号+allAmount 12423432432allAmount 作为key存入
//            RedisUtils.setCacheObject(tradeNo + "allAmount", Boolean.TRUE, Duration.ofMinutes(5));
//        }
////                // 保存待付款记录
////                remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo,
////                        PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(),
////                        busyPrice, PayObject.BusyOrderReceive.name(), Long.valueOf(masterOrderNo),
////                        user.getUserId(), payModel.getMasterId());
//        // 保存待付款记录
//        remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), busyPrice, PayObject.BusyOrderReceive.name(), user.getUserId(), busyOrderReceiveModel.getUserId(), payModel.getMasterId());
//        // 保存分销单记录
//        independentOrderBoLists.stream().filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject())).findFirst().ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
//        independentOrderService.insertByBoList(independentOrderBoLists);
//    }
//
//    /**
//     * pc二维码支付
//     *
//     * @param payModel
//     * @param user
//     * @param body
//     * @param yiMaPayConfig
//     */
//    private String getPcCodePay(SohuPrePayBo payModel, LoginUser user, StringBuilder body, YiMaPayConfig yiMaPayConfig) {
//        PaySourceEnum paySourceEnum = PaySourceEnum.MAP.get(payModel.getPaySource());
//        if (paySourceEnum == null) {
//            return null;
//        }
//        // 翼码支付请求参数组装-公共信息
//        ScanpayRequest request = new ScanpayRequest();
//        request.setPosId(yiMaPayConfig.getPosId());
//        request.setIsspid(yiMaPayConfig.getIssPid());
//        request.setSystemId(yiMaPayConfig.getSystemId());
//        request.setStoreId(yiMaPayConfig.getStoreId());
//        // 支付参数组装
//        Scanpay scanpay = new Scanpay();
//        scanpay.setPayType("503");
//        scanpay.setNotifyUrl(yiMaPayConfig.getNotifyUrl());
//        // 商品信息
//        List<GoodsDetail> goodsDetailList;
//        // 返回对象
//        ScanpayResponse response = null;
//        /*
//         * paySource = 1 商城商品
//         * paySource = 2 商单-支付预付款
//         * paySource = 3 商户付款码扫码支付
//         * paySource = 4 发单方-支付接单酬金
//         */
//        // 主订单号
//        String masterOrderNo = payModel.getMasterOrderNo();
//        // 是否分账配置
//        ExtendParams extendParams = new ExtendParams();
//        // 通过缓存获取预下单对象
//        String key = CacheConstants.ORDER_PAY_TWO + masterOrderNo;
//        boolean exists = RedisUtils.isExistsObject(key);
//        // 如果存在直接唤醒
//        if (exists && payModel.getPaySource() == 1) {
//            return RedisUtils.getCacheObject(key);
//        }
//        switch (paySourceEnum) {
//            case SHOP_ORDER_PAY:
//                SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(masterOrderNo);
//                // 状态判断
//                if (ObjectUtil.isNull(masterOrder)) {
//                    throw new ServiceException("order does not exist");
//                }
//                if (masterOrder.getIsCancel()) {
//                    throw new ServiceException("order is cancelled");
//                }
//                if (masterOrder.getPaid()) {
//                    throw new ServiceException("order not paid");
//                }
//                // 剩余组装信息
//                Long merId = remoteShopOrderService.getListByMasterNo(payModel.getMasterOrderNo()).get(0).getMerId();
//                SohuMerchantModel merchantModel = remoteMerchantService.selectById(merId);
//                body.append(merchantModel.getName());
//                if (StringUtils.isBlank(body)) {
//                    body.append("用户下单");
//                }
//                // 备注
//                request.setMemo(body.toString());
//                // 唯一订单号
//                request.setPosSeq(masterOrder.getOrderNo());
//                // 总金额
//                scanpay.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getPayPrice()));
//                // 组装商品信息
//                goodsDetailList = exchangeGoodsDetailList(masterOrder.getOrderNo());
//                scanpay.setGoodsDetail(goodsDetailList);
//                // 延时分账
//                extendParams.setSplitFlag("D");
//                extendParams.setPlanSplitDate(DateUtils.getDate().replaceAll("-", ""));
//                scanpay.setExtendParams(extendParams);
//                // 设置请求参数
//                request.setScanpayRequest(scanpay);
//                log.info("微信支付请求返回：{}" + JSONUtil.toJsonStr(request));
//                // 请求第三方-返回微信支付下单的响应数据
//                response = Client.getClient().execute(request);
//                break;
//            case BUSY_ORDER_PREPAY:
//                // 查询商单
//                SohuBusyOrderModel sohuBusyOrder = remoteBusyOrderService.queryBusyOrder(Long.valueOf(masterOrderNo));
//                if (Objects.isNull(sohuBusyOrder)) {
//                    throw new ServiceException(MessageUtils.message("BUSY_ORDER_NOT_FOUNT"));
//                }
//                if (!Objects.equals(sohuBusyOrder.getUserId(), user.getUserId())) {
//                    throw new ServiceException(MessageUtils.message("BUSY_ORDER_OPERATE_NO_AUTH"));
//                }
//                if (!Objects.equals(sohuBusyOrder.getPrepayState(), PayStatus.WaitPay.name()) && !Objects.equals(sohuBusyOrder.getPrepayState(), BusyOrderStatus.Prepay.name())) {
//                    throw new ServiceException(MessageUtils.message("BUSY_ORDER_WRONG_STATUS"));
//                }
//                String outTradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_ORDER_PREFIX);
//                request.setMemo("商单预付款支付");
//                request.setPosSeq(outTradeNo);
//                // 转换价格y-f
//                int busyOrderTotalPrice = CalUtils.yuanToCent(sohuBusyOrder.getPrepayAmount()).intValue();
//                // 商单价格对象
//                scanpay.setTxAmt(busyOrderTotalPrice);
//                // 不分账 -- R实时分账 --D延时分账
//                extendParams.setSplitFlag("N");
//                scanpay.setExtendParams(extendParams);
//                // 设置请求参数
//                request.setScanpayRequest(scanpay);
//                log.info("yi-ma-pay busyorder request：{}" + JSONUtil.toJsonStr(request));
//                // 请求第三方-返回微信支付下单的响应数据
//                response = Client.getClient().execute(request);
//                // 保存待付款记录 todo 阶段id
//                remoteBusyOrderService.saveBusyOrderPay("sohuBusyOrder.getId()", outTradeNo, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), sohuBusyOrder.getPrepayAmount(), PayObject.BusyOrder.name(), 0L, user.getUserId(), 0L);
//                break;
//            case MERCHANT_CODE_PAY:
//
//                break;
//            case TASK_PARTY_PAY:
//                // todo 阶段性付款
////                SohuBusyTaskDeliveryModel taskDeliveryModel = remoteBusyOrderService.getDeliveryInfo(payModel.getMasterId());
//                // 支付总金额
//                BigDecimal amount = payModel.getAmount();
//                if (amount == null || CalUtils.isLessEqualZero(amount)) {
//                    throw new ServiceException(MessageUtils.message("付款金额不能小于等于0"));
//                }
//                // 根据子订单号+已接单查询唯一订单
//                SohuBusyTaskReceiveModel busyOrderReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(payModel.getMasterOrderNo(), Boolean.TRUE);
//                if (Objects.isNull(busyOrderReceiveModel)) {
//                    throw new ServiceException(MessageUtils.message("order.rece.not.exist"));
//                }
//                // 查询商单子单
//                SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(payModel.getMasterOrderNo());
//                if (Objects.isNull(taskSiteModel)) {
//                    throw new ServiceException(MessageUtils.message("BUSY_ORDER_NOT_FOUNT"));
//                }
//                // 分账信息
//                SplitInfo splitInfo = new SplitInfo();
//                // 先查询分拥等级 分拥等级—1:拿我拉新的人分拥  2:拿我拉新人的拉新的分拥—以此类推（默认为我拉新人的分拥 1）
//                SohuIndependentLevelConfig levelConfig = levelConfigService.queryByStatusAndSiteId(0, 11L);
//                Integer level = ObjectUtils.isNull(levelConfig) ? levelConfig.getLevel() : 2;
//                // 查询接单人是否有拉新人-上上级拉新人
//                Long regUserId = inviteService.selectByInviteCount(busyOrderReceiveModel.getUserId(), level);
//                Boolean regUser = Boolean.FALSE;
//                if (null != regUserId) {
//                    regUser = Boolean.TRUE;
//                }
//                // 是否是拉新人
//                Boolean finalRegUser = regUser;
//                // 组装id
//                SohuIndependentIdPayBo independentIdBo = new SohuIndependentIdPayBo();
//                // 根据城市站点id获取城市站长id
//                Long cityId = siteService.queryById(taskSiteModel.getSiteId()).getStationmasterId();
//                // 根据城市站点id获取国家站点用户id
//                Long countryId = siteService.selectSiteByPid(taskSiteModel.getSiteId()).getStationmasterId();
//                // todo 查询是否有代理人 代理角色暂未启用
//                Boolean agencyId = Boolean.FALSE;
//                // 分销人的拉新人
//                Long distributionInviteUserId = null;
//                log.warn("busyOrderReceModel : {}", JsonUtils.toJsonString(busyOrderReceiveModel));
//                if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() != 0L) {
//                    log.warn("distributionUserId : {}", busyOrderReceiveModel.getSharePerson());
////                    sohuBusyOrderQuery.setIsRecommend(Boolean.TRUE);
//                    distributionInviteUserId = inviteService.selectByInviteCount(busyOrderReceiveModel.getSharePerson(), level);
//                }
//                log.warn("distributionInviteUserId : {}", distributionInviteUserId);
//                // 查询分账模板
//                // 根据城市站点、商品分账模板类型 查询分账模板
//                SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(taskSiteModel.getSiteId(), 2);
//                if (Objects.isNull(templateModel)) {
//                    throw new ServiceException("商单分账模板为空,请联系系统管理员");
//                }
//                // 分账金额
//                BigDecimal independentPrice = taskSiteModel.getDistributionAmount();
//                if (payModel.getAllAmount() || payModel.getIsDelivery()) {
//                    independentPrice = BigDecimal.ZERO;
//                }
////                // 一口价
////                if (taskSiteModel.getKickbackType().equals(KickbackType.price.getCode())) {
////                    independentPrice = taskSiteModel.getKickbackValue();
////                } else if (taskSiteModel.getKickbackType().equals(KickbackType.percentage.getCode())) {
////                    // 任务分销总金额
////                    independentPrice = taskSiteModel.getDistributionAmount();
////                    log.warn("任务按照百分比分销金额：{}", independentPrice);
////                }
//                // 分账方组装
//                Map<String, BigDecimal> calculateMap;
//                // 分账请求流水号
//                String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
//                // 分销单组装
//                List<SohuIndependentOrderBo> independentOrderBoLists = new ArrayList<>();
//                // 如果是佣金结算
//                if (null != payModel.getIsIndependentAmount() && payModel.getIsIndependentAmount()) {
//                    request.setMemo("支付商单接佣金-" + user.getNickname());
//                    String tradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_RECE_ORDER_PREFIX);
//                    request.setPosSeq(tradeNo);
//                    // 总金额
//                    scanpay.setTxAmt(BigDecimalUtils.yuanToFen(independentPrice));
//                    // 商单接单分账算价(是否是分销单),需要判断是否有分销人 busyOrderReceiveModel.getSharePerson()
//                    calculateMap = this.calculateBusyOrderDistribution(templateModel, taskSiteModel.getFullAmount(), independentPrice, busyOrderReceiveModel.getSharePerson() != null, finalRegUser, distributionInviteUserId, agencyId, Boolean.TRUE);
//                    for (String independentObject : calculateMap.keySet()) {
//                        // 分账金额
//                        BigDecimal itemPrice = calculateMap.get(independentObject);
//                        SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
//                        independentOrderBo.setOrderNo(tradeNo);
//                        independentOrderBo.setTradeType(BusyType.BusyOrder.name());
//                        independentOrderBo.setIndependentStatus(2);
//                        independentOrderBo.setTradeNo(delayOrderNo);
//                        if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
//                            // todo
//                            independentOrderBo.setUserId(2L);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.country.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(countryId);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.city.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(cityId);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.invite.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(regUserId);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distributionInvite.getKey(), independentObject)) {
//                            independentOrderBo.setUserId(distributionInviteUserId);
//                        } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
//                            // todo 代理人id
//                            independentOrderBo.setUserId(0L);
//                        }
//                        if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
//                            log.info("{} 用户ID为空", independentObject);
//                            continue;
//                        }
//                        if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
//                            log.info("分账用户-{} 所得金额为空", independentObject);
//                            continue;
//                        }
//                        independentOrderBo.setIndependentObject(independentObject);
//                        independentOrderBo.setMerId(0L);
//                        independentOrderBo.setSiteId(taskSiteModel.getSiteId());
//                        independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
//                        independentOrderBoLists.add(independentOrderBo);
//                    }
//                    // 平台手续费百分比
////                    BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), PERCENTAGE);
////                    log.warn("平台手续费百分比：{}", platformDivide);
//                    // todo
//                    independentIdBo.setAdminId(2L);
//                    independentIdBo.setCountryId(countryId);
//                    independentIdBo.setCityId(cityId);
//                    // todo 代理id
////                    if (null != agencyId)
//                    // 分销人id
//                    if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() > 0L) {
//                        independentIdBo.setDistributorId(busyOrderReceiveModel.getSharePerson());
//                    }
//                    // 拉新人的上上级id
//                    if (regUserId != null && regUserId > 0L) {
//                        independentIdBo.setInviteId(regUserId);
//                    }
//                    // 接单人id
//                    independentIdBo.setReceiveId(busyOrderReceiveModel.getUserId());
//                    // 分销人的拉新人id
//                    if (null != distributionInviteUserId && distributionInviteUserId != 0L) {
//                        independentIdBo.setDistributorInviteId(distributionInviteUserId);
//                    }
//                    // 查询所有分账人的翼码帐户信息
//                    List<Long> allUserIds = getAllIds(independentIdBo);
//                    log.warn("allUserIds : {}", allUserIds);
//                    // 查询所有参与分账人员的翼码账户信息
//                    Map<Long, SohuAccountBankVo> accountBankVoMap = accountBankService.queryMapByUserIds(allUserIds);
//                    log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
//                    Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
//                    // 分账账号信息参数
//                    List<SplitList> splitLists = new ArrayList<>();
//                    // 累计的没有分出去的金额
//                    BigDecimal newPlatePrice = BigDecimal.ZERO;
//                    for (Long userId : independentOrderMap.keySet()) {
//                        // 获取用户开户信息
//                        SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
//                        // todo
//                        if (userId != 2L) {
//                            if (Objects.isNull(sohuAccountBankVo)) {
//                                log.warn("翼码账户为空,{}", userId);
//                                List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
//                                newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
//                                continue;
//                            }
//                            List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
//                            if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
//                                continue;
//                            }
//                            SplitList item = new SplitList();
//                            BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                            if (CalUtils.isLessEqualZero(divAmt)) {
//                                continue;
//                            }
//                            if (sohuIndependentOrderBos.size() > 1) {
//                                log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
//                            }
//                            item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
//                            // 是否有翼码用户id
//                            item.setMerchantId(sohuAccountBankVo.getMerchantId());
//                            splitLists.add(item);
//                        }
//                    }
//                    // 去重从新计算之后的传给第三方的分账信息
//                    List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId, Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
//                        SplitList newSplitList = new SplitList();
//                        newSplitList.setMerchantId(entry.getKey());
//                        newSplitList.setDivAmt(entry.getValue().toString());
//                        return newSplitList;
//                    }).collect(Collectors.toList());
//                    // 设置分账参数
//                    splitInfo.setSplitList(splitListsNew);
//                    // 平台总金额
//                    BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);
//                    // 平台总留存金额
//                    splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
//                    // 分账信息
//                    extendParams.setSplitInfo(splitInfo);
//                    // 不分账 -- R实时分账 --D延时分账
//                    extendParams.setSplitFlag("R");
//                    // 添加分账扩展参数
//                    scanpay.setExtendParams(extendParams);
//                    // 设置请求参数
//                    request.setScanpayRequest(scanpay);
//                    log.info("yi-ma-pay busy-order-independent request：{}", JSONUtil.toJsonStr(request));
//                    // 请求第三方-返回微信支付下单的响应数据
//                    response = Client.getClient().execute(request);
//                    // todo 是否一次性支付商单酬金  一次性支付的话就直接完成任务 改掉所有阶段性状态为已完成
//                    if (payModel.getIsIndependentAmount()) {
//                        // 如果是佣金结算 将支付单号+isIndependentAmount 12423432432isIndependentAmount 作为key存入
//                        RedisUtils.setCacheObject(tradeNo + "isIndependentAmount", Boolean.TRUE, Duration.ofMinutes(5));
//                    }
//                    // 保存待付款记录
//                    remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), independentPrice, PayObject.BusyOrderReceive.name(), user.getUserId(), busyOrderReceiveModel.getUserId(), null);
//                    // 保存分销单记录
//                    independentOrderBoLists.stream().filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject())).findFirst().ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
//                    independentOrderService.insertByBoList(independentOrderBoLists);
//                    break;
//                }
//                // 是否分销
//                Boolean isRecommend = Boolean.TRUE;
//                // 判断是否设置佣金
//                if (taskSiteModel.getKickbackType().equals(KickbackType.none.getCode())) isRecommend = Boolean.FALSE;
//                // 用户信息
//                Map<Long, LoginUser> userMap = remoteUserService.selectMap(Collections.singletonList(busyOrderReceiveModel.getUserId()));
//                LoginUser recipient = userMap.get(busyOrderReceiveModel.getUserId());
//                request.setMemo("支付商单接单酬金-" + recipient.getNickname());
//                String tradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_RECE_ORDER_PREFIX);
//                request.setPosSeq(tradeNo);
//                // 商单总金额
//                BigDecimal busyPrice = BigDecimal.ZERO;
//                // 根据条件计算busyPrice和independentPrice 如果没有分销人-商单总金额为商单价值金额
//                if (busyOrderReceiveModel.getSharePerson() == null || busyOrderReceiveModel.getSharePerson().equals(0L)) {
//                    // 没有分销人时的逻辑
//                    if (payModel.getIsDelivery() || payModel.getAllAmount()) {
//                        busyPrice = amount;
//                        independentPrice = BigDecimal.ZERO; // 确保independentPrice也被设置为0
//                    } else {
//                        busyPrice = taskSiteModel.getFullAmount();
//                    }
//                } else {
//                    // 有分销人时的逻辑  是否是提前结算、阶段性支付
//                    if (!payModel.getAllAmount() && !payModel.getIsDelivery()) {
//                        busyPrice = CalUtils.add(taskSiteModel.getFullAmount(), independentPrice);
//                        // log.warn("busyPrice-child: {}", JsonUtils.toJsonString(busyPrice));
//                    } else {
//                        busyPrice = CalUtils.add(amount, independentPrice);
//                        // log.warn("busyPrice-all: {}", JsonUtils.toJsonString(busyPrice));
//                    }
//                }
//                // 总金额
//                scanpay.setTxAmt(BigDecimalUtils.yuanToFen(busyPrice));
//                // 阶段性商单接单分账算价
//                calculateMap = this.calculateBusyOrderDistribution(templateModel, amount, independentPrice, isRecommend, finalRegUser, distributionInviteUserId, agencyId, Boolean.FALSE);
//                for (String independentObject : calculateMap.keySet()) {
//                    // 分账金额
//                    BigDecimal itemPrice = calculateMap.get(independentObject);
//                    SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
//                    independentOrderBo.setOrderNo(tradeNo);
//                    independentOrderBo.setTradeType(BusyType.BusyOrder.name());
//                    independentOrderBo.setIndependentStatus(2);
//                    independentOrderBo.setTradeNo(delayOrderNo);
//                    if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
//                        // todo
//                        independentOrderBo.setUserId(2L);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.country.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(countryId);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.city.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(cityId);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.rece.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.invite.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(regUserId);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distributionInvite.getKey(), independentObject)) {
//                        independentOrderBo.setUserId(distributionInviteUserId);
//                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
//                        // todo 代理人id
//                        independentOrderBo.setUserId(0L);
//                    }
//                    if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
//                        log.info("{} 用户ID为空", independentObject);
//                        continue;
//                    }
//                    if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
//                        log.info("分账用户-{} 所得金额为空", independentObject);
//                        continue;
//                    }
//                    independentOrderBo.setIndependentObject(independentObject);
//                    independentOrderBo.setMerId(0L);
//                    independentOrderBo.setSiteId(taskSiteModel.getSiteId());
//                    independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
//                    independentOrderBoLists.add(independentOrderBo);
//                }
//                // 平台手续费百分比
////                    BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), PERCENTAGE);
////                    log.warn("平台手续费百分比：{}", platformDivide);
//                independentIdBo.setAdminId(2L);
//                independentIdBo.setCountryId(countryId);
//                independentIdBo.setCityId(cityId);
//                // todo 代理id
////                    if (null != agencyId)
//                // 分销人id
//                if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() > 0L) {
//                    independentIdBo.setDistributorId(busyOrderReceiveModel.getSharePerson());
//                }
//                // 拉新人的上上级id
//                if (regUserId != null && regUserId > 0L) {
//                    independentIdBo.setInviteId(regUserId);
//                }
//                // 接单人id
//                independentIdBo.setReceiveId(busyOrderReceiveModel.getUserId());
//                // 分销人的拉新人id
//                if (null != distributionInviteUserId && distributionInviteUserId != 0L) {
//                    independentIdBo.setDistributorInviteId(distributionInviteUserId);
//                }
//                // 查询所有分账人的翼码帐户信息
//                List<Long> allUserIds = getAllIds(independentIdBo);
//                log.warn("allUserIds : {}", allUserIds);
//                // 查询所有参与分账人员的翼码账户信息
//                Map<Long, SohuAccountBankVo> accountBankVoMap = accountBankService.queryMapByUserIds(allUserIds);
//                log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
//                Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
//                // 分账账号信息参数
//                List<SplitList> splitLists = new ArrayList<>();
//                // 累计的没有分出去的金额
//                BigDecimal newPlatePrice = BigDecimal.ZERO;
//                for (Long userId : independentOrderMap.keySet()) {
//                    SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
//                    // todo
//                    if (userId != 2L) {
//                        if (Objects.isNull(sohuAccountBankVo)) {
//                            log.warn("翼码账户为空,{}", userId);
//                            List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
//                            newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
//                            continue;
//                        }
//                        List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
//                        if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
//                            continue;
//                        }
//                        SplitList item = new SplitList();
//                        BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        if (CalUtils.isLessEqualZero(divAmt)) {
//                            continue;
//                        }
//                        if (sohuIndependentOrderBos.size() > 1) {
//                            log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
//                        }
//                        item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
//                        item.setMerchantId(sohuAccountBankVo.getMerchantId());
//                        splitLists.add(item);
//                    }
//                }
////                    BigDecimal totalDivAmt = splitLists.stream()
////                        .map(splitList -> new BigDecimal(splitList.getDivAmt()))
////                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                // 接单人金额 = 商单总价值 - 平台总手续费
//                BigDecimal receivePrice = CalUtils.sub(busyPrice, calculateMap.get(SohuIndependentObject.platform.getKey()), calculateMap.get(SohuIndependentObject.invite.getKey()), calculateMap.get(SohuIndependentObject.country.getKey()), calculateMap.get(SohuIndependentObject.city.getKey()), calculateMap.get(SohuIndependentObject.agency.getKey()), calculateMap.get(SohuIndependentObject.distribution.getKey()), calculateMap.get(SohuIndependentObject.distributionInvite.getKey()));
//                SohuIndependentOrderBo independentOrderBoReceive = new SohuIndependentOrderBo();
//                independentOrderBoReceive.setOrderNo(tradeNo);
//                independentOrderBoReceive.setTradeType(BusyType.BusyOrder.name());
//                independentOrderBoReceive.setIndependentStatus(2);
//                independentOrderBoReceive.setTradeNo(delayOrderNo);
//                independentOrderBoReceive.setUserId(busyOrderReceiveModel.getUserId());
//                independentOrderBoReceive.setIndependentObject(SohuIndependentObject.rece.getKey());
//                independentOrderBoReceive.setMerId(0L);
//                independentOrderBoReceive.setSiteId(taskSiteModel.getSiteId());
//                independentOrderBoReceive.setIndependentPrice(receivePrice);
//                independentOrderBoLists.add(independentOrderBoReceive);
//                // 接单人翼码账户信息
//                SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(busyOrderReceiveModel.getUserId());
//                // 接单人
//                SplitList itemReceive = new SplitList();
//                itemReceive.setDivAmt(BigDecimalUtils.yuanToFen(receivePrice).toString());
//                itemReceive.setMerchantId(sohuAccountBankVo.getMerchantId());
//                log.warn("itemReceive: {}", JsonUtils.toJsonString(itemReceive));
//                splitLists.add(itemReceive);
//                // 去重从新计算之后的传给第三方的分账信息
//                List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId, Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
//                    SplitList newSplitList = new SplitList();
//                    newSplitList.setMerchantId(entry.getKey());
//                    newSplitList.setDivAmt(entry.getValue().toString());
//                    return newSplitList;
//                }).collect(Collectors.toList());
//                // 设置分账参数
//                splitInfo.setSplitList(splitListsNew);
//                // 平台总金额
//                BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);
//                // 平台总留存金额
//                splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
//                // 分账信息
//                extendParams.setSplitInfo(splitInfo);
//                // 不分账 -- R实时分账 --D延时分账
//                extendParams.setSplitFlag("R");
//                // 添加分账扩展参数
//                scanpay.setExtendParams(extendParams);
//                // 设置请求参数
//                request.setScanpayRequest(scanpay);
//                log.info("wxpay busyorderrece request：{}", JSONUtil.toJsonStr(request));
//                // 请求第三方-返回微信支付下单的响应数据
//                response = Client.getClient().execute(request);
//                // todo 是否一次性支付商单酬金  一次性支付的话就直接完成任务 改掉所有阶段性状态为已完成
//                if (payModel.getAllAmount()) {
////                    SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(masterOrderNo, Boolean.TRUE);
//                    // 如果是一次性支付商单酬金 将支付单号+allAmount 12423432432allAmount 作为key存入
//                    RedisUtils.setCacheObject(tradeNo + "allAmount", Boolean.TRUE, Duration.ofMinutes(5));
//                }
////                // 保存待付款记录
////                remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo,
////                        PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(),
////                        busyPrice, PayObject.BusyOrderReceive.name(), Long.valueOf(masterOrderNo),
////                        user.getUserId(), payModel.getMasterId());
//                // 保存待付款记录
//                remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), busyPrice, PayObject.BusyOrderReceive.name(), user.getUserId(), busyOrderReceiveModel.getUserId(), payModel.getMasterId());
//                // 保存分销单记录
//                independentOrderBoLists.stream().filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject())).findFirst().ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
//                independentOrderService.insertByBoList(independentOrderBoLists);
//                break;
//            case MCN_ORDER_PAY:
//                response = this.getPcCodePayOfMcnOrderPay(masterOrderNo, request, scanpay, extendParams);
//                break;
//            // 商单支付保证金
//            case BUSY_TASK_PROMISE_PAY:
//                response = this.getBusyTaskPromisePay(payModel.getMasterId(), request, scanpay, extendParams, user.getUserId());
//                break;
//            // 单个视频付费
//            case VIDEO_PAY:
//                response = this.getVideoPayPc(payModel.getMasterId(), request, scanpay, extendParams, user.getUserId());
//                break;
//            // 整部剧付费
//            case PLAYLET_PAY:
//                response = this.getPlayletPayPc(payModel.getMasterId(), request, scanpay, extendParams, user.getUserId());
//                break;
//            // 虚拟币支付
//            case SOHU_VIRTUAL_CURRENCY_RECHARGE:
//                response = this.getCoinPayPc(payModel.getMasterId(), request, scanpay, extendParams, user.getUserId());
//                break;
//            default:
//        }
//        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(5));
//        // 转换第三方请求响应参数
//        String payment = JSONUtil.toJsonStr(response);
//        log.warn("翼码支付请求返回：{}", payment);
//        return payment;
//    }
//
//    /**
//     * pc二维码支付-MCN订单支付-开通一键分发功能
//     *
//     * @param masterOrderNo 主订单号
//     * @param request       翼码支付请求参数组装-公共信息
//     * @param scanpay       支付参数组装
//     * @param extendParams  是否分账配置
//     */
//    private ScanpayResponse getPcCodePayOfMcnOrderPay(String masterOrderNo, ScanpayRequest request, Scanpay scanpay, ExtendParams extendParams) {
//        SohuMcnMasterOrderModel masterOrder = remoteMcnOrderService.selectByMasterOrderNo(masterOrderNo);
//        // 状态判断
//        if (ObjectUtil.isNull(masterOrder)) {
//            throw new ServiceException("order does not exist");
//        }
//        if (masterOrder.getIsCancel()) {
//            throw new ServiceException("order is cancelled");
//        }
//        if (masterOrder.getPaid()) {
//            throw new ServiceException("order not paid");
//        }
//        // 备注
//        request.setMemo("MCN会员功能支付");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.MCN_PREFIX_MASTER);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 总金额
//        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(masterOrder.getPayPrice()));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        scanpay.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setScanpayRequest(scanpay);
//        log.info("MCN微信支付请求返回：{}", JSONUtil.toJsonStr(request));
//        // 请求第三方-返回微信支付下单的响应数据
//        ScanpayResponse scanpayResponse = Client.getClient().execute(request);
//        //更新第三方支付单号
//        SohuMcnMasterOrderModel masterOrderModel = BeanUtil.copyProperties(masterOrder, SohuMcnMasterOrderModel.class);
//        masterOrderModel.setOutTradeNo(posSeq);
//        this.remoteMcnOrderService.updateMcnMasterOrderByMasterId(masterOrderModel);
//        return scanpayResponse;
//    }
//
//    /**
//     * 商单保证金-mobile
//     *
//     * @param receiveId
//     * @param request
//     * @param unifiedOrder
//     * @param extendParams
//     * @param userId
//     */
//    private UnifiedorderResponse getBusyTaskPromisePayMobile(Long receiveId, UnifiedorderRequest request, Unifiedorder unifiedOrder, ExtendParams extendParams, Long userId) {
//        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryBusyTaskRece(receiveId);
//        if (ObjectUtil.isNull(taskReceiveModel)) {
//            throw new ServiceException("order receive does not exist");
//        }
//        if (!StrUtil.equalsAnyIgnoreCase(taskReceiveModel.getState(), CommonState.WaitApprove.name())) {
//            throw new ServiceException("Payment status is incorrect");
//        }
//        // 备注
//        request.setMemo("任务支付保证金");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.BUSY_TASK_PROMISE_SUBUNIT);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 总金额
//        unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(taskReceiveModel.getAmount()));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        unifiedOrder.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setUnifiedorderRequest(unifiedOrder);
//        UnifiedorderResponse execute = Client.getClient().execute(request);
//        log.info("任务支付保证金请求返回：{}", JSONUtil.toJsonStr(execute));
//        remoteBusyOrderService.saveBusyOrderPay(taskReceiveModel.getTaskNumber(), posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), taskReceiveModel.getAmount(), PayObject.BusyTaskPromise.name(), 0L, taskReceiveModel.getUserId(), 0L);
//        // 将接单id根据支付流水号保存到缓存里面
//        RedisUtils.setCacheObject(posSeq, receiveId, Duration.ofMinutes(5));
//        return execute;
//    }
//
//    /**
//     * 单个视频付费
//     *
//     * @param videoId
//     * @param request
//     * @param unifiedOrder
//     * @param extendParams
//     * @param userId
//     * @return
//     */
//    private UnifiedorderResponse getVideoPayMobile(Long videoId, UnifiedorderRequest request, Unifiedorder unifiedOrder, ExtendParams extendParams, Long userId) {
//        SohuVideoVo videoVo = sohuVideoService.selectVoById(videoId);
//        if (ObjectUtil.isNull(videoVo)) {
//            throw new ServiceException("video does not exist");
//        }
//        if (!StrUtil.equalsAnyIgnoreCase(videoVo.getState(), CommonState.OnShelf.name())) {
//            throw new ServiceException("Payment status is incorrect");
//        }
//        SohuPlayletVo playletVo = sohuPlayletService.queryByEpisodeRelevance(videoVo.getEpisodeRelevance());
//        // 备注
//        request.setMemo("单个视频付费-移动端");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.VIDEO_PAY_PREFIX);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 总金额
//        unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(playletVo.getSinglePrice().abs()));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        unifiedOrder.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setUnifiedorderRequest(unifiedOrder);
//        UnifiedorderResponse execute = Client.getClient().execute(request);
//        // 记录单个视频付费单
//        sohuPlayletPayService.saveVideoPay(userId, videoId, playletVo.getSinglePrice().abs(), posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
//        return execute;
//    }
//
//    /**
//     * 整部剧付费-mobile
//     *
//     * @param playletId
//     * @param request
//     * @param unifiedOrder
//     * @param extendParams
//     * @param userId
//     * @return
//     */
//    private UnifiedorderResponse getPlayletPayMobile(Long playletId, UnifiedorderRequest request, Unifiedorder unifiedOrder, ExtendParams extendParams, Long userId) {
//        SohuPlayletVo playletVo = sohuPlayletService.queryById(playletId);
//        if (ObjectUtil.isNull(playletVo)) {
//            throw new ServiceException("playlet does not exist");
//        }
//        if (!StrUtil.equalsAnyIgnoreCase(playletVo.getState(), CommonState.OnShelf.name())) {
//            throw new ServiceException("playlet status is incorrect");
//        }
//        Long count = sohuPlayletService.userNeedPayVideoCount(playletVo.getEpisodeRelevance(), userId);
//        if (count == null || count <= 0L) {
//            throw new ServiceException("playlet pay error");
//        }
//        // 备注
//        request.setMemo("整部剧付费-移动端");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.PLAYLET_PAY_PREFIX);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 单集乘以待付费金额
//        BigDecimal amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count));
//        if (playletVo.getIsPayLeave() != null && playletVo.getIsPayLeave() && playletVo.getDiscount() != null && CalUtils.isGreatZero(playletVo.getDiscount())) {
//            // 总金额 * 折扣
//            amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count), playletVo.getDiscount(), new BigDecimal(0.01));
//        }
//        unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(amount));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        unifiedOrder.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setUnifiedorderRequest(unifiedOrder);
//        log.info("整部剧付费请求参数：{}", JSONUtil.toJsonStr(request));
//        UnifiedorderResponse execute = Client.getClient().execute(request);
//        // 保存整部剧付费购买记录
//        sohuPlayletPayService.savePlayletPay(userId, playletId, count, amount, posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
//        return execute;
//    }
//
//    /**
//     * 狐币充值-手机端
//     *
//     * @param rechargeId
//     * @param request
//     * @param unifiedOrder
//     * @param extendParams
//     * @param userId
//     * @return
//     */
//    private UnifiedorderResponse getCoinPayMobile(Long rechargeId, UnifiedorderRequest request, Unifiedorder unifiedOrder, ExtendParams extendParams, Long userId) {
//        SohuRechargeListVo rechargeListVo = sohuRechargeListService.queryById(rechargeId);
//        if (ObjectUtil.isNull(rechargeListVo)) {
//            throw new ServiceException("recharge does not exist");
//        }
//        if (!StrUtil.equalsAnyIgnoreCase(rechargeListVo.getState(), CommonState.OnShelf.name())) {
//            throw new ServiceException("playlet status is incorrect");
//        }
//        // 备注
//        request.setMemo("狐币充值");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.VIRTUAL_CURRENCY_PREFIX);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 所需金额
//        BigDecimal amount = rechargeListVo.getAmount();
//        unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(amount));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        unifiedOrder.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setUnifiedorderRequest(unifiedOrder);
//        log.info("狐币充值请求参数：{}", JSONUtil.toJsonStr(request));
//        UnifiedorderResponse execute = Client.getClient().execute(request);
//        // 保存充值记录
//        SohuTradeRecordBo recordBo = new SohuTradeRecordBo(SohuTradeRecordEnum.Type.VirtualRecharge.getCode(), SohuTradeRecordEnum.Type.VirtualRecharge.getCode(), String.valueOf(rechargeId), amount, SohuTradeRecordEnum.Type.VirtualRecharge.getMsg(), SohuTradeRecordEnum.AmountType.InCome.getCode(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), Constants.CHANNEL_MOBILE, posSeq);
//        recordBo.setUserId(userId);
//        sohuTradeRecordService.insertByBo(recordBo);
//        return execute;
//    }
//
//    /**
//     * 商单保证金-pc
//     *
//     * @param videoId
//     * @param request
//     * @param scanpay
//     * @param extendParams
//     * @param userId
//     */
//    private ScanpayResponse getVideoPayPc(Long videoId, ScanpayRequest request, Scanpay scanpay, ExtendParams extendParams, Long userId) {
//        SohuVideoVo videoVo = sohuVideoService.selectVoById(videoId);
//        if (ObjectUtil.isNull(videoVo)) {
//            throw new ServiceException("video does not exist");
//        }
//        if (!StrUtil.equalsAnyIgnoreCase(videoVo.getState(), CommonState.OnShelf.name())) {
//            throw new ServiceException("Payment status is incorrect");
//        }
//        SohuPlayletVo playletVo = sohuPlayletService.queryByEpisodeRelevance(videoVo.getEpisodeRelevance());
//        // 备注
//        request.setMemo("单个视频付费-PC");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.VIDEO_PAY_PREFIX);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 总金额
//        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(playletVo.getSinglePrice().abs()));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        scanpay.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setScanpayRequest(scanpay);
//        ScanpayResponse scanpayResponse = Client.getClient().execute(request);
//        // 记录单个视频付费单
//        sohuPlayletPayService.saveVideoPay(userId, videoId, playletVo.getSinglePrice().abs(), posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
//        // 将接单id根据支付流水号保存到缓存里面
//        RedisUtils.setCacheObject(posSeq, videoId, Duration.ofMinutes(5));
//        return scanpayResponse;
//    }
//
//    /**
//     * 整部剧付费-pc
//     *
//     * @param playletId
//     * @param request
//     * @param scanpay
//     * @param extendParams
//     * @param userId
//     */
//    private ScanpayResponse getPlayletPayPc(Long playletId, ScanpayRequest request, Scanpay scanpay, ExtendParams extendParams, Long userId) {
//        SohuPlayletVo playletVo = sohuPlayletService.queryById(playletId);
//        if (ObjectUtil.isNull(playletVo)) {
//            throw new ServiceException("playlet does not exist");
//        }
//        if (!StrUtil.equalsAnyIgnoreCase(playletVo.getState(), CommonState.OnShelf.name())) {
//            throw new ServiceException("playlet status is incorrect");
//        }
//        Long count = sohuPlayletService.userNeedPayVideoCount(playletVo.getEpisodeRelevance(), userId);
//        if (count == null || count <= 0L) {
//            throw new ServiceException("playlet pay error");
//        }
//        // 备注
//        request.setMemo("整部剧付费-移动");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.PLAYLET_PAY_PREFIX);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 单集乘以待付费金额
//        BigDecimal amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count));
//        if (playletVo.getIsPayLeave() != null && playletVo.getIsPayLeave() && playletVo.getDiscount() != null && CalUtils.isGreatZero(playletVo.getDiscount())) {
//            // 总金额 * 折扣
//            amount = CalUtils.multiply(playletVo.getSinglePrice().abs(), new BigDecimal(count), playletVo.getDiscount(), new BigDecimal(0.01));
//        }
//        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(amount));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        scanpay.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setScanpayRequest(scanpay);
//        log.info("整部剧付费请求参数：{}", JSONUtil.toJsonStr(request));
//        ScanpayResponse scanpayResponse = Client.getClient().execute(request);
//        // 保存整部剧付费购买记录
//        sohuPlayletPayService.savePlayletPay(userId, playletId, count, amount, posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
//        // 将接单id根据支付流水号保存到缓存里面
//        RedisUtils.setCacheObject(posSeq, playletId, Duration.ofMinutes(5));
//        return scanpayResponse;
//    }
//
//    /**
//     * 狐币充值-pc
//     *
//     * @param rechargeId
//     * @param request
//     * @param scanpay
//     * @param extendParams
//     * @param userId
//     */
//    private ScanpayResponse getCoinPayPc(Long rechargeId, ScanpayRequest request, Scanpay scanpay, ExtendParams extendParams, Long userId) {
//        SohuRechargeListVo rechargeListVo = sohuRechargeListService.queryById(rechargeId);
//        if (ObjectUtil.isNull(rechargeListVo)) {
//            throw new ServiceException("recharge does not exist");
//        }
//        if (!StrUtil.equalsAnyIgnoreCase(rechargeListVo.getState(), CommonState.OnShelf.name())) {
//            throw new ServiceException("playlet status is incorrect");
//        }
//        // 备注
//        request.setMemo("狐币充值");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.VIRTUAL_CURRENCY_PREFIX);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 所需金额
//        BigDecimal amount = rechargeListVo.getAmount();
//        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(amount));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        scanpay.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setScanpayRequest(scanpay);
//        log.info("狐币充值请求参数：{}", JSONUtil.toJsonStr(request));
//        ScanpayResponse scanpayResponse = Client.getClient().execute(request);
//        // 保存充值记录
//        SohuTradeRecordBo recordBo = new SohuTradeRecordBo(SohuTradeRecordEnum.Type.VirtualRecharge.getCode(), SohuTradeRecordEnum.Type.VirtualRecharge.getCode(),
//                String.valueOf(rechargeId), amount, SohuTradeRecordEnum.Type.VirtualRecharge.getMsg(), SohuTradeRecordEnum.AmountType.InCome.getCode(),
//                PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), Constants.CHANNEL_PC, posSeq);
//        recordBo.setUserId(userId);
//        sohuTradeRecordService.insertByBo(recordBo);
//        // 将接单id根据支付流水号保存到缓存里面
//        RedisUtils.setCacheObject(posSeq, rechargeId, Duration.ofMinutes(5));
//        return scanpayResponse;
//    }
//
//    /**
//     * 商单保证金-pc
//     *
//     * @param receiveId
//     * @param request
//     * @param scanpay
//     * @param extendParams
//     * @param userId
//     */
//    private ScanpayResponse getBusyTaskPromisePay(Long receiveId, ScanpayRequest request, Scanpay scanpay, ExtendParams extendParams, Long userId) {
//        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryBusyTaskRece(receiveId);
//        if (ObjectUtil.isNull(taskReceiveModel)) {
//            throw new ServiceException("order receive does not exist");
//        }
//        if (!StrUtil.equalsAnyIgnoreCase(taskReceiveModel.getState(), "WaitApprove")) {
//            throw new ServiceException("Payment status is incorrect");
//        }
//        // 备注
//        request.setMemo("任务支付保证金");
//        //第三方支付单号
//        String posSeq = NumberUtil.getOrderNo(OrderConstants.BUSY_TASK_PROMISE_SUBUNIT);
//        // 唯一订单号
//        request.setPosSeq(posSeq);
//        // 总金额
//        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(taskReceiveModel.getAmount()));
//        // 不分账 -- R实时分账 --D延时分账
//        extendParams.setSplitFlag("N");
//        scanpay.setExtendParams(extendParams);
//        // 设置请求参数
//        request.setScanpayRequest(scanpay);
//        ScanpayResponse scanpayResponse = Client.getClient().execute(request);
//        log.info("任务支付保证金请求返回：{}", JSONUtil.toJsonStr(scanpayResponse));
//        remoteBusyOrderService.saveBusyOrderPay(taskReceiveModel.getTaskNumber(), posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), taskReceiveModel.getAmount(), PayObject.BusyTaskPromise.name(), 0L, taskReceiveModel.getUserId(), 0L);
//        // 将接单id根据支付流水号保存到缓存里面
//        RedisUtils.setCacheObject(posSeq, receiveId, Duration.ofMinutes(5));
//        return scanpayResponse;
//    }
//
//
//    @Override
//    public SohuPayResultModel paySuccess(SohuPayQueryBo payQueryBo) {
//        WechatPayOrderQueryRequest queryRequest = new WechatPayOrderQueryRequest();
//        queryRequest.setOutTradeNo(payQueryBo.getOrderNo());
//        // 回调时不是支付成功状态先主动查询，如果主动查询状态也是不是SUCCESS就返回false
//        WechatPayQueryResponse wechatPayQueryResponse;
//        SohuPayResultModel payPalResultResponse = new SohuPayResultModel();
//        try {
//            wechatPayQueryResponse = WechatPayService.queryOrder(queryRequest, getWechatPayConfig(payQueryBo.getPayType()));
//            if (!wechatPayQueryResponse.getTradeState().equals("SUCCESS") || ObjectUtil.isNull(wechatPayQueryResponse) || wechatPayQueryResponse.getCode().equals("ORDER_NOT_EXIST")) {
//                payPalResultResponse.setStatus(Boolean.FALSE).setOrderNo(payQueryBo.getOrderNo());
//                return payPalResultResponse;
//            }
//            BigDecimal centToYuanTotal = BigDecimalUtils.centToYuan(new BigDecimal(wechatPayQueryResponse.getAmount().getPayerTotal()));
//            // 返回支付成功相关结果
//            payPalResultResponse.setPayPrice(centToYuanTotal).setOrderNo(wechatPayQueryResponse.getOutTradeNo()).setStatus(Boolean.TRUE);
//        } catch (WechatPayException e) {
//            throw new RuntimeException(e);
//        }
//        return payPalResultResponse;
//    }
//
//    @Override
//    public void payCallback(HttpServletRequest request) {
//
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public String payCallback(CallbackRequest request) {
//        log.info("yima notify message : " + request);
//        // 支付来源
//        //int paySource = 0;
//        PaySourceEnum paySourceEnum = null;
//        String outTradeNo = request.getPosSeq();
//        if (outTradeNo.startsWith(OrderConstants.ORDER_PREFIX_PLATFORM)) {
//            //paySource = 1;
//            paySourceEnum = PaySourceEnum.SHOP_ORDER_PAY;
//        } else if (outTradeNo.startsWith(OrderConstants.BUSY_ORDER_PREFIX)) {
//            //paySource = 2;
//            paySourceEnum = PaySourceEnum.BUSY_ORDER_PREPAY;
//        } else if (outTradeNo.startsWith(OrderConstants.BUSY_RECE_ORDER_PREFIX)) {
//            //paySource = 4;
//            paySourceEnum = PaySourceEnum.TASK_PARTY_PAY;
//        } else if (outTradeNo.startsWith(OrderConstants.MCN_PREFIX_MASTER)) {
//            //paySource = 5;
//            paySourceEnum = PaySourceEnum.MCN_ORDER_PAY;
//        } else if (outTradeNo.startsWith(OrderConstants.BUSY_TASK_PROMISE_SUBUNIT)) {
//            //paySource = 6;
//            paySourceEnum = PaySourceEnum.BUSY_TASK_PROMISE_PAY;
//        } else if (outTradeNo.startsWith(OrderConstants.VIDEO_PAY_PREFIX)) {
//            //paySource = 7;
//            paySourceEnum = PaySourceEnum.VIDEO_PAY;
//        } else if (outTradeNo.startsWith(OrderConstants.PLAYLET_PAY_PREFIX)) {
//            //paySource = 8;
//            paySourceEnum = PaySourceEnum.PLAYLET_PAY;
//        } else if (outTradeNo.startsWith(OrderConstants.VIRTUAL_CURRENCY_PREFIX)) {
//            //paySource = 8;
//            paySourceEnum = PaySourceEnum.SOHU_VIRTUAL_CURRENCY_RECHARGE;
//        } else if (outTradeNo.startsWith(OrderConstants.BUSY_RECE_AFTER_SALES_PREFIX)) {
//            //paySource = 10;
//            paySourceEnum = PaySourceEnum.TASK_AFTER_SALES_PAY;
//        }
//        // 支付状态不为已支付不走修改订单逻辑
//        if (!request.getStatus().equals("1")) {
//            throw new ServiceException("未支付成功");
//        }
//        /*
//         * paySource = 1 商城商品
//         * paySource = 2 商单-支付预付款
//         * paySource = 3 商户付款码扫码支付
//         * paySource = 4 发单方-支付接单酬金
//         * paySource = 5 MCN订单支付
//         * paySource = 6 发单方-任务保证金支付
//         */
//        if (Objects.nonNull(paySourceEnum)) {
//            switch (paySourceEnum) {
//                case SHOP_ORDER_PAY:
//                    SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(outTradeNo);
//                    // todo 更新用户下单数量
////            user.setPayCount(user.getPayCount() + 1);
//                    // 添加:系统内部的订单号、支付流水号、修改支付状态
//                    masterOrder.setOutTradeNo(outTradeNo).setTransactionId(request.getTradeNo()).setPaid(true).setChargePrice(CalUtils.centToYuan(new BigDecimal(request.getChargeAmount())));
//                    List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(masterOrder.getOrderNo());
//                    log.warn("storeOrderList pay now 计算每个子单的手续费之前: {}", JSONObject.toJSONString(storeOrderList));
//                    // 计算每个子单的手续费
//                    distributeFee(storeOrderList, masterOrder.getPayPrice(), masterOrder.getChargePrice());
//                    log.warn("storeOrderList pay update 计算每个子单的手续费: {}", JSONObject.toJSONString(storeOrderList));
//                    // todo 帐单记录
////            Bill bill = billInit(masterOrder);
////            List<MerchantBill> merchantBillList = merchantBillInit(storeOrderList);
//                    // 添加支付成功修改订单状态  --弃用redis队列
//                    Boolean execute = transactionTemplate.execute(e -> {
//                        for (SohuShopOrderModel storeOrder : storeOrderList) {
//                            storeOrder.setPaid(true);
//                            storeOrder.setPayTime(new Date());
//                            storeOrder.setPayType(masterOrder.getPayType());
//                            storeOrder.setStatus(OrderConstants.ORDER_STATUS_SHIPPING);
//                        }
//                        remoteShopOrderService.updateBatchById(storeOrderList);
//                        // todo 用户相关
////                userService.updateById(user);
//                        // todo 帐单记录
////                billService.save(bill);
////                merchantBillList.forEach(b -> b.setPid(bill.getId()));
////                merchantBillService.saveBatch(merchantBillList);
//                        remoteMasterOrderService.updateById(masterOrder);
//                        return Boolean.TRUE;
//                    });
//                    if (Boolean.FALSE.equals(execute)) {
//                        log.error("wechat pay error : 订单更新失败==》" + masterOrder.getOutTradeNo());
//                        throw new ServiceException("订单回调修改数据失败");
//                    }
//                    // todo 队列修改-支付成功相关处理
//                    // 创建订单日志
////                    createOrderLogs(storeOrderList);
//                    // 订单支付成功后续处理
////                paySyncService.payOrder(masterOrder);
////                payOrderService.delayConfirm(masterOrder.getOrderNo());
//
//                    // todo 赠送优惠券
//                    return "success";
//                case BUSY_ORDER_PREPAY:
//                    SohuBusyOrderPayModel busyOrderPayModel = remoteBusyOrderService.queryBusyOrderPayByOrderNo(outTradeNo);
//                    if (Objects.isNull(busyOrderPayModel)) {
//                        throw new ServiceException("商单预付款记录不存在");
//                    }
//                    remoteBusyOrderService.updateBusyOrderSuccess(outTradeNo, request.getTradeNo());
//                    return "success";
//                case MERCHANT_CODE_PAY:
//
//                    break;
//                case TASK_PARTY_PAY:
//                    // 手续费
//                    BigDecimal chargeAmount = CalUtils.centToYuan(new BigDecimal(request.getChargeAmount()));
//                    // todo
//                    SohuIndependentOrder independentOrder = independentOrderService.selectByOrderAndUserId(2L, outTradeNo);
//                    if (ObjectUtils.isNotNull(independentOrder)) {
//                        independentOrder.setIndependentPrice(CalUtils.sub(independentOrder.getIndependentPrice(), chargeAmount));
//                        SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
//                        BeanUtils.copyProperties(independentOrder, independentOrderBo);
//                        // 修改管理员扣除手续费后的金额
//                        independentOrderService.updateByBo(independentOrderBo);
//                    }
//                    // 更新分账单表
//                    independentOrderService.updateIndependentStatus(outTradeNo, BusyType.BusyOrder.name(), 1);
//                    SohuBusyOrderPayModel busyOrderPayModelReceive = remoteBusyOrderService.queryBusyOrderPayByOrderNo(outTradeNo);
//                    if (Objects.isNull(busyOrderPayModelReceive)) {
//                        throw new ServiceException("发单方支付酬金记录不存在");
//                    }
//                    // 第三方支付流水号
//                    busyOrderPayModelReceive.setTransactionId(request.getTradeNo());
//                    // 账单、分账单、执行记录相关
//                    remoteBusyOrderService.updateBusyOrderReceiveSuccess(outTradeNo, request.getTradeNo());
//                    // 是否是佣金支付
//                    boolean isIndependent = RedisUtils.isExistsObject(outTradeNo + "isIndependentAmount");
//                    if (isIndependent) {
//                        busyOrderPayModelReceive.setIsIndependentAmount(Boolean.TRUE);
//                    }
//                    // 是否是一次性支付所有酬金
//                    boolean isAll = RedisUtils.isExistsObject(outTradeNo + "allAmount");
//                    if (isAll) {
//                        busyOrderPayModelReceive.setAllAmount(Boolean.TRUE);
//                    }
//                    // 支付回调后续相关状态操作
//                    MqMessaging mqReceive = new MqMessaging(JSONUtil.toJsonStr(busyOrderPayModelReceive), "busy_task_pay_party");
//                    remoteStreamMqService.sendDelayMsg(mqReceive, 2L);
//                    return "success";
//                case TASK_AFTER_SALES_PAY:
//                    this.payCallbackOfAfterSalesOrderPay(outTradeNo, request);
//                    return "success";
//                case MCN_ORDER_PAY:
//                    this.payCallbackOfMcnOrderPay(outTradeNo, request);
//                    return "success";
//                case BUSY_TASK_PROMISE_PAY:
//                    SohuBusyOrderPayModel payModel = remoteBusyOrderService.queryBusyOrderPayByOrderNo(outTradeNo);
//                    if (Objects.isNull(payModel)) {
//                        throw new ServiceException("任务支付保证金记录不存在");
//                    }
//                    remoteBusyOrderService.updateBusyOrderSuccess(outTradeNo, request.getTradeNo());
//                    boolean exists = RedisUtils.isExistsObject(outTradeNo);
//                    // 如果存在直接唤醒
//                    if (exists) {
//                        Long receiveId = RedisUtils.getCacheObject(outTradeNo);
//                        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryBusyTaskRece(receiveId);
//                        // 走修改其他子单、主单逻辑
//                        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(taskReceiveModel), "busy_task_receive");
//                        remoteStreamMqService.sendDelayMsg(mqMessaging, 1L);
//                    }
//                    return "success";
//                case VIDEO_PAY:
//                    Boolean successVideo = sohuPlayletPayService.paySuccess(outTradeNo, request.getTradeNo());
//                    return successVideo ? "success" : "fail";
//                case PLAYLET_PAY:
//                    Boolean successPlaylet = sohuPlayletPayService.paySuccess(outTradeNo, request.getTradeNo());
//                    return successPlaylet ? "success" : "fail";
//                case SOHU_VIRTUAL_CURRENCY_RECHARGE:
//                    Boolean successTrade = sohuTradeRecordService.updatePayStatus(outTradeNo, request.getTradeNo(), PayStatus.Paid);
//                    return successTrade ? "success" : "fail";
//                default:
//
//            }
//        }
//        return "success";
//    }
//
//
//    /**
//     * 售后支付酬金回调
//     *
//     * @param outTradeNo
//     * @param request
//     */
//    private void payCallbackOfAfterSalesOrderPay(String outTradeNo, CallbackRequest request) {
//        BigDecimal chargeAmount = CalUtils.centToYuan(new BigDecimal(request.getChargeAmount()));
//        // todo
//        SohuIndependentOrder independentOrder = independentOrderService.selectByOrderAndUserId(2L, outTradeNo);
//        if (ObjectUtils.isNotNull(independentOrder)) {
//            independentOrder.setIndependentPrice(CalUtils.sub(independentOrder.getIndependentPrice(), chargeAmount));
//            SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
//            BeanUtils.copyProperties(independentOrder, independentOrderBo);
//            // 修改管理员扣除手续费后的金额
//            independentOrderService.updateByBo(independentOrderBo);
//        }
//        // 更新分账单表
//        independentOrderService.updateIndependentStatus(outTradeNo, BusyType.BusyOrder.name(), 1);
//        SohuBusyOrderPayModel busyOrderPayModelReceive = remoteBusyOrderService.queryBusyOrderPayByOrderNo(outTradeNo);
//        if (Objects.isNull(busyOrderPayModelReceive)) {
//            throw new ServiceException("发单方支付酬金记录不存在");
//        }
//        // 第三方支付流水号
//        busyOrderPayModelReceive.setTransactionId(request.getTradeNo());
//        // 账单、分账单、执行记录相关
//        remoteBusyOrderService.updateBusyOrderReceiveSuccess(outTradeNo, request.getTradeNo());
//        // 是否是佣金支付
//        busyOrderPayModelReceive.setIsAfterSalesAmount(Boolean.TRUE);
//        // 是否是一次性支付所有酬金
//        boolean isAll = RedisUtils.isExistsObject(outTradeNo + "allAmount");
//        if (isAll) {
//            busyOrderPayModelReceive.setAllAmount(Boolean.TRUE);
//        }
//        // 支付回调后续相关状态操作
//        MqMessaging mqReceive = new MqMessaging(JSONUtil.toJsonStr(busyOrderPayModelReceive), "busy_task_pay_party");
//        remoteStreamMqService.sendDelayMsg(mqReceive, 1L);
//    }
//
//    /**
//     * 支付回调-MCN订单支付-开通一键分发功能
//     */
//    private void payCallbackOfMcnOrderPay(String outTradeNo, CallbackRequest request) {
//        SohuMcnMasterOrderModel masterOrder = remoteMcnOrderService.selectByMasterOutTradeNo(outTradeNo);
//        // 添加:系统内部的订单号、支付流水号、修改支付状态
//        masterOrder.setOutTradeNo(outTradeNo).setTransactionId(request.getTradeNo()).setPaid(true).setChargePrice(CalUtils.centToYuan(new BigDecimal(request.getChargeAmount())));
//        List<SohuMcnOrderModel> mcnOrderList = remoteMcnOrderService.getListByMasterNo(masterOrder.getOrderNo());
//        log.warn("mcnOrderList pay now 计算每个子单的手续费之前: {}", JSONObject.toJSONString(mcnOrderList));
//        // 计算每个子单的手续费
//        distributeFeeOfMcn(mcnOrderList, masterOrder.getPayPrice(), masterOrder.getChargePrice());
//        log.warn("mcnOrderList pay update 计算每个子单的手续费: {}", JSONObject.toJSONString(mcnOrderList));
//        // 添加支付成功修改订单状态  --弃用redis队列
//        Boolean execute = transactionTemplate.execute(e -> {
//            for (SohuMcnOrderModel storeOrder : mcnOrderList) {
//                storeOrder.setPaid(true);
//                storeOrder.setPayTime(new Date());
//                storeOrder.setPayType(masterOrder.getPayType());
//                storeOrder.setStatus(OrderConstants.ORDER_STATUS_OVER);
//            }
//            remoteMcnOrderService.updateBatchById(mcnOrderList);
//            remoteMcnOrderService.updateMcnMasterOrderByMasterId(masterOrder);
//            return Boolean.TRUE;
//        });
//        if (Boolean.FALSE.equals(execute)) {
//            log.error("wechat pay error : 订单更新失败==》" + masterOrder.getOutTradeNo());
//            throw new ServiceException("订单回调修改数据失败");
//        }
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public Boolean refund(SohuOrderRefundModel refundModel) {
//        SohuShopRefundOrderModel refundOrder = remoteRefundOrderService.getByRefundOrderNo(refundModel.getRefundOrderNo());
//        List<SohuMerchantModel> sohuMerchantModels = remoteMerchantService.selectByUserId(LoginHelper.getUserId());
//        List<Long> merIds = sohuMerchantModels.stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
//        if (!merIds.contains(refundOrder.getMerId())) {
//            throw new ServiceException("无法操作非自己商户的订单");
//        }
//        SohuShopOrderModel storeOrder = remoteShopOrderService.getByOrderNo(refundOrder.getShopOrderNo());
//        if (!storeOrder.getPaid()) {
//            throw new ServiceException("未支付无法退款");
//        }
//        // 查询翼码支付配置
//        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
//        // 组装微信小程序退款请求参数
//        BarcodeReverseRequest request = new BarcodeReverseRequest();
//        request.setPosId(yiMaPayConfig.getPosId());
//        request.setIsspid(yiMaPayConfig.getIssPid());
//        request.setSystemId(yiMaPayConfig.getSystemId());
//        request.setStoreId(yiMaPayConfig.getStoreId());
//        // 退款参数封装
//        BarcodeReverse reverse = new BarcodeReverse();
//        reverse.setPayType(yiMaPayConfig.getPayType());
//        /*
//         * paySource = 1 商城商品
//         * paySource = 2 商单-支付预付款
//         * paySource = 3 商户付款码扫码支付
//         * paySource = 4 发单方-支付接单酬金
//         */
//        switch (refundModel.getPaySource()) {
//            case 1:
//                List<GoodsDetail> goodsDetailList = null;
//                // 存在退单的则将商品信息传入
//                if (StringUtils.isNotBlank(refundOrder.getMasterOrderNo())) {
//                    goodsDetailList = this.exchangeRefundGoodsDetailList(refundOrder.getShopOrderNo());
//                }
//                // 查询主订单信息
//                SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(refundOrder.getMasterOrderNo());
//                request.setPosSeq(refundOrder.getRefundOrderNo());
//                // 支付请求流水号
//                reverse.setOrgPosSeq(masterOrder.getOrderNo());
//                // 退款商品信息
//                reverse.setGoodsDetail(goodsDetailList);
//                // 所有退款单
////                List<SohuShopRefundOrderModel> refundOrderModels = remoteRefundOrderService.queryListByMasterOrderNo(masterOrder.getOrderNo());
//                // 退款金额
//                if (storeOrder.getStatus().equals(OrderConstants.ORDER_STATUS_OVER) && CollectionUtils.isNotEmpty(independentOrderService.queryListByOrderNo(storeOrder.getOrderNo()))) {
//                    if (null != refundModel.getIsDelayIndependent()) {
//                        reverse.setTxAmt(BigDecimalUtils.yuanToFen(refundModel.getIsDelayIndependent()));
//                    } else {
//                        // 总金额减去手续费
//                        reverse.setTxAmt(BigDecimalUtils.yuanToFen(storeOrder.getPayPrice()));
//                    }
//                } else {
//                    // 直接退总金额
//                    reverse.setTxAmt(BigDecimalUtils.yuanToFen(storeOrder.getPayPrice()));
//                }
//                request.setBarcodeReverseRequest(reverse);
//                // todo 平台分销退款，是否有分销人、拉新人。售后效期
//                // 微信小程序发起退款接口
//                BarcodeReverseResponse response = Client.getClient().execute(request);
//                // 判断微信退款状态
//                List<String> resultList = Lists.newArrayList("9998", "0000");
//                if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
//                    log.error("微信退款异常");
//                    throw new RuntimeException(response.getResult().getComment());
//                }
//                // 修改订单退款状态
//                storeOrder.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_REFUNDING);
//                refundOrder.setRefundStatus(OrderConstants.MERCHANT_REFUND_ORDER_STATUS_REFUNDING);
//                refundOrder.setRefundTime(new Date());
//                Boolean execute = transactionTemplate.execute(e -> {
//                    remoteRefundOrderService.updateById(refundOrder);
//                    remoteShopOrderService.updateById(storeOrder);
//                    // 新增日志
//                    remoteShopOrderStatusService.saveRefund(storeOrder.getOrderNo(), refundOrder.getRefundPrice(), "退款中");
//                    remoteRefundOrderStatusService.createLog(refundOrder.getRefundOrderNo(), OrderConstants.REFUND_ORDER_LOG_TYPE_APPLY, OrderConstants.ORDER_LOG_MESSAGE_REFUND_PRICE.replace("{amount}", refundOrder.getRefundPrice().toString()));
//                    return Boolean.TRUE;
//                });
//                if (Boolean.FALSE.equals(execute)) {
//                    remoteShopOrderStatusService.saveRefund(storeOrder.getOrderNo(), storeOrder.getPayPrice(), "失败");
//                    throw new ServiceException("订单更新失败");
//                }
//                // 5s后延迟 消息队列去做退款回调的事情-主动查询退款结果、订单状态、商品回滚
//                // todo 去消息队列查询退款状态去添加 -修改主订单信息——添加第三方退款单号
////                refundOrder.setRefundId(response.getWxpayResInfo().getTradeNo());
//                MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(refundOrder.getRefundOrderNo()), MqKeyEnum.YI_MA_REFUND_PAY.getKey());
//                remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
//                return Boolean.TRUE;
//            case 2:
//            case 3:
//                break;
//        }
//
//        return Boolean.FALSE;
//    }
//
//    @Override
//    @Async
//    public Boolean refund(SohuBusyOrderPayModel sohuBusyOrderPayModel) {
//        log.info("执行商单预付款退款逻辑:{}", JSONUtil.toJsonStr(sohuBusyOrderPayModel));
//        String busyOrder = sohuBusyOrderPayModel.getBusyOrder();
//        // todo
////        SohuBusyOrderModel sohuBusyOrderModel = remoteBusyOrderService.queryBusyOrder(busyOrder);
////        SohuBusyOrderModel sohuBusyOrderModel = remoteBusyOrderService.queryBusyOrder(1L);
////        SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(busyOrder);
//        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(busyOrder, Boolean.TRUE);
//        SohuBusyOrderPayModel orderPayModel = remoteBusyOrderService.queryBusyOrderPay(busyOrder, PayObject.BusyTaskPromise.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
//        // 商单接单人数上限
////        Integer applyMax = taskSiteModel.getApplyMax();
//
////        Long count = remoteBusyOrderService.receCount(busyOrder);
////        Long count = remoteBusyOrderService.receCount(1L);
////        if (count != null && applyMax != null && count.intValue() == applyMax.intValue()) {
//        if (orderPayModel.getPayAmount() != null && CalUtils.isZero(taskReceiveModel.getAmount()) && StrUtil.equalsAnyIgnoreCase(orderPayModel.getPayStatus(), PayStatus.Paid.name())) {
//            remoteBusyOrderService.updateBusyOrderAndRece(orderPayModel.getId());
//            return Boolean.TRUE;
//        }
////            SohuBusyOrderPayModel busyOrderPayModel = remoteBusyOrderService.queryBusyOrderPay(busyOrder,
////                    PayObject.BusyOrder.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
////        SohuBusyOrderPayModel busyOrderPayModel = remoteBusyOrderService.queryBusyOrderPay(1L,
////                PayObject.BusyOrder.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
//        // 查询翼码支付配置
//        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
//        // 组装微信小程序退款请求参数
//        BarcodeReverseRequest request = new BarcodeReverseRequest();
//        request.setPosId(yiMaPayConfig.getPosId());
//        request.setIsspid(yiMaPayConfig.getIssPid());
//        request.setSystemId(yiMaPayConfig.getSystemId());
//        request.setStoreId(yiMaPayConfig.getStoreId());
//        // 退款参数封装
//        BarcodeReverse reverse = new BarcodeReverse();
//        reverse.setPayType(yiMaPayConfig.getPayType());
//        // 请求退款单号拼接
//        request.setPosSeq("R" + System.nanoTime());
//        // 支付请求流水号
//        reverse.setOrgPosSeq(orderPayModel.getOrderNo());
//        reverse.setTxAmt(BigDecimalUtils.yuanToFen(orderPayModel.getPayAmount()));
//        request.setBarcodeReverseRequest(reverse);
//        log.info("商单预付款退款请求:{}", JSONUtil.toJsonStr(request));
//
//        BarcodeReverseResponse response = Client.getClient().execute(request);
//        List<String> resultList = Lists.newArrayList("9998", "0000");
//        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
//            log.error("商单预付款退款异常：{}", JSONUtil.toJsonStr(response));
//            throw new RuntimeException(response.getResult().getComment());
//        }
//        orderPayModel.setPayableAmount(orderPayModel.getPayAmount());
//        remoteBusyOrderService.savePrepaymentRefundBillRecord(orderPayModel);
//        // 更新商单状态完成，且正在合作中的接单改为终止
////        remoteBusyOrderService.updateBusyOrderAndRece(orderPayModel.getId());
//        return Boolean.TRUE;
////        }
////        return Boolean.FALSE;
//    }
//
//    @Override
//    public Boolean refundCallback(HttpServletRequest wxRequest) {
//        return null;
//    }
//
//    @Override
//    public Boolean refundSuccess(String refundOrderNo) {
//        try {
//            // 退款查询 商家退款单号--微信outRefundNo
//            WechatPayRefundQueryRequest refundRequest = new WechatPayRefundQueryRequest();
//            // 微信退款查询参数--退款单号
//            refundRequest.setOutRefundNo(refundOrderNo);
//            WechatPayRefundQueryResponse queryResponse = WechatPayService.refundQuery(refundRequest, getWechatPayConfig(PayTypeEnum.PAY_TYPE_WECHAT_JSAPI.getStatus()));
//            if (!queryResponse.isSuccess() && !queryResponse.getStatus().equals("SUCCESS")) {
//                return Boolean.FALSE;
//            }
//            return Boolean.TRUE;
//        } catch (WechatPayException e) {
//            throw new RuntimeException(e.getMessage());
//        }
//    }
//
//    /**
//     * 退款商品信息-YiMa
//     *
//     * @param merOrderNo
//     */
//    private List<GoodsDetail> exchangeRefundGoodsDetailList(String merOrderNo) {
//        // 商户商品订单表
//        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(merOrderNo);
//        // 微信支付请求需要的商品详情集合
//        List<GoodsDetail> goodsDetailList = Lists.newArrayList();
//        for (SohuShopOrderModel storeOrder : storeOrderList) {
//            // 微信支付请商品详情
//            GoodsDetail goodsDetail = new GoodsDetail();
//            // 订单详情表
//            List<SohuShopOrderInfoModel> storeOrderInfos = remoteShopOrderInfoService.getListByOrderNo(storeOrder.getOrderNo());
//            // 获取商品数量+id
//            Map<Long, Integer> productMap = storeOrderInfos.stream().collect(Collectors.toMap(
//                    // 键是 getProductId
//                    SohuShopOrderInfoModel::getProductId,
//                    // 值是 getPayNum
//                    SohuShopOrderInfoModel::getPayNum));
//            // 商品详情-当前订单所有购买的商品id
//            List<SohuProductModel> storeProductList = remoteProductService.listByIds(new ArrayList<>(productMap.keySet()));
//            for (SohuProductModel storeProduct : storeProductList) {
//                goodsDetail.setGoodsId(storeProduct.getId().toString());
//                goodsDetail.setGoodsName(storeProduct.getStoreName());
//                goodsDetail.setPrice(BigDecimalUtils.yuanToFen(storeProduct.getPrice()));
//                // 根据商品id与订单详情表商品id匹配，设置商品购买数量
//                goodsDetail.setQuantity(productMap.get(storeProduct.getId()));
//                goodsDetail.setBody(storeProduct.getStoreName());
//                goodsDetailList.add(goodsDetail);
//            }
//        }
//        return goodsDetailList;
//    }
//
//    /**
//     * 根据主订单号查询所有商品详情并组装商品详细数据
//     *
//     * @param orderNo
//     * @return WechatPayUnifiedOrderRequest.DiscountDetail
//     */
//    private List<GoodsDetail> exchangeGoodsDetailList(String orderNo) {
//        // 商户商品订单表
//        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(orderNo);
//        // 微信支付请求需要的商品详情集合
//        List<GoodsDetail> goodsDetailList = Lists.newArrayList();
//        for (SohuShopOrderModel storeOrder : storeOrderList) {
//            // 订单详情表
//            List<SohuShopOrderInfoModel> storeOrderInfos = remoteShopOrderInfoService.getListByOrderNo(storeOrder.getOrderNo());
//            //
//            // 获取商品数量+id
//            Map<Long, Integer> productMap = storeOrderInfos.stream().collect(Collectors.toMap(
//                    // 键是 getProductId
//                    SohuShopOrderInfoModel::getProductId,
//                    // 值是 getPayNum
//                    SohuShopOrderInfoModel::getPayNum));
//            // 商品详情-当前订单所有购买的商品id
//            List<SohuProductModel> storeProductList = remoteProductService.listByIds(new ArrayList<>(productMap.keySet()));
//            for (SohuProductModel storeProduct : storeProductList) {
//                // 微信支付请商品详情
//                GoodsDetail goodsDetail = new GoodsDetail();
//                goodsDetail.setGoodsId(storeProduct.getId().toString());
//                goodsDetail.setGoodsName(storeProduct.getStoreName());
//                goodsDetail.setPrice(BigDecimalUtils.yuanToFen(storeProduct.getPrice()));
//                // 根据商品id与订单详情表商品id匹配，设置商品购买数量
//                goodsDetail.setQuantity(productMap.get(storeProduct.getId()));
//                goodsDetail.setBody(storeProduct.getStoreName());
//                goodsDetailList.add(goodsDetail);
//            }
//        }
//        return goodsDetailList;
//    }
//
//    /**
//     * 根据支付方式查询支付配置
//     *
//     * @param payType
//     * @return WechatPayConfig
//     */
//    private WechatPayConfig getWechatPayConfig(String payType) {
//        SysDictData dictData = remoteDictService.getDictData(DictEnum.payConfig.getKey(), payType);
//        if (Objects.isNull(dictData)) {
//            log.error("{}配置为空", PayTypeEnum.PAY_TYPE_WECHAT_JSAPI.getDescription());
//            return null;
//        }
//        WechatPayConfig wechatPayConfig = JSONObject.parseObject(dictData.getDictValue(), WechatPayConfig.class);
//        wechatPayConfig.setTradeType(WechatPayConstants.TradeType.JSAPI);
//        wechatPayConfig.setSignType("MD5");
//        return wechatPayConfig;
//    }
//
//    /**
//     * 商单接单分账算价
//     *
//     * @param templateModel         分账模板
//     * @param busyOrderReceivePrice 商单接单酬金（商单价值+佣金）  12
//     * @param independentPrice      商单分销佣金 2
//     * @param independentOrder      是否是分销单
//     * @param inviteUser            是否是拉新
//     * @param agencyId              是否有代理人
//     * @param independentInviteId   是否有分销人
//     * @param isIndependentAmount   是否是佣金支付
//     */
//    private Map<String, BigDecimal> calculateBusyOrderDistribution(SohuIndependentTemplateModel templateModel, BigDecimal busyOrderReceivePrice, BigDecimal independentPrice, Boolean independentOrder, Boolean inviteUser, Long independentInviteId, Boolean agencyId, Boolean isIndependentAmount) {
//        log.info("templateModel、independentPrice：{}、{}", JSONUtil.toJsonStr(templateModel), independentPrice);
//        // 分账对象
//        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
//        // 平台总分账金额 = 平台总手续费 * 平台分账比例
//        BigDecimal platPrice = null;
//        if (isIndependentAmount) {
//            if (CalUtils.isGreatZero(independentPrice)) {
//                if (independentOrder) {
//                    // 分销人佣金百分比
//                    BigDecimal distributorRatioPercentage = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
//                    BigDecimal distributorPrice = CalUtils.multiply(independentPrice, distributorRatioPercentage);
//                    log.warn("distributorPrice : {}", distributorPrice);
//                    // 分销人金额
//                    orderIndependentPrice.setDistributorPrice(distributorPrice);
//                    if (null != independentInviteId && independentInviteId > 0L) {
//                        // 分销人的拉新人金额
//                        orderIndependentPrice.setDistributorInvitePrice(CalUtils.sub(independentPrice, distributorPrice));
//                    } else {
//                        // 剩下的钱给平台
//                        platPrice = CalUtils.sub(independentPrice, distributorPrice);
//                    }
//                } else {
//                    // 没有分销人全部给平台
//                    platPrice = independentPrice;
//                }
//            }
//        } else {
//            // 平台手续费百分比-3.9%
//            BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
//            // 平台百分比
//            BigDecimal adminDivide = BigDecimalUtils.divide(templateModel.getAdminRatio(), CalUtils.PERCENTAGE);
//            // 消费者拉新人百分比
//            BigDecimal consumerDivide = BigDecimalUtils.divide(templateModel.getConsumerInviteRatio(), CalUtils.PERCENTAGE);
//            // 平台总手续费 = 商品价格 * 平台手续费比例 不计分拥金额
//            BigDecimal independentPlatformPrice = busyOrderReceivePrice.multiply(platformDivide).setScale(2, RoundingMode.HALF_UP);
//            log.warn("平台总手续费：{}", independentPlatformPrice);
//            BigDecimal consumerInvitePrice;
//            if (inviteUser) {
//                platPrice = independentPlatformPrice.multiply(adminDivide).setScale(2, RoundingMode.HALF_UP);
//                consumerInvitePrice = independentPlatformPrice.multiply(consumerDivide).setScale(2, RoundingMode.HALF_UP);
//            } else {
//                platPrice = independentPlatformPrice.multiply(CalUtils.add(adminDivide, consumerDivide)).setScale(2, RoundingMode.HALF_UP);
//                consumerInvitePrice = BigDecimal.ZERO;
//            }
//            log.warn("consumerInvitePrice : {}", consumerInvitePrice);
//            // 消费者拉新人分账金额 = 平台总分账金额 - 平台分账金额
//            orderIndependentPrice.setInvitePrice(consumerInvitePrice);
//            // 分红总金额 =  平台总手续费 - 平台分账金额 - 消费者拉新人分账金额
//            BigDecimal sharePrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice);
//            log.warn("sharePrice : {}", sharePrice);
//            // 国家站站长分账金额 = 分红总金额 * 国家站站长分账比例
//            BigDecimal countryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCountryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//            orderIndependentPrice.setCountryPrice(countryPrice);
//            log.warn("countryPrice : {}", countryPrice);
//            BigDecimal agencyPrice = BigDecimal.ZERO;
//            // 城市站站长分账金额 = 分红总金额 * 城市站站长分账比例
//            BigDecimal cityPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//            log.warn("cityPrice : {}", cityPrice);
//            // 代理算价
//            if (agencyId && null != templateModel.getAgencyRatio() && !BigDecimal.ZERO.equals(BigDecimalUtils.divide(templateModel.getAgencyRatio(), CalUtils.PERCENTAGE))) {
//                // 代理分账金额 = 平台总手续费 -（平台分账金额+国家站长分账金额+城市站长分账金额）
//                agencyPrice = CalUtils.sub(independentPlatformPrice, platPrice, countryPrice);
//            } else {
//                // 剩下的钱
//                BigDecimal lastPrice = CalUtils.sub(sharePrice, countryPrice, cityPrice);
//                platPrice = CalUtils.add(platPrice, lastPrice);
//            }
//            log.warn("platPrice : {}", platPrice);
//            // 如果是分销订单并且有拉新人就计算拉新人的费用
//            // 扣除平台手续费后的分销佣金
////        BigDecimal distributionPrice = CalUtils.multiply(independentPrice, subtractPlatform);
//            // 分销人佣金百分比
//            BigDecimal distributorRatioPercentage = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
//            log.info("distributionPrice:{}", independentPrice);
//            if (CalUtils.isGreatZero(independentPrice)) {
//                if (independentOrder) {
//                    BigDecimal distributorPrice = CalUtils.multiply(independentPrice, distributorRatioPercentage);
//                    log.warn("distributorPrice : {}", distributorPrice);
//                    // 分销人金额
//                    orderIndependentPrice.setDistributorPrice(distributorPrice);
//                    if (null != independentInviteId && independentInviteId > 0L) {
//                        // 分销人的拉新人金额
//                        orderIndependentPrice.setDistributorInvitePrice(CalUtils.sub(independentPrice, distributorPrice));
//                    } else {
//                        // 剩下的钱
//                        BigDecimal lastPrice = CalUtils.sub(independentPrice, distributorPrice);
//                        // 剩下的钱给平台
//                        platPrice = CalUtils.add(platPrice, lastPrice);
//                    }
//                } else {
//                    // 没有分销人全部给平台
//                    platPrice = CalUtils.add(platPrice, independentPrice);
//                }
//            }
//            orderIndependentPrice.setCityPrice(cityPrice);
//            orderIndependentPrice.setAgencyPrice(agencyPrice);
//        }
//        // 平台分账金额
//        orderIndependentPrice.setAdminPrice(platPrice);
//        // 分销单状态
//        orderIndependentPrice.setIndependentOrder(independentOrder);
//        log.info("calculateBusyOrderDistribution 计算结果：{}", JSONUtil.toJsonStr(orderIndependentPrice));
//        // 组装分账信息
//        Map<String, BigDecimal> calculateMap = new HashMap<>();
//        addIfValid(calculateMap, SohuIndependentObject.platform.getKey(), orderIndependentPrice.getAdminPrice());
//        addIfValid(calculateMap, SohuIndependentObject.agency.getKey(), orderIndependentPrice.getAgencyPrice());
//        addIfValid(calculateMap, SohuIndependentObject.distributionInvite.getKey(), orderIndependentPrice.getDistributorInvitePrice());
//        addIfValid(calculateMap, SohuIndependentObject.country.getKey(), orderIndependentPrice.getCountryPrice());
//        addIfValid(calculateMap, SohuIndependentObject.city.getKey(), orderIndependentPrice.getCityPrice());
//        addIfValid(calculateMap, SohuIndependentObject.distribution.getKey(), orderIndependentPrice.getDistributorPrice());
//        addIfValid(calculateMap, SohuIndependentObject.invite.getKey(), orderIndependentPrice.getInvitePrice());
//        log.warn("calculateMap : {}", JsonUtils.toJsonString(calculateMap));
//        return calculateMap;
//    }
//
//    /**
//     * 组装map校验
//     *
//     * @param map
//     * @param key
//     * @param value
//     */
//    private void addIfValid(Map<String, BigDecimal> map, String key, BigDecimal value) {
//        if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
//            map.put(key, value);
//        }
//    }
//
//    /**
//     * 根据对象获取id集合
//     *
//     * @param bo
//     */
//    public static List<Long> getAllIds(SohuIndependentIdPayBo bo) {
//        return Stream.of(bo.getAdminId(), bo.getCountryId(), bo.getCityId(), bo.getDistributorId(), bo.getInviteId(), bo.getDistributorInviteId(), bo.getReceiveId()).filter(Objects::nonNull).collect(Collectors.toList());
//    }
//
//    /**
//     * 计算每个子单的手续费
//     *
//     * @param yourObjects
//     * @param totalAmount
//     * @param fee
//     */
//    private static void distributeFee(List<SohuShopOrderModel> yourObjects, BigDecimal totalAmount, BigDecimal fee) {
//        // 计算每个对象的金额占比
//        for (SohuShopOrderModel obj : yourObjects) {
//            BigDecimal price = obj.getPayPrice();
//            BigDecimal ratio = price.divide(totalAmount, 4, RoundingMode.HALF_UP);
//            BigDecimal allocatedFee = ratio.multiply(fee).setScale(2, RoundingMode.HALF_UP);
//            obj.setChargePrice(allocatedFee);
//            obj.setAdminPrice(CalUtils.sub(obj.getAdminPrice(), allocatedFee));
//        }
//    }
//
//    /**
//     * 计算每个子单的手续费
//     *
//     * @param yourObjects
//     * @param totalAmount
//     * @param fee
//     */
//    private static void distributeFeeOfMcn(List<SohuMcnOrderModel> yourObjects, BigDecimal totalAmount, BigDecimal fee) {
//        // 计算每个对象的金额占比
//        for (SohuMcnOrderModel obj : yourObjects) {
//            BigDecimal price = obj.getPayPrice();
//            BigDecimal ratio = price.divide(totalAmount, 4, RoundingMode.HALF_UP);
//            BigDecimal allocatedFee = ratio.multiply(fee).setScale(2, RoundingMode.HALF_UP);
//            obj.setChargePrice(allocatedFee);
//        }
//    }
//
//}
