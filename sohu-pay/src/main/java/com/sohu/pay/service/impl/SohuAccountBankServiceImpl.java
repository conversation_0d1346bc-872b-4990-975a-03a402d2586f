package com.sohu.pay.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.SohuBillRecordBo;
import com.sohu.middle.api.bo.SohuBillRecordQueryBo;
import com.sohu.middle.api.bo.SohuMerBillRecordBo;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.enums.AuditState;
import com.sohu.middle.api.enums.PictrueTypeEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.SohuAreaCodeVo;
import com.sohu.middle.api.vo.SohuBillRecordVo;
import com.sohu.middle.api.vo.SohuMerBillRecordVo;
import com.sohu.middle.api.vo.WithdrawalFailVo;
import com.sohu.pay.api.bo.*;
import com.sohu.pay.api.enums.AccountEnum;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.pay.api.vo.SohuYmOnlineBankVo;
import com.sohu.pay.api.vo.WithdrawalCalVo;
import com.sohu.pay.domain.SohuAccountBank;
import com.sohu.pay.mapper.SohuAccountBankMapper;
import com.sohu.pay.service.*;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.system.api.RemoteUserService;
import com.wangcaio2o.ipossa.sdk.request.merchantwithdraw.MerchantWithdraw;
import com.wangcaio2o.ipossa.sdk.request.merchantwithdraw.MerchantWithdrawRequest;
import com.wangcaio2o.ipossa.sdk.response.merchantopen.MerchantOpenResponse;
import com.wangcaio2o.ipossa.sdk.response.merchantopenmodify.MerchantOpenmodifyResponse;
import com.wangcaio2o.ipossa.sdk.response.merchantwithdraw.MerchantWithdrawResponse;
import com.wangcaio2o.ipossa.sdk.response.onlinebanklist.OnlineBanklistResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 开户银行Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-11
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuAccountBankServiceImpl implements ISohuAccountBankService {

    private final SohuAccountBankMapper baseMapper;
    private final ISohuAccountService sohuAccountService;

    private final ISohuYmOnlineBankService sohuYmOnlineBankService;

    private final ISohuOnlineBankLogService onlineBankLogService;
    private final AsyncConfig asyncConfig;
    @DubboReference
    private RemoteMiddleAreaCodeService remoteMiddleAreaCodeService;
    @DubboReference
    private RemoteMiddleBillRecordService remoteMiddleBillRecordService;
    private final YmBaseService ymBaseService;
    @DubboReference
    private RemoteMiddleUserBalanceService remoteMiddleUserBalanceService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteMiddleMerTradeRecordService remoteMiddleMerTradeRecordService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;

    /**
     * 绑定失败消息
     */
    private final static String errorMsg = "银行卡绑定失败，点击可重新绑定。\n" +
            "失败原因：%s  \n" +
            "如您有疑问，请联系我们。感谢您的支持！";

    /**
     * 绑定成功消息
     */
    private final static String successMsg = "您的银行卡绑定成功，可在钱包查看收支明细";

    /**
     * 查询开户银行
     */
    @Override
    public SohuAccountBankVo queryById(Long id) {
        LambdaQueryWrapper<SohuAccountBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAccountBank::getUserId, id);
        SohuAccountBank sohuAccountBank = baseMapper.selectOne(lqw);
        SohuAccountBankVo sohuAccountBankVo = BeanUtil.copyProperties(sohuAccountBank, SohuAccountBankVo.class);
        // 隐私数据脱敏1.0
        if (sohuAccountBank.getState().equalsIgnoreCase(AuditState.Refuse.name())) {
            sohuAccountBankVo.setCardNo(sohuAccountBank.getCardNo());
        } else {
            sohuAccountBankVo.setCardNoEncrypt(sohuAccountBank.getCardNo());
            sohuAccountBankVo.setCardNo(null);
        }
        return sohuAccountBankVo;
    }

    /**
     * 查询开户银行列表
     */
    @Override
    public TableDataInfo<SohuAccountBankVo> queryPageList(SohuAccountBankBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAccountBank> lqw = buildQueryWrapper(bo);
        Page<SohuAccountBankVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询开户银行列表
     */
    @Override
    public List<SohuAccountBankVo> queryList(SohuAccountBankBo bo) {
        LambdaQueryWrapper<SohuAccountBank> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuAccountBank> buildQueryWrapper(SohuAccountBankBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuAccountBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuAccountBank::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getMerchantId()), SohuAccountBank::getMerchantId, bo.getMerchantId());
        lqw.eq(StringUtils.isNotBlank(bo.getSettleType()), SohuAccountBank::getSettleType, bo.getSettleType());
        lqw.eq(bo.getSettlementWay() != null, SohuAccountBank::getSettlementWay, bo.getSettlementWay());
        lqw.eq(StringUtils.isNotBlank(bo.getCardNo()), SohuAccountBank::getCardNo, bo.getCardNo());
        lqw.like(StringUtils.isNotBlank(bo.getParentBankName()), SohuAccountBank::getParentBankName, bo.getParentBankName());
        lqw.eq(StringUtils.isNotBlank(bo.getParentBankCode()), SohuAccountBank::getParentBankCode, bo.getParentBankCode());
        lqw.like(StringUtils.isNotBlank(bo.getBranchBankCode()), SohuAccountBank::getBranchBankCode, bo.getBranchBankCode());
        lqw.eq(StringUtils.isNotBlank(bo.getBranchBankName()), SohuAccountBank::getBranchBankName, bo.getBranchBankName());
        lqw.eq(StringUtils.isNotBlank(bo.getProvince()), SohuAccountBank::getProvince, bo.getProvince());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), SohuAccountBank::getCity, bo.getCity());
        lqw.eq(StringUtils.isNotBlank(bo.getSettlePhotoA()), SohuAccountBank::getSettlePhotoA, bo.getSettlePhotoA());
        lqw.eq(StringUtils.isNotBlank(bo.getSettlePhotoB()), SohuAccountBank::getSettlePhotoB, bo.getSettlePhotoB());
        lqw.eq(StringUtils.isNotBlank(bo.getSettlePhotoFront()), SohuAccountBank::getSettlePhotoFront, bo.getSettlePhotoFront());
        lqw.eq(StringUtils.isNotBlank(bo.getSettlePhotoBack()), SohuAccountBank::getSettlePhotoBack, bo.getSettlePhotoBack());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuAccountBank::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getRejectReason()), SohuAccountBank::getRejectReason, bo.getRejectReason());
        lqw.orderByDesc(SohuAccountBank::getId);
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(SohuAccountBank::getCreateTime, DateUtils.beginOfTime(bo.getStartTime()));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(SohuAccountBank::getCreateTime, DateUtils.endOfTime(bo.getEndTime()));
        }
        return lqw;
    }

    /**
     * 新增开户银行
     */
    @Override
    public Boolean insertByBo(SohuAccountBankBo bo) {
        validEntityBeforeInsert(bo);
        SohuAccountBank add = BeanUtil.toBean(bo, SohuAccountBank.class);
        boolean flag;
        try {
            // 执行插入数据的操作
            flag = baseMapper.insertOrUpdate(add);
        } catch (DuplicateKeyException e) {
            // 处理重复插入的情况
            throw new RuntimeException(MessageUtils.message("OPERATION_REPEAT"));
        }
        if (flag) {
            bo.setId(add.getId());
            // 发消息到队列
            MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(bo), MqKeyEnum.ACCOUNT_BANK_ADD.getKey());
            remoteStreamMqService.sendNormalMsg(mqMessaging);
        }
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBoV2(SohuAccountBankAddBo bo) {
        Long userId = LoginHelper.getUserId();
        SohuAccountBank accountBank = baseMapper.selectOne(SohuAccountBank::getUserId, userId);
        if (Objects.nonNull(accountBank)) {
            //throw new RuntimeException(MessageUtils.message("OPERATION_REPEAT"));
            //兼容奇葩代码提交
            SohuAccountBankEditBo editBo = BeanUtil.copyProperties(bo, SohuAccountBankEditBo.class);
            editBo.setId(accountBank.getId());
            return this.updateByBoV2(editBo);
        }
        SohuAccountVo accountVo = sohuAccountService.queryByUserIdOfPass(userId);
        if (Objects.isNull(accountVo)) {
            throw new RuntimeException("请待实名认证通过后再绑定银行卡");
        }
        SohuAccountBank add = BeanUtil.toBean(bo, SohuAccountBank.class);
        add.setSettlementWay(0);
        add.setMerchantId(accountVo.getMerchantId());
        boolean business = StringUtils.equals(accountVo.getAccountType(), UserAuthEnum.business.getType());
        add.setSettleType(business ? "1" : "2");
        //对公账号判断
        if (business && StringUtils.isBlank(bo.getBranchBankCode()) && StringUtils.isBlank(bo.getBranchBankName())) {
            throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
        }
        //密码加密
        if (StringUtils.isNotBlank(bo.getPassword())) {
            remoteUserService.updatePayPwd(userId, bo.getPassword());
        }
        add.setUserId(userId);
        add.setHasMerchant(false);
        boolean flag = this.baseMapper.insert(add) > 0;
        if (flag) {
            // 发消息到队列
            MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(add), MqKeyEnum.ACCOUNT_BANK_ADD.getKey());
            remoteStreamMqService.sendNormalMsg(mqMessaging);
        }
        return flag;
    }

    /**
     * 新增校验
     *
     * @param bo
     */
    @Deprecated
    private void validEntityBeforeInsert(SohuAccountBankBo bo) {
        //唯一校验
        //if (bo.getId() == null) {
        SohuAccountBank accountBank = baseMapper.selectOne(SohuAccountBank::getUserId, bo.getUserId());
        if (Objects.nonNull(accountBank)) {
            throw new RuntimeException(MessageUtils.message("OPERATION_REPEAT"));
        }
        //}
        //SohuAccountVo accountVo = sohuAccountService.queryByUserId(bo.getUserId());
        SohuAccountVo accountVo = sohuAccountService.queryByUserIdOfPass(bo.getUserId());
        if (Objects.isNull(accountVo)) {
            //throw new RuntimeException(MessageUtils.message("WRONG_INFO"));
            throw new RuntimeException("请待实名认证通过后再绑定银行卡");
        }
        bo.setSettlementWay(0);
        bo.setCreateTime(new Date());
        bo.setMerchantId(accountVo.getMerchantId());
        boolean business = StringUtils.equals(accountVo.getAccountType(), UserAuthEnum.business.getType());
        bo.setSettleType(business ? "1" : "2");
        //对公账号判断
        if (business && StringUtils.isBlank(bo.getBranchBankCode()) && StringUtils.isBlank(bo.getBranchBankName())) {
            throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
        }
        //密码加密
        if (StringUtils.isNotBlank(bo.getPassword())) {
            remoteUserService.updatePayPwd(bo.getUserId(), bo.getPassword());
        }
    }

    /**
     * 修改开户银行
     */
    @Override
    public Boolean updateByBo(SohuAccountBankBo bo) {
        SohuAccountBank update = BeanUtil.toBean(bo, SohuAccountBank.class);
        update.setState(CommonState.WaitApprove.getCode());
        validEntityBeforeUpdate(update);
        return baseMapper.updateById(update) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBoV2(SohuAccountBankEditBo bo) {
        SohuAccountBank accountBank = baseMapper.selectOne(SohuAccountBank::getId, bo.getId(), SohuAccountBank::getUserId, LoginHelper.getUserId());
        if (Objects.isNull(accountBank)) {
            throw new RuntimeException("账号不存在");
        }
        this.copyAccountBankEdit(bo, accountBank);

        LambdaUpdateWrapper<SohuAccountBank> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SohuAccountBank::getId, accountBank.getId()).set(SohuAccountBank::getRejectReason, null);
        accountBank.setState(CommonState.WaitApprove.getCode());
        accountBank.setRejectReason(null);
        boolean flag = this.baseMapper.update(accountBank, wrapper) > 0;
        if (flag) {
            // 发消息到队列
            MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(accountBank), MqKeyEnum.ACCOUNT_BANK_ADD.getKey());
            remoteStreamMqService.sendNormalMsg(mqMessaging);
        }
        return flag;
    }

    private void copyAccountBankEdit(SohuAccountBankEditBo bo, SohuAccountBank entity) {
        entity.setCardNo(bo.getCardNo());
        entity.setParentBankCode(bo.getParentBankCode());
        entity.setParentBankName(bo.getParentBankName());
        entity.setBranchBankCode(bo.getBranchBankCode());
        entity.setBranchBankName(bo.getBranchBankName());
        entity.setProvince(bo.getProvince());
        entity.setCity(bo.getCity());
        entity.setSettlePhotoBack(bo.getSettlePhotoBack());
        entity.setSettlePhotoFront(bo.getSettlePhotoFront());
    }

    /**
     * 修改校验
     */
    @Deprecated
    private void validEntityBeforeUpdate(SohuAccountBank bo) {
        SohuAccountBankVo vo = baseMapper.selectVoById(bo.getId());
        if (ObjectUtil.isEmpty(vo)) {
            throw new RuntimeException(MessageUtils.message("WRONG_INFO"));
        }
        bo.setUserId(vo.getUserId());
        bo.setMerchantId(vo.getMerchantId());
        bo.setSettleType(vo.getSettleType());
        bo.setSettlementWay(vo.getSettlementWay());
        //图片处理
        bo.setSettlePhotoA(ymBaseService.urlChangeBase64(bo.getSettlePhotoFront(), PictrueTypeEnum.SettlePhotoA));
        bo.setSettlePhotoB(ymBaseService.urlChangeBase64(bo.getSettlePhotoBack(), PictrueTypeEnum.SettlePhotoB));
        // 翼码业务入驻修改
        MerchantOpenmodifyResponse response = ymBaseService.merchantOpenmodify(bo);
        log.info("MerchantOpenResponse :{}", JSONObject.toJSONString(response));
        if (!response.getResult().getId().equals(OrderConstants.YM_SUCCESS)) {
            //失败
            throw new RuntimeException(response.getResult().getComment());
        }
    }

    /**
     * 获取用户银行卡数据
     */
    @Override
    public SohuAccountBankVo queryByUserId(Long userId) {
        LambdaQueryWrapper<SohuAccountBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAccountBank::getUserId, userId);
        SohuAccountBankVo accountBankVo = baseMapper.selectVoOne(lqw);
        if (Objects.nonNull(accountBankVo)) {
            SohuAreaCodeVo areaCodeVo = remoteMiddleAreaCodeService.queryByCityCode(accountBankVo.getCity());
            if (Objects.nonNull(areaCodeVo)) {
                accountBankVo.setCityName(areaCodeVo.getCityName());
                accountBankVo.setProvinceName(areaCodeVo.getProvinceName());
            }
            // 隐私数据脱敏1.0
            if (!accountBankVo.getState().equalsIgnoreCase(AuditState.Refuse.name())) {
                accountBankVo.setCardNoEncrypt(accountBankVo.getCardNo());
                accountBankVo.setCardNo(null);
            }
        }
        return accountBankVo;
    }

    /**
     * 通过手机号+验证码或者支付密码获取用户银行卡信息 1.0
     */
    @Override
    public SohuAccountBankVo getInfoBySmsOrPayPassword(Long userId, String smsCode, String payPassword) {
        // 验证码或支付密码不为空
        if (StringUtils.isNotEmpty(smsCode) || StringUtils.isNotEmpty(payPassword)) {
            SohuAccountVo accountVo = sohuAccountService.getInfoBySms(userId, smsCode);

            // 校验验证码或支付密码
            if (ObjectUtil.isNotNull(accountVo) || Boolean.TRUE.equals(checkPassword(userId, payPassword))) {
                LambdaQueryWrapper<SohuAccountBank> lqw = Wrappers.lambdaQuery();
                lqw.eq(SohuAccountBank::getUserId, userId);

                SohuAccountBankVo accountBankVo = baseMapper.selectVoOne(lqw);

                if (Objects.nonNull(accountBankVo)) {
                    SohuAreaCodeVo areaCodeVo = remoteMiddleAreaCodeService.queryByCityCode(accountBankVo.getCity());

                    if (Objects.nonNull(areaCodeVo)) {
                        accountBankVo.setCityName(areaCodeVo.getCityName());
                        accountBankVo.setProvinceName(areaCodeVo.getProvinceName());
                    }
                }

                return accountBankVo;
            }
        }
        return null;
    }

    /**
     * @param userId
     * @return
     */
    @Override
    public List<SohuAccountBankVo> queryListByUserId(List<Long> userId) {
        LambdaQueryWrapper<SohuAccountBank> lqw = Wrappers.lambdaQuery();
        lqw.in(SohuAccountBank::getUserId, userId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<SohuAccountBankVo> queryPassListByUserId(List<Long> userId) {
        LambdaQueryWrapper<SohuAccountBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAccountBank::getState, AuditState.Pass);
        lqw.in(SohuAccountBank::getUserId, userId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Integer payPassword(Long userId, String password, String oldPassword) {
        if (!StringUtils.validNumberPassword(password)) {
            throw new RuntimeException("支付密码必须为6位纯数字");
        }
        if (StringUtils.isNotBlank(oldPassword)) {
            LoginUser user = remoteUserService.queryById(userId);
            String payPassword = user.getPayPassword();
            if (!BCrypt.checkpw(oldPassword, payPassword)) {
                throw new RuntimeException("修改密码失败，旧密码错误");
            }
            if (BCrypt.checkpw(password, payPassword)) {
                throw new RuntimeException("新密码不能与旧密码相同");
            }
        }
        Integer result = remoteUserService.updatePayPwd(userId, password);
        // 修改支付密码发送消息通知
        if (StringUtils.isNotBlank(oldPassword)) {
            CompletableFuture.runAsync(() -> remoteUserService.sendMsgOfUpdatePayPwd(userId), asyncConfig.getAsyncExecutor());
        }
        return result;
    }

    @Override
    public Integer setPayPassword(Long userId, String password, String oldPassword) {
        // 校验两次输入密码是否一致
        if (!StringUtils.equals(oldPassword, password)) {
            throw new RuntimeException("两次输入的密码不一致");
        }
        return remoteUserService.updatePayPwd(userId, password);
    }

    @Override
    public Boolean checkPassword(Long userId, String password) {
        LoginUser user = remoteUserService.queryById(userId);
        String payPassword = user.getPayPassword();
        if (!BCrypt.checkpw(password, payPassword)) {
            throw new RuntimeException("密码错误");
        }
        return true;
    }

    @Override
    public Boolean withdrawal(SohuWithdrawBo bo) {
        if (bo == null || CalUtils.isLessEqualZero(bo.getWithdrawalAmount())) {
            throw new RuntimeException(MessageUtils.message("WRONG_INFO"));
        }
        Long userId = bo.getUserId();
        // 转换为分
        BigDecimal amountInCent = bo.getWithdrawalAmount().multiply(CalUtils.PERCENTAGE);
        // 设置小数位数为0，确保是整数
        amountInCent = amountInCent.setScale(0);
        // 转换为Integer类型
        Integer amountInCentInteger = amountInCent.intValue();
        String posSeq = NumberUtil.getOrderNo(OrderConstants.BANK_ORDER_PREFIX);
        MerchantWithdrawResponse response = userWithdrawal(userId, amountInCentInteger, bo.getRemark(), posSeq);
        String code = response.getTransStatus();
        //如果提现失败，抛出错误信息
        if (StringUtils.equals(code, OrderConstants.BANK_FAIL)) {
            throw new RuntimeException(response.getErrorMsg());
        }
        //查询该用户的银行卡号
        SohuAccountBankVo accountBank = queryListByUserId(List.of(userId)).get(0);
        // 账单流水记录-发单方余额支出记录
        SohuBillRecordBo billRecordDO = new SohuBillRecordBo();
        billRecordDO.setPayerId(userId);
        billRecordDO.setPayeeId(0L);
        billRecordDO.setAmount(bo.getWithdrawalAmount());
        billRecordDO.setTransactionType(SohuTransactionTypeEnum.withdrawal.name());
        billRecordDO.setBusyCode(0L);
        billRecordDO.setTitle("提现");
        billRecordDO.setState(PayStatus.Processing.name());
        billRecordDO.setBillType(SohuBillType.Balance.name());
        billRecordDO.setTradeNo(posSeq);
        billRecordDO.setUserId(userId);
        billRecordDO.setTaskNumber(bo.getTaskNumber());
        billRecordDO.setWalletBalance(BigDecimal.ZERO);
        billRecordDO.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
        billRecordDO.setWithdrawId(response.getWithdrawSeq());
        billRecordDO.setWithdrawTime(new Date());
        billRecordDO.setWithdrawBankNo(accountBank.getCardNo());
        billRecordDO.setWithdrawBankName(accountBank.getParentBankName());
        billRecordDO.setSiteType(bo.getSiteType());
        billRecordDO.setSiteId(bo.getSiteId());
        billRecordDO.setWithdrawalUserType(bo.getWithdrawalUserType());
//        // 可提现余额
//        List<AccountBalanceQueryList> balanceQueryList = ymBaseService.queryUserBalance(userId);
//        if (CollUtil.isNotEmpty(balanceQueryList)) {
//            int totalAvlAmt = balanceQueryList.stream().mapToInt(AccountBalanceQueryList::getAvlAmt).sum();
//            //查询商户可提现余额
//            BigDecimal merchantBalance = remoteMiddleMerTradeRecordService.selectMerBalance(userId, null);
//            billRecordDO.setWalletBalance(CalUtils.centToYuan(new BigDecimal(totalAvlAmt).subtract(merchantBalance)));
//        }
        // 查询用户余额
        BigDecimal balance = remoteUserService.getUserIncome(userId, bo.getSiteType(), bo.getSiteId(), bo.getWithdrawalUserType(), IndependentStatusEnum.DISTRIBUTED.getCode(), null, null);
        billRecordDO.setWalletBalance(balance.subtract(bo.getWithdrawalAmount()));
        remoteMiddleBillRecordService.insertByBo(billRecordDO);
        //计入用户流水明细表
        SohuTradeRecordBo tradeRecordDO = SohuTradeRecordBo.builder().build();
        tradeRecordDO.setUserId(userId);
        tradeRecordDO.setType("Withdrawal");
        tradeRecordDO.setAmount(bo.getWithdrawalAmount());
        tradeRecordDO.setConsumeType("Withdrawal");
        tradeRecordDO.setConsumeCode(posSeq);
        tradeRecordDO.setMsg("翼码余额提现");
        tradeRecordDO.setAmountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        tradeRecordDO.setPayType(SohuBillType.Balance.name());
        tradeRecordDO.setPayStatus(PayStatus.Paid.name());
        tradeRecordDO.setAccountType(SohuTradeRecordEnum.AccountType.Amount.name());
        tradeRecordDO.setPayNumber(posSeq);
        tradeRecordDO.setUnq(System.nanoTime() + RandomUtils.randomString(3));
        tradeRecordDO.setOperateChannel(Constants.CHANNEL_MOBILE);
        tradeRecordDO.setPayTime(new Date());
        tradeRecordDO.setSiteType(bo.getSiteType());
        tradeRecordDO.setSiteId(bo.getSiteId());
        remoteMiddleTradeRecordService.insertByBo(tradeRecordDO);
        //发送提现成功通知
        CompletableFuture.runAsync(() -> {
            NoticeSystemContent content = new NoticeSystemContent();
            content.setTitle(SystemNoticeEnum.withdraw);
            content.setNoticeTime(DateUtils.getTime());
            content.setType(SystemNoticeEnum.SubType.withdrawPass.name());
            content.setDetailId(userId);
            NoticeContentDetail detail = new NoticeContentDetail();
            detail.setUserId(userId);
            detail.setDesc(SystemNoticeEnum.withdrawDesc);
            content.setContent(detail);
            String contentJson = JSONUtil.toJsonStr(content);
            remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.withdraw, contentJson, SystemNoticeEnum.Type.withdraw);
        }, asyncConfig.getAsyncExecutor());
        return Boolean.TRUE;
    }

    @Override
    public Boolean merWithdrawal(SohuAccountBankBo bo) {
        if (bo == null || CalUtils.isLessEqualZero(bo.getWithdrawalAmount())) {
            throw new RuntimeException(MessageUtils.message("WRONG_INFO"));
        }
        String key = "mer_withdrawal_" + bo.getUserId();
        if (!RedisUtils.setObjectIfAbsent(key, 1, Duration.ofSeconds(10L))) {
            throw new RuntimeException("请勿重复提交");
        }
        // 转换为分
        BigDecimal amountInCent = bo.getWithdrawalAmount().multiply(CalUtils.PERCENTAGE);
        // 设置小数位数为0，确保是整数
        amountInCent = amountInCent.setScale(0);
        // 转换为Integer类型
        Integer amountInCentInteger = amountInCent.intValue();
        String posSeq = NumberUtil.getOrderNo(OrderConstants.BANK_ORDER_PREFIX);
        MerchantWithdrawResponse response = userWithdrawal(bo.getUserId(), amountInCentInteger, bo.getRemark(), posSeq);
        String code = response.getTransStatus();
        //如果提现失败，抛出错误信息
        if (StringUtils.equals(code, OrderConstants.BANK_FAIL)) {
            throw new RuntimeException(response.getErrorMsg());
        }
        // 账单流水记录-发单方余额支出记录
        SohuMerBillRecordBo billRecordDO = new SohuMerBillRecordBo();
        billRecordDO.setPayerId(bo.getUserId());
        billRecordDO.setUserId(bo.getUserId());
        billRecordDO.setPayeeId(0L);
        billRecordDO.setAmount(bo.getWithdrawalAmount());
        billRecordDO.setTransactionType(SohuTransactionTypeEnum.withdrawal.name());
        billRecordDO.setBusyCode(0L);
        billRecordDO.setTitle("提现");
        //交易状态:s-成功 p-处理中 f-失败
        if (StringUtils.equals(response.getTransStatus(), OrderConstants.BANK_SUCCESS)) {
            billRecordDO.setState(PayStatus.Paid.name());
        } else if (StringUtils.equals(response.getTransStatus(), OrderConstants.BANK_PROCESS)) {
            billRecordDO.setState(PayStatus.WaitPay.name());
        }
        //提现流水号
        billRecordDO.setTradeNo(posSeq);
        billRecordDO.setBillType(SohuBillType.Balance.name());
        // 可提现余额 = 商户可提现余额 - 减去本次提现金额
        BigDecimal merchantBalance = remoteMiddleMerTradeRecordService.selectMerBalance(bo.getUserId(), null);
        billRecordDO.setWalletBalance(merchantBalance.subtract(bo.getWithdrawalAmount()));
        Boolean b = remoteMiddleBillRecordService.insertMerRecord(billRecordDO);
        //删除锁
        RedisUtils.deleteObject(key);
        return b;
    }

    @Override
    public Boolean YiMaBankAdd(SohuAccountBank bo) {
        //bo.setState(AuditState.Pass.name());
        //图片处理
//        bo.setSettlePhotoA(urlChangeBase64(bo.getSettlePhotoFront(), PictrueTypeEnum.SettlePhotoA));
//        bo.setSettlePhotoB(urlChangeBase64(bo.getSettlePhotoBack(), PictrueTypeEnum.SettlePhotoB));
        //不严谨，还需要优化
        //SohuAccountBank updateEntity = new SohuAccountBank();
        //updateEntity.setId(bo.getId());
        try {
            //图片处理
            bo.setSettlePhotoA(ymBaseService.urlChangeBase64(bo.getSettlePhotoFront(), PictrueTypeEnum.SettlePhotoA));
            bo.setSettlePhotoB(ymBaseService.urlChangeBase64(bo.getSettlePhotoBack(), PictrueTypeEnum.SettlePhotoB));
//            bo.setSettlePhotoA(bo.getSettlePhotoA());
//            bo.setSettlePhotoB(bo.getSettlePhotoB());
        } catch (Exception e) {
            bo.setState(AuditState.Refuse.name());
            bo.setRejectReason("翼码图片上传失败");
            baseMapper.updateById(bo);
            this.sendNotice(bo.getUserId(), "银行卡绑定失败", String.format(errorMsg, "翼码图片上传失败"));
            return Boolean.FALSE;
        }
        if (BooleanUtil.isTrue(bo.getHasMerchant())) {
            // 翼码业务入驻修改
            MerchantOpenmodifyResponse response = ymBaseService.merchantOpenmodify(bo);
            log.info("MerchantOpenResponse :{}", JSONObject.toJSONString(response));
            if (!response.getResult().getId().equals(OrderConstants.YM_SUCCESS)) {
                //失败
                bo.setState(AuditState.Refuse.name());
                bo.setRejectReason(response.getResult().getComment());
                baseMapper.updateById(bo);
                this.sendNotice(bo.getUserId(), "银行卡绑定失败", String.format(errorMsg,
                        AccountEnum.AccountErrEnum.getByCode(bo.getRejectReason())));
                return Boolean.FALSE;
            }
        } else {
            // 翼码业务入驻
            MerchantOpenResponse response = ymBaseService.merchantOpen(bo);
            log.info("MerchantOpenResponse :{}", JSONObject.toJSONString(response));
            if (!response.getResult().getId().equals(OrderConstants.YM_SUCCESS)) {
                //失败
                bo.setState(AuditState.Refuse.name());
//            String[] split = response.getResult().getComment().split("-");
                bo.setRejectReason(response.getResult().getComment());
                baseMapper.updateById(bo);
                this.sendNotice(bo.getUserId(), "银行卡绑定失败", String.format(errorMsg,
                        AccountEnum.AccountErrEnum.getByCode(bo.getRejectReason())));
                return Boolean.FALSE;
            }
//            else {
////                bo.setState(AuditState.Pass.name());
////                bo.setHasMerchant(true);
////                LambdaUpdateWrapper<SohuAccountBank> wrapper = new LambdaUpdateWrapper<>();
////                wrapper.eq(SohuAccountBank::getId, bo.getId()).set(SohuAccountBank::getRejectReason, null);
////                return baseMapper.update(bo, wrapper) > 0;
//            }
        }
        bo.setState(AuditState.Pass.name());
        bo.setHasMerchant(true);
        bo.setRejectReason(null);
        this.sendNotice(bo.getUserId(), "银行卡绑定成功", successMsg);
        LambdaUpdateWrapper<SohuAccountBank> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SohuAccountBank::getId, bo.getId()).set(SohuAccountBank::getRejectReason, null);
        return baseMapper.update(bo, wrapper) > 0;
    }

    @Override
    public Map<Long, SohuAccountBankVo> queryMapByUserIds(List<Long> userIds) {
        List<SohuAccountBankVo> bankVos = this.queryListByUserId(userIds);
        return CollUtil.isEmpty(bankVos) ? new HashMap<>() : bankVos.stream().collect(Collectors.toMap(SohuAccountBankVo::getUserId, u -> u));
    }

    /**
     * 用户取现
     *
     * @param userId 用户id
     * @param amount 取现金额(分)
     * @param remark 备注（可以为空）
     * @return
     */
    protected MerchantWithdrawResponse userWithdrawal(Long userId, Integer amount, String remark, String posSeq) {
        SohuAccountVo accountVo = sohuAccountService.queryByUserIdOfPass(userId);
        if (ObjectUtil.isEmpty(accountVo)) {
            throw new RuntimeException(MessageUtils.message("WRONG_INFO"));
        }
        MerchantWithdrawResponse response = ymBaseService.merchantWithdraw(accountVo.getMerchantId(), amount, remark, posSeq);
        log.info("MerchantWithdrawResponse :{}", JSONObject.toJSONString(response));
        return response;
    }

    protected MerchantWithdrawResponse userWithdrawalRes(Long userId, Integer amount, String remark, String posSeq, Long billId) {
        SohuAccountVo accountVo = sohuAccountService.queryByUserIdOfPass(userId);
        if (ObjectUtil.isEmpty(accountVo)) {
            throw new RuntimeException(MessageUtils.message("WRONG_INFO"));
        }
        MerchantWithdrawRequest request = new MerchantWithdrawRequest();
        MerchantWithdraw withdraw = new MerchantWithdraw();
        withdraw.setMerchantId(accountVo.getMerchantId());
        withdraw.setAmount(amount);
        withdraw.setRemark(remark);
        request.setPosSeq(posSeq);
        request.setMerchantWithdrawRequest(withdraw);
        MerchantWithdrawResponse response = ymBaseService.merchantWithdraw(accountVo.getMerchantId(), amount, remark, posSeq);
        //记录日志
        SohuOnlineBankLogBo onlineBankLogBo = new SohuOnlineBankLogBo();
        onlineBankLogBo.setRequestType(request.getRequestType());
        onlineBankLogBo.setRequestStr(JSONUtil.toJsonStr(request));
        onlineBankLogBo.setResponseStr(JSONUtil.toJsonStr(response));
        onlineBankLogBo.setPosSeq(posSeq);
        onlineBankLogBo.setBillId(billId);
        onlineBankLogService.insertByBo(onlineBankLogBo);
        return response;
    }

    /**
     * 批量删除开户银行
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            //TODO 做一些业务上的校验,判断是否需要校验
//        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<SohuBillRecordVo> queryBillRecordList(SohuBillRecordBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuBillRecordVo> dataInfo = remoteMiddleBillRecordService.queryPageList(bo, pageQuery);
        if (CollUtil.isNotEmpty(dataInfo.getData()) && (StringUtils.isBlank(bo.getTransactionType()) || bo.getTransactionType().equals(SohuTransactionTypeEnum.withdrawal.name()))) {
            //审核中提现状态
            List<SohuBillRecordVo> filteredRecords = dataInfo.getData().stream().filter(record -> StringUtils.equals(record.getState(), PayStatus.WaitPay.name()) && StringUtils.equals(record.getTransactionType(), SohuTransactionTypeEnum.withdrawal.name())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(filteredRecords)) {
                for (SohuBillRecordVo item : filteredRecords) {
                    //s：成功， p：处理中，f：失败
                    String withdrawal = ymBaseService.queryWithdrawal(item.getTradeNo());
                    if (StringUtils.isNotBlank(withdrawal) && !StringUtils.equals(withdrawal, OrderConstants.BANK_PROCESS)) {
                        switch (withdrawal) {
                            case OrderConstants.BANK_SUCCESS:
                                item.setState(PayStatus.Paid.name());
                                break;
                            case OrderConstants.BANK_FAIL:
                                item.setState(PayStatus.Fail.name());
                                break;
                        }
                        //修改状态
                        SohuBillRecordBo update = BeanUtil.toBean(item, SohuBillRecordBo.class);
                        remoteMiddleBillRecordService.updateByBo(update);
                    }
                }
            }
        }
        return dataInfo;
    }

    @Override
    public TableDataInfo<SohuMerBillRecordVo> queryMerBillRecordList(SohuMerBillRecordBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuMerBillRecordVo> dataInfo = remoteMiddleBillRecordService.queryPageMerBillList(bo, pageQuery);
        if (CollUtil.isNotEmpty(dataInfo.getData()) && (StringUtils.isBlank(bo.getTransactionType()) || bo.getTransactionType().equals(SohuTransactionTypeEnum.withdrawal.name()))) {
            //审核中提现状态
            List<SohuMerBillRecordVo> filteredRecords = dataInfo.getData().stream().filter(record -> StringUtils.equals(record.getState(), PayStatus.WaitPay.name()) && StringUtils.equals(record.getTransactionType(), SohuTransactionTypeEnum.withdrawal.name())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(filteredRecords)) {
                for (SohuMerBillRecordVo item : filteredRecords) {
                    //s：成功， p：处理中，f：失败
                    String withdrawal = ymBaseService.queryWithdrawal(item.getTradeNo());
                    if (StringUtils.isNotBlank(withdrawal) && !StringUtils.equals(withdrawal, OrderConstants.BANK_PROCESS)) {
                        switch (withdrawal) {
                            case OrderConstants.BANK_SUCCESS:
                                item.setState(PayStatus.Paid.name());
                                break;
                            case OrderConstants.BANK_FAIL:
                                item.setState(PayStatus.Fail.name());
                                break;
                        }
                        //修改状态
                        SohuMerBillRecordBo update = BeanUtil.toBean(item, SohuMerBillRecordBo.class);
                        remoteMiddleBillRecordService.updateMerByBo(update);
                    }
                }
            }
        }
        return dataInfo;
    }

    @Override
    public void taskAuth() {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        // 校验用户是否实名且绑定银行卡
        SohuAccountVo sohuAccountVo = sohuAccountService.queryByUserIdOfPass(userId);
        if (Objects.isNull(sohuAccountVo)) {
            throw new ServiceException("请先进行实名认证且审核通过");
        }
        LambdaQueryWrapper<SohuAccountBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuAccountBank::getUserId, userId);
        lqw.eq(SohuAccountBank::getState, AuditState.Pass.name());
        Long count = baseMapper.selectCount(lqw);
        if (count == 0) {
            throw new ServiceException("请先绑定银行卡且审核通过");
        }
    }

    @Override
    public TableDataInfo<SohuBillRecordVo> withdrawalList(SohuBillRecordQueryBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuBillRecordVo> dataInfo = remoteMiddleBillRecordService.withdrawalList(bo, pageQuery);
        return dataInfo;
    }

    @Override
    public WithdrawalCalVo withdrawalCal(List<Long> ids) {
        WithdrawalCalVo calVo = new WithdrawalCalVo();
        List<SohuBillRecordVo> list = remoteMiddleBillRecordService.queryWithdrawalByIds(ids);
        if (CollUtil.isNotEmpty(list)) {
            BigDecimal totalAmt = list.stream().map(SohuBillRecordVo::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal txAmt = CalUtils.divide(totalAmt, new BigDecimal("0.987")).setScale(0, RoundingMode.CEILING);
            calVo.setTotalAmt(totalAmt);
            calVo.setTxAmt(txAmt);
        }
        return calVo;
    }

    @Override
    public Boolean withdrawalSend(Long id) {
        SohuBillRecordVo sohuBillRecordVo = remoteMiddleBillRecordService.queryById(id);
        if (Objects.isNull(sohuBillRecordVo)) {
            throw new ServiceException("提现记录不存在");
        }
        if (!sohuBillRecordVo.getState().equals(PayStatus.Fail.name())) {
            throw new ServiceException("非失败无法重新发起提现");
        }
        // 流水号
        String tradeNo = NumberUtil.getOrderNo(OrderConstants.BANK_ORDER_PREFIX);
        //发起翼码提现
        // 转换为分
        BigDecimal amountInCent = sohuBillRecordVo.getAmount().multiply(CalUtils.PERCENTAGE);
        Map<String, String> resMap = sendYmWithdrawal(sohuBillRecordVo.getUserId(), tradeNo, amountInCent, sohuBillRecordVo.getTitle(), sohuBillRecordVo.getId());
        if (StrUtil.isNotEmpty(resMap.get("trans_status")) && !StringUtils.equals(resMap.get("trans_status"), OrderConstants.BANK_FAIL)) {
            // 更新提现记录状态 失败->处理中
            remoteMiddleBillRecordService.updateStatusByTradeNo(sohuBillRecordVo.getTradeNo(), tradeNo, PayStatus.Processing.name());
        }
        return Boolean.TRUE;
    }

    @Override
    public TableDataInfo<WithdrawalFailVo> failList(PageQuery pageQuery) {
        SohuBillRecordQueryBo bo = new SohuBillRecordQueryBo();
        bo.setState(PayStatus.Fail.name());
        TableDataInfo<SohuBillRecordVo> dataInfo = remoteMiddleBillRecordService.withdrawalList(bo, pageQuery);
        List<WithdrawalFailVo> failList = new ArrayList<>();
        if (CollUtil.isNotEmpty(dataInfo.getData())) {
            dataInfo.getData().forEach(item -> {
                WithdrawalFailVo failVo = new WithdrawalFailVo();
                failVo.setId(item.getId());
                failVo.setUserName(item.getUserName());
                failVo.setPhoneNumber(item.getPhoneNumber());
                failVo.setCreateTime(item.getCreateTime());
                failVo.setErrorMsg(item.getRejectionReason());
                failVo.setAmount(item.getAmount());
                //查询失败的报文
                failVo.setResponseStr(onlineBankLogService.getResponse(item.getTradeNo()));
                failList.add(failVo);
            });
        }
        return new TableDataInfo<>(failList, dataInfo.getTotal());
    }

    @Override
    public Map<String, String> sendYmWithdrawal(Long userId, String tradeNo, BigDecimal amount, String remark, Long billId) {
        Map<String, String> resMap = new HashMap<>();
        MerchantWithdrawResponse response = userWithdrawalRes(userId, amount.intValue(), remark, tradeNo, billId);
        log.info("用户提现返回信息 :{}", JSONObject.toJSONString(response));
        resMap.put("trans_status", response.getTransStatus());
        resMap.put("withdraw_seq", response.getWithdrawSeq());
        resMap.put("error_msg", response.getErrorMsg());
        return resMap;
    }

    @Override
    public Boolean syncOnlineBank(String gateType, String orderType) {
        OnlineBanklistResponse response = ymBaseService.onlineBanklist(gateType, orderType);
        System.out.println(response);
        return true;
    }

    @Override
    public TableDataInfo<SohuYmOnlineBankVo> getOnlineBank(PageQuery pageQuery) {
        return sohuYmOnlineBankService.getOnlineBank(pageQuery);
    }

    @Override
    public String queryResWithdrawal(String tradeNo, Long billId) {
        return ymBaseService.queryResWithdrawal(tradeNo, billId);
    }

    @Override
    public Boolean isBind() {
        List<SohuAccountBankVo> list = queryPassListByUserId(Arrays.asList(LoginHelper.getUserId()));
        return list.size() > 0;
    }

    /**
     * 发送系统通知
     *
     * @param receiverId 接单人id
     * @param title
     * @param contentMsg
     */
    public void sendNotice(Long receiverId, String title, String contentMsg) {
        NoticeSystemContent content = new NoticeSystemContent();
        content.setTitle(title);
        content.setNoticeTime(DateUtils.getTime());
        content.setType(SystemNoticeEnum.SubType.bankBind.name());
        content.setDetailId(receiverId);
        NoticeContentDetail detail = new NoticeContentDetail();
        detail.setUserId(receiverId);
        detail.setDesc(contentMsg);
        content.setContent(detail);
        String contentJson = JSONUtil.toJsonStr(content);
        remoteMiddleSystemNoticeService.sendAdminNotice(receiverId, title, contentJson, SystemNoticeEnum.Type.bank);
    }
}
