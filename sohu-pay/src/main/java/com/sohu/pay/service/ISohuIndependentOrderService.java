package com.sohu.pay.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.bo.SohuIndependentOrderBo;
import com.sohu.pay.api.model.SohuIndependentOrderModel;
import com.sohu.pay.api.vo.SohuIndependentOrderAggrVo;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.pay.domain.SohuIndependentOrder;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 第三方分账单Service接口
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
public interface ISohuIndependentOrderService {

    /**
     * 查询第三方分账单
     */
    SohuIndependentOrderVo queryById(Long id);

    /**
     * 查询第三方分账单列表
     */
    TableDataInfo<SohuIndependentOrderVo> queryPageList(SohuIndependentOrderBo bo, PageQuery pageQuery);

    /**
     * 查询第三方分账单列表
     */
    List<SohuIndependentOrderVo> queryList(SohuIndependentOrderBo bo);

    /**
     * 查询第三方分账单列表-根据子订单号
     *
     * @param orderNo
     */
    List<SohuIndependentOrderVo> queryListByOrderNo(String orderNo);

    /**
     * 修改第三方分账单
     */
    Boolean insertByBo(SohuIndependentOrderBo bo);

    /**
     * 修改第三方分账单
     */
    Boolean updateByBo(SohuIndependentOrderBo bo);

    /**
     * 校验并批量删除第三方分账单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据子单号删除数据
     *
     * @param orderNo
     * @return
     */
    Boolean deleteByByOrderNo(String orderNo);

    /**
     * 批量添加
     */
    Boolean insertByBoList(List<SohuIndependentOrderBo> boList);

    /**
     * 账单列表详情
     */
    SohuIndependentOrderVo orderDetail(String orderNo, String independentObject);

    /**
     * 查询商品分销统计或者商单分销统计 的金额总和
     *
     * @param userId    用户ID
     * @param tradeType {@link com.sohu.common.core.enums.BusyType}
     * @param startTime
     * @param endTime
     */
    BigDecimal selectSumDistributionAmount(Long userId, String tradeType, Integer independentStatus, String startTime, String endTime);

    /**
     * 查询商品分销统计或者商单分销统计 的分销单数
     *
     * @param userId    用户ID
     * @param tradeType {@link com.sohu.common.core.enums.BusyType}
     * @param startTime
     * @param endTime
     */
    Long selectCountOrder(Long userId, String tradeType, Integer independentStatus, String startTime, String endTime);

    /**
     * 查询商品分销统计或者商单分销聚合统计
     *
     * @param userId
     * @param tradeType
     * @param independentStatus
     * @param startTime
     * @param endTime
     * @return
     */
    SohuIndependentOrderAggrVo selectAggrBySharePerson(Long userId, String tradeType, Integer independentStatus, String startTime, String endTime);

    /**
     * 查询
     *
     * @param userId
     * @param tradeType
     * @param independentStatus
     * @param startTime
     * @param endTime
     */
    BigDecimal selectSumOrderAmount(Long userId, String tradeType, Integer independentStatus, String startTime, String endTime);

    /**
     * 根据userId和订单号查询唯一分账记录
     *
     * @param userId
     * @param outTradeNo
     */
    SohuIndependentOrder selectByOrderAndUserId(Long userId, String outTradeNo);

    /**
     * 修改分账单状态
     *
     * @param outTradeNo
     * @param name
     * @param i
     */
    void updateIndependentStatus(String outTradeNo, String name, int i);

    /**
     * 修改分账单状态
     *
     * @param outTradeNo
     * @param i
     */
    void updateIndependentStatus(String outTradeNo, int i);

    /**
     * 根据用户id获取分账处理中的金额
     */
    BigDecimal selectIndependentingByUserId(Long userId, Integer siteType, Long siteId, List<String> selectRoles,String independentObject, Integer independentStatus, Date startTime, Date endTime);

    /**
     * 分页查询用户的分账记录
     *
     * @param userId            用户ID
     * @param independentObject 分账方对象：平台、分销人、拉新人、国家站长、城市站长 {@link com.sohu.common.core.enums.SohuIndependentObject}
     * @param startTime         开始时间,2023-10-19
     * @param endTime           结束时间,2023-10-19
     * @param pageNum           页码
     * @param pageSize          每页数量
     * @return {@link List}
     */
    Page<SohuIndependentOrderModel> queryByUserId(Long userId, String independentObject, String startTime, String endTime, Integer pageNum, Integer pageSize);

    /**
     * 根据用户id获取邀请好友的金额
     */
    BigDecimal selectIndependentInviteByUserId(Long userId);


    /**
     * 查询邀请好友分账金额
     */
    List<SohuIndependentOrder> inviteAmount(Long userId);

    /**
     * 批量修改分账单
     *
     * @param independentOrderBoList
     */
    Boolean updateByBoList(List<SohuIndependentOrder> independentOrderBoList);

    /**
     * 通过交易流水号更新分账状态
     *
     * @param tradeNo
     * @param independentStatus
     * @return
     */
    Boolean updateIndependentStatusByTradeNo(String tradeNo, Integer independentStatus);

    /**
     * 查询第三方分账单列表-根据子订单号
     *
     * @param tradeNo
     */
    List<SohuIndependentOrderVo> queryListByTradeNo(String tradeNo, Integer independentStatus);

    /**
     * 根据子订单号查询
     *
     * @param userId
     * @param orderNo
     * @param independentStatus
     * @return
     */
    SohuIndependentOrderVo queryByUserOrderNo(Long userId, String orderNo, Integer independentStatus);

    /**
     * 分组查询用户分账金额
     *
     * @param userId
     * @param siteId
     * @param selectRoles
     * @return
     */
    List<SohuIndependentOrderModel> groupIndependentByUserId(Long userId, Long siteId, List<String> selectRoles);

    /**
     * 根据用户id查询邀请用户的分账记录
     *
     * @param consumerUserId    消费者ID
     * @param inviteUserId      邀请人ID
     * @param independentObject 邀请人角色
     * @param independentStatus 分账状态
     * @return
     */
    List<SohuIndependentOrderModel> queryByInviteUser(Long consumerUserId, Long inviteUserId, String independentObject, List<Integer> independentStatus);

}
