package com.sohu.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.AppPathTypeEnum;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.PushJiGuangBizTypeEnum;
import com.sohu.common.core.enums.UserAuthEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.service.SensitiveService;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.entry.api.RemoteAccountEnterService;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.middle.api.bo.SohuEventReportBo;
import com.sohu.middle.api.bo.SohuSiteUserBo;
import com.sohu.middle.api.enums.IdentityTypeEnum;
import com.sohu.middle.api.enums.PictrueTypeEnum;
import com.sohu.middle.api.enums.report.ActionTypeEnum;
import com.sohu.middle.api.enums.report.RegisterReportEnum;
import com.sohu.middle.api.service.RemoteMiddleEventReportService;
import com.sohu.middle.api.service.RemoteMiddleInviteService;
import com.sohu.middle.api.vo.SohuCommonEventReportVo;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.enums.AccountEnum;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.pay.domain.SohuAccount;
import com.sohu.pay.domain.SohuAccountBank;
import com.sohu.pay.enums.IdentityPictureType;
import com.sohu.pay.service.ISohuAccountService;
import com.sohu.pay.service.YmBaseService;
import com.sohu.resource.api.RemoteJiguangService;
import com.sohu.resource.api.domain.bo.SohuJiguangPush2UserReqBo;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.RemoteUserService;
import com.wangcaio2o.ipossa.sdk.response.merchantcreate.MerchantCreateResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 开户账号Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-09
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuAccountServiceImpl extends SohuAccountBaseServiceImpl implements ISohuAccountService, SensitiveService {
    private final AsyncConfig asyncConfig;
    private final YmBaseService ymBaseService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteImService remoteImService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemoteAccountEnterService remoteAccountEnterService;
    @DubboReference
    private RemoteJiguangService remoteJiguangService;
    @DubboReference
    private RemoteAdminService remoteAdminService;
    @DubboReference
    private RemoteMiddleEventReportService remoteMiddleEventReportService;

    @DubboReference
    private RemoteMerchantService remoteMerchantService;

    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

    @DubboReference
    private RemoteMiddleInviteService remoteMiddleInviteService;

    /**
     * 查询开户账号
     */
    @Override
    public SohuAccountVo queryById(Long id) {
        SohuAccountVo sohuAccountVo = baseMapper.selectVoById(id);
        if (Objects.nonNull(sohuAccountVo)) {
            LoginUser user = remoteUserService.queryById(sohuAccountVo.getUserId());
            if (Objects.nonNull(user)) {
                sohuAccountVo.setAvatar(user.getAvatar());
                sohuAccountVo.setNickname(user.getNickname());
            }
        }
        return sohuAccountVo;
    }

    /**
     * 通过userId查询开户账号
     */
    @Override
    public SohuAccountVo queryByUserId(Long userId) {
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuAccount::getUserId, userId);
        SohuAccountVo vo = baseMapper.selectVoOne(wrapper);
        if (Objects.nonNull(vo)) {
            vo.setIdentityTypeName(IdentityTypeEnum.getNameByCode(vo.getIdentityType()));
        }
        return vo;
    }

    @Override
    public SohuAccountVo queryByUserIdOfPass(Long userId) {
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuAccount::getUserId, userId);
        wrapper.eq(SohuAccount::getState, AccountEnum.AccountStatusEnum.Pass.name());
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public SohuAccountVo queryByUserIdOfPassOrWaitApprove(Long userId) {
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuAccount::getUserId, userId);
        wrapper.in(SohuAccount::getState, AccountEnum.AccountStatusEnum.Pass.name(), AccountEnum.AccountStatusEnum.WaitApprove.name());
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public SohuAccountVo queryByUserIdOfRefuse(Long userId) {
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuAccount::getUserId, userId);
        wrapper.eq(SohuAccount::getState, AccountEnum.AccountStatusEnum.Refuse.name());
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public SohuAccountVo queryByUserIdOfEncrypt(Long userId) {
        SohuAccountVo vo = this.queryByUserId(userId);
        this.buildAccountOfEncrypt(vo);
        return vo;
    }

    @Override
    public SohuAccountVo queryByIdOfEncrypt(Long id) {
        SohuAccountVo vo = this.queryById(id);
        this.buildAccountOfEncrypt(vo);
        return vo;
    }

    /**
     * 返回数据脱敏加密
     *
     * @param vo
     * @return
     */
    private void buildAccountOfEncrypt(SohuAccountVo vo) {
        if (Objects.nonNull(vo)) {
            vo.setIdentityTypeName(IdentityTypeEnum.getNameByCode(vo.getIdentityType()));
            vo.setIdentityNoEncrypt(vo.getIdentityNo());
            vo.setIdentityNo(null);
        }
    }

    @Override
    public TableDataInfo<SohuAccountVo> selectPassAccountList(PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SohuAccount::getState, CommonState.Pass);
        IPage<SohuAccountVo> page = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), wrapper);
        return TableDataInfoUtils.build(page);
    }

    @Override
    public TableDataInfo<SohuAccountVo> selectPassAccounts(PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuAccount::getState, AccountEnum.AccountStatusEnum.Pass.name());
        IPage<SohuAccountVo> page = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), wrapper);
        return TableDataInfoUtils.build(page);
    }

    @Override
    public long selectPassAccountCount() {
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SohuAccount::getState, CommonState.Pass);
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public Boolean updateStateOfTimeout() {
        LambdaQueryWrapper<SohuAccount> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAccount::getState, AccountEnum.AccountStatusEnum.Pass)
                .eq(SohuAccount::getIdentityValidityType, 0)
                .lt(SohuAccount::getIdentityEndDate, DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
//        //设置分页参数
//        // 查询总数
//        long total = baseMapper.selectCount(lqw);
//        // 总页数
//        long totalPages = (total + BATCH_SIZE - 1) / BATCH_SIZE;
//        for (int i = 1; i <= totalPages; i++) {
//            // 分页查询
//            Page<SohuAccount> page = new Page<>(i, BATCH_SIZE);
//            log.info("过期查询SohuAccount 第{}/{}", i, totalPages);
//            IPage<SohuAccount> pageResult = baseMapper.selectPage(page, lqw);
//            // 处理查询结果
//            //数据量大了再优化
//            if (CollUtil.isNotEmpty(pageResult.getRecords())) {
//                for (SohuAccount entity:pageResult.getRecords()){
//                    SohuAccount updateEntity=new SohuAccount();
//                    updateEntity.setId(entity.getId());
//                    updateEntity.setState(AccountEnum.AccountStatusEnum.Expire.name());
//                    this.baseMapper.updateById(updateEntity);
//                }
//            }
//        }
        String day = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        List<SohuAccount> list = this.baseMapper.selectTimeoutList(day);
        if (CollUtil.isNotEmpty(list)) {
            for (SohuAccount entity : list) {
                SohuAccount updateEntity = new SohuAccount();
                updateEntity.setId(entity.getId());
                updateEntity.setState(AccountEnum.AccountStatusEnum.Expire.name());
                updateEntity.setRejectReason("实名认证已过期");
                this.baseMapper.updateById(updateEntity);
            }
        }
        return true;
    }

    @Override
    public Boolean updateBailState(Long id, String bailState, String bailSource) {
        SohuAccount entity = this.baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new RuntimeException("数据不存在");
        }
        SohuAccount updateEntity = new SohuAccount();
        updateEntity.setId(id);
        updateEntity.setBailState(bailState);
        updateEntity.setBailSource(bailSource);
        this.baseMapper.updateById(updateEntity);
        entity.setBailState(updateEntity.getBailState());
        entity.setBailSource(updateEntity.getBailSource());
        CompletableFuture.runAsync(() -> this.sendMsgOfBailPayPass(entity), asyncConfig.getAsyncExecutor());
        return true;
    }

    @Override
    public Map<Long, Boolean> afterFreezeMap(List<Long> userIds, Date time) {
        Map<Long, Boolean> map = new HashMap<>();
        LambdaQueryWrapper<SohuAccount> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuAccount::getUserId, userIds);
        lqw.in(SohuAccount::getState, Arrays.asList(AccountEnum.AccountStatusEnum.FreezeUpgrade.name(), AccountEnum.AccountStatusEnum.WaitApprove.name()));
        lqw.isNotNull(SohuAccount::getFreezeTime);
        lqw.lt(SohuAccount::getFreezeTime, time);
        List<SohuAccount> entitys = this.baseMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(entitys)) {
            for (SohuAccount item : entitys) {
                map.put(item.getUserId(), Boolean.TRUE);
            }
        }
        return map;
    }

    @Override
    public SohuAccountVo getInfoByUserPhone(String phone) {
        LoginUser user = remoteUserService.getUserInfoByPhone(phone);
        if (Objects.isNull(user)) {
            throw new ServiceException("用户不存在");
        }
        return queryByUserId(user.getUserId());
    }

    /**
     * 查询开户账号列表
     */
    @Override
    public TableDataInfo<SohuAccountVo> queryPageList(SohuAccountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuAccount> lqw = buildQueryWrapper(bo);
        Page<SohuAccountVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (CollUtil.isEmpty(result.getRecords())) {
            return TableDataInfoUtils.build(result);
        }
        List<Long> userIds = result.getRecords().stream().map(SohuAccountVo::getUserId).collect(Collectors.toList());
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
        List<SohuAccountBankVo> bankVoList = sohuAccountBankMapper.selectVoList(new LambdaQueryWrapper<SohuAccountBank>().in(SohuAccountBank::getUserId, userIds));
        Map<Long, SohuAccountBankVo> bankMap = bankVoList.stream().collect(Collectors.toMap(SohuAccountBankVo::getUserId, Function.identity()));
        for (SohuAccountVo record : result.getRecords()) {
            LoginUser user = userMap.get(record.getUserId());
            record.setExistBank(bankMap.get(record.getUserId()) == null ? false : true);
            if (Objects.nonNull(user)) {
                record.setBindPhone(user.getPhoneNumber());
                record.setAvatar(user.getAvatar());
                record.setNickname(user.getNickname());
            }
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询开户账号列表
     */
    @Override
    public List<SohuAccountVo> queryList(SohuAccountBo bo) {
        LambdaQueryWrapper<SohuAccount> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuAccount> buildQueryWrapper(SohuAccountBo bo) {
        LambdaQueryWrapper<SohuAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuAccount::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountType()), SohuAccount::getAccountType, bo.getAccountType());
        lqw.eq(StringUtils.isNotBlank(bo.getMerchantId()), SohuAccount::getMerchantId, bo.getMerchantId());
        lqw.like(StringUtils.isNotBlank(bo.getMerchantName()), SohuAccount::getMerchantName, bo.getMerchantName());
        lqw.eq(StringUtils.isNotBlank(bo.getIdentityType()), SohuAccount::getIdentityType, bo.getIdentityType());
        lqw.like(StringUtils.isNotBlank(bo.getLegalName()), SohuAccount::getLegalName, bo.getLegalName());
        lqw.eq(StringUtils.isNotBlank(bo.getIdentityNo()), SohuAccount::getIdentityNo, bo.getIdentityNo());
        lqw.eq(StringUtils.isNotBlank(bo.getIdentityBeginDate()), SohuAccount::getIdentityBeginDate, bo.getIdentityBeginDate());
        lqw.eq(StringUtils.isNotBlank(bo.getIdentityEndDate()), SohuAccount::getIdentityEndDate, bo.getIdentityEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getIdentityValidityType()), SohuAccount::getIdentityValidityType, bo.getIdentityValidityType());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), SohuAccount::getContactPhone, bo.getContactPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getCertPhotoA()), SohuAccount::getCertPhotoA, bo.getCertPhotoA());
        lqw.eq(StringUtils.isNotBlank(bo.getCertPhotoB()), SohuAccount::getCertPhotoB, bo.getCertPhotoB());
        lqw.like(StringUtils.isNotBlank(bo.getContactName()), SohuAccount::getContactName, bo.getContactName());
        lqw.eq(StringUtils.isNotBlank(bo.getContactEmail()), SohuAccount::getContactEmail, bo.getContactEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getLicensePhoto()), SohuAccount::getLicensePhoto, bo.getLicensePhoto());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseCode()), SohuAccount::getLicenseCode, bo.getLicenseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseBeginDate()), SohuAccount::getLicenseBeginDate, bo.getLicenseBeginDate());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseEndDate()), SohuAccount::getLicenseEndDate, bo.getLicenseEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseValidityType()), SohuAccount::getLicenseValidityType, bo.getLicenseValidityType());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseProvinceCode()), SohuAccount::getLicenseProvinceCode, bo.getLicenseProvinceCode());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseCityCode()), SohuAccount::getLicenseCityCode, bo.getLicenseCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseAreaCode()), SohuAccount::getLicenseAreaCode, bo.getLicenseAreaCode());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseAddress()), SohuAccount::getLicenseAddress, bo.getLicenseAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getCertPhotoFront()), SohuAccount::getCertPhotoFront, bo.getCertPhotoFront());
        lqw.eq(StringUtils.isNotBlank(bo.getCertPhotoBack()), SohuAccount::getCertPhotoBack, bo.getCertPhotoBack());
        lqw.eq(StringUtils.isNotBlank(bo.getQualificationPhoto()), SohuAccount::getQualificationPhoto, bo.getQualificationPhoto());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuAccount::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getRejectReason()), SohuAccount::getRejectReason, bo.getRejectReason());
        lqw.like(StringUtils.isNotBlank(bo.getLicenseProvinceName()), SohuAccount::getLicenseProvinceName, bo.getLicenseProvinceName());
        lqw.like(StringUtils.isNotBlank(bo.getLicenseCityName()), SohuAccount::getLicenseCityName, bo.getLicenseCityName());
        lqw.like(StringUtils.isNotBlank(bo.getLicenseAreaName()), SohuAccount::getLicenseAreaName, bo.getLicenseAreaName());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseType()), SohuAccount::getLicenseType, bo.getLicenseType());
        lqw.eq(bo.getIsUpgrage() != null, SohuAccount::getIsUpgrage, bo.getIsUpgrage());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(SohuAccount::getCreateTime, DateUtils.beginOfTime(bo.getStartTime()));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(SohuAccount::getCreateTime, DateUtils.endOfTime(bo.getEndTime()));
        }
        lqw.orderByDesc(SohuAccount::getId);
        return lqw;
    }

    /**
     * 新增开户账号
     */
    @Override
    public Long insertByBo(SohuAccountBo bo) {
        Long userId = Objects.nonNull(LoginHelper.getUserId()) ? LoginHelper.getUserId() : bo.getUserId();
        if (!RedisUtils.setObjectIfAbsent("account:lock:" + userId, 1, Duration.ofSeconds(5))) {
            throw new ServiceException("请勿重复提交实名认证信息");
        }
        bo.setUserId(userId);
        LoginUser user = remoteUserService.selectById(userId);
        Objects.requireNonNull(user, "用户不存在");

        SohuAccountVo vo = this.queryByUserId(userId);
        if (Objects.nonNull(vo)) {
            //兼容奇葩调用
            bo.setId(vo.getId());
            this.updateByBo(bo);
            return vo.getId();
        }
        if (StrUtil.isEmpty(bo.getMerchantName())) {
            bo.setMerchantName(bo.getLegalName());
        }
        if (StrUtil.isEmpty(bo.getContactPhone())) {
            bo.setContactPhone(user.getPhoneNumber());
        }
        bo.setState(AccountEnum.AccountStatusEnum.WaitApprove.name());
        SohuAccount add = BeanUtil.toBean(bo, SohuAccount.class);
        validEntityBeforeSave(add);
        validateCertPass(add.getIdentityNo(), add.getLicenseCode());
        //入库
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        // 异步进行翼码校验
        CompletableFuture.runAsync(() -> this.audit(bo), asyncConfig.getAsyncExecutor());
        // 实名认证提交发送消息通知
        if (bo.getNeedMsgNotify()) {
            CompletableFuture.runAsync(() -> this.sendMsgOfEntrySubmit(add.getId(), add.getUserId()), asyncConfig.getAsyncExecutor());
        }
        return add.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(SohuAccountBo bo) {
        validateCertPass(bo.getIdentityNo(), bo.getLicenseCode());

        SohuAccount updateEntity = new SohuAccount();
        this.buildAccountOfEdit(bo, updateEntity);
        updateEntity.setState(AccountEnum.AccountStatusEnum.WaitApprove.name());
        LambdaUpdateWrapper<SohuAccount> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuAccount::getId, bo.getId())
                .set(SohuAccount::getRejectReason, null)
                .set(SohuAccount::getAuditTime, null);
        // 异步进行翼码校验
        CompletableFuture.runAsync(() -> this.audit(bo), asyncConfig.getAsyncExecutor());
        // 实名认证提交发送消息通知
        return baseMapper.update(updateEntity, luw) > 0;
    }

    /**
     * 修改开户账号
     */
    @Override
    public Boolean updateByBo(SohuAccountBo bo) {
        Long userId = Objects.nonNull(LoginHelper.getUserId()) ? LoginHelper.getUserId() : bo.getUserId();
//        if (!RedisUtils.setObjectIfAbsent("account:lock:" + userId, 1, Duration.ofSeconds(5))) {
//            throw new ServiceException("请勿重复提交实名认证信息");
//        }

        SohuAccountVo vo = this.queryByUserId(userId);
        Objects.requireNonNull(vo, "实名认证信息不存在");

        validateCertPass(bo.getIdentityNo(), bo.getLicenseCode());
        Boolean flag = this.updateByBoOfCommon(bo, vo);
        // 异步进行翼码校验
        CompletableFuture.runAsync(() -> this.audit(bo), asyncConfig.getAsyncExecutor());
        // 实名认证提交发送消息通知
        CompletableFuture.runAsync(() -> this.sendMsgOfEntrySubmit(vo.getId(), vo.getUserId()), asyncConfig.getAsyncExecutor());
        return flag;
    }

    @Override
    public Boolean save(SohuAccountBo bo) {
        bo.setId(null);
        Long userId = bo.getUserId();
        if (!RedisUtils.setObjectIfAbsent("account:lock:" + userId, 1, Duration.ofSeconds(5))) {
            throw new ServiceException("请勿重复提交实名认证信息");
        }
        bo.setUserId(userId);
        LoginUser user = remoteUserService.selectById(userId);
        Objects.requireNonNull(user, "用户不存在");

        SohuAccountVo vo = this.queryByUserId(userId);
        if (Objects.nonNull(vo)) {
            //兼容奇葩调用
            bo.setId(vo.getId());

            validateCertPass(bo.getIdentityNo(), bo.getLicenseCode());
            Boolean flag = this.updateByBoOfCommon(bo, vo);
            // 异步进行翼码校验
            CompletableFuture.runAsync(() -> this.audit(bo), asyncConfig.getAsyncExecutor());
            return Boolean.TRUE;
        }
        if (StrUtil.isEmpty(bo.getMerchantName())) {
            bo.setMerchantName(bo.getLegalName());
        }
        if (StrUtil.isEmpty(bo.getContactPhone())) {
            bo.setContactPhone(user.getPhoneNumber());
        }
        bo.setState(AccountEnum.AccountStatusEnum.WaitApprove.name());
        SohuAccount add = BeanUtil.toBean(bo, SohuAccount.class);
        validEntityBeforeSave(add);
        validateCertPass(add.getIdentityNo(), add.getLicenseCode());
        //入库
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        // 异步进行翼码校验
        CompletableFuture.runAsync(() -> this.audit(bo), asyncConfig.getAsyncExecutor());
        return Boolean.TRUE;
    }

    /**
     * 修改开户账号-公共部分
     *
     * @param bo
     * @return
     */
    public Boolean updateByBoOfCommon(SohuAccountBo bo, SohuAccountVo vo) {
        if (!Objects.equals(vo.getId(), bo.getId())) {
            throw new RuntimeException("非法修改");
        }
        SohuAccount updateEntity = new SohuAccount();
        if (BooleanUtil.isTrue(vo.getHasMerchant())) {
            //有分账商户（翼码）
            if (!Objects.equals(bo.getAccountType(), vo.getAccountType())) {
                throw new RuntimeException("账号类型不能修改");
            }
            this.buildAccountOfHasMerchant(bo, updateEntity);
        } else if (StrUtil.isNotEmpty(vo.getMerchantId())) {
            //已经有翼码id
            if (!Objects.equals(bo.getAccountType(), vo.getAccountType())) {
                throw new RuntimeException("账号类型不能修改");
            }
            this.buildAccountOfHasMerchantId(bo, vo, updateEntity);
        } else {
            this.buildAccountOfEdit(bo, updateEntity);
        }
        updateEntity.setState(AccountEnum.AccountStatusEnum.WaitApprove.name());
        LambdaUpdateWrapper<SohuAccount> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuAccount::getId, bo.getId())
                .set(SohuAccount::getRejectReason, null)
                .set(SohuAccount::getAuditTime, null);
        return baseMapper.update(updateEntity, luw) > 0;
    }


    @Override
    public Boolean saveByBo(SohuAccountBo bo) {
        SohuAccountVo vo = this.queryByUserId(LoginHelper.getUserId());
        if (Objects.isNull(vo)) {
            return this.insertByBo(bo) > 0;
        } else {
            if (Objects.equals(vo.getState(), AccountEnum.AccountStatusEnum.WaitApprove.name())
                    || Objects.equals(vo.getState(), AccountEnum.AccountStatusEnum.Pass.name())) {
                //此状态不能修改，直接跳过
                return true;
            }
            bo.setId(vo.getId());
            return this.updateByBoOfCommon(bo, vo);
        }
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuAccount entity) {
        if (entity.getIdentityType().equals(IdentityTypeEnum.passport.getCode())) {
            if (StrUtil.isEmpty(entity.getCertPhotoFront())) {
                throw new RuntimeException("护照图片不能为空");
            }
        }

        if (entity.getIdentityType().equals(IdentityTypeEnum.IdentificationCard.getCode())) {
            if (StrUtil.isEmpty(entity.getCertPhotoFront()) || StrUtil.isEmpty(entity.getCertPhotoBack())) {
                throw new RuntimeException("身份证正面或反面图片不能为空");
            }
        }

    }

    @Override
    public List<SohuAccountVo> selectListByUserIdsOfPass(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuAccount::getState, AccountEnum.AccountStatusEnum.Pass.name());
        wrapper.in(SohuAccount::getUserId, userIds);
        return this.baseMapper.selectVoList(wrapper);
    }

    @Override
    public Map<Long, SohuAccountVo> selectMapByUserIdsOfPass(Collection<Long> userIds) {
        List<SohuAccountVo> list = selectListByUserIdsOfPass(userIds);
        return list.stream().collect(Collectors.toMap(SohuAccountVo::getUserId, p -> p));
    }

    @Override
    public Map<Long, SohuAccountVo> selectAccountMapByUserIds(Collection<Long> userIds) {
        LambdaQueryWrapper<SohuAccount> wrapper = Wrappers.lambdaQuery();
        //wrapper.eq(SohuAccount::getState, AccountEnum.AccountStatusEnum.Pass.name());
        wrapper.in(SohuAccount::getUserId, userIds);
        List<SohuAccountVo> list = this.baseMapper.selectVoList(wrapper);
        return CollUtil.isEmpty(list) ? new HashMap<>() : list.stream().collect(Collectors.toMap(SohuAccountVo::getUserId, p -> p));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(SohuAccountBo bo) {
        SohuAccountVo sohuAccountVo = baseMapper.selectVoById(bo.getId());
        // 业务数据校验
        validateEntityBeforeAudit(sohuAccountVo);
        sohuAccountVo.setMerId(bo.getMerId());
        return this.auditOfCommon(sohuAccountVo, true);
    }

    /**
     * 审核-公共部分
     *
     * @param needMsgNotify 需要消息通知
     * @return
     */
    private Boolean auditOfCommon(SohuAccountVo sohuAccountVo, Boolean needMsgNotify) {
        try {
            //证件图片上传
            uploadPicture(sohuAccountVo);
            //判断商户是否已经注册
            if (StrUtil.isEmpty(sohuAccountVo.getMerchantId())) {
                this.registerMerchant(sohuAccountVo);
            }
            // 判断用户在翼码是否存在分账账户,存在则变更翼码商户分账信息，否则新增分账账户
            boolean isExist = BooleanUtil.isTrue(sohuAccountVo.getHasMerchant());
            if (isExist) {
                this.modifyYmAccount(sohuAccountVo);
            } else {
                this.registerYmAccount(sohuAccountVo);
            }
            // 审核通过数据更新
            return updateSohuAccountOfSuccess(sohuAccountVo, needMsgNotify);
        } catch (Exception e) {
            log.warn("翼码实名认证异常{}", e.getMessage());
            e.printStackTrace();
            // 审核不通过数据更新
            sohuAccountVo.setRejectReason(e.getMessage());
            return updateSohuAccountOfRefuse(sohuAccountVo, needMsgNotify);
        }
    }

    /**
     * 翼码分账商户开户
     *
     * @param sohuAccountVo
     */
    private void registerYmAccount(SohuAccountVo sohuAccountVo) {
        String accountType = sohuAccountVo.getAccountType();
        //企业分账商户开户
        if (AccountEnum.AccountTypeEnum.BUSINESS.getCode().equals(accountType)) {
            ymBaseService.registerBusinessAccount(sohuAccountVo);
        } else {
            //个人分账商户开户
            ymBaseService.registerPersonalAccount(sohuAccountVo);
        }
    }

    /**
     * 翼码分账商户修改
     *
     * @param sohuAccountVo
     */
    private void modifyYmAccount(SohuAccountVo sohuAccountVo) {
        String accountType = sohuAccountVo.getAccountType();
        if (AccountEnum.AccountTypeEnum.BUSINESS.getCode().equals(accountType)) {
            //修改企业分账商户信息
            ymBaseService.modifyBusinessAccount(sohuAccountVo);
        } else {
            //修改个人分账商户信息
            ymBaseService.modifyPersonalAccount(sohuAccountVo);
        }
    }

    @Override
    public String auditByThirdParty(SohuAccountBo bo) {
        SohuAccountVo vo = this.queryByUserId(bo.getUserId());
        if (Objects.isNull(vo)) {
            throw new RuntimeException("实名认证信息不存在");
        }
        if (AccountEnum.AccountStatusEnum.Pass.name().equals(vo.getState())) {
            //已经通过，直接跳过
            return null;
        } else if (AccountEnum.AccountStatusEnum.Refuse.name().equals(vo.getState()) || AccountEnum.AccountStatusEnum.Expire.name().equals(vo.getState())) {
            //已经拒绝，给出原因
            return vo.getRejectReason();
        }
        if (!this.auditOfCommon(vo, false)) {
            return vo.getRejectReason();
        } else {
            return null;
        }
    }

    /**
     * 审核通过后异步处理其它业务逻辑
     *
     * @param sohuAccountVo SohuAccountVo
     */
    private Boolean updateSohuAccountOfSuccess(@NotNull SohuAccountVo sohuAccountVo, Boolean needMsgNotify) {
        sohuAccountVo.setState(AccountEnum.AccountStatusEnum.Pass.name());
        sohuAccountVo.setAuditTime(new Date());
        sohuAccountVo.setHasMerchant(true);
        SohuAccount update = BeanUtil.toBean(sohuAccountVo, SohuAccount.class);
        baseMapper.updateById(update);
        // 审核通过更新代理邀请信息
        if (remoteAdminService.existsAgentUserOfWaitJoin(sohuAccountVo.getUserId())) {
            remoteAdminService.updateInviteState(sohuAccountVo.getUserId(), sohuAccountVo.getMerchantName(), sohuAccountVo.getAccountType(), sohuAccountVo.getContactEmail());
        }
        //绑定拉新关系
        remoteUserService.saveInviteInfo(sohuAccountVo.getUserId());
        if (needMsgNotify) {
            // 审核通过发送消息通知
            CompletableFuture.runAsync(() -> sendMsgOfEntryPass(sohuAccountVo.getUserId()), asyncConfig.getAsyncExecutor());
            CompletableFuture.runAsync(() -> sendMsgOfBankBind(sohuAccountVo.getUserId()), asyncConfig.getAsyncExecutor());
            //发送极光推送审核结果通知
            CompletableFuture.runAsync(() -> this.pushAuditResultNotice(update), asyncConfig.getAsyncExecutor());
            // 实名认证成功埋点
            CompletableFuture.runAsync(() -> this.buildSuccessEventReport(sohuAccountVo), asyncConfig.getAsyncExecutor());
        }
        //如果认证信息升级，解绑银行卡后需重新绑定
        if (sohuAccountVo.getIsUpgrage() == Constants.ONE) {
            SohuAccountBank sohuAccountBank = sohuAccountBankMapper.selectOne(new LambdaQueryWrapper<SohuAccountBank>().eq(SohuAccountBank::getUserId, sohuAccountVo.getUserId()));
            if (Objects.nonNull(sohuAccountBank) && !sohuAccountBank.getHasMerchant()) {
                boolean business = StringUtils.equals(sohuAccountVo.getAccountType(), UserAuthEnum.business.getType());
                sohuAccountBank.setSettleType(business ? "1" : "2");
                sohuAccountBank.setMerchantId(sohuAccountVo.getMerchantId());
                // 发消息到队列
                MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(sohuAccountBank), MqKeyEnum.ACCOUNT_BANK_ADD.getKey());
                remoteStreamMqService.sendNormalMsg(mqMessaging);
            }
            //更新绑定银行卡信息
//            LambdaUpdateWrapper<SohuAccountBank> updateWrapper = new LambdaUpdateWrapper<>();
//            updateWrapper.eq(SohuAccountBank::getUserId, sohuAccountVo.getUserId());
//            updateWrapper.set(SohuAccountBank::getMerchantId, null);
//            updateWrapper.set(SohuAccountBank::getHasMerchant, false);
//            updateWrapper.set(SohuAccountBank::getState,  AccountEnum.AccountStatusEnum.Refuse.name());
//            updateWrapper.set(SohuAccountBank::getRejectReason, "对公结算，银行信息不完整");
//            sohuAccountBankMapper.update(null, updateWrapper);
        }
        return true;
    }

    /**
     * 实名认证成功埋点
     *
     * @param sohuAccountVo SohuAccountVo
     */
    private void buildSuccessEventReport(SohuAccountVo sohuAccountVo) {
        SohuCommonEventReportVo loginEventReportVo = new SohuCommonEventReportVo();
        loginEventReportVo.setAccount(true);
        getUserGenderAndAge(sohuAccountVo, loginEventReportVo);
        SohuEventReportBo bo = new SohuEventReportBo(
                RegisterReportEnum.getCode(RegisterReportEnum.SSRZ.getType()),
                RegisterReportEnum.SSRZ.getDesc(),
                ActionTypeEnum.Register.name(),
                JSONUtil.toJsonStr(loginEventReportVo),
                RegisterReportEnum.SSRZ.getType(),
                sohuAccountVo.getUserId());
        remoteMiddleEventReportService.getEventId(bo);
    }

    /**
     * 获取用户年龄和性别
     *
     * @param sohuAccountVo      SohuAccountVo
     * @param loginEventReportVo SohuCommonEventReportVo
     */
    private static void getUserGenderAndAge(SohuAccountVo sohuAccountVo, SohuCommonEventReportVo loginEventReportVo) {
        // 获取身份证号的第17位
        String identityNo = sohuAccountVo.getIdentityNo();
        if (identityNo.length() >= 17) {
            char genderChar = identityNo.charAt(16);
            int genderInt = Character.getNumericValue(genderChar);

            // 判断第17位是否为奇数
            if (genderInt % 2 != 0) {
                loginEventReportVo.setGender("0");
            } else {
                loginEventReportVo.setGender("1");
            }

            // 计算用户的实际年龄
            String birthDateStr = identityNo.substring(6, 14);
            LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDate currentDate = LocalDate.now();
            int age = Period.between(birthDate, currentDate).getYears();
            loginEventReportVo.setAge(age);
        } else {
            loginEventReportVo.setGender("2");
        }
    }


    /**
     * 前端传递参数校验
     *
     * @param bo SohuAccountBo
     */
    private void validateParams(SohuAccountBo bo) {
        if (bo.getId() == null || StringUtils.isBlank(bo.getState())) {
            throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    /**
     * 审核前的数据校验
     *
     * @param sohuAccountVo 审核对象
     */
    private void validateEntityBeforeAudit(SohuAccountVo sohuAccountVo) {
        if (ObjectUtil.isEmpty(sohuAccountVo)) {
            throw new RuntimeException(MessageUtils.message("WRONG_AUDIT_INFO_IS_NOT_EXIST"));
        }
        if (!sohuAccountVo.getState().equals(AccountEnum.AccountStatusEnum.WaitApprove.name())) {
            throw new RuntimeException(MessageUtils.message("already.audit"));
        }
    }

    /**
     * 校验翼码图片是否完整
     *
     * @param sohuAccountVo SohuAccountVo
     */
    private Boolean validateCertPhotos(SohuAccountVo sohuAccountVo) {
        if (sohuAccountVo.getCertPhotoFront().isEmpty() || sohuAccountVo.getCertPhotoBack().isEmpty()) {
            throw new RuntimeException("证件照正反面不能为空");
        }
        if (StringUtils.isEmpty(sohuAccountVo.getCertPhotoA()) || StringUtils.isEmpty(sohuAccountVo.getCertPhotoB())) {
            throw new RuntimeException("上传翼码证件照正反面不能为空");
        }
        return true;
    }

    /**
     * 审核后更新数据库数据
     *
     * @param vo vo
     * @return
     */
    private Boolean updateSohuAccountOfRefuse(SohuAccountVo vo, Boolean needMsgNotify) {
        vo.setState(AccountEnum.AccountStatusEnum.Refuse.name());
        vo.setAuditTime(new Date());
        SohuAccount update = BeanUtil.toBean(vo, SohuAccount.class);
        baseMapper.updateById(update);
        if (needMsgNotify) {
            // 认证失败发送IM消息
            CompletableFuture.runAsync(() -> sendMsgOfEntryFail(vo.getUserId(), vo.getRejectReason()), asyncConfig.getAsyncExecutor());
            //发送极光推送审核结果通知
            CompletableFuture.runAsync(() -> this.pushAuditResultNotice(update), asyncConfig.getAsyncExecutor());
            // 实名认证失败埋点
            CompletableFuture.runAsync(() -> this.buildRefuseEventReport(vo), asyncConfig.getAsyncExecutor());
            // 更新店铺状态
            CompletableFuture.runAsync(() -> remoteMerchantService.updateMerchantStatus(vo.getUserId(), vo.getMerId(), CommonState.AuthFail.getCode(), "实名未认证通过"), asyncConfig.getAsyncExecutor());
        }
        return true;
    }

    /**
     * 实名认证失败埋点
     *
     * @param sohuAccountVo SohuAccountVo
     */
    private void buildRefuseEventReport(SohuAccountVo sohuAccountVo) {
        SohuCommonEventReportVo loginEventReportVo = new SohuCommonEventReportVo();
        loginEventReportVo.setAccount(false);
        getUserGenderAndAge(sohuAccountVo, loginEventReportVo);
        SohuEventReportBo bo = new SohuEventReportBo(
                RegisterReportEnum.getCode(RegisterReportEnum.SSRZ.getType()),
                RegisterReportEnum.SSRZ.getDesc(),
                ActionTypeEnum.Register.name(),
                JSONUtil.toJsonStr(loginEventReportVo),
                RegisterReportEnum.SSRZ.getType(),
                sohuAccountVo.getUserId());
        remoteMiddleEventReportService.getEventId(bo);
    }

    /**
     * 推送实名认证审核结果通知
     *
     * @param sohuAccount
     */
    private void pushAuditResultNotice(SohuAccount sohuAccount) {
        SohuJiguangPush2UserReqBo reqBo = new SohuJiguangPush2UserReqBo();
        reqBo.setUserIds(Lists.newArrayList(sohuAccount.getUserId()));
        reqBo.setJumpPathType(AppPathTypeEnum.ENTRY_VERIFIED_DETAIL);
        reqBo.setBizType(PushJiGuangBizTypeEnum.ACCOUNT);
        String title = "";
        String alert = "";
        if (StringUtils.equals(AccountEnum.AccountStatusEnum.Pass.name(), sohuAccount.getState())) {
            title = "实名认证审核通过";
            alert = "您的实名认证申请已通过审核。";
        } else if (StringUtils.equals(AccountEnum.AccountStatusEnum.Refuse.name(), sohuAccount.getState())) {
            title = "实名认证审核未通过";
            alert = String.format("未通过原因：%s", sohuAccount.getRejectReason());
        }
        reqBo.setTitle(title);
        reqBo.setAlert(alert);
        try {
            remoteJiguangService.push2User(reqBo);
        } catch (Exception e) {
            log.warn("实名认证审核极光推送异常，原因: {}", e.getMessage());
        }
    }

    /**
     * 添加分账商户
     *
     * @param sohuAccountVo SohuAccountVo
     * @return String
     */
    public void registerMerchant(SohuAccountVo sohuAccountVo) {
        AccountEnum.AccountTypeEnum accountTypeEnum = AccountEnum.AccountTypeEnum.getByCode(sohuAccountVo.getAccountType());
        // 获取用户数据
        LoginUser user = remoteUserService.queryById(sohuAccountVo.getUserId());
        // 添加翼码用户
        MerchantCreateResponse response = ymBaseService.createMerchant(user.getUsername(), user.getPhoneNumber(), sohuAccountVo.getMerchantName(), accountTypeEnum.getYmtype());
        if (!response.isSuccess()) {
            throw new RuntimeException(convertErrMsg(response.getResult().getComment()));
        }
        sohuAccountVo.setMerchantId(response.getMerchantId());
    }

    /**
     * 转换错误信息
     *
     * @param msg
     * @return
     */
    private String convertErrMsg(String msg) {
        return AccountEnum.AccountErrEnum.getByCode(msg);
    }

    /**
     * 图片处理
     *
     * @param sohuAccountVo
     */
    private Boolean uploadPicture(SohuAccountVo sohuAccountVo) {
        //法人证件处理
        String identityType = sohuAccountVo.getIdentityType();
        if (StringUtils.isNotBlank(identityType)) {
            PictrueTypeEnum pictureType = IdentityPictureType.getPictureType(identityType);
            if (pictureType != null) {
                // 处理证件照片
                if (StringUtils.isNotBlank(sohuAccountVo.getCertPhotoFront())) {
                    sohuAccountVo.setCertPhotoA(ymBaseService.urlChangeBase64(sohuAccountVo.getCertPhotoFront(), pictureType));
                }
                if (StringUtils.isNotBlank(sohuAccountVo.getCertPhotoBack())) {
                    sohuAccountVo.setCertPhotoB(ymBaseService.urlChangeBase64(sohuAccountVo.getCertPhotoBack(), pictureType));
                }
            }
        }
        // 处理资质照片
        if (StringUtils.isNotBlank(sohuAccountVo.getQualificationPhoto()) && StringUtils.isBlank(sohuAccountVo.getLicensePhoto())) {
            sohuAccountVo.setLicensePhoto(ymBaseService.urlChangeBase64(sohuAccountVo.getQualificationPhoto(), PictrueTypeEnum.LicensePhoto));
        }
        return true;
    }

    /**
     * 批量删除开户账号
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            //TODO 做一些业务上的校验,判断是否需要校验
//        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    /**
     * 通过手机号+验证码获取用户开户信息
     *
     * @param userId
     * @param smsCode
     * @return {@link SohuAccountVo}
     */
    @Override
    public SohuAccountVo getInfoBySms(Long userId, String smsCode) {
        if (StringUtils.isEmpty(smsCode)) {
            return null;
        }
        // 根据用户id查询实名认证手机号
//        LambdaQueryWrapper<SohuAccount> lqw = Wrappers.lambdaQuery();
//        lqw.eq(SohuAccount::getUserId, userId);
//        SohuAccountVo sohuAccountVo = baseMapper.selectVoOne(lqw);
        SohuAccountVo vo = this.queryByUserId(userId);
        String contactPhone = vo.getContactPhone();
        // 校验验证码
        validateSmsCode(contactPhone, smsCode);
        return vo;
    }

    @Override
    public List<SohuAccountVo> getAccountBySiteId(SohuSiteUserBo sohuSiteUserBo) {
//        if (CollUtil.isEmpty(sohuSiteUserBo.getCitySiteId())) {
//            throw new RuntimeException("当前无城市站点");
//        }
        if (CollUtil.isEmpty(sohuSiteUserBo.getCitySiteId())) {
            return baseMapper.getAccountBySiteId(null);
        }
        return baseMapper.getAccountBySiteId(sohuSiteUserBo.getCitySiteId());
    }

    /**
     * 校验短信验证码
     */
    public boolean validateSmsCode(String phoneNumber, String smsCode) {
        String code = RedisUtils.getCacheObject(CacheConstants.PRIVACY_CODE_KEY + phoneNumber);
        if (StringUtils.isBlank(code)) {
            throw new RuntimeException("输入的验证码有误");
        }
        boolean result = code.equals(smsCode);
        // 验证码只能使用一次
        if (result) {
            RedisUtils.deleteObject(CacheConstants.PRIVACY_CODE_KEY + phoneNumber);
        } else {
            throw new RuntimeException("输入的验证码有误");
        }
        return result;
    }

    /**
     * 数据脱敏接口
     */
    @Override
    public boolean isSensitive() {
        // TODO 手机+验证码通过则无需脱敏
        return !LoginHelper.isAdmin();
    }

}
