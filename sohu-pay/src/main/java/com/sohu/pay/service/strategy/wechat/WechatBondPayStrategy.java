package com.sohu.pay.service.strategy.wechat;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.admin.api.RemoteMerchantBondService;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.SohuMerchantBondVo;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.pay.api.RemoteMerchantBondPayService;
import com.sohu.pay.api.bo.SohuMerchantBondPayBo;
import com.sohu.pay.api.bo.SohuMerchantBondRefundBo;
import com.sohu.pay.api.domain.SohuPayQueryBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.enums.BondPayTypeEnum;
import com.sohu.pay.api.model.SohuPayResultModel;
import com.sohu.pay.api.vo.SohuMerchantBondPayVo;
import com.sohu.pay.api.vo.SohuMerchantBondRefundVo;
import com.sohu.pay.service.ISohuMerchantBondPayService;
import com.sohu.pay.service.ISohuMerchantBondRefundService;
import com.sohu.pay.service.strategy.PaymentProcessor;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.pay.vo.RefundResultVo;
import com.sohu.shopgoods.api.RemoteProductCategoryPcService;
import com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.third.wechat.pay.bean.WechatPayConfig;
import com.sohu.third.wechat.pay.constant.WechatPayConstants;
import com.sohu.third.wechat.pay.exception.WechatPayException;
import com.sohu.third.wechat.pay.request.WechatPayOrderQueryRequest;
import com.sohu.third.wechat.pay.request.WechatPayRefundRequest;
import com.sohu.third.wechat.pay.request.WechatPayUnifiedOrderRequest;
import com.sohu.third.wechat.pay.response.WechatPayQueryResponse;
import com.sohu.third.wechat.pay.response.WechatPayRefundQueryResponse;
import com.sohu.third.wechat.pay.response.WechatPayRefundResponse;
import com.sohu.third.wechat.pay.response.WechatPaymentResponse;
import com.sohu.third.wechat.pay.service.WechatPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: leibo
 * @Date: 2025/5/12 19:46
 **/
@Slf4j
@Component
public class WechatBondPayStrategy implements WechatPayStrategy {

    @DubboReference
    private RemoteMerchantBondService merchantBondService;
    @Autowired
    private ISohuMerchantBondPayService merchantBondPayService;
    @Autowired
    private ISohuMerchantBondRefundService merchantBondRefundService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteMerchantService merchantService;
    @DubboReference
    private RemoteProductCategoryPcService productCategoryPcService;
    @DubboReference
    private RemoteMerchantBondPayService remoteMerchantBondPayService;

    @Override
    public String payment(SohuPrePayBo payBo) {
        Long masterId = payBo.getMasterId();
        if (Objects.isNull(masterId)) {
            throw new ServiceException("业务id不能为空");
        }
        BigDecimal amount = payBo.getAmount();
        if (amount == null || CalUtils.isLessEqualZero(amount)) {
            throw new ServiceException(MessageUtils.message("付款金额不能小于等于0"));
        }
        // 基于保证金id查询保证金记录
        SohuMerchantBondVo merchantBondVo = merchantBondService.getById(payBo.getMasterId());
        if (Objects.isNull(merchantBondVo) || !merchantBondVo.getPayStatus().equals(CommonState.WaitPay.getCode())) {
            throw new ServiceException("当前保证金记录异常,请刷新后重试");
        }
        BigDecimal waitPayAmount = CalUtils.sub(merchantBondVo.getBondAmount(), merchantBondVo.getPaidAmount());
        if (CalUtils.isLessEqualZero(waitPayAmount)) {
            throw new ServiceException(MessageUtils.message("待付款金额不能小于等于0"));
        }
        if (!CalUtils.equal(amount, waitPayAmount)) {
            throw new ServiceException(MessageUtils.message("付款金额与待付款金额不一致,请刷新后重试"));
        }
        // 判断是否存在线下审核中的记录,存在则打回
        List<String> statusList = new ArrayList<>();
        statusList.add(CommonState.WaitApprove.getCode());
        List<SohuMerchantBondPayVo> merchantBondPayList = merchantBondPayService.queryListByMerchantBondIdAndStatus(merchantBondVo.getId(), statusList);
        if (!CollectionUtils.isEmpty(merchantBondPayList)) {
            throw new ServiceException("当前已存在审核中的缴纳记录，请等待审核完成后再进行付款");
        }
        // 不存在,则查询是否存在线上记录，存在则执行关单操作
        statusList.add(CommonState.WaitApprove.getCode());
        merchantBondPayList = merchantBondPayService.queryListByMerchantBondIdAndStatus(merchantBondVo.getId(), statusList);
        if (!CollectionUtils.isEmpty(merchantBondPayList)) {
            // 执行关单操作
            for (SohuMerchantBondPayVo merchantBondPayVo : merchantBondPayList) {
                if (remoteMerchantBondPayService.cancelBondPay(merchantBondPayVo.getOrderNo())) {
                    throw new ServiceException("当前类目保证金状态有变更,请刷新后再试");
                }
            }
        }
        // 查询店铺信息
        SohuMerchantModel merchant = merchantService.selectById(merchantBondVo.getMerId());
        // 查询分类信息
        SohuProductCategoryPcVo categoryPcVo = productCategoryPcService.queryById(merchantBondVo.getCateId());
        // 进入微信小程序支付
        try {
            // 用户信息
            LoginUser user = LoginHelper.getLoginUser();
            validLoginUser(user);
            // 组装下单数据对象-公共信息
            WechatPayUnifiedOrderRequest request = new WechatPayUnifiedOrderRequest();
            // 查询微信配置config
            WechatPayConfig wechatPayConfig = getWechatPayConfig(payBo.getPayType());
            wechatPayConfig.setTradeType(WechatPayConstants.TradeType.APP);
            request.setGoodsTag("WXG");
            request.setNotifyUrl(wechatPayConfig.getNotifyUrl());
            // 返回对象
            WechatPaymentResponse wechatPaymentResponse = null;
            WechatPayUnifiedOrderRequest.Amount wechatAmount = new WechatPayUnifiedOrderRequest.Amount();
            wechatAmount.setTotal(CalUtils.yuanToCent(waitPayAmount).intValue());
            request.setOutTradeNo(NumberUtil.getOrderNo(OrderConstants.BOND_PREFIX));
            request.setDescription(merchant.getName() + "支付" + categoryPcVo.getName() + "分类的保证金");
            request.setAmount(wechatAmount);
            log.info("微信支付请求返回入参：{}" + JSONUtil.toJsonStr(request));
            wechatPaymentResponse = WechatPayService.unifiedOrder(request, wechatPayConfig);
            // 存储记录
            SohuMerchantBondPayBo merchantBondPayBo = new SohuMerchantBondPayBo();
            merchantBondPayBo.setUserId(LoginHelper.getUserId());
            merchantBondPayBo.setMerchantBondId(masterId);
            // 订单支付单号
            merchantBondPayBo.setOrderNo(request.getOutTradeNo());
            // 支付方式
            merchantBondPayBo.setPayType(BondPayTypeEnum.WECHAT.getCode());
            // 状态
            merchantBondPayBo.setPayStatus(CommonState.WaitPay.getCode());
            // 支付金额
            merchantBondPayBo.setPayAmount(waitPayAmount);
            // 无手续费
            merchantBondPayBo.setChargeAmount(BigDecimal.ZERO);
            // 分类保证金的区间
            merchantBondPayBo.setGmvInterval(merchantBondVo.getGmvInterval());
            merchantBondPayService.insertByBo(merchantBondPayBo);
            // 加入自动未支付自动取消队列
            MqMessaging mqCancelMessaging = new MqMessaging(request.getOutTradeNo(), MqKeyEnum.CANCEL_BOND_ORDER.getKey());
            remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 7L);
            log.info("已发送延时取消队列-cancel_bond_order:{}", request.getOutTradeNo());
            String payment = JSONUtil.toJsonStr(wechatPaymentResponse);
            log.warn("微信支付请求返回：{}" + payment);
            return payment;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        try {
            // 支付配置
            WechatPayConfig wechatPayConfig = getWechatPayConfig(refundPayBo.getPayType());
            // 组装微信小程序退款请求参数
            WechatPayRefundRequest wechatPayRefundRequest = new WechatPayRefundRequest();
            wechatPayRefundRequest.setTransactionId(refundPayBo.getTransactionId());
            wechatPayRefundRequest.setOutRefundNo(refundPayBo.getOutTradeNo());
            // 拼接退款回调
            wechatPayRefundRequest.setNotifyUrl(wechatPayConfig.getNotifyUrl() + "/refund");
            wechatPayRefundRequest.setReason("保证金正常退款");
            // 退款金额、数量——目前只支持全部退款，不支持部分退款
            WechatPayRefundRequest.Amount amount = new WechatPayRefundRequest.Amount();
            Integer refundPrice = BigDecimalUtils.yuanToFen(refundPayBo.getAmount());
            amount.setRefund(refundPrice);
            amount.setTotal(refundPrice);
            wechatPayRefundRequest.setAmount(amount);
            // 微信小程序发起退款接口
            WechatPayRefundResponse refundResponse = WechatPayService.refund(wechatPayRefundRequest, wechatPayConfig);
            MqMessaging mqCancelMessaging = new MqMessaging(refundPayBo.getOutTradeNo(), MqKeyEnum.REFUND_RESULT_QUERY.getKey());
            remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 5L);
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public String payCallback(String callbackResponse) {
        // 实现微信支付的支付回调逻辑
        log.info("WechatAppPay支付 - 支付回调 ：{}", callbackResponse);
        return null;
    }

    @Override
    public SohuPayResultModel paySuccess(SohuPayQueryBo payQueryBo) {
        WechatPayOrderQueryRequest queryRequest = new WechatPayOrderQueryRequest();
        queryRequest.setOutTradeNo(payQueryBo.getOrderNo());
        // 回调时不是支付成功状态先主动查询，如果主动查询状态也是不是SUCCESS就返回false
        WechatPayQueryResponse wechatPayQueryResponse;
        SohuPayResultModel payPalResultResponse = new SohuPayResultModel();
        try {
            wechatPayQueryResponse = WechatPayService.queryOrder(queryRequest, getWechatPayConfig(payQueryBo.getPayType()));
            if (!wechatPayQueryResponse.getTradeState().equals("SUCCESS") || ObjectUtil.isNull(wechatPayQueryResponse) || wechatPayQueryResponse.getCode().equals("ORDER_NOT_EXIST")) {
                payPalResultResponse.setStatus(Boolean.FALSE).setOrderNo(payQueryBo.getOrderNo());
                return payPalResultResponse;
            }
            BigDecimal centToYuanTotal = BigDecimalUtils.centToYuan(new BigDecimal(wechatPayQueryResponse.getAmount().getPayerTotal()));
            // 返回支付成功相关结果
            payPalResultResponse.setPayPrice(centToYuanTotal).setOrderNo(wechatPayQueryResponse.getOutTradeNo()).setStatus(Boolean.TRUE);
        } catch (WechatPayException e) {
            throw new RuntimeException(e);
        }
        return payPalResultResponse;
    }

    @Override
    public Boolean close(String outTradeNo, String payType) {
        WechatPayConfig wechatPayConfig = getWechatPayConfig(payType);
        try {
            return WechatPayService.closeOrder(outTradeNo, wechatPayConfig).isSuccess();
        } catch (WechatPayException e) {
            throw new RuntimeException(e);
        }
    }

    public RefundResultVo queryRefundResult(String transactionId, String refundOrderNo) {
        log.info("查询微信退款返回结果入参transactionId:{},refundOrderNo:{}", transactionId, refundOrderNo);
        // 支付配置
        WechatPayConfig wechatPayConfig = getWechatPayConfig(PayTypeEnum.PAY_TYPE_WECHAT_APP.getStatus());
        try {
            WechatPayRefundQueryResponse response = WechatPayService.refundQuery(refundOrderNo, wechatPayConfig);
            log.info("查询微信退款返回结果response:{}", JsonUtils.toJsonString(response));
            RefundResultVo resultVo = new RefundResultVo();
            if (response.isSuccess() && (response.getStatus().equals("SUCCESS") || response.getStatus().equals("PROCESSING"))){
                if (response.getStatus().equals("SUCCESS")) {
                    // 退款成功
                    resultVo.setStatus(Boolean.TRUE);
                    resultVo.setRefundTransactionId(response.getRefundId());
                    resultVo.setSuccessTime(DateUtils.parseDate(response.getSuccessTime()));
                }
                if (response.getStatus().equals("PROCESSING")) {
                    // 退款中
                    MqMessaging mqCancelMessaging = new MqMessaging(refundOrderNo, MqKeyEnum.REFUND_RESULT_QUERY.getKey());
                    remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 5L);
                    return null;
                }
            } else {
                // 退款失败
                resultVo.setStatus(Boolean.FALSE);
                resultVo.setErrorReason(response.getMessage());
            }
            return resultVo;
        }catch (Exception e) {
            log.error("查询询微信退款异常, transactionId:{}, refundOrderNo:{}", transactionId, refundOrderNo);
            throw new ServiceException("查询询微信退款异常");
        }
    }
}
