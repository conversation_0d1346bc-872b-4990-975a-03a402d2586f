package com.sohu.pay.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.bo.SohuIndependentPlayetTemplateBo;
import com.sohu.pay.api.bo.SohuIndependentTemplateBo;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.vo.SohuIndependentTemplateVo;

import java.util.Collection;
import java.util.List;

/**
 * 分账模版Service接口
 *
 * <AUTHOR>
 * @date 2023-10-11
 */
public interface ISohuIndependentTemplateService {

    /**
     * 查询分账模版
     */
    SohuIndependentTemplateVo queryById(Long id);

    /**
     * 查询分账模版列表
     */
    TableDataInfo<SohuIndependentTemplateVo> queryPageList(SohuIndependentTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询分账模版列表
     */
    List<SohuIndependentTemplateVo> queryList(SohuIndependentTemplateBo bo);

    /**
     * 修改分账模版
     */
    Boolean insertByBo(SohuIndependentTemplateBo bo);

    /**
     * 修改分账模版
     */
    Boolean updateByBo(SohuIndependentTemplateBo bo);

    /**
     * 校验并批量删除分账模版信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验是否重复
     *
     * @param siteId
     * @param templateType
     */
    SohuIndependentTemplateVo exitData(Long siteId, Integer templateType);

    /**
     * 根据siteId和模板类型查询模板
     *
     * @param siteId
     * @param type
     */
    SohuIndependentTemplateVo queryByIdAndType(Integer siteType, Long siteId, Integer type);

    /**
     * 根据模板类型查询模板
     *
     * @param type
     */
    SohuIndependentTemplateVo queryByIdAndType(Integer type);

    /**
     * 根据类型校验通用模板是否重复
     */
    Boolean exitCommonData(Integer type);

    /**
     * 根据批量siteId和模板类型查询模板
     *
     * @param addSiteIds
     * @param i
     */
    List<SohuIndependentTemplateVo> listByIdAndType(List<Long> addSiteIds, int i);

    /**
     * 获取短剧分账模版详细信息
     */
    SohuIndependentTemplateVo getPlayletInfo();

    /**
     * 新增短剧分账模版
     */
    Boolean addPlaylet(SohuIndependentPlayetTemplateBo bo);

    /**
     * 查询通用分账模版
     *
     * @param type
     * @return
     */
    SohuIndependentTemplateVo queryCommonTemp(Integer type);

    /**
     * 根据行业站点id查询模版配置
     *
     * @param industryId
     * @return
     */
    Boolean queryByIndustryId(Long industryId);

    /**
     * 根据站点id查询模版配置
     *
     * @param siteId
     * @param type
     * @return
     */
    SohuIndependentTemplateModel queryTemplateInfo(Integer siteType, Long siteId, Integer type);
}
