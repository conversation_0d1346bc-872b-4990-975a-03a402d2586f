package com.sohu.pay.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.SohuIndependentObject;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.middle.api.service.RemoteBusyBlackService;
import com.sohu.middle.api.service.RemoteMiddleInviteService;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.service.RemotePlatformIndustryService;
import com.sohu.middle.api.vo.SohuBusyBlackVo;
import com.sohu.middle.api.vo.SohuOrderIndependentPriceVo;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.pay.api.bo.AccountDistributionBo;
import com.sohu.pay.api.bo.AccountPlatformBo;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.domain.SohuIndependentLevelConfig;
import com.sohu.pay.service.AccountProcessService;
import com.sohu.pay.service.ISohuIndependentLevelConfigService;
import com.sohu.pay.service.ISohuIndependentTemplateService;
import com.sohu.shoporder.api.bo.SohuIndependentTempBo;
import com.sohu.system.api.RemoteSysRoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/3 19:58
 */
@Service
@Slf4j
public class AccountProcessServiceImpl implements AccountProcessService {

    protected static final Long PLATFORM_USER_ID = 2L;
    @Resource
    protected ISohuIndependentLevelConfigService levelConfigService;
    @Resource
    private ISohuIndependentTemplateService templateService;
    @DubboReference
    protected RemoteMiddleInviteService remoteMiddleInviteService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemotePlatformIndustryService remotePlatformIndustryService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemoteBusyBlackService remoteBusyBlackService;

    @Override
    public List<SohuIndependentTempBo> accountPlatformObjects(AccountPlatformBo bo) {
        log.info("accountPlatformObjects {}", JSONObject.toJSONString(bo));
        Long userId = bo.getUserId();
        String busyType = bo.getBusyType();
        Long busyCode = bo.getBusyCode();
        Long entranceSiteId = bo.getEntranceSiteId();
        Integer siteType = bo.getSiteType();
        Long citySiteId = bo.getCitySiteId();
        Long industryType = bo.getIndustryType();
        BigDecimal payPrice = bo.getPayPrice();
        BigDecimal chargeFee = bo.getChargeFee();
        SohuIndependentTemplateModel templateModel = bo.getTemplateModel();
        //初始化角色用户Id
        Long consumerInviteUserId = null;
        Long countrySiteUserId = null;
        Long entranceSiteUserId = null;
        Long inviteSiteUserId = null;
        Long citySiteUserId = null;
        Long industrySiteUserId = null;
        Long agencyUserId = null;
        Boolean inviteSiteType = false;
        //查询接单人是否存在上级,接单人拉新人与服务商拉新人在同一笔订单中只能选其一,普通用户上上级，
        Long higherUserId = getInviteId(userId);
        //服务商的角色定义
        List<String> agencyRole = Arrays.asList("agent", "qiye", "provider");
        //普通用户的角色定义
        List<String> commonRole = Arrays.asList("common", "article");
        // 查询上级角色
        Set<String> roles = remoteSysRoleService.selectRoleKeyByUserId(higherUserId);
        Long inviteSiteId = null;
        if ((roles.size() == Constants.ONE && (roles.contains("common") || roles.contains("article"))) || (roles.size() == Constants.TWO && roles.containsAll(commonRole))) {
            consumerInviteUserId = remoteMiddleInviteService.selectByInviteCount(userId, Constants.TWO);
        } else if (roles.contains("agent") || roles.contains("qiye") || roles.contains("provider")) {
            agencyUserId = higherUserId;
        } else if (roles.contains("cityStationAgent") && Objects.nonNull(entranceSiteId) && entranceSiteId > 0) {
            //通过拉新站长ID查询站长所在站
            SohuSiteVo siteVo = remoteMiddleSiteService.queryByStationmasterId(higherUserId);
            inviteSiteId = siteVo.getId();
            inviteSiteType = siteVo.getSiteCate();
            if (!inviteSiteId.equals(entranceSiteUserId)) {
                inviteSiteUserId = higherUserId;
            }
        }
        Long countrySiteId = null;
        //获取入口站点
        if (Objects.nonNull(entranceSiteId) && entranceSiteId > 0 && siteType == Constants.ONE) {
            // 根据城市站点id获取国家站点用户id
            countrySiteId = remoteMiddleSiteService.selectSiteByPid(entranceSiteId).getId();
        }
//        Long citySiteId = taskSiteModel.getSiteId();
        Long industrySiteId = remotePlatformIndustryService.queryIndustryIdByCategoryIdAndBusyType(industryType, busyType);
        //城市站集合
        Set<Long> citySiteSet = new HashSet<>();
        citySiteSet.add(countrySiteId);
        citySiteSet.add(citySiteId);
        //行业站集合
        Set<Long> industrySiteSet = new HashSet<>();
        industrySiteSet.add(industrySiteId);
        if (Objects.nonNull(entranceSiteId) && entranceSiteId > 0) {
            if (siteType == Constants.ONE) {
                citySiteSet.add(entranceSiteId);
            } else {
                industrySiteSet.add(entranceSiteId);
            }
        }
        Map<Long, SohuSiteVo> citySiteMap = remoteMiddleSiteService.queryMap(citySiteSet);
        Map<Long, SohuSiteVo> industrySiteMap = remoteMiddleSiteService.queryIndustryMap(citySiteSet);
        countrySiteUserId = citySiteMap.get(countrySiteId) != null ? citySiteMap.get(countrySiteId).getStationmasterId() : null;
        if (siteType == Constants.ONE) {
            entranceSiteUserId = citySiteMap.get(entranceSiteId) != null ? citySiteMap.get(entranceSiteId).getStationmasterId() : null;
        } else {
            entranceSiteUserId = industrySiteMap.get(entranceSiteId) != null ? industrySiteMap.get(entranceSiteId).getStationmasterId() : null;
        }
        //查询业务商单是否被站长所屏蔽
//        SohuBusyBlackVo cityBusyVo = remoteBusyBlackService.queryByParam(busyCode, Constants.ONE, BusyType.BusyTask.getType());
//        SohuBusyBlackVo industryBusyVo = remoteBusyBlackService.queryByParam(busyCode, Constants.TWO, BusyType.BusyTask.getType());
        SohuBusyBlackVo cityBusyVo = remoteBusyBlackService.queryByParam(busyCode, Constants.ONE, busyType);
        SohuBusyBlackVo industryBusyVo = remoteBusyBlackService.queryByParam(busyCode, Constants.TWO, busyType);
        if (Objects.isNull(cityBusyVo)) {
            citySiteUserId = citySiteMap.get(citySiteId) != null ? citySiteMap.get(citySiteId).getStationmasterId() : null;
        }
        if (Objects.isNull(industryBusyVo)) {
            industrySiteUserId = industrySiteMap.get(industrySiteId) != null ? industrySiteMap.get(industrySiteId).getStationmasterId() : null;
        }
        Map<String, BigDecimal> calculateMap = getPlatformIndependentPrice(entranceSiteId, payPrice, chargeFee, Objects.nonNull(consumerInviteUserId), Objects.nonNull(countrySiteUserId), Objects.nonNull(entranceSiteUserId), Objects.nonNull(inviteSiteUserId), Objects.nonNull(citySiteUserId), Objects.nonNull(industrySiteUserId), Objects.nonNull(agencyUserId), templateModel);
        List<SohuIndependentTempBo> independentTempBoList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : calculateMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = entry.getValue();
            if (value != null && value.compareTo(BigDecimal.ZERO) > 0) {
                SohuIndependentTempBo item = new SohuIndependentTempBo();
                item.setIndependentObject(key);
                item.setIndependentPrice(value);
                if (key.equals(SohuIndependentObject.platform.getKey())) {
                    item.setUserId(PLATFORM_USER_ID);
                    item.setSiteId(0L);
                }
                if (key.equals(SohuIndependentObject.invite.getKey())) {
                    item.setUserId(consumerInviteUserId);
                    item.setSiteId(0L);
                }
                if (key.equals(SohuIndependentObject.agency.getKey())) {
                    item.setUserId(agencyUserId);
                    item.setSiteId(0L);
                }
                if (key.equals(SohuIndependentObject.country.getKey())) {
                    item.setUserId(countrySiteUserId);
                    item.setSiteId(countrySiteId);
                }
                if (key.equals(SohuIndependentObject.city.getKey())) {
                    item.setUserId(citySiteUserId);
                    item.setSiteId(citySiteId);
                    item.setSiteType(1);
                }
                if (key.equals(SohuIndependentObject.entrance.getKey())) {
                    item.setUserId(entranceSiteUserId);
                    item.setSiteId(entranceSiteId);
                    item.setSiteType(siteType);
                }
                if (key.equals(SohuIndependentObject.invitecity.getKey())) {
                    item.setUserId(inviteSiteUserId);
                    item.setSiteId(inviteSiteId);
                    item.setSiteType(inviteSiteType ? 1 : 2);
                }
                if (key.equals(SohuIndependentObject.industrysite.getKey())) {
                    item.setUserId(industrySiteUserId);
                    item.setSiteId(industrySiteId);
                    item.setSiteType(2);
                }
                independentTempBoList.add(item);
            }
        }
        return independentTempBoList;
    }

    @Override
    public List<SohuIndependentTempBo> accountDistributionObjects(AccountDistributionBo bo) {
        log.info("accountPlatformObjects {}", JSONObject.toJSONString(bo));
        Long shareUserId = bo.getShareUserId();
        Long shareInviteId = remoteMiddleInviteService.selectByInviteCount(shareUserId, Constants.ONE);
        Map<String, BigDecimal> calculateMap = getDistributionIndependentPrice(bo.getShareAmount(), bo.getChargeFee(), Objects.nonNull(shareInviteId), bo.getTemplateModel());
        List<SohuIndependentTempBo> independentTempBoList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : calculateMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = entry.getValue();
            if (value != null && value.compareTo(BigDecimal.ZERO) > 0) {
                SohuIndependentTempBo item = new SohuIndependentTempBo();
                item.setIndependentObject(key);
                item.setIndependentPrice(value);
                if (key.equals(SohuIndependentObject.platform.getKey())) {
                    item.setUserId(PLATFORM_USER_ID);
                }
                if (key.equals(SohuIndependentObject.distribution.getKey())) {
                    item.setUserId(shareUserId);
                }
                if (key.equals(SohuIndependentObject.distributionInvite.getKey())) {
                    item.setUserId(shareInviteId);
                }
                independentTempBoList.add(item);
            }
        }
        return independentTempBoList;
    }

    protected Long getInviteId(Long userId) {
        SohuIndependentLevelConfig levelConfig = levelConfigService.queryByStatusAndSiteId(Constants.ONE, null);
        Integer level = ObjectUtils.isNull(levelConfig) ? levelConfig.getLevel() : Constants.ONE;
        //查询是否有拉新人-上级拉新人
        Long regUserId = remoteMiddleInviteService.selectByInviteCount(userId, level);
        return regUserId;
    }


    /**
     * 平台分账对象
     *
     * @param siteId             入口站点
     * @param payPrice           支付金额
     * @param changerFee         分摊的手续费
     * @param consumerInviteUser 是否存在消费者拉新人
     * @param entranceUser       是否存在入口站长
     * @param inviteSiteUser     是否存在拉新站长
     * @param citySiteUser       是否存在城市站长
     * @param countrySiteUser    是否存在行业站长
     * @param industrySiteUser   是否存在行业站长
     * @param agencyUser         是否存在服务商
     * @return
     */
    private Map<String, BigDecimal> getPlatformIndependentPrice(Long siteId, BigDecimal payPrice, BigDecimal changerFee, Boolean consumerInviteUser, Boolean countrySiteUser, Boolean entranceUser, Boolean inviteSiteUser, Boolean citySiteUser, Boolean industrySiteUser, Boolean agencyUser, SohuIndependentTemplateModel templateModel) {
        // 分账对象
        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
        // 平台服务费
        BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
        // 平台百分比
        BigDecimal adminDivide = BigDecimalUtils.divide(templateModel.getAdminRatio(), CalUtils.PERCENTAGE);
        // 消费者拉新人百分比
        BigDecimal consumerDivide = BigDecimalUtils.divide(templateModel.getConsumerInviteRatio(), CalUtils.PERCENTAGE);
        // 平台总手续费 = 商品价格 * 平台手续费比例 - 手续费
        BigDecimal independentPlatformPrice = payPrice.multiply(platformDivide).setScale(2, RoundingMode.HALF_UP);
        independentPlatformPrice = independentPlatformPrice.subtract(changerFee);
        log.warn("平台总手续费：{}", independentPlatformPrice);
        // 平台总分账金额 = 平台总手续费 * 平台分账比例
        BigDecimal platPrice = independentPlatformPrice.multiply(adminDivide).setScale(2, RoundingMode.HALF_UP);
        ;
        // 消费者拉新人分账金额 = 平台总分账金额 - 平台分账金额
        BigDecimal consumerInvitePrice = BigDecimal.ZERO;
        if (consumerInviteUser) {
            consumerInvitePrice = independentPlatformPrice.multiply(consumerDivide).setScale(2, RoundingMode.HALF_UP);
        }
        // 分红总金额 =  平台总手续费 - 平台分账金额 - 消费者拉新人分账金额
        BigDecimal sharePrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice);
        // 国家站站长分账金额 = 分红总金额 * 国家站站长分账比例
        BigDecimal countryPrice = BigDecimal.ZERO;
        if (countrySiteUser) {
            countryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCountryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        }
        // 入口站站长分账金额 = 分红总金额 * 入口站站长分账比例
        BigDecimal entrancePrice = BigDecimal.ZERO;
        // 拉新站长分账金额 = 入口站站长分账金额 * 拉新站长分账比例
        BigDecimal stationInvitePrice = BigDecimal.ZERO;
        if (Objects.nonNull(siteId) && entranceUser) {
            entrancePrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getEntranceRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            if (!inviteSiteUser) {
                stationInvitePrice = entrancePrice.multiply(BigDecimalUtils.divide(templateModel.getInviteCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            }
        }
        // 城市站站长分账金额 = 分红总金额 * 城市站站长分账比例
        BigDecimal cityPrice = BigDecimal.ZERO;
        if (citySiteUser) {
            cityPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        }
        // 行业站站长分账金额 = 分红总金额 * 行业站站长分账比例
        BigDecimal industryPrice = BigDecimal.ZERO;
        if (industrySiteUser) {
            industryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getIndustryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        }
        // 代理商分账金额 = 分红总金额 * 代理商分账比例
        BigDecimal agencyPrice = BigDecimal.ZERO;
        if (agencyUser) {
            agencyPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getAgencyRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        }

        BigDecimal lastPrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice, countryPrice, entrancePrice, cityPrice, industryPrice, agencyPrice);
        platPrice = CalUtils.add(platPrice, lastPrice);
        // 平台分账金额
        orderIndependentPrice.setAdminPrice(platPrice);
        orderIndependentPrice.setInvitePrice(consumerInvitePrice);
        orderIndependentPrice.setCountryPrice(countryPrice);
        orderIndependentPrice.setEntrancePrice(entrancePrice.subtract(stationInvitePrice));
        orderIndependentPrice.setCityPrice(cityPrice);
        orderIndependentPrice.setIndustryPrice(industryPrice);
        orderIndependentPrice.setAgencyPrice(agencyPrice);
        orderIndependentPrice.setInviteCityPrice(stationInvitePrice);
        // 组装分账信息
        Map<String, BigDecimal> calculateMap = new HashMap<>();
        calculateMap.put(SohuIndependentObject.platform.getKey(), orderIndependentPrice.getAdminPrice());
        calculateMap.put(SohuIndependentObject.agency.getKey(), orderIndependentPrice.getAgencyPrice());
//        calculateMap.put(SohuIndependentObject.distributionInvite.getKey(), orderIndependentPrice.getDistributorInvitePrice());
        calculateMap.put(SohuIndependentObject.country.getKey(), orderIndependentPrice.getCountryPrice());
        calculateMap.put(SohuIndependentObject.city.getKey(), orderIndependentPrice.getCityPrice());
//        calculateMap.put(SohuIndependentObject.distribution.getKey(), orderIndependentPrice.getDistributorPrice());
        calculateMap.put(SohuIndependentObject.invite.getKey(), orderIndependentPrice.getInvitePrice());
        calculateMap.put(SohuIndependentObject.entrance.getKey(), orderIndependentPrice.getEntrancePrice());
        calculateMap.put(SohuIndependentObject.invitecity.getKey(), orderIndependentPrice.getInviteCityPrice());
        calculateMap.put(SohuIndependentObject.industrysite.getKey(), orderIndependentPrice.getIndustryPrice());
        return calculateMap;
    }

    private Map<String, BigDecimal> getDistributionIndependentPrice(BigDecimal payPrice, BigDecimal changerFee, Boolean shareInviteUserId, SohuIndependentTemplateModel templateModel) {
        // 平台服务费
        BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
        BigDecimal platPrice = CalUtils.multiply(payPrice, platformDivide);
        // 分红总金额 =  分销金额 - 平台分账金额 - 消费者拉新人分账金额
        BigDecimal sharePrice = CalUtils.sub(payPrice, platPrice);
        //分销人金额 = 分红总金额 * 分销比例
        BigDecimal shareUserPrice = CalUtils.multiply(sharePrice, templateModel.getDistributorRatio());
        // 分销人拉新人金额 = 分红总金额 - 分销人金额
        BigDecimal shareInvitePrice = BigDecimal.ZERO;
        if (shareInviteUserId) {
            shareInvitePrice = CalUtils.sub(sharePrice, shareUserPrice);
        }
        BigDecimal lastPrice = CalUtils.sub(payPrice, platPrice, shareUserPrice, shareInvitePrice);
        platPrice = CalUtils.add(platPrice, lastPrice);
        // 组装分账信息
        Map<String, BigDecimal> calculateMap = new HashMap<>();
        calculateMap.put(SohuIndependentObject.platform.getKey(), platPrice.subtract(changerFee));
        calculateMap.put(SohuIndependentObject.distribution.getKey(), shareUserPrice);
        calculateMap.put(SohuIndependentObject.distributionInvite.getKey(), shareInvitePrice);
        return calculateMap;
    }
}
