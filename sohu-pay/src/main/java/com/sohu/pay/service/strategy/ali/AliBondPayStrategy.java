package com.sohu.pay.service.strategy.ali;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.response.*;
import com.sohu.admin.api.RemoteMerchantBondService;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.SohuMerchantBondVo;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.PaySourceEnum;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.pay.api.RemoteMerchantBondPayService;
import com.sohu.pay.api.bo.SohuMerchantBondPayBo;
import com.sohu.pay.api.domain.SohuPayQueryBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.enums.BondPayTypeEnum;
import com.sohu.pay.api.model.SohuPayResultModel;
import com.sohu.pay.api.vo.SohuMerchantBondPayVo;
import com.sohu.pay.service.ISohuMerchantBondPayService;
import com.sohu.pay.service.strategy.PaymentProcessor;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.pay.vo.RefundResultVo;
import com.sohu.shopgoods.api.RemoteProductCategoryPcService;
import com.sohu.shopgoods.api.vo.SohuProductCategoryPcVo;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.third.alipay.pay.bean.AlipayBaseConfig;
import com.sohu.third.alipay.pay.request.AlipayOrderCreateRequest;
import com.sohu.third.alipay.pay.request.AlipayOrderRefundRequest;
import com.sohu.third.alipay.pay.service.AlipayPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.rmi.Remote;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 支付宝支付
 *
 * @Author: leibo
 * @Date: 2025/5/14 15:28
 **/
@Slf4j
@Component
public class AliBondPayStrategy implements AliPayStrategy {

    @DubboReference
    private RemoteMerchantBondService merchantBondService;
    @Autowired
    private ISohuMerchantBondPayService merchantBondPayService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteMerchantService merchantService;
    @DubboReference
    private RemoteProductCategoryPcService productCategoryPcService;
    @DubboReference
    private RemoteMerchantBondPayService remoteMerchantBondPayService;

    @Override
    public SohuPayResultModel paySuccess(SohuPayQueryBo payQueryBo) {
        AlipayTradeQueryResponse queryResponse = null;
        try {
            AlipayBaseConfig config = getAlipayConfig(payQueryBo.getPayType());
            queryResponse = AlipayPayService.tradeQuery(null, payQueryBo.getOrderNo(), config);
        } catch (Exception e) {
            log.error("查询失败");
        }
        SohuPayResultModel payResultModel = new SohuPayResultModel();
        if (Objects.nonNull(queryResponse) && queryResponse.getTradeStatus().equals("TRADE_SUCCESS")) {
            payResultModel.setStatus(Boolean.TRUE);
            payResultModel.setPayPrice(new BigDecimal(queryResponse.getTotalAmount()));
            payResultModel.setOrderNo(queryResponse.getOutTradeNo());
            payResultModel.setTradeNo(queryResponse.getTradeNo());
            payResultModel.setPayDate(queryResponse.getSendPayDate());
        } else {
            payResultModel.setStatus(Boolean.FALSE);
        }
        return payResultModel;
    }

    @Override
    public Object paySuccessQuery(SohuPayQueryBo payQueryBo) {
        return null;
    }

    @Override
    public Boolean refundSuccess(String refundOrderNo) {
        return null;
    }

    @Override
    public String payment(SohuPrePayBo payBo) {
        Long masterId = payBo.getMasterId();
        if (Objects.isNull(masterId)) {
            throw new ServiceException("业务id不能为空");
        }
        BigDecimal amount = payBo.getAmount();
        if (amount == null || CalUtils.isLessEqualZero(amount)) {
            throw new ServiceException(MessageUtils.message("付款金额不能小于等于0"));
        }
        // 基于保证金id查询保证金记录
        SohuMerchantBondVo merchantBondVo = merchantBondService.getById(payBo.getMasterId());
        if (Objects.isNull(merchantBondVo) || !merchantBondVo.getPayStatus().equals(CommonState.WaitPay.getCode())) {
            throw new ServiceException("当前保证金记录异常,请刷新后重试");
        }
        BigDecimal waitPayAmount = CalUtils.sub(merchantBondVo.getBondAmount(), merchantBondVo.getPaidAmount());
        if (CalUtils.isLessEqualZero(waitPayAmount)) {
            throw new ServiceException(MessageUtils.message("待付款金额不能小于等于0"));
        }
        if (!CalUtils.equal(amount, waitPayAmount)) {
            throw new ServiceException(MessageUtils.message("付款金额与待付款金额不一致,请刷新后重试"));
        }
        // 判断是否存在线下审核中的记录,存在则打回
        List<String> statusList = new ArrayList<>();
        statusList.add(CommonState.WaitApprove.getCode());
        List<SohuMerchantBondPayVo> merchantBondPayList = merchantBondPayService.queryListByMerchantBondIdAndStatus(merchantBondVo.getId(), statusList);
        if (!CollectionUtils.isEmpty(merchantBondPayList)) {
            throw new ServiceException("当前已存在审核中的缴纳记录，请等待审核完成后再进行付款");
        }
        // 不存在,则查询是否存在线上记录，存在则执行关单操作
        statusList.add(CommonState.WaitApprove.getCode());
        merchantBondPayList = merchantBondPayService.queryListByMerchantBondIdAndStatus(merchantBondVo.getId(), statusList);
        if (!CollectionUtils.isEmpty(merchantBondPayList)) {
            // 执行关单操作
            for (SohuMerchantBondPayVo merchantBondPayVo : merchantBondPayList) {
                if (remoteMerchantBondPayService.cancelBondPay(merchantBondPayVo.getOrderNo())) {
                    throw new ServiceException("当前类目保证金状态有变更,请刷新后再试");
                }
            }
        }
        // 查询店铺信息
        SohuMerchantModel merchant = merchantService.selectById(merchantBondVo.getMerId());
        // 查询分类信息
        SohuProductCategoryPcVo categoryPcVo = productCategoryPcService.queryById(merchantBondVo.getCateId());
        // 进入阿里支付
        try {
            // 用户信息
            AlipayBaseConfig config = getAlipayConfig(payBo.getPayType());
            AlipayTradeAppPayModel tradeRequest = new AlipayTradeAppPayModel();
            tradeRequest.setOutTradeNo(NumberUtil.getOrderNo(OrderConstants.BOND_PREFIX));
            tradeRequest.setTotalAmount(waitPayAmount.toString());
            tradeRequest.setSubject(merchant.getName() + "支付" + categoryPcVo.getName() + "分类的保证金");
            // 设置产品码
            tradeRequest.setProductCode("QUICK_MSECURITY_PAY");
            tradeRequest.setPassbackParams(tradeRequest.getOutTradeNo());
            log.info("阿里云支付保证金入参:{}", JsonUtils.toJsonString(tradeRequest));
            AlipayTradeAppPayResponse response = AlipayPayService.tradeCreate(tradeRequest, config);
            // 存储记录
            SohuMerchantBondPayBo merchantBondPayBo = new SohuMerchantBondPayBo();
            merchantBondPayBo.setUserId(LoginHelper.getUserId());
            merchantBondPayBo.setMerchantBondId(masterId);
            // 订单支付单号
            merchantBondPayBo.setOrderNo(tradeRequest.getOutTradeNo());
            // 支付方式
            merchantBondPayBo.setPayType(BondPayTypeEnum.ALIPAY.getCode());
            // 状态
            merchantBondPayBo.setPayStatus(CommonState.WaitPay.getCode());
            // 支付金额
            merchantBondPayBo.setPayAmount(waitPayAmount);
            // 无手续费
            merchantBondPayBo.setChargeAmount(BigDecimal.ZERO);
            // 分类保证金的区间
            merchantBondPayBo.setGmvInterval(merchantBondVo.getGmvInterval());
            merchantBondPayService.insertByBo(merchantBondPayBo);
            // 加入自动未支付自动取消队列
            MqMessaging mqCancelMessaging = new MqMessaging(tradeRequest.getOutTradeNo(), MqKeyEnum.CANCEL_BOND_ORDER.getKey());
            remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 7L);
            log.info("已发送延时取消队列-cancel_bond_order:{}", tradeRequest.getOutTradeNo());
            String payment = JSONUtil.toJsonStr(response);
            log.info("阿里云支付保证金返回结果:{}", payment);
            return payment;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        AlipayBaseConfig config = getAlipayConfig(refundPayBo.getPayType());
        AlipayTradeRefundModel param = new AlipayTradeRefundModel();
        // 支付单号
        param.setOutTradeNo(refundPayBo.getMasterOrderNo());
        // 退款原因
        param.setRefundReason("正常退款");
        // 退款金额
        param.setRefundAmount(refundPayBo.getAmount().toString());
        // 流水号
        param.setOutRequestNo(refundPayBo.getOutTradeNo());
        log.info("阿里云保证金退款入参:{}", JsonUtils.toJsonString(param));
        AlipayTradeRefundResponse response = AlipayPayService.tradeRefund(param, config);
        log.info("阿里云保证金退款返回结果:{}", JsonUtils.toJsonString(response));
        MqMessaging mqCancelMessaging = new MqMessaging(refundPayBo.getOutTradeNo(), MqKeyEnum.REFUND_RESULT_QUERY.getKey());
        remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);
        return Boolean.TRUE;
    }

    @Override
    public String payCallback(String callbackResponse) {
        try {
            Map<String, String> map = JSON.parseObject(callbackResponse, new com.alibaba.fastjson.TypeReference<Map<String, String>>() {
            });
            String orderNo = map.get("out_trade_no");
            String tradeStatus = map.get("trade_status");
            String tradeNo = map.get("trade_no");
            String gmtPayment = map.get("gmt_payment");
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                merchantBondPayService.handleBondPay(orderNo, tradeNo, format.parse(gmtPayment));
                return "success";
            } else {
                merchantBondPayService.cancelBondPay(orderNo);
            }
        } catch (Exception e) {
            log.error("异常,Exception:{}", e.getMessage());
        }
        return "fail";
    }

    @Override
    public Boolean close(String outTradeNo, String payType) {
        AlipayBaseConfig config = getAlipayConfig(payType);
        return AlipayPayService.tradeCancel(outTradeNo, config);
    }

    public RefundResultVo queryRefundResult(String transactionId, String refundOrderNo) {
        log.info("查询支付宝返回结果入参transactionId:{},refundOrderNo:{}", transactionId, refundOrderNo);
        AlipayBaseConfig config = getAlipayConfig(PayTypeEnum.PAY_TYPE_ALI_PAY.getStatus());
        AlipayTradeFastpayRefundQueryResponse response = AlipayPayService.refundQuery(transactionId, null, refundOrderNo,
                config);
        log.info("查询支付宝返回结果response:{}", JsonUtils.toJsonString(response));
        if (Objects.isNull(response)) {
            log.error("查询支付宝退款异常, transactionId:{}, refundOrderNo:{}", transactionId, refundOrderNo);
            throw new ServiceException("查询支付宝退款异常");
        }
        RefundResultVo resultVo = new RefundResultVo();
        if (response.isSuccess() && response.getRefundStatus().equals("REFUND_SUCCESS")) {
            // 退款成功
            resultVo.setStatus(Boolean.TRUE);
            resultVo.setRefundTransactionId(response.getTradeNo());
            resultVo.setSuccessTime(response.getGmtRefundPay());
        } else {
            // 退款失败
            resultVo.setStatus(Boolean.FALSE);
            resultVo.setErrorReason(response.getSubMsg());
        }
        return resultVo;
    }
}
