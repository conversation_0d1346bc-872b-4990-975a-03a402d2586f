package com.sohu.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.Mail;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.model.SohuBusyOrderPayModel;
import com.sohu.busyorder.api.vo.SohuBusyTaskPayVo;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.pay.api.bo.SohuIndependentOrderBo;
import com.sohu.pay.api.model.SohuIndependentOrderModel;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.pay.api.vo.SohuIndependentOrderAggrVo;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.pay.domain.SohuIndependentOrder;
import com.sohu.pay.mapper.SohuIndependentOrderMapper;
import com.sohu.pay.service.ISohuAccountService;
import com.sohu.pay.service.ISohuIndependentOrderService;
import com.sohu.shoporder.api.RemoteShopOrderService;
import com.sohu.shoporder.api.model.SohuShopOrderInfoModel;
import com.sohu.shoporder.api.model.SohuShopOrderModel;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.hibernate.validator.internal.engine.constraintvalidation.PredefinedScopeConstraintValidatorManagerImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 第三方分账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuIndependentOrderServiceImpl implements ISohuIndependentOrderService {


    private final SohuIndependentOrderMapper baseMapper;
    private final ISohuAccountService sohuAccountService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;
    @DubboReference
    private RemoteBusyOrderService remoteBusyOrderService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;

    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;

    private static final Integer INDEPENDENTING = 2;

    /**
     * 查询第三方分账单
     */
    @Override
    public SohuIndependentOrderVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询第三方分账单列表
     */
    @Override
    public TableDataInfo<SohuIndependentOrderVo> queryPageList(SohuIndependentOrderBo bo, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            return TableDataInfoUtils.build();
        }
        if (!LoginHelper.isAdmin(userId)) {
            if (LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
                // 拥有国家站权限
                SohuSiteVo sohuSiteVo = remoteMiddleSiteService.queryByStationmasterId(userId);
                if (Objects.isNull(sohuSiteVo)) {
                    log.warn("该用户查询所属国家站点失败！:{}", userId);
                    return TableDataInfoUtils.build();
                }
                List<Long> siteIds = remoteMiddleSiteService.queryChildSiteIds(sohuSiteVo.getId());
                bo.setSiteIds(siteIds);
            } else if (LoginHelper.hasRole(RoleCodeEnum.CityStationAgent)) {
                // 拥有城市站权限
                SohuSiteVo sohuSiteVo = remoteMiddleSiteService.queryByStationmasterId(userId);
                if (Objects.isNull(sohuSiteVo)) {
                    log.warn("该用户查询所属城市站点失败！:{}", userId);
                    return TableDataInfoUtils.build();
                }
                bo.setSiteId(sohuSiteVo.getId());
            }
        }

        LambdaQueryWrapper<SohuIndependentOrder> lqw = buildQueryWrapper(bo);
        Page<SohuIndependentOrderVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuIndependentOrderVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> userIds = new HashSet<>();
            for (SohuIndependentOrderVo record : records) {
                userIds.add(record.getUserId());
            }
            Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
            Map<Long, SohuAccountVo> sohuAccountMap = sohuAccountService.selectMapByUserIdsOfPass(userIds);
            for (SohuIndependentOrderVo record : records) {
                LoginUser user = userMap.get(record.getUserId());
                if (Objects.nonNull(user)) {
                    record.setUserName(user.getNickname());
                    record.setUserAvatar(user.getAvatar());
                }
                SohuAccountVo sohuAccount = sohuAccountMap.get(record.getUserId());
                if (Objects.nonNull(sohuAccount)) {
                    record.setMerchantName(sohuAccount.getMerchantName());
                }
            }
            result.setRecords(records);
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询第三方分账单列表
     */
    @Override
    public List<SohuIndependentOrderVo> queryList(SohuIndependentOrderBo bo) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = buildQueryWrapper(bo);
        List<SohuIndependentOrderVo> list = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setAmountTypeName(SohuTradeRecordEnum.AmountType.getValue(item.getAmountType()));
                item.setTemplateTypeName(SohuTradeRecordEnum.TemplateType.getDesc(item.getTemplateType()));
                item.setIndependentStatusName(IndependentStatusEnum.getValue(item.getIndependentStatus()));
                item.setIndependentObjectName(SohuIndependentObject.MAP.get(item.getIndependentObject()));
            });
        }
        return list;
    }

    @Override
    public List<SohuIndependentOrderVo> queryListByOrderNo(String orderNo) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentOrder::getOrderNo, orderNo);
        return this.baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuIndependentOrder> buildQueryWrapper(SohuIndependentOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuIndependentOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuIndependentOrder::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), SohuIndependentOrder::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeNo()), SohuIndependentOrder::getTradeNo, bo.getTradeNo());
        if (StringUtils.isNotBlank(bo.getTradeType())) {
            if (bo.getTradeType().equalsIgnoreCase(BusyType.Playlet.getType())) {
                lqw.in(SohuIndependentOrder::getTradeType, Arrays.asList(TradeTypeEnum.PlayletPlatform, TradeTypeEnum.PlayletCopyright));
            } else {
                lqw.eq(SohuIndependentOrder::getTradeType, bo.getTradeType());
            }
        }
        lqw.eq(StringUtils.isNotBlank(bo.getIndependentObject()), SohuIndependentOrder::getIndependentObject, bo.getIndependentObject());
        lqw.eq(bo.getIndependentPrice() != null, SohuIndependentOrder::getIndependentPrice, bo.getIndependentPrice());
        lqw.eq(null != bo.getIndependentStatus(), SohuIndependentOrder::getIndependentStatus, bo.getIndependentStatus());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(SohuIndependentOrder::getCreateTime, DateUtils.beginOfTime(bo.getStartTime()));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(SohuIndependentOrder::getCreateTime, DateUtils.endOfTime(bo.getEndTime()));
        }
        lqw.eq(null != bo.getMerId(), SohuIndependentOrder::getMerId, bo.getMerId());
        lqw.eq(null != bo.getSiteId(), SohuIndependentOrder::getSiteId, bo.getSiteId());
        if (CollUtil.isNotEmpty(bo.getSiteIds())) {
            lqw.in(SohuIndependentOrder::getSiteId, bo.getSiteIds());
        }
        lqw.like(StringUtils.isNotBlank(bo.getTaskNumber()), SohuIndependentOrder::getTaskNumber, bo.getTaskNumber());
        lqw.like(StringUtils.isNotBlank(bo.getTaskTitle()), SohuIndependentOrder::getTaskTitle, bo.getTaskTitle());
        lqw.like(StringUtils.isNotBlank(bo.getTaskUserName()), SohuIndependentOrder::getTaskUserName, bo.getTaskUserName());
        lqw.ge(Objects.nonNull(bo.getMinTaskFullAmount()), SohuIndependentOrder::getTaskFullAmount, bo.getMinTaskFullAmount());
        lqw.le(Objects.nonNull(bo.getMaxTaskFullAmount()), SohuIndependentOrder::getTaskFullAmount, bo.getMaxTaskFullAmount());
        lqw.orderByDesc(SohuEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增第三方分账单
     */
    @Override
    public Boolean insertByBo(SohuIndependentOrderBo bo) {
        SohuIndependentOrder add = BeanUtil.toBean(bo, SohuIndependentOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改第三方分账单
     */
    @Override
    public Boolean updateByBo(SohuIndependentOrderBo bo) {
        SohuIndependentOrder update = BeanUtil.toBean(bo, SohuIndependentOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuIndependentOrder entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量添加
     */
    @Override
    public Boolean insertByBoList(List<SohuIndependentOrderBo> boList) {
        List<SohuIndependentOrder> list = BeanUtil.copyToList(boList, SohuIndependentOrder.class);
        return baseMapper.insertBatch(list);
    }

    @Override
    public SohuIndependentOrderVo orderDetail(String orderNo, String independentObject) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentOrder::getOrderNo, orderNo).eq(SohuIndependentOrder::getIndependentObject, independentObject);
        SohuIndependentOrderVo sohuIndependentOrderVo = baseMapper.selectVoOne(lqw);
        Objects.requireNonNull(sohuIndependentOrderVo, MessageUtils.message("WRONG_PARAMS"));

        Map<Long, SohuAccountVo> sohuAccountMap = sohuAccountService.selectMapByUserIdsOfPass(Arrays.asList(sohuIndependentOrderVo.getUserId()));
        SohuAccountVo sohuAccount = sohuAccountMap.get(sohuIndependentOrderVo.getUserId());
        if (Objects.nonNull(sohuAccount)) {
            sohuIndependentOrderVo.setMerchantName(sohuAccount.getMerchantName());
        }
        LoginUser user = remoteUserService.selectById(sohuIndependentOrderVo.getUserId());
        if (Objects.nonNull(user)) {
            sohuIndependentOrderVo.setUserName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
            sohuIndependentOrderVo.setUserAvatar(user.getAvatar());
        }

        if (BusyType.Goods.name().equals(sohuIndependentOrderVo.getTradeType())) {
            SohuShopOrderModel storeOrder = remoteShopOrderService.getByOrderNo(orderNo);
            sohuIndependentOrderVo.setShopOrderModel(storeOrder);
        } else if (BusyType.BusyOrder.name().equals(sohuIndependentOrderVo.getTradeType())) {
            SohuBusyOrderPayModel busyOrderModel = remoteBusyOrderService.queryBusyOrderPayByOrderNo(orderNo);
            // TODO
            //sohuIndependentOrderVo.setBusyOrderModel(busyOrderModel);
        } else {
            throw new RuntimeException("交易类型不准确");
        }
        return sohuIndependentOrderVo;
    }

    /**
     * 查询商品分销统计或者商单分销统计 的金额总和
     *
     * @param userId    用户ID
     * @param tradeType {@link com.sohu.common.core.enums.BusyType}
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public BigDecimal selectSumDistributionAmount(Long userId, String tradeType, Integer independentStatus, String startTime, String endTime) {
        return baseMapper.selectSumDistributionAmount(userId, tradeType, independentStatus, startTime, endTime);
    }

    /**
     * 查询商品分销统计或者商单分销统计 的分销单数
     *
     * @param userId    用户ID
     * @param tradeType {@link com.sohu.common.core.enums.BusyType}
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Long selectCountOrder(Long userId, String tradeType, Integer independentStatus, String startTime, String endTime) {
        return baseMapper.selectCountOrder(userId, tradeType, independentStatus, startTime, endTime);
    }

    /**
     * 分销统计聚合
     *
     * @param userId
     * @param tradeType
     * @param independentStatus
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public SohuIndependentOrderAggrVo selectAggrBySharePerson(Long userId, String tradeType, Integer independentStatus, String startTime, String endTime) {
        return baseMapper.selectAggrBySharePerson(userId, tradeType, independentStatus, startTime, endTime);
    }

    @Override
    public BigDecimal selectSumOrderAmount(Long userId, String tradeType, Integer independentStatus, String startTime, String endTime) {
        List<String> orderNos = baseMapper.listOrderNo(userId, tradeType, independentStatus, startTime, endTime);
        if (CollUtil.isEmpty(orderNos)) {
            return BigDecimal.ZERO;
        }
        if (StrUtil.equalsAnyIgnoreCase(tradeType, BusyType.Goods.name())) {
            List<SohuShopOrderInfoModel> list = remoteShopOrderService.list(orderNos);
            if (CollUtil.isEmpty(list)) {
                return BigDecimal.ZERO;
            }
            // 使用 Stream API 和 lambda 表达式计算字段的总和
            return list.stream().map(SohuShopOrderInfoModel::getPrice) // 提取每个对象的 value 字段
                    .reduce(BigDecimal.ZERO, BigDecimal::add); // 求和
        }
        if (StrUtil.equalsAnyIgnoreCase(tradeType, BusyType.BusyOrder.name())) {
            BigDecimal sum = BigDecimal.ZERO;
            List<SohuBusyOrderPayModel> list = remoteBusyOrderService.list(orderNos);
            if (CollUtil.isNotEmpty(list)) {
                sum = sum.add(list.stream().map(SohuBusyOrderPayModel::getPayAmount) // 提取每个对象的 value 字段
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryBusyTaskPayList(orderNos, PayStatus.Paid.name());
            if (CollUtil.isNotEmpty(payList)) {
                sum = sum.add(payList.stream().map(SohuBusyTaskPayVo::getPayAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            return sum;

        }
        return BigDecimal.ZERO;
    }

    @Override
    public SohuIndependentOrder selectByOrderAndUserId(Long userId, String outTradeNo) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuIndependentOrder::getUserId, userId).eq(SohuIndependentOrder::getOrderNo, outTradeNo);
        return this.baseMapper.selectOne(lqw);
    }

    @Override
    public void updateIndependentStatus(String outTradeNo, String name, int i) {
        this.baseMapper.updateIndependentStatus(outTradeNo, name, i);
    }

    /**
     * 批量删除第三方分账单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean deleteByByOrderNo(String orderNo) {
        return baseMapper.delete(SohuIndependentOrder::getOrderNo, orderNo);
    }

    @Override
    public BigDecimal selectIndependentingByUserId(Long userId, Integer siteType, Long siteId, List<String> selectRoles, String independentObject, Integer independentStatus, Date startTime, Date endTime) {
        LambdaQueryWrapper<SohuIndependentOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentOrder::getUserId, userId);
        wrapper.eq(siteType != null, SohuIndependentOrder::getSiteType, siteType);
        wrapper.eq(siteId != null, SohuIndependentOrder::getSiteId, siteId);
        wrapper.in(CollUtil.isNotEmpty(selectRoles), SohuIndependentOrder::getIndependentObject, selectRoles);
        wrapper.eq(independentStatus != null, SohuIndependentOrder::getIndependentStatus, independentStatus);
        wrapper.eq(StrUtil.isNotEmpty(independentObject), SohuIndependentOrder::getIndependentObject, independentObject);
        wrapper.between(startTime != null && endTime != null, SohuIndependentOrder::getUpdateTime, startTime, endTime);
        wrapper.select(SohuIndependentOrder::getIndependentPrice);

        List<SohuIndependentOrder> sohuIndependentOrdersList = baseMapper.selectList(wrapper);
        // 分账中的金额汇总
        BigDecimal totalIndependentPrice = sohuIndependentOrdersList.stream()
                .map(SohuIndependentOrder::getIndependentPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return totalIndependentPrice;
    }

    @Override
    public Page<SohuIndependentOrderModel> queryByUserId(Long userId, String independentObject, String startTime, String endTime, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentOrder::getUserId, userId);
        if (StrUtil.isNotBlank(independentObject)) {
            lqw.eq(SohuIndependentOrder::getIndependentObject, independentObject);
        }
        if (StrUtil.isNotBlank(startTime)) {
            lqw.ge(SohuEntity::getCreateTime, DateUtils.beginOfTime(startTime));
        }
        if (StrUtil.isNotBlank(endTime)) {
            lqw.lt(SohuEntity::getCreateTime, DateUtils.endOfTime(endTime));
        }
        // 分页查询
        Page<SohuIndependentOrder> page = new Page<>(pageNum, pageSize);
        Page<SohuIndependentOrder> sohuIndependentOrderPage = this.baseMapper.selectPage(page, lqw);
        Page<SohuIndependentOrderModel> result = new Page<>();
        if (CollUtil.isNotEmpty(sohuIndependentOrderPage.getRecords())) {
            result.setTotal(sohuIndependentOrderPage.getTotal());
            List<SohuIndependentOrderModel> records = new ArrayList<>();
            for (SohuIndependentOrder order : sohuIndependentOrderPage.getRecords()) {
                SohuIndependentOrderModel item = new SohuIndependentOrderModel();
                BeanUtil.copyProperties(order, item);
                records.add(item);
            }
            result.setRecords(records);
        }
        return result;
    }

    @Override
    public BigDecimal selectIndependentInviteByUserId(Long userId) {
        LambdaQueryWrapper<SohuIndependentOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentOrder::getUserId, userId);
        wrapper.eq(SohuIndependentOrder::getIndependentObject, SohuIndependentObject.invite.getKey());
        wrapper.select(SohuIndependentOrder::getIndependentPrice);

        List<SohuIndependentOrder> sohuIndependentOrdersList = baseMapper.selectList(wrapper);
        // 分账中的金额汇总
        return sohuIndependentOrdersList.stream()
                .map(SohuIndependentOrder::getIndependentPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<SohuIndependentOrder> inviteAmount(Long userId) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentOrder::getUserId, userId);
        lqw.eq(SohuIndependentOrder::getIndependentObject, SohuIndependentObject.invite.getKey());
        return baseMapper.selectList(lqw);
    }

    @Override
    public void updateIndependentStatus(String outTradeNo, int i) {
        this.baseMapper.updateIndependentStatusByOrderNo(outTradeNo, i);
    }

    @Override
    public Boolean updateByBoList(List<SohuIndependentOrder> independentOrderBoList) {
        return this.baseMapper.updateBatchById(independentOrderBoList);
    }

    @Override
    public Boolean updateIndependentStatusByTradeNo(String tradeNo, Integer independentStatus) {
        LambdaUpdateWrapper<SohuIndependentOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SohuIndependentOrder::getTradeNo, tradeNo);
        wrapper.set(SohuIndependentOrder::getIndependentStatus, independentStatus);
        if (this.baseMapper.update(null, wrapper) > 0) {
            return true;
        }
        return false;
    }

    @Override
    public List<SohuIndependentOrderVo> queryListByTradeNo(String tradeNo, Integer independentStatus) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentOrder::getTradeNo, tradeNo);
        lqw.eq(SohuIndependentOrder::getIndependentStatus, independentStatus);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public SohuIndependentOrderVo queryByUserOrderNo(Long userId, String orderNo, Integer independentStatus) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentOrder::getOrderNo, orderNo);
        lqw.eq(SohuIndependentOrder::getUserId, userId);
        lqw.eq(SohuIndependentOrder::getIndependentStatus, independentStatus);
        lqw.last(" limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public List<SohuIndependentOrderModel> groupIndependentByUserId(Long userId, Long siteId, List<String> selectRoles) {
        LambdaQueryWrapper<SohuIndependentOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentOrder::getUserId, userId);
        wrapper.in(SohuIndependentOrder::getIndependentObject, selectRoles);
        wrapper.eq(siteId != null, SohuIndependentOrder::getSiteId, siteId);
        List<SohuIndependentOrder> sohuIndependentOrdersList = baseMapper.selectList(wrapper);
        return BeanUtil.copyToList(sohuIndependentOrdersList, SohuIndependentOrderModel.class);
    }

    @Override
    public List<SohuIndependentOrderModel> queryByInviteUser(Long consumerUserId, Long inviteUserId, String independentObject, List<Integer> independentStatus) {
        LambdaQueryWrapper<SohuIndependentOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuIndependentOrder::getUserId, inviteUserId);
        wrapper.eq(SohuIndependentOrder::getConsumerUserId, consumerUserId);
        wrapper.in(SohuIndependentOrder::getIndependentObject, independentObject);
        wrapper.in(SohuIndependentOrder::getIndependentStatus,  independentStatus);
        wrapper.orderByDesc(SohuIndependentOrder::getCreateTime);
        List<SohuIndependentOrder> sohuIndependentOrdersList = baseMapper.selectList(wrapper);
        return BeanUtil.copyToList(sohuIndependentOrdersList, SohuIndependentOrderModel.class);
    }
}
