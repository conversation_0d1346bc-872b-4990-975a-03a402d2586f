package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.bo.SohuBusyTaskPayBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskRefundBo;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.busyorder.api.vo.SohuBusyTaskPayVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.bo.DelayConfirmqueryBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.enums.PaySceceTypeEnum;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.domain.SysUserInfo;
import com.sohu.third.wechat.pay.constant.WechatPayConstants;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 任务商单支付酬金
 */
@Component
@Slf4j
public class YiMaBusyTaskCommonStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    private RemoteIndependentTemplateService remoteIndependentTemplateService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteBusyTaskReceiveService remoteBusyTaskReceiveService;
    @Autowired
    private AsyncConfig asyncConfig;

    private static final String BUSY_TASK_PAY_PREFIX = "BUSY_TASK_COMMON:";
    private static final String BUSY_TASK_CALL_PREFIX = "BUSY_TASK_CALLBACK:";

    @Override
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 任务商单 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        if (!RedisUtils.setObjectIfAbsent(BUSY_TASK_PAY_PREFIX + payBo.getMasterId(), 1, Duration.ofSeconds(1))) {
            throw new ServiceException("请勿重复提交");
        }
        return getPay(payBo);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - 任务商单 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        String taskNumber = refundPayBo.getMasterOrderNo();
        //查询支付订单
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.Paid.name());
        if (CollUtil.isEmpty(payList)) {
            throw new RuntimeException("任务商单支付记录不存在");
        }
        for (SohuBusyTaskPayVo sohuBusyTaskPayVo : payList) {
            //退款流水号
            String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
            sendRefund(sohuBusyTaskPayVo, refundNumber);
            saveRefundRecord(sohuBusyTaskPayVo, refundNumber);
        }
        return true;
    }

    /**
     * @param callbackResponse 三方回调信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 任务商单回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        if (!RedisUtils.setObjectIfAbsent(BUSY_TASK_CALL_PREFIX + outTradeNo, 1, Duration.ofSeconds(3L))) {
            log.info("回调正在执行中......");
            return "success";
        }
        String transactionId = response.getTradeNo();
        String chargeAmount = response.getChargeAmount();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        SohuBusyTaskPayVo sohuBusyTaskPayVo = remoteBusyTaskService.queryBusyTaskPay(outTradeNo, PayStatus.WaitPay.name());
        if (ObjectUtils.isNull(sohuBusyTaskPayVo)) {
            return "success";
        }
        try {
            SohuBusyTaskVo busyTaskVo = remoteBusyTaskService.getByTaskNo(sohuBusyTaskPayVo.getTaskNumber());
            if (ObjectUtils.isNull(busyTaskVo)) {
                throw new RuntimeException("任务商单不存在");
            }
            //平台服务费比例
            BigDecimal platformRatio = sohuBusyTaskPayVo.getPlatformRatio();
            //三方手续费 f->y
            BigDecimal chargeFee = CalUtils.divide(new BigDecimal(chargeAmount), CalUtils.PERCENTAGE);
            //手续费承担方 0 分账方 1 平台
            int chargeUndertake = sohuBusyTaskPayVo.getChargeUndertake();
            //是否是分销佣金支付
            int paySceneType = sohuBusyTaskPayVo.getPaySceneType();
            // 平台自留金额 1% 支付金额*1%
            BigDecimal platformAmt = sohuBusyTaskPayVo.getPayAmount().multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
            BigDecimal payAmount = sohuBusyTaskPayVo.getPayAmount();
            BigDecimal busyAmount = sohuBusyTaskPayVo.getBusyAmount();
            BigDecimal shareAmount = sohuBusyTaskPayVo.getShareAmount();
            //平台服务费 支付金额*分账平台设置比例
            BigDecimal platformCharge = CalUtils.multiply(payAmount, CalUtils.divide(platformRatio, CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            // 商单金额占比
            BigDecimal distributorRadio = CalUtils.divide(busyAmount, payAmount);
            // 平台商单服务费
            BigDecimal platformChargeFee = CalUtils.multiply(platformCharge, distributorRadio).setScale(2, RoundingMode.HALF_UP);
            // 平台分销服务费
            BigDecimal platformShareAmount = platformCharge.subtract(platformChargeFee);
            //商单分账总金额
            BigDecimal distributorAmount = BigDecimal.ZERO;
            //商单分销总金额
            BigDecimal shareDistributorAmount = BigDecimal.ZERO;
            //承担手续费 0 分账方 1 平台
            if (chargeUndertake == Constants.ONE) {
                distributorAmount = paySceneType == Constants.ONE ? busyAmount.subtract(platformChargeFee) : busyAmount;
                platformChargeFee = platformChargeFee.subtract(chargeFee);
                shareDistributorAmount = paySceneType == Constants.TWO ? shareAmount.subtract(platformShareAmount) : shareAmount;
            } else if (chargeUndertake == Constants.ZERO) {
                BigDecimal distributorChargeAmount = CalUtils.multiply(chargeFee, distributorRadio).setScale(2, RoundingMode.HALF_UP);
                distributorAmount = paySceneType == Constants.ONE ? busyAmount.subtract(platformChargeFee).subtract(distributorChargeAmount) : busyAmount;
                shareDistributorAmount = paySceneType == Constants.TWO ? shareAmount.subtract(platformShareAmount).subtract(CalUtils.sub(chargeFee, distributorChargeAmount)) : shareAmount;
            }

            Date now = new Date();
            //更新支付状态 待支付->已支付
            SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
            payBo.setId(sohuBusyTaskPayVo.getId());
            payBo.setTransactionId(transactionId);
            payBo.setChargeAmount(chargeFee);
            payBo.setPayTime(now);
            payBo.setReceiveDistributorAmount(distributorAmount);
            payBo.setPlatformAmount(platformChargeFee);
            payBo.setShareDistributorAmount(shareDistributorAmount);
            payBo.setPlatformShareAmount(platformShareAmount);
            payBo.setPayStatus(PayStatus.Paid.name());
            remoteBusyTaskService.updateBusyTaskPay(payBo);
            //新增交易流水
            SohuTradeRecordBo recordBo = SohuTradeRecordBo.builder().build();
            recordBo.setUserId(sohuBusyTaskPayVo.getUserId());
            recordBo.setType(SohuTradeRecordEnum.Type.BusyTaskCommon.getCode());
            recordBo.setConsumeType(SohuTradeRecordEnum.Type.BusyTaskCommon.getCode());
            recordBo.setConsumeCode(sohuBusyTaskPayVo.getTaskNumber());
            recordBo.setMsg(SohuTradeRecordEnum.Type.BusyTaskCommon.getMsg());
            recordBo.setAmountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
            recordBo.setPayNumber(outTradeNo);
            recordBo.setAccountType(SohuTradeRecordEnum.AccountType.Amount.name());
            recordBo.setPayStatus(PayStatus.Paid.name());
            recordBo.setPayTime(now);
            recordBo.setAmount(sohuBusyTaskPayVo.getPayAmount());
            recordBo.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
            recordBo.setOperateChannel(sohuBusyTaskPayVo.getPayChannel());
            recordBo.setUnq(getUnq());
            // 保存流水记录 - 钱 - 支出
            saveTradeRecord(recordBo);
            //修改商单状态
            MqMessaging platformMessaging = new MqMessaging(outTradeNo, MqKeyEnum.DELAY_TASK_UPDATE.getKey());
            remoteStreamMqService.sendDelayMsg(platformMessaging, 1L);
            log.info("1111111");
            //异步通用商单分账
            if(sohuBusyTaskPayVo.getPaySceneType().equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode())) {
                SohuBusyTaskReceiveVo receiveVo = remoteBusyTaskReceiveService.queryByMasterTaskNumberrAndReceiveUserId(sohuBusyTaskPayVo.getTaskNumber(), sohuBusyTaskPayVo.getReceiveUserId());
                log.info("查询接单记录 {}",receiveVo);
                DelayConfirmqueryBo bo = new DelayConfirmqueryBo(BusyType.BusyTask.getType(), receiveVo.getId());
                MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(bo), "income_account_process");
                remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
                log.info("发送消息成功。。。。。。");
            }
            log.info("********");
            //异步新增埋点数据
            CompletableFuture.runAsync(() -> {
                LoginUser loginUser = remoteUserService.queryById(sohuBusyTaskPayVo.getUserId());
                if (sohuBusyTaskPayVo.getPaySceneType().equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode())) {
                    SohuBusyTaskVo sohuBusyTaskModel = remoteBusyTaskService.getByTaskNo(sohuBusyTaskPayVo.getTaskNumber());
                    SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
                    eventAttribute.setContentNo(sohuBusyTaskModel.getTaskNumber());
                    eventAttribute.setContentName(sohuBusyTaskModel.getTitle());
                    eventAttribute.setAmount(sohuBusyTaskPayVo.getPayAmount());
                    eventAttribute.setContentType("通用");
                    syncPayUserBehavior(loginUser.getUserId(), loginUser.getNickname(), "sd_pay_success", "愿望赏金支付成功", eventAttribute);
                }
                if (sohuBusyTaskPayVo.getPaySceneType().equals(PaySceceTypeEnum.BUSY_BOND_PAY.getCode())) {
                    SohuBusyTaskVo sohuBusyTaskModel = remoteBusyTaskService.getByTaskNo(sohuBusyTaskPayVo.getTaskNumber());
                    SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
                    eventAttribute.setContentNo(sohuBusyTaskModel.getTaskNumber());
                    eventAttribute.setContentName(sohuBusyTaskModel.getTitle());
                    eventAttribute.setAmount(sohuBusyTaskPayVo.getPayAmount().divide(BigDecimal.valueOf(0.2)).setScale(2, RoundingMode.HALF_UP));
                    eventAttribute.setPayAmount(sohuBusyTaskPayVo.getPayAmount());
                    eventAttribute.setContentType("通用");
                    syncPayUserBehavior(loginUser.getUserId(), loginUser.getNickname(), "sd_pay_deposit_success", "保证金支付成功", eventAttribute);
                }
            }, asyncConfig.getAsyncExecutor());
        } catch (Exception e) {
            //异常退款
            //退款流水号
            String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
            sendRefund(sohuBusyTaskPayVo, refundNumber);
            saveRefundRecord(sohuBusyTaskPayVo, refundNumber);
        }
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        Long masterId = payBo.getMasterId();
        Long userId = LoginHelper.getUserId();
        int paySceneType = payBo.getPaySceneType();
        BigDecimal amount = payBo.getAmount();
        Long receiveUserId = payBo.getReceiveUserId();
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON + payBo.getMasterId() + StrPool.COLON + paySceneType + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + payBo.getUserId() + StrPool.COLON + amount;
        if (StringUtils.isNotBlank(payBo.getPayChannel()) && payBo.getPayChannel().equals("mobile")) {
            boolean exists = RedisUtils.isExistsObject(key);
            // 如果存在直接唤醒
            if (exists) {
                log.info("任务保证金支付订单存在，直接唤醒支付，订单号：{}，缓存值：{}", payBo.getMasterOrderNo(), RedisUtils.getCacheObject(key));
                return RedisUtils.getCacheObject(key);
            }
        }
        if (paySceneType == PaySceceTypeEnum.BUSY_ORDER_PAY.getCode()) {
            if (amount == null || CalUtils.isLessEqualZero(amount)) {
                throw new ServiceException(MessageUtils.message("商单支付金额不能小于等于0"));
            }
            if (receiveUserId == null) {
                throw new ServiceException(MessageUtils.message("接单用户Id不能为空"));
            }
        }
        // 查询商单
        SohuBusyTaskModel sohuBusyTaskModel = remoteBusyTaskService.queryById(masterId);
        Objects.requireNonNull(sohuBusyTaskModel, "任务商单不存在");
        String taskNumber = sohuBusyTaskModel.getTaskNumber();
        // 判断是否未支付记录，如果存在删除撤销之前的交易
        SohuBusyTaskPayVo payVo = remoteBusyTaskService.queryByPayScene(sohuBusyTaskModel.getTaskNumber(), PayStatus.WaitPay.name(), paySceneType);
        if (Objects.nonNull(payVo)) {
            barcodeCancelPay(payVo.getId());
            //删除缓存
            String oldKey = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON + payBo.getMasterId() + StrPool.COLON + paySceneType + StrPool.COLON + payVo.getPayChannel() + StrPool.COLON + payVo.getUserId();
            if (RedisUtils.isExistsObject(oldKey)) {
                RedisUtils.deleteObject(oldKey);
            }
        }

        // 查询分账模板
        SohuIndependentTemplateModel templateModel = remoteIndependentTemplateService.queryTemplateInfo(null,null, 2);
        if (Objects.isNull(templateModel)) {
            throw new ServiceException("商单分账模板为空,请联系系统管理员");
        }
        // 支付酬金金额
        BigDecimal busyAmount = BigDecimal.ZERO;
        BigDecimal disAmount = BigDecimal.ZERO;
        BigDecimal payAmount = BigDecimal.ZERO;
        if (paySceneType == PaySceceTypeEnum.BUSY_SHARE_PAY.getCode()) {
            disAmount = sohuBusyTaskModel.getKickbackValue();
            payAmount = sohuBusyTaskModel.getKickbackValue();
        } else if (paySceneType == PaySceceTypeEnum.BUSY_BOND_PAY.getCode()) {
            SohuBusyTaskPayVo sohuBusyTaskPayVo = remoteBusyTaskService.queryByPayScene(taskNumber, PayStatus.Paid.name(), PaySceceTypeEnum.BUSY_ORDER_PAY.getCode());
            if (sohuBusyTaskPayVo == null) {
                throw new ServiceException("发单方未支付商单金额");
            }
            checkTimeOut(taskNumber, paySceneType, sohuBusyTaskPayVo.getPayTime());
            BigDecimal bondAmount = CalUtils.multiply(sohuBusyTaskPayVo.getPayAmount(), BigDecimal.valueOf(0.2));
            payAmount = bondAmount;
        } else {
            checkTimeOut(taskNumber, paySceneType, null);
            busyAmount = amount;
            payAmount = amount;
        }
        String outTradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_TASK_COMMON);
        //封装支付请求参数
        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        ScanpayRequest request = getScanpayRequest();
        // 备注
        unifiedorderRequest.setMemo("通用商单支付酬金");
        request.setMemo("通用商单支付酬金");
        // 唯一订单号
        unifiedorderRequest.setPosSeq(outTradeNo);
        request.setPosSeq(outTradeNo);
        // 转换价格y-f
        int totalPrice = CalUtils.yuanToCent(payAmount).intValue();
        // 商单价格对象
        // 总金额
        Unifiedorder unifiedOrder = getUnifiedorder(payBo.getUserId());
        Scanpay scanpay = getScanpay();
        unifiedOrder.setTxAmt(totalPrice);
        scanpay.setTxAmt(totalPrice);
        // 是否分账配置
        ExtendParams extendParams = new ExtendParams();
        // 不分账 -- R实时分账 --D延时分账
        extendParams.setSplitFlag(ExtendParams.D);
        unifiedOrder.setExtendParams(extendParams);
        scanpay.setExtendParams(extendParams);
        // 设置请求参数
        unifiedorderRequest.setUnifiedorderRequest(unifiedOrder);
        request.setScanpayRequest(scanpay);
        //新增商单支付记录
        SohuBusyTaskPayBo sohuBusyTaskPayBo = new SohuBusyTaskPayBo();
        sohuBusyTaskPayBo.setUserId(userId);
        sohuBusyTaskPayBo.setPayAmount(payAmount);
        sohuBusyTaskPayBo.setPayNumber(outTradeNo);
        sohuBusyTaskPayBo.setPayStatus(PayStatus.WaitPay.name());
        sohuBusyTaskPayBo.setBusyType(BusyType.BusyTaskCommon.name());
        sohuBusyTaskPayBo.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
        sohuBusyTaskPayBo.setPayChannel(getOperateChannel(payBo.getPayChannel()));
        sohuBusyTaskPayBo.setOrderNo(outTradeNo);
        sohuBusyTaskPayBo.setTaskNumber(taskNumber);
        sohuBusyTaskPayBo.setPlatformRatio(templateModel.getPlatformRatio());
        sohuBusyTaskPayBo.setAdminRatio(templateModel.getAdminRatio());
        sohuBusyTaskPayBo.setConsumerInviteRatio(templateModel.getConsumerInviteRatio());
        sohuBusyTaskPayBo.setDistributorRatio(templateModel.getDistributorRatio());
        sohuBusyTaskPayBo.setDistributorInviteRatio(templateModel.getDistributorInviteRatio());
        sohuBusyTaskPayBo.setBusyAmount(busyAmount);
        sohuBusyTaskPayBo.setShareAmount(disAmount);
        sohuBusyTaskPayBo.setChargeUndertake(templateModel.getChargeUndertake());
        sohuBusyTaskPayBo.setPaySceneType(paySceneType);
        sohuBusyTaskPayBo.setReceiveUserId(receiveUserId);
        remoteBusyTaskService.saveBusyTaskPay(sohuBusyTaskPayBo);

        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-通用商单支付请求request：{}", JSONUtil.toJsonStr(request));
            ScanpayResponse response = Client.getClient().execute(request);
            log.info("翼码支付 - PC-通用商单支付请求response返回：{}", JSONUtil.toJsonStr(response));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- 通用商单支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        response.setTaskNumber(sohuBusyTaskModel.getTaskNumber());
        log.info("翼码支付 -MOBILE- 通用商单支付请求response返回：{}", JSONUtil.toJsonStr(response));
        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(2L));
        return JSONUtil.toJsonStr(response);
    }

    /**
     * 撤销交易
     */
    private void barcodeCancelPay(Long payId) {
        //更新支付状态 待支付->取消支付
        SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
        payBo.setId(payId);
        payBo.setPayStatus(PayStatus.Cancel.name());
        remoteBusyTaskService.updateBusyTaskPay(payBo);
    }

    /**
     * 校验支付已超时
     *
     * @param taskNumber
     * @param paySceneType
     */
    private void checkTimeOut(String taskNumber, Integer paySceneType, Date payTime) {
        if (paySceneType.equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode())) {
            Date receiveTime = remoteBusyTaskService.getTaskTimeWithMasterTaskNumber(taskNumber);
            if (DateUtil.offsetHour(receiveTime, 24).before(new Date())) {
                throw new ServiceException("商单金额24小时支付超时");
            }
        }
        if (paySceneType.equals(PaySceceTypeEnum.BUSY_BOND_PAY.getCode())) {
            if (DateUtil.offsetHour(payTime, 4).before(new Date())) {
                throw new ServiceException("商单保证金支付超时");
            }
        }
    }

    private void sendRefund(SohuBusyTaskPayVo sohuBusyTaskPayVo, String refundNumber) {
        int totalPrice = CalUtils.yuanToCent(sohuBusyTaskPayVo.getPayAmount()).intValue();
        //翼码退款
        BarcodeReverseRequest barcodeReverseRequest = getBarcodeReverseRequest();
        barcodeReverseRequest.setPosSeq(refundNumber);
        BarcodeReverse barcodeReverse = new BarcodeReverse();
        barcodeReverse.setTxAmt(totalPrice);
        barcodeReverse.setOrgPosSeq(sohuBusyTaskPayVo.getPayNumber());
        barcodeReverseRequest.setBarcodeReverseRequest(barcodeReverse);
        log.info("退款请求request={}", JSONObject.toJSONString(barcodeReverseRequest));
        BarcodeReverseResponse barcodeReverseResponse = Client.getClient().execute(barcodeReverseRequest);
        log.info("退款请求response返回={}", JSONObject.toJSONString(barcodeReverseResponse));
        List<String> resultList = Lists.newArrayList("9998", "0000");
        if (ObjectUtils.isNull(barcodeReverseResponse) || !resultList.contains(barcodeReverseResponse.getResult().getId())) {
            log.error("流量商单退款异常：{}", JSONUtil.toJsonStr(barcodeReverseResponse));
            throw new RuntimeException(barcodeReverseResponse.getResult().getComment());
        }

    }

    private void saveRefundRecord(SohuBusyTaskPayVo sohuBusyTaskPayVo, String refundNumber) {
        //新增退款记录
        SohuBusyTaskRefundBo refundBo = new SohuBusyTaskRefundBo();
        refundBo.setTaskNumber(sohuBusyTaskPayVo.getTaskNumber());
        refundBo.setRefundOrderNo(refundNumber);
        refundBo.setRefundNumber(refundNumber);
        refundBo.setUserId(sohuBusyTaskPayVo.getUserId());
        refundBo.setTransactionId(sohuBusyTaskPayVo.getTransactionId());
        refundBo.setRefundAmount(sohuBusyTaskPayVo.getPayAmount());
        refundBo.setRefundStatus(WechatPayConstants.RefundStatus.SUCCESS);
        remoteBusyTaskService.saveBusyTaskRefund(refundBo);
        //更新支付表中退款信息
        SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
        payBo.setId(sohuBusyTaskPayVo.getId());
        payBo.setRefundAmount(sohuBusyTaskPayVo.getPayAmount());
        payBo.setRefundId(refundNumber);
        payBo.setPayStatus(PayStatus.Refund.name());
        remoteBusyTaskService.updateBusyTaskPay(payBo);
        //更新流水记录状态 待支付->已支付
        remoteMiddleTradeRecordService.updatePayStatus(sohuBusyTaskPayVo.getTaskNumber(), null, PayStatus.Paid.name(), PayStatus.Refund.name());
        //新增退款流水
        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = SohuTradeRecordBo.builder();
        recordBo.userId(sohuBusyTaskPayVo.getUserId());
        recordBo.amount(sohuBusyTaskPayVo.getPayAmount());
        recordBo.payType(sohuBusyTaskPayVo.getPayType());
        recordBo.payStatus(PayStatus.Refund.name());
        recordBo.payTime(new Date());
        recordBo.operateChannel(sohuBusyTaskPayVo.getPayChannel());
        recordBo.type(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
        recordBo.consumeCode("0");
        recordBo.msg("通用商单-" + PaySceceTypeEnum.getByCode(sohuBusyTaskPayVo.getPaySceneType()).getDesc() + "退款");
        recordBo.amountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
        recordBo.payNumber(sohuBusyTaskPayVo.getPayNumber());
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
    }

}
