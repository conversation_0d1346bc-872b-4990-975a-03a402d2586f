
package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.busyorder.api.domain.SohuBusyOrderSaveOrderPayReqBo;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.SohuBusyOrderPayModel;
import com.sohu.busyorder.api.model.SohuBusyTaskReceiveModel;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.service.RemoteMiddleInviteService;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.vo.SohuOrderIndependentPriceVo;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.bo.SohuIndependentIdPayBo;
import com.sohu.pay.api.bo.SohuIndependentOrderBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.domain.SohuIndependentLevelConfig;
import com.sohu.pay.domain.SohuIndependentOrder;
import com.sohu.pay.service.ISohuAccountBankService;
import com.sohu.pay.service.ISohuIndependentLevelConfigService;
import com.sohu.pay.service.ISohuIndependentOrderService;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteUserService;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.model.SplitInfo;
import com.wangcaio2o.ipossa.sdk.model.SplitList;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sohu.common.core.enums.SohuTradeRecordEnum.Type.BusyTaskParty;

/**
 * 发单方支付酬金
 */
@Component
@Slf4j
public class YiMaTaskPartyPayStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    @Resource
    private ISohuIndependentOrderService sohuIndependentOrderService;
    @DubboReference
    private RemoteBusyOrderService remoteBusyOrderService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleInviteService remoteMiddleInviteService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemoteIndependentTemplateService remoteIndependentTemplateService;
    @Resource
    private ISohuAccountBankService sohuAccountBankService;
    @DubboReference
    private RemoteImService remoteImService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 发单方支付酬金 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return pay(payBo);
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - 发单方支付酬金 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        String busyOrder = refundPayBo.getMasterOrderNo();
        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(busyOrder, Boolean.TRUE);
        SohuBusyOrderPayModel orderPayModel = remoteBusyOrderService.queryBusyOrderPay(busyOrder, PayObject.BusyTaskPromise.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
        if (orderPayModel.getPayAmount() != null && CalUtils.isZero(taskReceiveModel.getAmount()) &&
                StrUtil.equalsAnyIgnoreCase(orderPayModel.getPayStatus(), PayStatus.Paid.name())) {
            remoteBusyOrderService.updateBusyOrderAndRece(orderPayModel.getBusyOrderReceiveId(), false);
            return Boolean.TRUE;
        }
        try {
            SohuBusyTaskSiteModel sohuBusyTaskSiteModel = remoteBusyOrderService.querySiteByTaskNo(taskReceiveModel.getTaskNumber());
            if (Objects.nonNull(sohuBusyTaskSiteModel)) {
                remoteImService.deleteGroupTaskNoLogin(sohuBusyTaskSiteModel.getUserId(), taskReceiveModel.getTaskNumber());
                log.info("任务群解散,taskNumber：{}", taskReceiveModel.getTaskNumber());
            }
        } catch (Exception e) {
            log.error("任务群解散失败,taskNumber：{}", taskReceiveModel.getTaskNumber());
        }

        YiMaPayConfig yiMaPayConfig = getPayConfig();
        // 组装微信小程序退款请求参数
        BarcodeReverseRequest request = getBarcodeReverseRequest();
        request.setPosId(yiMaPayConfig.getPosId());
        request.setIsspid(yiMaPayConfig.getIssPid());
        request.setSystemId(yiMaPayConfig.getSystemId());
        request.setStoreId(yiMaPayConfig.getStoreId());
        // 退款参数封装
        BarcodeReverse reverse = new BarcodeReverse();
        String payType = getPayType(orderPayModel.getPayChannel());
        reverse.setPayType(payType);
        // 请求退款单号拼接
        request.setPosSeq("R" + System.nanoTime());
        // 支付请求流水号
        reverse.setOrgPosSeq(orderPayModel.getOrderNo());
        reverse.setTxAmt(BigDecimalUtils.yuanToFen(orderPayModel.getPayAmount()));
        request.setBarcodeReverseRequest(reverse);
        log.info("发单方支付酬金退款请求:{}", JSONUtil.toJsonStr(request));

        BarcodeReverseResponse response = Client.getClient().execute(request);
        List<String> resultList = Lists.newArrayList("9998", "0000");
        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
            log.error("发单方支付酬金退款请求异常：{}", JSONUtil.toJsonStr(response));
            throw new RuntimeException(response.getResult().getComment());
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 发单方支付酬金回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        // 手续费
        BigDecimal chargeAmount = CalUtils.centToYuan(new BigDecimal(response.getChargeAmount()));
        SohuIndependentOrder independentOrder = sohuIndependentOrderService.selectByOrderAndUserId(2L, outTradeNo);
        if (ObjectUtils.isNotNull(independentOrder)) {
            independentOrder.setIndependentPrice(CalUtils.sub(independentOrder.getIndependentPrice(), chargeAmount));
            SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
            BeanUtils.copyProperties(independentOrder, independentOrderBo);
            // 修改管理员扣除手续费后的金额
            sohuIndependentOrderService.updateByBo(independentOrderBo);
        }
        // 更新分账单表
        sohuIndependentOrderService.updateIndependentStatus(outTradeNo, BusyType.BusyOrder.name(), 1);
        SohuBusyOrderPayModel busyOrderPayModelReceive = remoteBusyOrderService.queryBusyOrderPayByOrderNo(outTradeNo);
        if (Objects.isNull(busyOrderPayModelReceive)) {
            throw new ServiceException("发单方支付酬金记录不存在");
        }
        // 第三方支付流水号
        busyOrderPayModelReceive.setTransactionId(response.getTradeNo());
        // 账单、分账单、执行记录相关
        remoteBusyOrderService.updateBusyOrderReceiveSuccess(outTradeNo, response.getTradeNo());
        // 更新支付单
        updatePayOrder(outTradeNo, chargeAmount, PayStatus.Paid.name(), response.getTradeNo());
        // 更新流水明细
        remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, response.getTradeNo(), PayStatus.Paid, null);
        // 是否是佣金支付
        boolean isIndependent = RedisUtils.isExistsObject(outTradeNo + "isIndependentAmount");
        if (isIndependent) {
            busyOrderPayModelReceive.setIsIndependentAmount(Boolean.TRUE);
        }
        // 是否是一次性支付所有酬金
        boolean isAll = RedisUtils.isExistsObject(outTradeNo + "allAmount");
        if (isAll) {
            busyOrderPayModelReceive.setAllAmount(Boolean.TRUE);
        }
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
                busyOrderPayModelReceive.getBusyOrder() + StrPool.COLON + busyOrderPayModelReceive.getPayChannel() + StrPool.COLON + "YiMaTaskParty";
        RedisUtils.deleteObject(key);
        // 发送任务完成通知
        remoteBusyTaskNoticeService.sendTaskSiteNotice(independentOrder.getTaskNumber(), TaskNoticeEnum.TASK_FINISH,
                LoginHelper.getUserId(), null, null, Boolean.FALSE);
        // TODO 传递接单方ID
        remoteBusyTaskNoticeService.sendTaskSiteNotice(independentOrder.getTaskNumber(), TaskNoticeEnum.EXECUTE_PLAN_PASS,
                independentOrder.getUserId(), null, null, Boolean.FALSE);
        // 支付回调后续相关状态操作
        MqMessaging mqReceive = new MqMessaging(JSONUtil.toJsonStr(busyOrderPayModelReceive), MqKeyEnum.BUSY_TASK_PAY_PARTY.getKey());
        remoteStreamMqService.sendDelayMsg(mqReceive, 2L);
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public String pay(SohuPrePayBo payBo) {
        String masterOrderNo = payBo.getMasterOrderNo();
        // 如果存在直接唤醒
        BigDecimal amount = payBo.getAmount();
        if (amount == null || CalUtils.isLessEqualZero(amount)) {
            throw new ServiceException(MessageUtils.message("付款金额不能小于等于0"));
        }
        // 根据子订单号+已接单查询唯一订单
        SohuBusyTaskReceiveModel busyOrderReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(masterOrderNo, Boolean.TRUE);
        if (Objects.isNull(busyOrderReceiveModel)) {
            throw new ServiceException(MessageUtils.message("order.rece.not.exist"));
        }
        // 查询商单子单
        SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(masterOrderNo);
        if (Objects.isNull(taskSiteModel)) {
            throw new ServiceException(MessageUtils.message("BUSY_ORDER_NOT_FOUNT"));
        }
        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        ScanpayRequest scanpayRequest = getScanpayRequest();

        LoginUser user = remoteUserService.queryById(payBo.getUserId());
        // 用户信息
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(Collections.singletonList(busyOrderReceiveModel.getUserId()));
        LoginUser recipient = userMap.get(busyOrderReceiveModel.getUserId());
        unifiedorderRequest.setMemo("支付任务商单酬金-" + recipient.getNickname());
        scanpayRequest.setMemo("支付任务商单酬金-" + recipient.getNickname());
        // 分账信息
        SplitInfo splitInfo = new SplitInfo();
        // 先查询分拥等级
        ISohuIndependentLevelConfigService levelConfigService = SpringUtils.getBean(ISohuIndependentLevelConfigService.class);
        SohuIndependentLevelConfig levelConfig = levelConfigService.queryByStatusAndSiteId(0, 11L);
        Integer level = ObjectUtils.isNull(levelConfig) ? levelConfig.getLevel() : 2;
        // 查询接单人是否有拉新人-上上级拉新人
        Long regUserId = remoteMiddleInviteService.selectByInviteCount(busyOrderReceiveModel.getUserId(), level);
        Boolean regUser = Boolean.FALSE;
        if (null != regUserId) {
            regUser = Boolean.TRUE;
        }
        Boolean finalRegUser = regUser;
        // 组装id
        SohuIndependentIdPayBo independentIdBo = new SohuIndependentIdPayBo();
        //任务方用户
        LoginUser taskLoginUser = remoteUserService.queryById(taskSiteModel.getUserId());
        if (Objects.nonNull(taskLoginUser)) {
            independentIdBo.setTaskUserName(taskLoginUser.getNickname());
        }

        // 根据城市站点id获取城市站长id
        Long cityId = remoteMiddleSiteService.queryById(taskSiteModel.getSiteId()).getStationmasterId();
        // 根据城市站点id获取国家站点用户id
        Long countryId = remoteMiddleSiteService.selectSiteByPid(taskSiteModel.getSiteId()).getStationmasterId();
        Boolean agencyId = Boolean.FALSE;
        // 分销人的拉新人
        Long distributionInviteUserId = null;
        log.warn("busyOrderReceModel : {}", JsonUtils.toJsonString(busyOrderReceiveModel));
        if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() != 0L) {
            log.warn("distributionUserId : {}", busyOrderReceiveModel.getSharePerson());
            distributionInviteUserId = remoteMiddleInviteService.selectByInviteCount(busyOrderReceiveModel.getSharePerson(), level);
        }
        log.warn("distributionInviteUserId : {}", distributionInviteUserId);
        // 查询分账模板
        // 根据城市站点、商品分账模板类型 查询分账模板
        SohuIndependentTemplateModel templateModel = remoteIndependentTemplateService.queryByIdAndType(taskSiteModel.getSiteId(), 2);
        if (Objects.isNull(templateModel)) {
            throw new ServiceException("商单分账模板为空,请联系系统管理员");
        }
        // 分销金额
        BigDecimal independentPrice = taskSiteModel.getDistributionAmount();
        if (!payBo.getAllAmount() || payBo.getIsDelivery()) {
            independentPrice = BigDecimal.ZERO;
        }

        // 分账方组装
        Map<String, BigDecimal> calculateMap;
        // 分账请求流水号
        String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
        // 分销单组装
        List<SohuIndependentOrderBo> independentOrderBoLists = new ArrayList<>();
        // 如果是佣金结算
        if (null != payBo.getIsIndependentAmount() && payBo.getIsIndependentAmount()) {
            log.info("存在佣金结算");
            String tradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_RECE_ORDER_PREFIX);
            unifiedorderRequest.setMemo("支付商单接佣金-" + user.getNickname());
            unifiedorderRequest.setPosSeq(tradeNo);
            scanpayRequest.setMemo("支付商单接佣金-" + user.getNickname());
            scanpayRequest.setPosSeq(tradeNo);
            Unifiedorder unifiedorder = getUnifiedorder(payBo.getUserId());
            Scanpay scanpay = getScanpay();
            // 总金额
            unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(independentPrice));
            scanpay.setTxAmt(BigDecimalUtils.yuanToFen(independentPrice));
            // 商单接单分账算价
            calculateMap = this.calculateBusyOrderDistribution(templateModel, taskSiteModel.getFullAmount(), independentPrice,
                    busyOrderReceiveModel.getSharePerson() != null, finalRegUser, distributionInviteUserId, agencyId, Boolean.TRUE);
            for (String independentObject : calculateMap.keySet()) {
                // 分账金额
                BigDecimal itemPrice = calculateMap.get(independentObject);
                SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
                independentOrderBo.setOrderNo(tradeNo);
                independentOrderBo.setTradeType(BusyType.BusyOrder.name());
                independentOrderBo.setIndependentStatus(2);
                independentOrderBo.setTradeNo(delayOrderNo);
                //冗余字段
                independentOrderBo.setTaskNumber(taskSiteModel.getTaskNumber());
                independentOrderBo.setTaskFullAmount(taskSiteModel.getFullAmount());
                independentOrderBo.setTaskTitle(taskSiteModel.getTitle());
                independentOrderBo.setTaskUserName(independentIdBo.getTaskUserName());
                // todo
                if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
                    independentOrderBo.setUserId(2L);
                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.country.getKey(), independentObject)) {
                    independentOrderBo.setUserId(countryId);
                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.city.getKey(), independentObject)) {
                    independentOrderBo.setUserId(cityId);
                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.invite.getKey(), independentObject)) {
                    independentOrderBo.setUserId(regUserId);
                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
                    independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distributionInvite.getKey(), independentObject)) {
                    independentOrderBo.setUserId(distributionInviteUserId);
                } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
                    // todo 代理人id
                    independentOrderBo.setUserId(0L);
                }
                if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
                    log.info("{} 用户ID为空", independentObject);
                    continue;
                }
                if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
                    log.info("分账用户-{} 所得金额为空", independentObject);
                    continue;
                }
                independentOrderBo.setIndependentObject(independentObject);
                independentOrderBo.setMerId(0L);
                independentOrderBo.setSiteId(taskSiteModel.getSiteId());
                independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
                independentOrderBoLists.add(independentOrderBo);
            }
            // 平台手续费百分比
            BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
            log.warn("平台手续费百分比：{}", platformDivide);
            independentIdBo.setAdminId(2L);
            independentIdBo.setCountryId(countryId);
            independentIdBo.setCityId(cityId);
            // todo 代理id
//                    if (null != agencyId)
            // 分销人id
            if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() > 0L) {
                independentIdBo.setDistributorId(busyOrderReceiveModel.getSharePerson());
            }
            // 拉新人的上上级id
            if (regUserId != null && regUserId > 0L) {
                independentIdBo.setInviteId(regUserId);
            }
            // 接单人id
            independentIdBo.setReceiveId(busyOrderReceiveModel.getUserId());
            // 分销人的拉新人id
            if (null != distributionInviteUserId && distributionInviteUserId != 0L) {
                independentIdBo.setDistributorInviteId(distributionInviteUserId);
            }
            // 查询所有分账人的翼码帐户信息
            List<Long> allUserIds = getAllIds(independentIdBo);
            log.warn("allUserIds : {}", allUserIds);

            // 查询所有参与分账人员的翼码账户信息
            Map<Long, SohuAccountBankVo> accountBankVoMap = sohuAccountBankService.queryMapByUserIds(allUserIds);
            log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));


            Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
            // 分账账号信息参数
            List<SplitList> splitLists = new ArrayList<>();
            // 累计的没有分出去的金额
            BigDecimal newPlatePrice = BigDecimal.ZERO;
            List<Long> yimaAccountExist = new ArrayList<>(List.of(2L));
            for (Long userId : independentOrderMap.keySet()) {
                SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
                // todo
                if (userId != 2L) {
                    if (Objects.isNull(sohuAccountBankVo)) {
                        log.warn("翼码账户为空,{}", userId);
                        List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
                        newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                        continue;
                    }
                    List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
                    if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
                        continue;
                    }
                    SplitList item = new SplitList();
                    BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (CalUtils.isLessEqualZero(divAmt)) {
                        continue;
                    }
                    yimaAccountExist.add(userId);
                    if (sohuIndependentOrderBos.size() > 1) {
                        log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
                    }
                    item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
                    item.setMerchantId(sohuAccountBankVo.getMerchantId());
                    splitLists.add(item);
                }
            }
            // 过滤掉没有分账的分账用户
            independentOrderBoLists = independentOrderBoLists.stream()
                    .filter(item -> yimaAccountExist.contains(item.getUserId()))
                    .collect(Collectors.toList());


            // 去重从新计算之后的传给第三方的分账信息
            List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId,
                    Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
                SplitList newSplitList = new SplitList();
                newSplitList.setMerchantId(entry.getKey());
                newSplitList.setDivAmt(entry.getValue().toString());
                return newSplitList;
            }).collect(Collectors.toList());
            // 平台总金额
            BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);

//            // 平台最少不能低于1%的留存
//            BigDecimal minPrice = CalUtils.multiply(independentPrice, new BigDecimal("0.01"));
//            // 给文化传媒的金额
//            BigDecimal cmPrice = CalUtils.sub(allPlatPrice, minPrice);
//            SplitList cmInfo = new SplitList();
//            cmInfo.setDivAmt(BigDecimalUtils.yuanToFen(cmPrice).toString());
//            cmInfo.setMerchantId("2290110");
//            splitListsNew.add(cmInfo);

            // 设置分账参数
            splitInfo.setSplitList(splitListsNew);
            // 平台总留存金额
//            splitInfo.setKeepAmt(BigDecimalUtils.yuanToFen(minPrice).toString());
            splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
            // 分账信息
            // 是否分账配置
            ExtendParams extendParams = new ExtendParams();
            extendParams.setSplitInfo(splitInfo);
            // 不分账 -- R实时分账 --D延时分账
            extendParams.setSplitFlag(ExtendParams.R);
            // 添加分账扩展参数
            unifiedorder.setExtendParams(extendParams);
            scanpay.setExtendParams(extendParams);
            // 设置请求参数
            unifiedorderRequest.setUnifiedorderRequest(unifiedorder);
            scanpayRequest.setScanpayRequest(scanpay);

            // todo 是否一次性支付商单酬金  一次性支付的话就直接完成任务 改掉所有阶段性状态为已完成
            if (payBo.getIsIndependentAmount()) {
                // 如果是佣金结算 将支付单号+isIndependentAmount 12423432432isIndependentAmount 作为key存入
                RedisUtils.setCacheObject(tradeNo + "isIndependentAmount", Boolean.TRUE, Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            }
            // 保存待付款记录
            SohuBusyOrderSaveOrderPayReqBo orderPayModel = new SohuBusyOrderSaveOrderPayReqBo()
                    .setOrderId(taskSiteModel.getTaskNumber())
                    .setOrderNo(tradeNo)
                    .setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus())
                    .setPayStatus(PayStatus.WaitPay.name())
                    .setAmount(independentPrice)
                    .setBusyType(PayObject.BusyOrderReceive.name())
                    .setPayeeId(user.getUserId())
                    .setUserId(busyOrderReceiveModel.getUserId())
                    .setDeliveryId(payBo.getMasterId());
            remoteBusyOrderService.saveBusyOrderPay(orderPayModel);

            payBo.setAmount(independentPrice);
            // 保存主支付单
            saveMasterPayOrder(payBo, tradeNo, PayStatus.WaitPay.name());
            // 保存子支付单
            savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + tradeNo, tradeNo, PayStatus.WaitPay.name());

            // 保存分销单记录
            independentOrderBoLists.stream().filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject()))
                    .findFirst().ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
            sohuIndependentOrderService.insertByBoList(independentOrderBoLists);
            List<SohuTradeRecordBo> tradeRecords = new ArrayList<>();
            for (SohuIndependentOrderBo orderBo : independentOrderBoLists) {
                SohuTradeRecordBo tradeRecordBo = SohuTradeRecordBo.builder().
                        userId(orderBo.getUserId()).
                        type(BusyTaskParty.getCode()).
                        consumeType(BusyTaskParty.getCode()).
                        consumeCode(masterOrderNo).
                        amount(orderBo.getIndependentPrice()).
                        amountType(SohuTradeRecordEnum.AmountType.InCome.getCode()).
                        payType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus()).
                        operateChannel(payBo.getPayChannel()).
                        payNumber(tradeNo).
                        payStatus(PayStatus.WaitPay.name()).
                        msg(taskSiteModel.getTitle() + StrPool.DASHED + BusyTaskParty.getMsg()).
                        independent(true).
                        independentObject(orderBo.getIndependentObject()).
                        independentStatus(orderBo.getIndependentStatus()).
                        accountType(SohuTradeRecordEnum.AccountType.Amount.name()).
                        unq(getUnq()).
                        build();
                tradeRecords.add(tradeRecordBo);
            }
            // 保存流水明细
            remoteMiddleTradeRecordService.insertBatch(tradeRecords);

            if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
                log.info("翼码支付 -PC- 发单方支付酬金 -存在佣金结算 请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
                ScanpayResponse response = Client.getClient().execute(scanpayRequest);
                log.info("翼码支付 -PC- 发单方支付酬金 -存在佣金结算 请求response返回：{}", JSONUtil.toJsonStr(response));
                return JSONUtil.toJsonStr(response);
            }
            log.info("翼码支付 -MOBILE- 发单方支付酬金 - 存在佣金结算 请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
            UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
            log.info("翼码支付 -MOBILE- 发单方支付酬金 - 存在佣金结算 请求response返回：{}", JSONUtil.toJsonStr(response));
            return JSONUtil.toJsonStr(response);

        }
        // 是否分销
        Boolean isRecommend = Boolean.TRUE;
        if (taskSiteModel.getKickbackType().equals(KickbackType.none.getCode())) {
            isRecommend = Boolean.FALSE;
        }
        String tradeNo = NumberUtil.getOrderNo(OrderConstants.BUSY_RECE_ORDER_PREFIX);
        unifiedorderRequest.setPosSeq(tradeNo);
        scanpayRequest.setPosSeq(tradeNo);
        // 参与分账任务金额
        BigDecimal payPrice = BigDecimal.ZERO;
        payPrice = taskSiteModel.getFullAmount();
        // 商单总金额
        BigDecimal busyPrice = BigDecimal.ZERO;
        // 根据条件计算busyPrice和independentPrice 如果没有分销人-商单总金额为商单价值金额
        if (busyOrderReceiveModel.getSharePerson() == null || busyOrderReceiveModel.getSharePerson().equals(0L)) {
            // 没有分销人时的逻辑
            if (payBo.getIsDelivery() || !payBo.getAllAmount()) {
                busyPrice = amount;
                payPrice = amount;
                independentPrice = BigDecimal.ZERO; // 确保independentPrice也被设置为0
            } else {
                busyPrice = taskSiteModel.getFullAmount();
                payPrice = taskSiteModel.getFullAmount();
            }
            isRecommend = Boolean.FALSE;
            independentPrice = BigDecimal.ZERO;
        } else {
            // 有分销人时的逻辑  是否是提前结算、阶段性支付
            if (payBo.getAllAmount() && !payBo.getIsDelivery()) {
                busyPrice = CalUtils.add(taskSiteModel.getFullAmount(), independentPrice);
                if (CalUtils.equal(busyPrice, amount)) {
                    amount = CalUtils.sub(amount, independentPrice);
                    payPrice = amount;
                    log.warn("busyPrice-amount酬金金额: {}", JsonUtils.toJsonString(amount));
                }
            } else if (!payBo.getAllAmount() && payBo.getIsDelivery()) {
                busyPrice = amount;
                payPrice = amount;
                independentPrice = BigDecimal.ZERO;
                // log.warn("busyPrice-all: {}", JsonUtils.toJsonString(busyPrice));
            } else {
                independentPrice = taskSiteModel.getDistributionAmount();
                busyPrice = amount;
                payPrice = CalUtils.sub(amount, independentPrice);
//                payPrice = CalUtils.add(amount, independentPrice);
            }
        }

        Unifiedorder unifiedorder = getUnifiedorder(payBo.getUserId());
        Scanpay scanpay = getScanpay();
        // 总金额
        unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(busyPrice));
        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(busyPrice));
        // 阶段性商单接单分账算价
        calculateMap = this.calculateBusyOrderDistribution(templateModel, payPrice, independentPrice, isRecommend, finalRegUser, distributionInviteUserId, agencyId, Boolean.FALSE);
        for (String independentObject : calculateMap.keySet()) {
            // 分账金额
            BigDecimal itemPrice = calculateMap.get(independentObject);
            SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
            independentOrderBo.setOrderNo(tradeNo);
            independentOrderBo.setTradeType(BusyType.BusyOrder.name());
            independentOrderBo.setIndependentStatus(2);
            independentOrderBo.setTradeNo(delayOrderNo);
            //冗余字段
            independentOrderBo.setTaskNumber(taskSiteModel.getTaskNumber());

            independentOrderBo.setTaskFullAmount(payPrice);
            independentOrderBo.setTaskTitle(taskSiteModel.getTitle());
            independentOrderBo.setTaskUserName(independentIdBo.getTaskUserName());
            if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
                // todo
                independentOrderBo.setUserId(2L);
            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.country.getKey(), independentObject)) {
                independentOrderBo.setUserId(countryId);
            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.city.getKey(), independentObject)) {
                independentOrderBo.setUserId(cityId);
            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.rece.getKey(), independentObject)) {
                independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.invite.getKey(), independentObject)) {
                independentOrderBo.setUserId(regUserId);
            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
                independentOrderBo.setUserId(busyOrderReceiveModel.getSharePerson());
//                independentOrderBo.setTaskFullAmount(independentPrice);
            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distributionInvite.getKey(), independentObject)) {
                independentOrderBo.setUserId(distributionInviteUserId);
//                independentOrderBo.setTaskFullAmount(independentPrice);
            } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
                // todo 代理人id
                independentOrderBo.setUserId(0L);
            }
            if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
                log.info("{} 用户ID为空", independentObject);
                continue;
            }
            if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
                log.info("分账用户-{} 所得金额为空", independentObject);
                continue;
            }
            independentOrderBo.setIndependentObject(independentObject);
            independentOrderBo.setMerId(0L);
            independentOrderBo.setSiteId(taskSiteModel.getSiteId());
            independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
            independentOrderBoLists.add(independentOrderBo);
        }
        // 平台手续费百分比
//                    BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), PERCENTAGE);
//                    log.warn("平台手续费百分比：{}", platformDivide);
        // todo
        independentIdBo.setAdminId(2L);
        independentIdBo.setCountryId(countryId);
        independentIdBo.setCityId(cityId);
        // todo 代理id
//                    if (null != agencyId)
        // 分销人id
        if (busyOrderReceiveModel.getSharePerson() != null && busyOrderReceiveModel.getSharePerson() > 0L) {
            independentIdBo.setDistributorId(busyOrderReceiveModel.getSharePerson());
            // 分销人的拉新人id
            if (null != distributionInviteUserId && distributionInviteUserId != 0L) {
                independentIdBo.setDistributorInviteId(distributionInviteUserId);
            }
        }
        // 拉新人的上上级id
        if (regUserId != null && regUserId > 0L) {
            independentIdBo.setInviteId(regUserId);
        }
        // 接单人id
        independentIdBo.setReceiveId(busyOrderReceiveModel.getUserId());

        // 查询所有分账人的翼码帐户信息
        List<Long> allUserIds = getAllIds(independentIdBo);
        log.warn("allUserIds : {}", allUserIds);
        // 查询所有参与分账人员的翼码账户信息
        Map<Long, SohuAccountBankVo> accountBankVoMap = sohuAccountBankService.queryMapByUserIds(allUserIds);
        log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
        Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
        // 分账账号信息参数
        List<SplitList> splitLists = new ArrayList<>();
        // 累计的没有分出去的金额
        BigDecimal newPlatePrice = BigDecimal.ZERO;
        List<Long> yimaAccountExist = new ArrayList<>(List.of(2L));
        for (Long userId : independentOrderMap.keySet()) {
            SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
            // todo
            if (userId != 2L) {
                if (Objects.isNull(sohuAccountBankVo)) {
                    log.warn("翼码账户为空,{}", userId);
                    List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
                    newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                    continue;
                }
                List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
                if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
                    continue;
                }
                SplitList item = new SplitList();
                BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (CalUtils.isLessEqualZero(divAmt)) {
                    continue;
                }
                yimaAccountExist.add(userId);
                if (sohuIndependentOrderBos.size() > 1) {
                    log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
                }
                item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
                item.setMerchantId(sohuAccountBankVo.getMerchantId());
                splitLists.add(item);
            }
        }
        independentOrderBoLists = independentOrderBoLists.stream()
                .filter(item -> yimaAccountExist.contains(item.getUserId()))
                .collect(Collectors.toList());

        // 接单人金额 = 商单总价值 - 平台总手续费
        BigDecimal receivePrice = CalUtils.sub(busyPrice,
                calculateMap.get(SohuIndependentObject.platform.getKey()),
                calculateMap.get(SohuIndependentObject.invite.getKey()),
                calculateMap.get(SohuIndependentObject.country.getKey()),
                calculateMap.get(SohuIndependentObject.city.getKey()),
                calculateMap.get(SohuIndependentObject.agency.getKey()),
                calculateMap.get(SohuIndependentObject.distribution.getKey()),
                calculateMap.get(SohuIndependentObject.distributionInvite.getKey()));
        SohuIndependentOrderBo independentOrderBoReceive = new SohuIndependentOrderBo();
        independentOrderBoReceive.setOrderNo(tradeNo);
        independentOrderBoReceive.setTradeType(BusyType.BusyOrder.name());
        independentOrderBoReceive.setIndependentStatus(2);
        independentOrderBoReceive.setTradeNo(delayOrderNo);
        independentOrderBoReceive.setUserId(busyOrderReceiveModel.getUserId());
        independentOrderBoReceive.setIndependentObject(SohuIndependentObject.rece.getKey());
        independentOrderBoReceive.setMerId(0L);
        independentOrderBoReceive.setSiteId(taskSiteModel.getSiteId());
        independentOrderBoReceive.setIndependentPrice(receivePrice);
        //冗余字段
        independentOrderBoReceive.setTaskNumber(taskSiteModel.getTaskNumber());
        independentOrderBoReceive.setTaskFullAmount(payPrice);
        independentOrderBoReceive.setTaskTitle(taskSiteModel.getTitle());
        independentOrderBoReceive.setTaskUserName(independentIdBo.getTaskUserName());
        independentOrderBoLists.add(independentOrderBoReceive);
        // 接单人翼码账户信息
        SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(busyOrderReceiveModel.getUserId());
        // 接单人
        SplitList itemReceive = new SplitList();
        itemReceive.setDivAmt(BigDecimalUtils.yuanToFen(receivePrice).toString());
        itemReceive.setMerchantId(sohuAccountBankVo.getMerchantId());
        log.warn("itemReceive: {}", JsonUtils.toJsonString(itemReceive));
        splitLists.add(itemReceive);
        // 去重从新计算之后的传给第三方的分账信息
        List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId,
                Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
            SplitList newSplitList = new SplitList();
            newSplitList.setMerchantId(entry.getKey());
            newSplitList.setDivAmt(entry.getValue().toString());
            return newSplitList;
        }).collect(Collectors.toList());

        // 平台总金额
        BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);
//        // 平台最少不能低于1%的留存
//        BigDecimal minPrice = CalUtils.multiply(busyPrice, new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
//        // 给文化传媒的金额
//        BigDecimal cmPrice = CalUtils.sub(allPlatPrice, minPrice);
//        SplitList cmInfo = new SplitList();
//        cmInfo.setDivAmt(BigDecimalUtils.yuanToFen(cmPrice).toString());
//        cmInfo.setMerchantId("2290110");
//        splitListsNew.add(cmInfo);

        // 设置分账参数
        splitInfo.setSplitList(splitListsNew);
        // 平台总留存金额
        splitInfo.setKeepAmt(BigDecimalUtils.yuanToFen(allPlatPrice).toString());
        // 是否分账配置
        ExtendParams extendParams = new ExtendParams();
        // 分账信息
        extendParams.setSplitInfo(splitInfo);
        // 不分账 -- R实时分账 --D延时分账
        extendParams.setSplitFlag(ExtendParams.R);
        // 添加分账扩展参数
        unifiedorder.setExtendParams(extendParams);
        scanpay.setExtendParams(extendParams);
        // 设置请求参数
        unifiedorderRequest.setUnifiedorderRequest(unifiedorder);
        scanpayRequest.setScanpayRequest(scanpay);

        // todo 是否一次性支付商单酬金  一次性支付的话就直接完成任务 改掉所有阶段性状态为已完成
        if (payBo.getAllAmount()) {
            // SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(masterOrderNo, Boolean.TRUE);
            // 如果是一次性支付商单酬金 将支付单号+allAmount 12423432432allAmount 作为key存入
            RedisUtils.setCacheObject(tradeNo + "allAmount", Boolean.TRUE, Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        }

        // 保存待付款记录
        SohuBusyOrderSaveOrderPayReqBo saveOrderPayModel = new SohuBusyOrderSaveOrderPayReqBo()
                .setOrderId(taskSiteModel.getTaskNumber())
                .setOrderNo(tradeNo)
                .setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus())
                .setPayStatus(PayStatus.WaitPay.name())
                .setAmount(busyPrice)
                .setBusyType(PayObject.BusyOrderReceive.name())
                .setPayeeId(user.getUserId())
                .setUserId(busyOrderReceiveModel.getUserId())
                .setDeliveryId(payBo.getMasterId());
        remoteBusyOrderService.saveBusyOrderPay(saveOrderPayModel);
//        remoteBusyOrderService.saveBusyOrderPay(taskSiteModel.getTaskNumber(), tradeNo,
//                PayTypeEnum.PAY_TYPE_YI_MA.getStatus(),
//                PayStatus.WaitPay.name(), busyPrice,
//                PayObject.BusyOrderReceive.name(), user.getUserId(),
//                busyOrderReceiveModel.getUserId(), payBo.getMasterId());

        payBo.setAmount(independentPrice);
        // 保存主支付单
        saveMasterPayOrder(payBo, tradeNo, PayStatus.WaitPay.name());
        // 保存子支付单
        savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + tradeNo, tradeNo, PayStatus.WaitPay.name());

        // 保存分销单记录
        independentOrderBoLists.stream().filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject()))
                .findFirst().ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
        sohuIndependentOrderService.insertByBoList(independentOrderBoLists);
        List<SohuTradeRecordBo> tradeRecords = new ArrayList<>();
        for (SohuIndependentOrderBo orderBo : independentOrderBoLists) {
            SohuTradeRecordBo tradeRecordBo = SohuTradeRecordBo.builder().
                    userId(orderBo.getUserId()).
                    type(BusyTaskParty.getCode()).
                    consumeType(BusyTaskParty.getCode()).
                    consumeCode(masterOrderNo).
                    amount(orderBo.getIndependentPrice()).
                    amountType(SohuTradeRecordEnum.AmountType.InCome.getCode()).
                    payType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus()).
                    operateChannel(payBo.getPayChannel()).
                    payNumber(tradeNo).
                    payStatus(PayStatus.WaitPay.name()).
                    msg(taskSiteModel.getTitle() + StrPool.DASHED + BusyTaskParty.getMsg()).
                    independent(true).
                    independentObject(orderBo.getIndependentObject()).
                    independentStatus(orderBo.getIndependentStatus()).
                    accountType(SohuTradeRecordEnum.AccountType.Amount.name()).
                    unq(getUnq()).
                    build();
            tradeRecords.add(tradeRecordBo);
        }
        // 保存流水明细
        remoteMiddleTradeRecordService.insertBatch(tradeRecords);

        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 -PC- 发单方支付酬金请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
            ScanpayResponse response = Client.getClient().execute(scanpayRequest);
            log.info("翼码支付 -PC- 发单方支付酬金请求response返回：{}", JSONUtil.toJsonStr(response));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- 发单方支付酬金请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        log.info("翼码支付 -MOBILE- 发单方支付酬金请求response返回：{}", JSONUtil.toJsonStr(response));
        return JSONUtil.toJsonStr(response);
    }


//    public static void main(String[] args) {
//        SohuIndependentTemplateModel templateModel = new SohuIndependentTemplateModel();
//        templateModel.setPlatformRatio(new BigDecimal("95"));
//        templateModel.setAdminRatio(new BigDecimal("92"));
//        templateModel.setCountryRatio(new BigDecimal("40"));
//        templateModel.setCityRatio(new BigDecimal("40"));
//        templateModel.setAgencyRatio(new BigDecimal("20"));
//        templateModel.setDistributorRatio(new BigDecimal("80"));
//        templateModel.setDistributorInviteRatio(new BigDecimal("20"));
//        templateModel.setConsumerInviteRatio(new BigDecimal("3"));
//        Map<String, BigDecimal> stringBigDecimalMap = calculateBusyOrderDistributions(templateModel, new BigDecimal("100"), new BigDecimal("20"), true, false, null, false, false);
//        System.out.println(stringBigDecimalMap);
//    }
//
//    /**
//     * 商单接单分账算价
//     *
//     * @param templateModel         分账模板
//     * @param busyOrderReceivePrice 商单接单酬金（商单价值+佣金）  120
//     * @param independentPrice      商单分销佣金 20
//     * @param independentOrder      是否是分销单
//     * @param inviteUser            是否是拉新
//     * @param agencyId              是否有代理人
//     * @param independentInviteId   是否有分销人
//     * @param isIndependentAmount   是否是佣金支付
//     */
//    public static Map<String, BigDecimal> calculateBusyOrderDistributions(SohuIndependentTemplateModel templateModel,
//                                                                   BigDecimal busyOrderReceivePrice,
//                                                                   BigDecimal independentPrice,
//                                                                   Boolean independentOrder,
//                                                                   Boolean inviteUser,
//                                                                   Long independentInviteId,
//                                                                   Boolean agencyId,
//                                                                   Boolean isIndependentAmount) {
//        log.info("templateModel、independentPrice：{}、{}", JSONUtil.toJsonStr(templateModel), independentPrice);
//        // 分账对象
//        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
//        // 平台总分账金额 = 平台总手续费 * 平台分账比例
//        BigDecimal platPrice = null;
//        if (isIndependentAmount) {
//            if (CalUtils.isGreatZero(independentPrice)) {
//                if (independentOrder) {
//                    // 分销人佣金百分比
//                    BigDecimal distributorRatioPercentage = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
//                    BigDecimal distributorPrice = CalUtils.multiply(independentPrice, distributorRatioPercentage);
//                    log.warn("distributorPrice : {}", distributorPrice);
//                    // 分销人金额
//                    orderIndependentPrice.setDistributorPrice(distributorPrice);
//                    if (null != independentInviteId && independentInviteId > 0L) {
//                        // 分销人的拉新人金额
//                        orderIndependentPrice.setDistributorInvitePrice(CalUtils.sub(independentPrice, distributorPrice));
//                    } else {
//                        // 剩下的钱给平台
//                        platPrice = CalUtils.sub(independentPrice, distributorPrice);
//                    }
//                } else {
//                    // 没有分销人全部给平台
//                    platPrice = independentPrice;
//                }
//            }
//        } else {
//            // 平台手续费百分比-3.9%
//            BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
//            // 平台百分比
//            BigDecimal adminDivide = BigDecimalUtils.divide(templateModel.getAdminRatio(), CalUtils.PERCENTAGE);
//            // 消费者拉新人百分比
//            BigDecimal consumerDivide = BigDecimalUtils.divide(templateModel.getConsumerInviteRatio(), CalUtils.PERCENTAGE);
//            // 平台总手续费 = 商品价格 * 平台手续费比例 不计分拥金额
//            BigDecimal independentPlatformPrice = busyOrderReceivePrice.multiply(platformDivide).setScale(2, RoundingMode.HALF_UP);
//            log.warn("平台总手续费：{}", independentPlatformPrice);
//            BigDecimal consumerInvitePrice;
//            if (inviteUser) {
//                platPrice = independentPlatformPrice.multiply(adminDivide).setScale(2, RoundingMode.HALF_UP);
//                consumerInvitePrice = independentPlatformPrice.multiply(consumerDivide).setScale(2, RoundingMode.HALF_UP);
//            } else {
//                platPrice = independentPlatformPrice.multiply(CalUtils.add(adminDivide, consumerDivide)).setScale(2, RoundingMode.HALF_UP);
//                consumerInvitePrice = BigDecimal.ZERO;
//            }
//            log.warn("consumerInvitePrice : {}", consumerInvitePrice);
//            // 消费者拉新人分账金额 = 平台总分账金额 - 平台分账金额
//            orderIndependentPrice.setInvitePrice(consumerInvitePrice);
//            // 分红总金额 =  平台总手续费 - 平台分账金额 - 消费者拉新人分账金额
//            BigDecimal sharePrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice);
//            log.warn("sharePrice : {}", sharePrice);
//            // 国家站站长分账金额 = 分红总金额 * 国家站站长分账比例
//            BigDecimal countryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCountryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//            orderIndependentPrice.setCountryPrice(countryPrice);
//            log.warn("countryPrice : {}", countryPrice);
//            BigDecimal agencyPrice = BigDecimal.ZERO;
//            // 城市站站长分账金额 = 分红总金额 * 城市站站长分账比例
//            BigDecimal cityPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
//            log.warn("cityPrice : {}", cityPrice);
//            // 代理算价
//            if (agencyId && null != templateModel.getAgencyRatio() && !BigDecimal.ZERO.equals(BigDecimalUtils.divide(templateModel.getAgencyRatio(), CalUtils.PERCENTAGE))) {
//                // 代理分账金额 = 平台总手续费 -（平台分账金额+国家站长分账金额+城市站长分账金额）
//                agencyPrice = CalUtils.sub(independentPlatformPrice, platPrice, countryPrice);
//            } else {
//                // 剩下的钱
//                BigDecimal lastPrice = CalUtils.sub(sharePrice, countryPrice, cityPrice);
//                platPrice = CalUtils.add(platPrice, lastPrice);
//            }
//            log.warn("platPrice : {}", platPrice);
//            // 如果是分销订单并且有拉新人就计算拉新人的费用
//            // 扣除平台手续费后的分销佣金
////        BigDecimal distributionPrice = CalUtils.multiply(independentPrice, subtractPlatform);
//            // 分销人佣金百分比
//            BigDecimal distributorRatioPercentage = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
//            log.info("distributionPrice:{}", independentPrice);
//            if (CalUtils.isGreatZero(independentPrice)) {
//                if (independentOrder) {
//                    BigDecimal distributorPrice = CalUtils.multiply(independentPrice, distributorRatioPercentage);
//                    log.warn("distributorPrice : {}", distributorPrice);
//                    // 分销人金额
//                    orderIndependentPrice.setDistributorPrice(distributorPrice);
//                    if (null != independentInviteId && independentInviteId > 0L) {
//                        // 分销人的拉新人金额
//                        orderIndependentPrice.setDistributorInvitePrice(CalUtils.sub(independentPrice, distributorPrice));
//                    } else {
//                        // 剩下的钱
//                        BigDecimal lastPrice = CalUtils.sub(independentPrice, distributorPrice);
//                        // 剩下的钱给平台
//                        platPrice = CalUtils.add(platPrice, lastPrice);
//                    }
//                } else {
//                    // 没有分销人全部给平台
//                    platPrice = CalUtils.add(platPrice, independentPrice);
//                }
//            }
//            orderIndependentPrice.setCityPrice(cityPrice);
//            orderIndependentPrice.setAgencyPrice(agencyPrice);
//        }
//        // 平台分账金额
//        orderIndependentPrice.setAdminPrice(platPrice);
//        // 分销单状态
//        orderIndependentPrice.setIndependentOrder(independentOrder);
//        log.info("calculateBusyOrderDistribution 计算结果：{}", JSONUtil.toJsonStr(orderIndependentPrice));
//        // 组装分账信息
//        Map<String, BigDecimal> calculateMap = new HashMap<>();
//        addIfValid(calculateMap, SohuIndependentObject.platform.getKey(), orderIndependentPrice.getAdminPrice());
//        addIfValid(calculateMap, SohuIndependentObject.agency.getKey(), orderIndependentPrice.getAgencyPrice());
//        addIfValid(calculateMap, SohuIndependentObject.distributionInvite.getKey(), orderIndependentPrice.getDistributorInvitePrice());
//        addIfValid(calculateMap, SohuIndependentObject.country.getKey(), orderIndependentPrice.getCountryPrice());
//        addIfValid(calculateMap, SohuIndependentObject.city.getKey(), orderIndependentPrice.getCityPrice());
//        addIfValid(calculateMap, SohuIndependentObject.distribution.getKey(), orderIndependentPrice.getDistributorPrice());
//        addIfValid(calculateMap, SohuIndependentObject.invite.getKey(), orderIndependentPrice.getInvitePrice());
//        log.warn("calculateMap : {}", JsonUtils.toJsonString(calculateMap));
//        return calculateMap;
//    }

    /**
     * 商单接单分账算价
     *
     * @param templateModel         分账模板
     * @param busyOrderReceivePrice 商单接单酬金（商单价值）  100
     * @param independentPrice      商单分销佣金 20
     * @param independentOrder      是否是分销单
     * @param inviteUser            是否是拉新
     * @param agencyId              是否有代理人
     * @param independentInviteId   是否有分销人
     * @param isIndependentAmount   是否是佣金支付
     */
    private Map<String, BigDecimal> calculateBusyOrderDistribution(SohuIndependentTemplateModel templateModel,
                                                                   BigDecimal busyOrderReceivePrice,
                                                                   BigDecimal independentPrice,
                                                                   Boolean independentOrder,
                                                                   Boolean inviteUser,
                                                                   Long independentInviteId,
                                                                   Boolean agencyId,
                                                                   Boolean isIndependentAmount) {
        log.info("templateModel、independentPrice：{}、{}", JSONUtil.toJsonStr(templateModel), independentPrice);
        // 分账对象
        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
        // 平台总分账金额 = 平台总手续费 * 平台分账比例
        BigDecimal platPrice = null;
        if (isIndependentAmount) {
            if (CalUtils.isGreatZero(independentPrice)) {
                if (independentOrder) {
                    // 分销人佣金百分比
                    BigDecimal distributorRatioPercentage = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
                    BigDecimal distributorPrice = CalUtils.multiply(independentPrice, distributorRatioPercentage);
                    log.warn("distributorPrice : {}", distributorPrice);
                    // 分销人金额
                    orderIndependentPrice.setDistributorPrice(distributorPrice);
                    if (null != independentInviteId && independentInviteId > 0L) {
                        // 分销人的拉新人金额
                        orderIndependentPrice.setDistributorInvitePrice(CalUtils.sub(independentPrice, distributorPrice));
                    } else {
                        // 剩下的钱给平台
                        platPrice = CalUtils.sub(independentPrice, distributorPrice);
                    }
                } else {
                    // 没有分销人全部给平台
                    platPrice = independentPrice;
                }
            }
        } else {
            // 平台手续费百分比-3.9%
            BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
            // 平台百分比
            BigDecimal adminDivide = BigDecimalUtils.divide(templateModel.getAdminRatio(), CalUtils.PERCENTAGE);
            // 消费者拉新人百分比
            BigDecimal consumerDivide = BigDecimalUtils.divide(templateModel.getConsumerInviteRatio(), CalUtils.PERCENTAGE);
            // 平台总手续费 = 商品价格 * 平台手续费比例 不计分拥金额
            BigDecimal independentPlatformPrice = busyOrderReceivePrice.multiply(platformDivide).setScale(2, RoundingMode.HALF_UP);
            log.warn("平台总手续费：{}", independentPlatformPrice);
            BigDecimal consumerInvitePrice;
            if (inviteUser) {
                platPrice = independentPlatformPrice.multiply(adminDivide).setScale(2, RoundingMode.HALF_UP);
                consumerInvitePrice = independentPlatformPrice.multiply(consumerDivide).setScale(2, RoundingMode.HALF_UP);
            } else {
                platPrice = independentPlatformPrice.multiply(CalUtils.add(adminDivide, consumerDivide)).setScale(2, RoundingMode.HALF_UP);
                consumerInvitePrice = BigDecimal.ZERO;
            }
            log.warn("consumerInvitePrice : {}", consumerInvitePrice);
            // 消费者拉新人分账金额 = 平台总分账金额 - 平台分账金额
            orderIndependentPrice.setInvitePrice(consumerInvitePrice);
            // 分红总金额 =  平台总手续费 - 平台分账金额 - 消费者拉新人分账金额
            BigDecimal sharePrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice);
            log.warn("sharePrice : {}", sharePrice);
            // 国家站站长分账金额 = 分红总金额 * 国家站站长分账比例
            BigDecimal countryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCountryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            orderIndependentPrice.setCountryPrice(countryPrice);
            log.warn("countryPrice : {}", countryPrice);
            BigDecimal agencyPrice = BigDecimal.ZERO;
            // 城市站站长分账金额 = 分红总金额 * 城市站站长分账比例
            BigDecimal cityPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            log.warn("cityPrice : {}", cityPrice);
            // 代理算价
            if (agencyId && null != templateModel.getAgencyRatio() && !BigDecimal.ZERO.equals(BigDecimalUtils.divide(templateModel.getAgencyRatio(), CalUtils.PERCENTAGE))) {
                // 代理分账金额 = 平台总手续费 -（平台分账金额+国家站长分账金额+城市站长分账金额）
                agencyPrice = CalUtils.sub(independentPlatformPrice, platPrice, countryPrice);
            } else {
                // 剩下的钱
                BigDecimal lastPrice = CalUtils.sub(sharePrice, countryPrice, cityPrice);
                platPrice = CalUtils.add(platPrice, lastPrice);
            }
            log.warn("platPrice : {}", platPrice);
            // 如果是分销订单并且有拉新人就计算拉新人的费用
            // 扣除平台手续费后的分销佣金
//        BigDecimal distributionPrice = CalUtils.multiply(independentPrice, subtractPlatform);
            // 分销人佣金百分比
            BigDecimal distributorRatioPercentage = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
            log.info("distributionPrice:{}", independentPrice);
            if (CalUtils.isGreatZero(independentPrice)) {
                if (independentOrder) {
                    BigDecimal distributorPrice = CalUtils.multiply(independentPrice, distributorRatioPercentage);
                    log.warn("distributorPrice : {}", distributorPrice);
                    // 分销人金额
                    orderIndependentPrice.setDistributorPrice(distributorPrice);
                    if (null != independentInviteId && independentInviteId > 0L) {
                        // 分销人的拉新人金额
                        orderIndependentPrice.setDistributorInvitePrice(CalUtils.sub(independentPrice, distributorPrice));
                    } else {
                        // 剩下的钱
                        BigDecimal lastPrice = CalUtils.sub(independentPrice, distributorPrice);
                        // 剩下的钱给平台
                        platPrice = CalUtils.add(platPrice, lastPrice);
                    }
                } else {
                    // 没有分销人全部给平台
                    platPrice = CalUtils.add(platPrice, independentPrice);
                }
            }
            orderIndependentPrice.setCityPrice(cityPrice);
            orderIndependentPrice.setAgencyPrice(agencyPrice);
        }
        // 平台分账金额
        orderIndependentPrice.setAdminPrice(platPrice);
        // 分销单状态
        orderIndependentPrice.setIndependentOrder(independentOrder);
        log.info("calculateBusyOrderDistribution 计算结果：{}", JSONUtil.toJsonStr(orderIndependentPrice));
        // 组装分账信息
        Map<String, BigDecimal> calculateMap = new HashMap<>();
        addIfValid(calculateMap, SohuIndependentObject.platform.getKey(), orderIndependentPrice.getAdminPrice());
        addIfValid(calculateMap, SohuIndependentObject.agency.getKey(), orderIndependentPrice.getAgencyPrice());
        addIfValid(calculateMap, SohuIndependentObject.distributionInvite.getKey(), orderIndependentPrice.getDistributorInvitePrice());
        addIfValid(calculateMap, SohuIndependentObject.country.getKey(), orderIndependentPrice.getCountryPrice());
        addIfValid(calculateMap, SohuIndependentObject.city.getKey(), orderIndependentPrice.getCityPrice());
        addIfValid(calculateMap, SohuIndependentObject.distribution.getKey(), orderIndependentPrice.getDistributorPrice());
        addIfValid(calculateMap, SohuIndependentObject.invite.getKey(), orderIndependentPrice.getInvitePrice());
        log.warn("calculateMap : {}", JsonUtils.toJsonString(calculateMap));
        return calculateMap;
    }

    /**
     * 组装map校验
     *
     * @param map
     * @param key
     * @param value
     */
    private void addIfValid(Map<String, BigDecimal> map, String key, BigDecimal value) {
        if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
            map.put(key, value);
        }
    }

    /**
     * 根据对象获取id集合
     *
     * @param bo
     */
    public static List<Long> getAllIds(SohuIndependentIdPayBo bo) {
        return Stream.of(bo.getAdminId(), bo.getCountryId(), bo.getCityId(), bo.getDistributorId(), bo.getInviteId(),
                bo.getDistributorInviteId(), bo.getReceiveId()).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
