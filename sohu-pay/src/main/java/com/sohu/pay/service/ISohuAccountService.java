package com.sohu.pay.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuSiteUserBo;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.vo.SohuAccountVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 开户账号Service接口
 *
 * <AUTHOR>
 * @date 2023-10-09
 */
public interface ISohuAccountService {

    /**
     * 查询开户账号
     */
    SohuAccountVo queryById(Long id);

    /**
     * 根据用户id查询开户账号
     *
     * @param userId
     * @return
     */
    SohuAccountVo queryByUserId(Long userId);

    /**
     * 根据用户id查询开户账号(通过的)
     *
     * @param userId
     * @return
     */
    SohuAccountVo queryByUserIdOfPass(Long userId);

    /**
     * 根据用户id查询开户账号(通过的或审核中)
     *
     * @param userId
     * @return
     */
    SohuAccountVo queryByUserIdOfPassOrWaitApprove(Long userId);

    /**
     * 根据用户id查询开户账号(拒绝的)
     *
     * @param userId
     * @return
     */
    SohuAccountVo queryByUserIdOfRefuse(Long userId);

    /**
     * 根据用户id查询开户账号(加密过的)
     *
     * @param userId
     * @return
     */
    SohuAccountVo queryByUserIdOfEncrypt(Long userId);

    /**
     * 根据id查询开户账号(加密过的)
     *
     * @param id
     * @return
     */
    SohuAccountVo queryByIdOfEncrypt(Long id);

    /**
     * 查询开户账号列表
     */
    TableDataInfo<SohuAccountVo> queryPageList(SohuAccountBo bo, PageQuery pageQuery);

    /**
     * 查询开户账号列表
     */
    List<SohuAccountVo> queryList(SohuAccountBo bo);

    /**
     * 新增开户账号
     */
    Long insertByBo(SohuAccountBo bo);

    /**
     * 修改开户账号
     */
    Boolean update(SohuAccountBo bo);

    /**
     * 修改开户账号
     */
    Boolean updateByBo(SohuAccountBo bo);

    /**
     * 保存开户账号
     */
    Boolean save(SohuAccountBo bo);

    /**
     * 保存开户账号
     */
    Boolean saveByBo(SohuAccountBo bo);

    /**
     * 校验并批量删除开户账号信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

//    /**
//     * 获取用户开户信息
//     *
//     * @param userId
//     * @return
//     */
//    @Deprecated
//    SohuAccountVo userList(Long userId);

//    /**
//     * 根据userIds查询开户账号信息
//     *
//     * @param userIds
//     * @return
//     */
//    @Deprecated
//    List<SohuAccount> selectListByUserIds(Collection<Long> userIds);

    /**
     * 根据userIds查询开户账号信息(通过的)
     *
     * @param userIds
     * @return
     */
    List<SohuAccountVo> selectListByUserIdsOfPass(Collection<Long> userIds);

//    /**
//     * 根据userIds查询开户账号信息
//     *
//     * @param userIds
//     * @return
//     */
//    @Deprecated
//    Map<Long, SohuAccount> selectMapByUserIds(Collection<Long> userIds);

    /**
     * 根据userIds查询开户账号信息
     *
     * @param userIds
     * @return
     */
    Map<Long, SohuAccountVo> selectMapByUserIdsOfPass(Collection<Long> userIds);

    /**
     * 根据userIds查询开户账号信息
     *
     * @param userIds
     * @return
     */
    Map<Long, SohuAccountVo> selectAccountMapByUserIds(Collection<Long> userIds);

    /**
     * 审核
     *
     * @param bo
     * @return
     */
    Boolean audit(SohuAccountBo bo);

    /**
     * 审核-通过第三方
     *
     * @param bo
     * @return 返回错误信息。没有错误信息，则是成功
     */
    String auditByThirdParty(SohuAccountBo bo);

    /**
     * 通过手机号+验证码获取用户开户信息
     *
     * @param smsCode
     * @return
     */
    SohuAccountVo getInfoBySms(Long userId, String smsCode);

//    /**
//     * 通过userId获取用户信息
//     * @param userId
//     * @return
//     */
//    @Deprecated
//    SohuAccountVo queryByUserId(Long userId);

    /**
     * 根据站点id筛选用户
     *
     * @param sohuSiteUserBo
     * @return List<SohuAccountVo>
     */
    List<SohuAccountVo> getAccountBySiteId(SohuSiteUserBo sohuSiteUserBo);

//    /**
//     * 保存账户信息
//     *
//     * @param sohuAccount SohuAccount
//     */
//    @Deprecated
//    void saveAccount(SohuAccount sohuAccount);

//    /**
//     * 获取企业认证翼码id
//     *
//     * @param userId
//     */
//    @Deprecated
//    String getBusinessAccountByUserId(Long userId);

//    /**
//     * 根据userIds查询开户账号信息
//     */
//    @Deprecated
//    SohuAccountModel queryAccountByUserId(Long userId);

//    /**
//     * 根据userId获取用户开户信息
//     *
//     * @return SohuAccountVo
//     */
//    @Deprecated
//    SohuAccountVo getInfoByUserId();

//    /**
//     * 根据userId查询成功开户账号信息
//     *
//     * @param userId 用户id
//     * @return SohuAccountVo
//     */
//    @Deprecated
//    SohuAccountVo selectPassAccountByUserId(Long userId);

//    /**
//     * 根据用户id查询账户信息
//     *
//     * @param userId
//     * @return SohuAccountModel
//     */
//    @Deprecated
//    SohuAccountModel checkAccountTypeByUserId(Long userId);

    /**
     * 获取审核通过的账户信息
     *
     * @return SohuAccountModel
     */
    @Deprecated
    TableDataInfo<SohuAccountVo> selectPassAccountList(PageQuery pageQuery);

//    /**
//     * 实名认证前的校验
//     *
//     */
//    SohuAccountVo checkAccountByUserId(Long userId);


    /**
     * 获取审核通过的账户信息
     *
     * @return SohuAccountVo
     */
    @Deprecated
    TableDataInfo<SohuAccountVo> selectPassAccounts(PageQuery pageQuery);

    /**
     * 获取审核通过的账户信息
     *
     * @return SohuAccountModel
     */
    long selectPassAccountCount();

    /**
     * 跟新过期状态
     *
     * @return
     */
    Boolean updateStateOfTimeout();

    /**
     * 修改保证金状态
     *
     * @param id
     * @param bailState  保证金（Paid=已支付，Refunded=已退款）
     * @param bailSource 保证金来源（Online=线上支付，Offline=线下打款）
     * @return
     */
    Boolean updateBailState(Long id, String bailState, String bailSource);

    /**
     * 判断账号是否处于冻结中，冻结后发生的交易不参与分销
     *
     * @param userIds
     * @param freezeTime
     * @return
     */
    Map<Long, Boolean> afterFreezeMap(List<Long> userIds, Date freezeTime);

    /**
     * 根据用户手机号查询开户账号详细信息
     *
     * @param phone
     * @return
     */
    SohuAccountVo getInfoByUserPhone(String phone);

}
