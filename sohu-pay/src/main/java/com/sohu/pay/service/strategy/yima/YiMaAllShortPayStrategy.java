package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.middle.api.bo.SohuMaterialPromotionOrderCmdBo;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.enums.MaterialTradeTypeEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.vo.*;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.bo.SohuIndependentIdPayBo;
import com.sohu.pay.api.bo.SohuIndependentOrderBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.pay.domain.SohuIndependentOrder;
import com.sohu.pay.service.ISohuAccountBankService;
import com.sohu.pay.service.ISohuIndependentOrderService;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteUserService;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.model.SplitInfo;
import com.wangcaio2o.ipossa.sdk.model.SplitList;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import io.seata.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sohu.common.core.enums.SohuTradeRecordEnum.Type.Playlet;
import static com.sohu.common.core.enums.SohuTradeRecordEnum.Type.Video;

/**
 * 短剧全集付费
 */
@Slf4j
@Component
public class YiMaAllShortPayStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {
    @DubboReference
    private RemoteMiddlePlayletPayService remoteMiddlePlayletPayService;
    @DubboReference
    private RemoteMiddleVideoService remoteMiddleVideoService;
    @DubboReference
    private RemoteMiddlePlayletService remoteMiddlePlayletService;
    @DubboReference
    private RemoteIndependentTemplateService remoteIndependentTemplateService;
    @DubboReference
    private RemoteMiddleMaterialPromotionOrderService remoteMiddleMaterialPromotionOrderService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;

    @Resource
    private ISohuAccountBankService accountBankService;
    @Resource
    private ISohuIndependentOrderService sohuIndependentOrderService;
    @Resource
    private TransactionTemplate transactionTemplate;

    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 短剧全集付费支付 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - 短剧全集付费退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 短剧全集付费支付回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        // 第三方交易流水号
        String transactionId = response.getTradeNo();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        // 手续费
        String chargeAmount = response.getChargeAmount();
        // 分账流水修改
        sohuIndependentOrderService.updateIndependentStatus(outTradeNo, 1);
        // 素材推广流水修改
        remoteMiddleMaterialPromotionOrderService.updateByTradeNo(outTradeNo, transactionId, 1);
        Boolean execute = transactionTemplate.execute(e -> {
            List<SohuIndependentOrderVo> independentOrderVos = sohuIndependentOrderService.queryListByOrderNo(outTradeNo);
            if (CollectionUtils.isNotEmpty(independentOrderVos)) {
                // 分销方平坦手续费、平台不出钱
                distributeFeeOfShort(independentOrderVos, new BigDecimal(response.getTxAmt()), new BigDecimal(chargeAmount));
                independentOrderVos.forEach(orderVo -> orderVo.setIndependentStatus(1));
                List<SohuIndependentOrder> independentOrderList = BeanCopyUtils.copyList(independentOrderVos, SohuIndependentOrder.class);
                sohuIndependentOrderService.updateByBoList(independentOrderList);
            }
            remoteMiddlePlayletPayService.paySuccess(outTradeNo, transactionId);
            // 更新流水明细状态
            remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, transactionId, PayStatus.Paid, null);
            // 更新支付单
            updatePayOrder(outTradeNo, CalUtils.centToYuan(new BigDecimal(chargeAmount)), PayStatus.Paid.name(), transactionId);
            // 发送钱包通知
            sendWalletNotice(transactionId);
            return Boolean.TRUE;
        });

        return Boolean.TRUE.equals(execute) ? "success" : "fail";
    }

    /**
     * 全集解锁付费
     *
     * @return {@link String}
     */
    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        Long masterId = payBo.getMasterId();

        SohuVideoVo videoVo = remoteMiddleVideoService.selectVoById(masterId);
        // todo 通过短剧的episode_relevance字段值去表sohu_playlet查询短剧名title 做流水插入关联

        if (ObjectUtil.isNull(videoVo)) {
            throw new ServiceException("video does not exist");
        }
        if (!StrUtil.equalsAnyIgnoreCase(videoVo.getState(), CommonState.OnShelf.name())) {
            throw new ServiceException("Payment status is incorrect");
        }
        SohuTradeRecordVo existPaid = remoteMiddleTradeRecordService.queryOne(payBo.getUserId(), Video.getCode(),
                Video.getCode(), String.valueOf(masterId), PayStatus.Paid.name());
        if (Objects.nonNull(existPaid)) {
            throw new ServiceException("该视频已付过费!");
        }

        // todo 查询短剧主表
        SohuPlayletVo playletVo = remoteMiddlePlayletService.queryByEpisodeRelevance(videoVo.getEpisodeRelevance());
        Long count = remoteMiddlePlayletService.userNeedPayVideoCount(playletVo.getEpisodeRelevance(), payBo.getUserId());
        if (count == null || count <= 0L) {
            throw new ServiceException("playlet pay error");
        }
        masterId = playletVo.getId();
        payBo.setMasterId(playletVo.getId());

        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
                masterId + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + payBo.getUserId();
        if (StringUtils.isNotBlank(payBo.getPayChannel()) && payBo.getPayChannel().equals("mobile")) {
            boolean exists = RedisUtils.isExistsObject(key);
            // 如果存在直接唤醒
            if (exists) {
                log.info("全集付费订单存在，直接唤醒支付，订单号：{}，缓存值：{}", payBo.getMasterId(), RedisUtils.getCacheObject(key));
                return RedisUtils.getCacheObject(key);
            }
        }

        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        ScanpayRequest scanpayRequest = getScanpayRequest();
//        // 备注
//        unifiedorderRequest.setMemo("单个视频付费");
//        scanpayRequest.setMemo("单个视频付费");
        // 分账请求流水号
        String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
        //第三方支付单号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.PLAYLET_PAY_PREFIX);
        // 用户信息
        LoginUser user = remoteUserService.queryById(payBo.getUserId());

//        Map<Long, LoginUser> userMap = remoteUserService.selectMap(Collections.singletonList(1L));
//        LoginUser recipient = userMap.get(busyOrderReceiveModel.getUserId());
        // 备注
        unifiedorderRequest.setMemo("付费解锁全集-" + user.getNickname());
        scanpayRequest.setMemo("付费解锁全集-" + user.getNickname());
        // 分账信息
        SplitInfo splitInfo = new SplitInfo();
        // 先查询分拥等级
//        ISohuIndependentLevelConfigService levelConfigService = SpringUtils.getBean(ISohuIndependentLevelConfigService.class);
//        SohuIndependentLevelConfig levelConfig = levelConfigService.queryByStatusAndSiteId(0, 11L);
//        Integer level = ObjectUtils.isNull(levelConfig) ? levelConfig.getLevel() : 2;
        // 查询分账模板
        SohuIndependentTemplateModel templateModel;
        if (payBo.getIsPlatform() != null && payBo.getIsPlatform() && !payBo.getIsIndependent()) {
            templateModel = null;
        } else {
//            if (payBo.getIsPlatform() != null && payBo.getIsPlatform() && payBo.getIsIndependent()) {
//                // 根据城市站点、商品分账模板类型 查询分账模板
//                templateModel = remoteIndependentTemplateService.queryByIdAndType(null, 3);
//            } else {
//                // 根据城市站点、商品分账模板类型 查询分账模板
            templateModel = remoteIndependentTemplateService.queryByIdAndType(payBo.getSiteId(), 3);
//            }
            if (Objects.isNull(templateModel)) {
                throw new ServiceException("商单分账模板为空,请联系系统管理员");
            }
        }
        // 素材推广订单流水对象
        SohuMaterialPromotionOrderCmdBo materialPromotionOrderBo = new SohuMaterialPromotionOrderCmdBo();
        // 素材流水订单号
        String mpOrderNo = NumberUtil.getOrderNo(OrderConstants.MATERIAL_PAY_PREFIX);
//        // 查询接单人是否有拉新人-上上级拉新人
//        Long regUserId = remoteMiddleInviteService.selectByInviteCount(user.getUserId(), level);
//        Boolean regUser = Boolean.FALSE;
//        if (null != regUserId) {
//            regUser = Boolean.TRUE;
//        }
//        Boolean finalRegUser = regUser;
        // 组装id
        SohuIndependentIdPayBo independentIdBo = new SohuIndependentIdPayBo();
        // 分销单组装
        List<SohuIndependentOrderBo> independentOrderBoLists = new ArrayList<>();
        // todo 是否走付费分账
        //  平台版权 1、直接付费不走分佣 只给平台。2、通过分销素材库链接进来 分销给 平台、分销人
        //  版权方版权 1、直接付费不走分佣 只给平台+版权方。2、通过分销素材库链接进来 分销给 平台、版权方、分销人
        // 是否是分销单
        if (payBo.getIsIndependent()) {
            // 如果是平台版权
            if (payBo.getIsPlatform()) {
                independentIdBo.setAdminId(2L);
                // 分销人id
                independentIdBo.setDistributorId(payBo.getIndependentUserId());
                // 分账金额
                // todo 查这个素材库 先查询到分销金额
                BigDecimal independentPrice = payBo.getPayPrice().multiply(BigDecimalUtils.divide(playletVo.getDistributorRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
                // 分账方组装
                Map<String, BigDecimal> calculateMap;
//                // 分销单组装
//                List<SohuIndependentOrderBo> independentOrderBoLists = new ArrayList<>();
                // app
                unifiedorderRequest.setMemo("全集解锁-" + user.getNickname());
                unifiedorderRequest.setPosSeq(posSeq);
                // pc
                scanpayRequest.setMemo("全集解锁-" + user.getNickname());
                scanpayRequest.setPosSeq(posSeq);
                // app
                Unifiedorder unifiedorder = getUnifiedorder(payBo.getUserId());
                // pc
                Scanpay scanpay = getScanpay();
                // 总金额
                unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getPayPrice()));
                scanpay.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getPayPrice()));
                // 短剧全额解锁分账算价
                calculateMap = this.calculateBusyOrderDistribution(templateModel, payBo.getPayPrice(), independentPrice,
                        Boolean.TRUE, Boolean.TRUE);
                for (String independentObject : calculateMap.keySet()) {
                    // 分账金额
                    BigDecimal itemPrice = calculateMap.get(independentObject);
                    SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
                    independentOrderBo.setOrderNo(posSeq);
                    independentOrderBo.setTradeType(BusyType.Playlet.name());
                    independentOrderBo.setIndependentStatus(0);
                    independentOrderBo.setTradeNo(delayOrderNo);
                    //冗余字段
                    independentOrderBo.setTaskNumber(posSeq);
                    // 短剧名称
                    independentOrderBo.setTaskTitle(playletVo.getTitle());
                    independentOrderBo.setTaskUserName(independentIdBo.getTaskUserName());
                    // todo
                    if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
                        independentOrderBo.setUserId(2L);
                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
                        // todo 代理人id
                        independentOrderBo.setUserId(0L);
                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
                        independentOrderBo.setUserId(payBo.getIndependentUserId());
                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.entrance.getKey(), independentObject)) {
                        independentOrderBo.setSiteId(payBo.getSiteId());
                        SohuSiteVo siteVo = remoteMiddleSiteService.queryById(payBo.getSiteId());
                        Long entranceSiteId = Objects.nonNull(siteVo) ? siteVo.getStationmasterId() : null;
                        independentOrderBo.setUserId(entranceSiteId);
                        independentOrderBo.setIndependentObject(SohuIndependentObject.city.getKey());
                    }
                    if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
                        log.info("{} 用户ID为空", independentObject);
                        continue;
                    }
                    if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
                        log.info("分账用户-{} 所得金额为空", independentObject);
                        continue;
                    }
                    independentOrderBo.setIndependentObject(independentObject);
                    // 平台
                    independentOrderBo.setMerId(0L);
                    // 平台
                    if (!StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.entrance.getKey(), independentObject)) {
                        independentOrderBo.setSiteId(0L);
                    }
                    independentOrderBo.setIndependentPrice(itemPrice);
                    independentOrderBo.setIndependentTotalPrice(calculateMap.get(independentObject));
                    independentOrderBo.setTaskFullAmount(payBo.getPayPrice());
                    independentOrderBo.setConsumerUserId(payBo.getUserId());
                    independentOrderBoLists.add(independentOrderBo);
                }
                independentIdBo.setAdminId(2L);
                // 分销人id
                if (null != payBo.getIndependentUserId() && payBo.getIndependentUserId() > 0L) {
                    independentIdBo.setDistributorId(payBo.getIndependentUserId());
                }
                // 查询所有分账人的翼码帐户信息
                List<Long> allUserIds = getAllIds(independentIdBo);
                log.warn("allUserIds : {}", allUserIds);
                // 查询所有参与分账人员的翼码账户信息
                Map<Long, SohuAccountBankVo> accountBankVoMap = accountBankService.queryMapByUserIds(allUserIds);
                log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
                Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
                // 分账账号信息参数
                List<SplitList> splitLists = new ArrayList<>();
                // 累计的没有分出去的金额
//                BigDecimal newPlatePrice = BigDecimal.ZERO;
                // 累计要扣除后留平台的金额
                BigDecimal newPrice = BigDecimal.ZERO;
                List<Long> yimaAccountExist = new ArrayList<>(List.of(2L));
                for (Long userId : independentOrderMap.keySet()) {
                    SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
                    // todo
                    if (userId != 2L) {
                        if (Objects.isNull(sohuAccountBankVo)) {
                            log.warn("翼码账户为空,{}", userId);
//                            List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
//                            newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                            continue;
                        }
                        List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
                        if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
                            continue;
                        }
                        SplitList item = new SplitList();
                        BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (CalUtils.isLessEqualZero(divAmt)) {
                            continue;
                        }
                        yimaAccountExist.add(userId);
                        if (sohuIndependentOrderBos.size() > 1) {
                            log.warn("分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
                        }
                        newPrice = CalUtils.add(newPrice, divAmt);
                        item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
                        item.setMerchantId(sohuAccountBankVo.getMerchantId());
                        splitLists.add(item);
                    }
                }
                // 过滤掉没有分账的分账用户
                independentOrderBoLists = independentOrderBoLists.stream()
                        .filter(item -> yimaAccountExist.contains(item.getUserId()))
                        .collect(Collectors.toList());
                // 商家分账信息
                SohuIndependentOrderBo independentOrderShop = new SohuIndependentOrderBo();
                independentOrderShop.setOrderNo(posSeq);
                independentOrderShop.setTradeType(BusyType.Playlet.name());
                independentOrderShop.setIndependentStatus(0);
                independentOrderShop.setTradeNo(delayOrderNo);
                independentOrderShop.setUserId(2L);
                independentOrderShop.setSiteId(payBo.getSiteId());
                independentOrderShop.setIndependentObject(SohuIndependentObject.copyright.getKey());
                independentOrderShop.setIndependentPrice(CalUtils.sub(payBo.getPayPrice(), calculateMap.get(SohuIndependentObject.platform.getKey()), calculateMap.get(SohuIndependentObject.distribution.getKey())));
                independentOrderShop.setIndependentTotalPrice(CalUtils.sub(payBo.getPayPrice(), calculateMap.get(SohuIndependentObject.platform.getKey()), calculateMap.get(SohuIndependentObject.distribution.getKey())));
                independentOrderShop.setMerId(playletVo.getUserId());
                independentOrderShop.setTaskTitle(playletVo.getTitle());
                independentOrderShop.setTaskFullAmount(payBo.getPayPrice());
                independentOrderShop.setConsumerUserId(payBo.getUserId());
                log.warn("商家分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderShop));
                independentOrderBoLists.add(independentOrderShop);
                // 去重从新计算之后的传给第三方的分账信息
                List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId,
                        Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
                    SplitList newSplitList = new SplitList();
                    newSplitList.setMerchantId(entry.getKey());
                    newSplitList.setDivAmt(entry.getValue().toString());
                    return newSplitList;
                }).collect(Collectors.toList());
                log.info("短剧全额解锁分账算价-splitListsNew:{}", JsonUtils.toJsonString(splitListsNew));
                // 平台总金额:平台分账金额+累计没有被分出去的金额
                BigDecimal allPlatPrice = CalUtils.sub(payBo.getPayPrice(), newPrice);
                // 设置分账参数
                splitInfo.setSplitList(splitListsNew);
                // 平台总留存金额
                splitInfo.setKeepAmt(Objects.requireNonNull(BigDecimalUtils.yuanToFen(allPlatPrice)).toString());
//                    splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
                // 分账信息
                // 是否分账配置
                ExtendParams extendParams = new ExtendParams();
                extendParams.setSplitInfo(splitInfo);
                // 不分账 -- R实时分账 --D延时分账
                extendParams.setSplitFlag(ExtendParams.R);
                // 添加分账扩展参数
                unifiedorder.setExtendParams(extendParams);
                scanpay.setExtendParams(extendParams);
                // 设置请求参数
                unifiedorderRequest.setUnifiedorderRequest(unifiedorder);
                scanpayRequest.setScanpayRequest(scanpay);
                // 组装素材推广流水单
                extractedMPBo(payBo, posSeq, user, materialPromotionOrderBo, mpOrderNo, calculateMap);
            } else {
                // 版权方版权
                independentIdBo.setAdminId(2L);
//            independentIdBo.setCountryId(countryId);
//            independentIdBo.setCityId(cityId);
                // 分销人id
                if (null != payBo.getIndependentUserId() && payBo.getIndependentUserId() > 0L) {
                    independentIdBo.setDistributorId(payBo.getIndependentUserId());
                }
                // 版权方id即商户id
                independentIdBo.setMerUserId(playletVo.getUserId());
                // 分账金额
                // todo 查这个素材库 先查询到分销金额
                BigDecimal independentPrice = payBo.getPayPrice().multiply(BigDecimalUtils.divide(playletVo.getDistributorRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
                // 分账方组装
                Map<String, BigDecimal> calculateMap;
                // app
                unifiedorderRequest.setMemo("全集解锁-" + user.getNickname());
                unifiedorderRequest.setPosSeq(posSeq);
                // pc
                scanpayRequest.setMemo("全集解锁-" + user.getNickname());
                scanpayRequest.setPosSeq(posSeq);
                // app
                Unifiedorder unifiedorder = getUnifiedorder(payBo.getUserId());
                // pc
                Scanpay scanpay = getScanpay();
                // 总金额
                unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getPayPrice()));
                scanpay.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getPayPrice()));
                // 总金额
//                unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(independentPrice));
//                scanpay.setTxAmt(BigDecimalUtils.yuanToFen(independentPrice));
                // 短剧全额解锁分账算价
                calculateMap = this.calculateBusyOrderDistribution(templateModel, payBo.getPayPrice(), independentPrice,
                        Boolean.TRUE, Boolean.TRUE);
                for (String independentObject : calculateMap.keySet()) {
                    // 分账金额
                    BigDecimal itemPrice = calculateMap.get(independentObject);
                    SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
                    independentOrderBo.setOrderNo(posSeq);
                    independentOrderBo.setTradeType(BusyType.Playlet.name());
                    independentOrderBo.setIndependentStatus(0);
                    independentOrderBo.setTradeNo(delayOrderNo);
                    //冗余字段
                    independentOrderBo.setTaskNumber(posSeq);
                    // 短剧名称
                    independentOrderBo.setTaskTitle(playletVo.getTitle());
                    independentOrderBo.setTaskUserName(independentIdBo.getTaskUserName());
                    // todo
                    if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
                        independentOrderBo.setUserId(2L);
                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.agency.getKey(), independentObject)) {
                        // todo 代理人id
                        independentOrderBo.setUserId(0L);
                    } else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.distribution.getKey(), independentObject)) {
                        independentOrderBo.setUserId(payBo.getIndependentUserId());
                    }else if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.entrance.getKey(), independentObject)) {
                        independentOrderBo.setSiteId(payBo.getSiteId());
                        SohuSiteVo siteVo = remoteMiddleSiteService.queryById(payBo.getSiteId());
                        Long entranceSiteId = Objects.nonNull(siteVo) ? siteVo.getStationmasterId() : null;
                        independentOrderBo.setUserId(entranceSiteId);
                        independentOrderBo.setIndependentObject(SohuIndependentObject.city.getKey());
                    }
                    if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
                        log.info("{} 用户ID为空", independentObject);
                        continue;
                    }
                    if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
                        log.info("分账用户-{} 所得金额为空", independentObject);
                        continue;
                    }
                    independentOrderBo.setIndependentObject(independentObject);
                    if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
                        // 平台
                        independentOrderBo.setMerId(0L);
                    } else {
                        independentOrderBo.setMerId(playletVo.getUserId());
                    }
                    // 平台
                    independentOrderBo.setSiteId(payBo.getSiteId());
                    independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
                    independentOrderBo.setIndependentTotalPrice(calculateMap.get(independentObject));
                    independentOrderBo.setTaskFullAmount(payBo.getPayPrice());
                    independentOrderBo.setConsumerUserId(payBo.getUserId());
                    independentOrderBoLists.add(independentOrderBo);
                }
                // 商家分账信息
                SohuIndependentOrderBo independentOrderShop = new SohuIndependentOrderBo();
                independentOrderShop.setOrderNo(posSeq);
                independentOrderShop.setTradeType(BusyType.Playlet.name());
                independentOrderShop.setIndependentStatus(0);
                independentOrderShop.setTradeNo(delayOrderNo);
                independentOrderShop.setUserId(playletVo.getUserId());
                independentOrderShop.setSiteId(payBo.getSiteId());
                independentOrderShop.setIndependentObject(SohuIndependentObject.copyright.getKey());
                independentOrderShop.setIndependentPrice(CalUtils.sub(payBo.getPayPrice(), independentPrice));
                independentOrderShop.setMerId(playletVo.getUserId());
                independentOrderShop.setTaskTitle(playletVo.getTitle());
                independentOrderShop.setTaskFullAmount(payBo.getPayPrice());
                independentOrderShop.setConsumerUserId(payBo.getUserId());
                independentOrderBoLists.add(independentOrderShop);
                log.warn("商家分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderShop));

//                // 平台手续费百分比
//                BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
//                log.warn("平台手续费百分比：{}", platformDivide);
//                independentIdBo.setAdminId(2L);

                // 查询所有分账人的翼码帐户信息
                List<Long> allUserIds = getAllIds(independentIdBo);
                log.warn("allUserIds : {}", allUserIds);
                // 查询所有参与分账人员的翼码账户信息
                Map<Long, SohuAccountBankVo> accountBankVoMap = accountBankService.queryMapByUserIds(allUserIds);
                log.warn("accountBankVoMap : {}", JSONUtil.toJsonStr(accountBankVoMap));
                Map<Long, List<SohuIndependentOrderBo>> independentOrderMap = independentOrderBoLists.stream().collect(Collectors.groupingBy(SohuIndependentOrderBo::getUserId));
                // 分账账号信息参数
                List<SplitList> splitLists = new ArrayList<>();
                // todo 还需要累计？
                // 累计的没有分出去的金额
                BigDecimal newPlatePrice = BigDecimal.ZERO;
                List<Long> yimaAccountExist = new ArrayList<>(List.of(2L));
                for (Long userId : independentOrderMap.keySet()) {
                    SohuAccountBankVo sohuAccountBankVo = accountBankVoMap.get(userId);
                    // todo
                    if (userId != 2L) {
                        if (Objects.isNull(sohuAccountBankVo)) {
                            log.warn("翼码账户为空,{}", userId);
                            List<SohuIndependentOrderBo> independentOrderBoList = independentOrderMap.get(userId);
                            newPlatePrice = CalUtils.add(newPlatePrice, independentOrderBoList.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                            continue;
                        }
                        List<SohuIndependentOrderBo> sohuIndependentOrderBos = independentOrderMap.get(userId);
                        if (CollUtil.isEmpty(sohuIndependentOrderBos)) {
                            continue;
                        }
                        SplitList item = new SplitList();
                        BigDecimal divAmt = sohuIndependentOrderBos.stream().map(SohuIndependentOrderBo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (CalUtils.isLessEqualZero(divAmt)) {
                            continue;
                        }
                        yimaAccountExist.add(userId);
                        if (sohuIndependentOrderBos.size() > 1) {
                            log.warn("商单接单分账有分账用户ID相同,{}", JSONUtil.toJsonStr(sohuIndependentOrderBos));
                        }
                        item.setDivAmt(BigDecimalUtils.yuanToFen(divAmt).toString());
                        item.setMerchantId(sohuAccountBankVo.getMerchantId());
                        splitLists.add(item);
                    }
                }

                // 过滤掉没有分账的分账用户
                independentOrderBoLists = independentOrderBoLists.stream()
                        .filter(item -> yimaAccountExist.contains(item.getUserId()))
                        .collect(Collectors.toList());
                // 去重从新计算之后的传给第三方的分账信息
                List<SplitList> splitListsNew = splitLists.stream().collect(Collectors.groupingBy(SplitList::getMerchantId,
                        Collectors.reducing(BigDecimal.ZERO, s -> new BigDecimal(s.getDivAmt()), BigDecimal::add))).entrySet().stream().map(entry -> {
                    SplitList newSplitList = new SplitList();
                    newSplitList.setMerchantId(entry.getKey());
                    newSplitList.setDivAmt(entry.getValue().toString());
                    return newSplitList;
                }).collect(Collectors.toList());
                // todo 还需要累计？
                // 平台总金额
                BigDecimal allPlatPrice = CalUtils.add(calculateMap.get(SohuIndependentObject.platform.getKey()), newPlatePrice);
                // 设置分账参数
                splitInfo.setSplitList(splitListsNew);
                // 平台总留存金额
                splitInfo.setKeepAmt(Objects.requireNonNull(BigDecimalUtils.yuanToFen(allPlatPrice)).toString());
//                    splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
                // 分账信息
                // 是否分账配置
                ExtendParams extendParams = new ExtendParams();
                extendParams.setSplitInfo(splitInfo);
                // 不分账 -- R实时分账 --D延时分账
                extendParams.setSplitFlag(ExtendParams.R);
                // 添加分账扩展参数
                unifiedorder.setExtendParams(extendParams);
                scanpay.setExtendParams(extendParams);
                // 设置请求参数
                unifiedorderRequest.setUnifiedorderRequest(unifiedorder);
                scanpayRequest.setScanpayRequest(scanpay);
                // 组装素材推广流水单
                extractedMPBo(payBo, posSeq, user, materialPromotionOrderBo, mpOrderNo, calculateMap);
            }
        } else {
            // 如果是平台版权
            if (payBo.getIsPlatform()) {
                // 直接不分账N
                independentIdBo.setAdminId(2L);

                // 唯一订单号
                unifiedorderRequest.setPosSeq(posSeq);
                scanpayRequest.setPosSeq(posSeq);
                Unifiedorder unifiedOrder = getUnifiedorder(payBo.getUserId());
                Scanpay scanpay = getScanpay();
                // todo 总金额由前端给
                // 总金额
                unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getPayPrice().abs()));
                scanpay.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getPayPrice().abs()));
                // 不分账 -- R实时分账 --D延时分账
                ExtendParams extendParams = new ExtendParams();

                extendParams.setSplitFlag(ExtendParams.N);

                unifiedOrder.setExtendParams(extendParams);
                // 设置请求参数
                unifiedorderRequest.setUnifiedorderRequest(unifiedOrder);
                scanpayRequest.setScanpayRequest(scanpay);
                // todo 回调的时候需要把手续费算上 总金额、手续费\、实际金额
                // 保存流水记录-购买者
                /*saveAmountTradeRecord(payBo, posSeq, Playlet.getCode(), Playlet.getCode(),
                        Objects.requireNonNull(masterId).toString(), Playlet.getMsg(), PayStatus.WaitPay.name(),
                        SohuTradeRecordEnum.AmountType.Expend.getCode(), 3, 1, 1);*/
                return getShortPayString(payBo, key, unifiedorderRequest, scanpayRequest, posSeq);

            } else {
                // 版权方版权短剧
                // 分账给平台+商家（版权方）R
                independentIdBo.setAdminId(2L);
                // 版权方id即商户id
                independentIdBo.setMerUserId(playletVo.getUserId());
                // 分账方组装
                Map<String, BigDecimal> calculateMap;
                // app
                unifiedorderRequest.setMemo("支付商单接佣金-" + user.getNickname());
                unifiedorderRequest.setPosSeq(posSeq);
                // pc
                scanpayRequest.setMemo("支付商单接佣金-" + user.getNickname());
                scanpayRequest.setPosSeq(posSeq);
                // app
                Unifiedorder unifiedorder = getUnifiedorder(payBo.getUserId());
                // pc
                Scanpay scanpay = getScanpay();
                // 总金额
                unifiedorder.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getPayPrice()));
                scanpay.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getPayPrice()));
                // 计算平台+版权方要留的金额
                // todo 查这个素材库 先查询到分销金额
                // 短剧全额解锁分账算价
                calculateMap = this.calculateDistribution(templateModel, payBo.getPayPrice());
                // 分账对象
                for (String independentObject : calculateMap.keySet()) {
                    // 分账金额
                    BigDecimal itemPrice = calculateMap.get(independentObject);
                    SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
                    independentOrderBo.setOrderNo(posSeq);
                    independentOrderBo.setTradeType(BusyType.Playlet.name());
                    independentOrderBo.setIndependentStatus(0);
                    independentOrderBo.setTradeNo(delayOrderNo);
                    //冗余字段
                    independentOrderBo.setTaskNumber(posSeq);
                    // 短剧名称
                    independentOrderBo.setTaskTitle(playletVo.getTitle());
                    independentOrderBo.setTaskUserName(independentIdBo.getTaskUserName());
                    // todo
                    if (StrUtil.equalsAnyIgnoreCase(SohuIndependentObject.platform.getKey(), independentObject)) {
                        independentOrderBo.setUserId(2L);
                    }
                    if (independentOrderBo.getUserId() == null || independentOrderBo.getUserId() <= 0L) {
                        log.info("{} 用户ID为空", independentObject);
                        continue;
                    }
                    if (itemPrice == null || CalUtils.isLessEqualZero(itemPrice)) {
                        log.info("分账用户-{} 所得金额为空", independentObject);
                        continue;
                    }
                    independentOrderBo.setIndependentObject(independentObject);
                    // 平台
                    independentOrderBo.setMerId(0L);
                    independentOrderBo.setSiteId(playletVo.getSiteId());
                    independentOrderBo.setIndependentPrice(calculateMap.get(independentObject));
                    independentOrderBo.setIndependentTotalPrice(calculateMap.get(independentObject));
                    independentOrderBo.setTaskFullAmount(payBo.getPayPrice());
                    independentOrderBo.setConsumerUserId(payBo.getUserId());
                    independentOrderBoLists.add(independentOrderBo);
                }
                // 分账账号信息参数
                List<SplitList> splitLists = new ArrayList<>();
                SplitList splitList = new SplitList();
                SohuAccountBankVo accountBankVo = accountBankService.queryByUserId(playletVo.getUserId());
                if (ObjectUtil.isNotNull(accountBankVo)) {

                    // 每笔子单商户应该获得的钱
                    BigDecimal shopPrice = CalUtils.sub(payBo.getPayPrice(), calculateMap.get(SohuIndependentObject.platform.getKey()));

                    // 商家分账信息
                    SohuIndependentOrderBo independentOrderShop = new SohuIndependentOrderBo();
                    independentOrderShop.setOrderNo(posSeq);
                    independentOrderShop.setTradeType(BusyType.Goods.name());
                    independentOrderShop.setIndependentStatus(0);
                    independentOrderShop.setTradeNo(delayOrderNo);
                    independentOrderShop.setUserId(playletVo.getUserId());
                    independentOrderShop.setSiteId(playletVo.getSiteId());
                    independentOrderShop.setIndependentObject(SohuIndependentObject.copyright.getKey());
                    independentOrderShop.setIndependentPrice(shopPrice);
                    independentOrderShop.setIndependentTotalPrice(shopPrice);
                    independentOrderShop.setMerId(playletVo.getUserId());
                    independentOrderShop.setTaskTitle(playletVo.getTitle());
                    independentOrderShop.setTaskFullAmount(payBo.getPayPrice());
                    independentOrderShop.setConsumerUserId(payBo.getUserId());
                    independentOrderBoLists.add(independentOrderShop);
                    log.warn("商家分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderShop));

                    splitList.setDivAmt(Objects.requireNonNull(BigDecimalUtils.yuanToFen(shopPrice)).toString());
                    splitList.setMerchantId(accountBankVo.getMerchantId());
                    splitLists.add(splitList);
                    // 设置分账参数
                    splitInfo.setSplitList(splitLists);
                    // 平台总留存金额
                    splitInfo.setKeepAmt(Objects.requireNonNull(BigDecimalUtils.yuanToFen(calculateMap.get(SohuIndependentObject.platform.getKey()))).toString());
//                    splitInfo.setKeepAmt(String.valueOf(CalUtils.yuanToCent(allPlatPrice).intValue()));
                    // 分账信息
                    // 是否分账配置
                    ExtendParams extendParams = new ExtendParams();
                    extendParams.setSplitInfo(splitInfo);
                    // 不分账 -- R实时分账 --D延时分账
                    extendParams.setSplitFlag(ExtendParams.R);
                    // 添加分账扩展参数
                    unifiedorder.setExtendParams(extendParams);
                    scanpay.setExtendParams(extendParams);
                    // 设置请求参数
                    unifiedorderRequest.setUnifiedorderRequest(unifiedorder);
                    scanpayRequest.setScanpayRequest(scanpay);
                } else {
                    // 分账信息
                    // 是否分账配置
                    ExtendParams extendParams = new ExtendParams();
                    extendParams.setSplitInfo(splitInfo);
                    // 不分账 -- R实时分账 --D延时分账
                    extendParams.setSplitFlag(ExtendParams.N);
                    // 添加分账扩展参数
                    unifiedorder.setExtendParams(extendParams);
                    scanpay.setExtendParams(extendParams);
                    // 设置请求参数
                    unifiedorderRequest.setUnifiedorderRequest(unifiedorder);
                    scanpayRequest.setScanpayRequest(scanpay);
                    return getShortPayString(payBo, key, unifiedorderRequest, scanpayRequest, posSeq);
                }
            }
        }
//        // 保存分销单记录
//        independentOrderBoLists.stream().filter(orderBo -> SohuIndependentObject.platform.getKey().equals(orderBo.getIndependentObject()))
//                .findFirst().ifPresent(orderBo -> orderBo.setIndependentPrice(allPlatPrice));
        // 保存分账流水
        if (CollectionUtils.isNotEmpty(independentOrderBoLists)) {
            List<SohuIndependentOrderBo> filteredList = independentOrderBoLists.stream()
                    .filter(orderBo -> orderBo.getIndependentPrice() != null &&
                            orderBo.getIndependentPrice().compareTo(BigDecimal.ZERO) != 0)
                    .collect(Collectors.toList());
            sohuIndependentOrderService.insertByBoList(filteredList);
        }
        List<SohuTradeRecordBo> tradeRecords = new ArrayList<>();
        for (SohuIndependentOrderBo orderBo : independentOrderBoLists) {
            SohuTradeRecordBo tradeRecordBo = SohuTradeRecordBo.builder().
                    userId(orderBo.getUserId()).
                    type(Playlet.getCode()).
                    consumeType(Playlet.getCode()).
                    consumeCode(Objects.requireNonNull(playletVo.getId().toString())).
                    amount(orderBo.getIndependentPrice()).
                    amountType(SohuTradeRecordEnum.AmountType.InCome.getCode()).
                    payType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus()).
                    operateChannel(payBo.getPayChannel()).
                    payNumber(posSeq).
                    payStatus(PayStatus.WaitPay.name()).
                    msg(playletVo.getTitle() + StrPool.DASHED + Video.getMsg()).
                    independent(true).
                    independentObject(orderBo.getIndependentObject()).
                    independentStatus(orderBo.getIndependentStatus()).
                    accountType(SohuTradeRecordEnum.AccountType.Amount.name()).
                    templateType(3).
                    userType(1).
                    sourceType(1).
                    unq(getUnq()).
                    sysSource(payBo.getSysSource()).
                    build();
            if (!payBo.getIsPlatform()) {
                tradeRecordBo.setTemplateType(4);
            }
            if (orderBo.getIndependentObject().equals(SohuIndependentObject.copyright.getKey())) {
                tradeRecordBo.setMsg(playletVo.getTitle() + StrPool.DASHED + "-" + SohuIndependentObject.copyright.getMsg() + "收益");
            }
            tradeRecords.add(tradeRecordBo);
        }
        // 保存流水明细
        remoteMiddleTradeRecordService.insertBatch(tradeRecords);
        if (payBo.getIsIndependent()) {
            // 素材推广流水添加记录
            remoteMiddleMaterialPromotionOrderService.save(materialPromotionOrderBo);
        }
        // 记录单个视频付费单
//        sohuPlayletPayService.saveVideoPay(payBo.getUserId(), masterId,
//                playletVo.getSinglePrice().abs(), posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name());
//        remoteMiddlePlayletPayService.saveVideoPay(payBo.getUserId(), masterId,
//                playletVo.getSinglePrice().abs(), posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.WaitPay.name(), playletVo.getCategoryId(), playletVo.getTitle());
        remoteMiddlePlayletPayService.savePlayletPay(payBo.getUserId(), payBo.getMasterId(), count, payBo.getPayPrice(),
                posSeq, PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), playletVo.getCategoryId(), playletVo.getTitle());
        payBo.setAmount(payBo.getPayPrice().abs());
        // todo 回调的时候需要把手续费算上 总金额、手续费\、实际金额

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = buildRecord(payBo);
        recordBo.type(SohuTradeRecordEnum.Type.Playlet.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.Playlet.getCode());
        recordBo.consumeCode(String.valueOf(playletVo.getId()));
        recordBo.msg(SohuTradeRecordEnum.Type.Playlet.getMsg());
        recordBo.amountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordBo.payNumber(posSeq);
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        recordBo.templateType(3);
        recordBo.userType(1);
        recordBo.sourceType(1);
        // 保存流水记录 -购买者- 钱 - 支出
        saveTradeRecord(recordBo.build());


        // todo 保存分账单流水 添加：总金额、手续费、实际金额
        // 保存主支付单
        saveMasterPayOrder(payBo, posSeq, PayStatus.WaitPay.name());
        // 保存子支付单
        savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + posSeq, posSeq, PayStatus.WaitPay.name());

        //同步收入统计
        MqMessaging mqCancelMessaging = new MqMessaging(posSeq, MqKeyEnum.INCOME_INFO.getKey());
        remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);

        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-短剧全集付费支付请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
            ScanpayResponse response = Client.getClient().execute(scanpayRequest);
            log.info("翼码支付 - PC-短剧全集付费支付请求response返回：{}", JSONUtil.toJsonStr(response));
//            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- 短剧全集付费支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        log.info("翼码支付 -MOBILE- 短剧全集付费支付请求response返回：{}", JSONUtil.toJsonStr(response));
        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);

    }

    /**
     * 不分账时只记录流水不记录平台分销所得金额
     *
     * @param payBo
     * @param key
     * @param unifiedorderRequest
     * @param scanpayRequest
     * @param posSeq
     * @return
     */
    private String getShortPayString(SohuPrePayBo payBo, String key, UnifiedorderRequest unifiedorderRequest, ScanpayRequest scanpayRequest, String posSeq) {
        payBo.setAmount(payBo.getPayPrice().abs());

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = buildRecord(payBo);
        recordBo.type(SohuTradeRecordEnum.Type.Playlet.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.Playlet.getCode());
        recordBo.consumeCode(String.valueOf(payBo.getMasterId()));
        recordBo.msg(SohuTradeRecordEnum.Type.Playlet.getMsg());
        recordBo.amountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordBo.payNumber(posSeq);
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        recordBo.templateType(3);
        recordBo.userType(1);
        recordBo.sourceType(1);
        // 保存流水记录 - 钱 - 支出
        saveTradeRecord(recordBo.build());
        // 平台流水-不分账时记录
        Long userId = payBo.getUserId();
        payBo.setUserId(2L);
        SohuTradeRecordBo.SohuTradeRecordBoBuilder platRecordBo = buildRecord(payBo);
        payBo.setUserId(userId);
        platRecordBo.independentObject(SohuIndependentObject.platform.getKey());
        platRecordBo.type(SohuTradeRecordEnum.Type.Playlet.getCode());
        platRecordBo.consumeType(SohuTradeRecordEnum.Type.Playlet.getCode());
        platRecordBo.consumeCode(String.valueOf(payBo.getMasterId()));
        platRecordBo.msg(SohuTradeRecordEnum.Type.Playlet.getMsg());
        platRecordBo.amountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
        platRecordBo.payNumber(posSeq);
        platRecordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        platRecordBo.templateType(3);
        platRecordBo.independent(Boolean.TRUE);
        platRecordBo.userType(2);
        platRecordBo.sourceType(1);
        // 保存流水记录 -平台- 钱 - 收入
        saveTradeRecord(platRecordBo.build());

        // todo 保存分账单流水 添加：总金额、手续费、实际金额
        // 保存主支付单
        saveMasterPayOrder(payBo, posSeq, PayStatus.WaitPay.name());
        // 保存子支付单
        savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + posSeq, posSeq, PayStatus.WaitPay.name());

        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-短剧全集付费支付请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
            ScanpayResponse response = Client.getClient().execute(scanpayRequest);
            log.info("翼码支付 - PC-短剧全集付费支付请求response返回：{}", JSONUtil.toJsonStr(response));
            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- 短剧全集付费支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        log.info("翼码支付 -MOBILE- 短剧全集付费支付请求response返回：{}", JSONUtil.toJsonStr(response));
        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);
    }

    /**
     * 组装素材推广流水单
     *
     * @param payBo
     * @param posSeq
     * @param user
     * @param materialPromotionOrderBo
     * @param mpOrderNo
     * @param calculateMap
     */
    private static void extractedMPBo(SohuPrePayBo payBo, String posSeq, LoginUser user, SohuMaterialPromotionOrderCmdBo materialPromotionOrderBo, String mpOrderNo, Map<String, BigDecimal> calculateMap) {
        materialPromotionOrderBo.setShareUserId(payBo.getIndependentUserId());
        materialPromotionOrderBo.setBuyUserId(user.getUserId());
        materialPromotionOrderBo.setBuyUserName(user.getNickname());
        materialPromotionOrderBo.setMaterialId(payBo.getMaterialId());
        materialPromotionOrderBo.setMaterialType(BusyType.Playlet.getType());
        materialPromotionOrderBo.setOrderNo(mpOrderNo);
        materialPromotionOrderBo.setTradeNo(posSeq);
        materialPromotionOrderBo.setPayPrice(payBo.getPayPrice());
        materialPromotionOrderBo.setIndependentPrice(calculateMap.get(SohuIndependentObject.distribution.getKey()));
        materialPromotionOrderBo.setIndependentStatus(0);
        materialPromotionOrderBo.setTradeType(MaterialTradeTypeEnum.DRAMA.getCode());
    }

    /**
     * 商单接单分账算价
     *
     * @param templateModel         分账模板
     * @param busyOrderReceivePrice 总金额  12
     * @param independentPrice      分销佣金
     * @param independentOrder      是否是分销单
     *                              //     * @param inviteUser            是否是拉新
     *                              //     * @param agencyId              是否有代理人
     *                              //     * @param independentInviteId   是否有分销人
     * @param isIndependentAmount   是否是佣金支付
     */
    private Map<String, BigDecimal> calculateBusyOrderDistribution(SohuIndependentTemplateModel templateModel,
                                                                   BigDecimal busyOrderReceivePrice,
                                                                   BigDecimal independentPrice,
                                                                   Boolean independentOrder,
//                                                                   Boolean inviteUser,
//                                                                   Long independentInviteId,
//                                                                   Boolean agencyId,
                                                                   Boolean isIndependentAmount) {
        log.info("templateModel、independentPrice：{}、{}", JSONUtil.toJsonStr(templateModel), independentPrice);
        // 分账对象
        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
        // 平台总分账金额 = 平台总手续费 * 平台分账比例
        BigDecimal platPrice = null;
        if (isIndependentAmount) {
            if (CalUtils.isGreatZero(independentPrice)) {
                if (independentOrder) {
                    // 分销人佣金百分比
                    BigDecimal distributorRatioPercentage = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
                    BigDecimal distributorPrice = CalUtils.multiply(independentPrice, distributorRatioPercentage);
                    log.warn("distributorPrice : {}", distributorPrice);
                    // 分销人金额
                    orderIndependentPrice.setDistributorPrice(distributorPrice);
//                    if (null != independentInviteId && independentInviteId > 0L) {
//                        // 分销人的拉新人金额
//                        orderIndependentPrice.setDistributorInvitePrice(CalUtils.sub(independentPrice, distributorPrice));
//                    } else {
                    // 剩下的钱给平台
                    platPrice = CalUtils.sub(independentPrice, distributorPrice);
//                    }
                } else {
                    // 没有分销人全部给平台
                    platPrice = independentPrice;
                }
            }
        } else {
            // 平台手续费百分比-3.9%
            BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
            // 平台百分比
            BigDecimal adminDivide = BigDecimalUtils.divide(templateModel.getAdminRatio(), CalUtils.PERCENTAGE);
            // 消费者拉新人百分比
            BigDecimal consumerDivide = BigDecimalUtils.divide(templateModel.getConsumerInviteRatio(), CalUtils.PERCENTAGE);
            // 平台总手续费 = 商品价格 * 平台手续费比例 不计分拥金额
            BigDecimal independentPlatformPrice = busyOrderReceivePrice.multiply(platformDivide).setScale(2, RoundingMode.HALF_UP);
            log.warn("平台总手续费：{}", independentPlatformPrice);
            BigDecimal consumerInvitePrice;
//            if (inviteUser) {
//                platPrice = independentPlatformPrice.multiply(adminDivide).setScale(2, RoundingMode.HALF_UP);
//                consumerInvitePrice = independentPlatformPrice.multiply(consumerDivide).setScale(2, RoundingMode.HALF_UP);
//            } else {
            platPrice = independentPlatformPrice.multiply(CalUtils.add(adminDivide, consumerDivide)).setScale(2, RoundingMode.HALF_UP);
            consumerInvitePrice = BigDecimal.ZERO;
//            }
            log.warn("consumerInvitePrice : {}", consumerInvitePrice);
            // 消费者拉新人分账金额 = 平台总分账金额 - 平台分账金额
            orderIndependentPrice.setInvitePrice(consumerInvitePrice);
            // 分红总金额 =  平台总手续费 - 平台分账金额 - 消费者拉新人分账金额
            BigDecimal sharePrice = CalUtils.sub(independentPlatformPrice, platPrice, consumerInvitePrice);
            log.warn("sharePrice : {}", sharePrice);
            // 国家站站长分账金额 = 分红总金额 * 国家站站长分账比例
            BigDecimal countryPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCountryRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            orderIndependentPrice.setCountryPrice(countryPrice);
            log.warn("countryPrice : {}", countryPrice);
            BigDecimal agencyPrice = BigDecimal.ZERO;
            // 城市站站长分账金额 = 分红总金额 * 城市站站长分账比例
            BigDecimal cityPrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getCityRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            log.warn("cityPrice : {}", cityPrice);
            // 入口站长
            BigDecimal entrancePrice = sharePrice.multiply(BigDecimalUtils.divide(templateModel.getEntranceRatio(), CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            log.warn("entrancePrice : {}", cityPrice);
//            // 代理算价
//            if (agencyId && null != templateModel.getAgencyRatio() && !BigDecimal.ZERO.equals(BigDecimalUtils.divide(templateModel.getAgencyRatio(), CalUtils.PERCENTAGE))) {
//                // 代理分账金额 = 平台总手续费 -（平台分账金额+国家站长分账金额+城市站长分账金额）
//                agencyPrice = CalUtils.sub(independentPlatformPrice, platPrice, countryPrice);
//            } else {
            // 剩下的钱
            BigDecimal lastPrice = CalUtils.sub(sharePrice, countryPrice, cityPrice, entrancePrice);
            platPrice = CalUtils.add(platPrice, lastPrice);
//            }
            log.warn("platPrice : {}", platPrice);
            // 如果是分销订单并且有拉新人就计算拉新人的费用
            // 扣除平台手续费后的分销佣金
//        BigDecimal distributionPrice = CalUtils.multiply(independentPrice, subtractPlatform);
            // 分销人佣金百分比
            BigDecimal distributorRatioPercentage = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
            log.info("distributionPrice:{}", independentPrice);
            if (CalUtils.isGreatZero(independentPrice)) {
                if (independentOrder) {
                    BigDecimal distributorPrice = CalUtils.multiply(independentPrice, distributorRatioPercentage);
                    log.warn("distributorPrice : {}", distributorPrice);
                    // 分销人金额
                    orderIndependentPrice.setDistributorPrice(distributorPrice);
//                    if (null != independentInviteId && independentInviteId > 0L) {
//                        // 分销人的拉新人金额
//                        orderIndependentPrice.setDistributorInvitePrice(CalUtils.sub(independentPrice, distributorPrice));
//                    } else {
                    // 剩下的钱
                    BigDecimal lastPrice2 = CalUtils.sub(independentPrice, distributorPrice);
                    // 剩下的钱给平台
                    platPrice = CalUtils.add(platPrice, lastPrice2);
//                    }
                } else {
                    // 没有分销人全部给平台
                    platPrice = CalUtils.add(platPrice, independentPrice);
                }
            }
            orderIndependentPrice.setCityPrice(cityPrice);
            orderIndependentPrice.setAgencyPrice(agencyPrice);
        }
        // 平台分账金额
        orderIndependentPrice.setAdminPrice(platPrice);
        // 分销单状态
        orderIndependentPrice.setIndependentOrder(independentOrder);
        log.info("calculateBusyOrderDistribution 计算结果：{}", JSONUtil.toJsonStr(orderIndependentPrice));
        // 组装分账信息
        Map<String, BigDecimal> calculateMap = new HashMap<>();
        addIfValid(calculateMap, SohuIndependentObject.platform.getKey(), orderIndependentPrice.getAdminPrice());
        addIfValid(calculateMap, SohuIndependentObject.agency.getKey(), orderIndependentPrice.getAgencyPrice());
        addIfValid(calculateMap, SohuIndependentObject.distributionInvite.getKey(), orderIndependentPrice.getDistributorInvitePrice());
        addIfValid(calculateMap, SohuIndependentObject.country.getKey(), orderIndependentPrice.getCountryPrice());
        addIfValid(calculateMap, SohuIndependentObject.city.getKey(), orderIndependentPrice.getCityPrice());
        addIfValid(calculateMap, SohuIndependentObject.distribution.getKey(), orderIndependentPrice.getDistributorPrice());
        addIfValid(calculateMap, SohuIndependentObject.invite.getKey(), orderIndependentPrice.getInvitePrice());
        addIfValid(calculateMap, SohuIndependentObject.entrance.getKey(), orderIndependentPrice.getEntrancePrice());
        log.warn("calculateMap : {}", JsonUtils.toJsonString(calculateMap));
        return calculateMap;
    }


    /**
     * 商单接单分账算价
     *
     * @param templateModel 分账模板
     * @param payPrice      总金额  12
     */
    private Map<String, BigDecimal> calculateDistribution(SohuIndependentTemplateModel templateModel, BigDecimal payPrice) {
        // 平台手续费百分比-3.9%
        BigDecimal platformDivide = BigDecimalUtils.divide(templateModel.getPlatformRatio(), CalUtils.PERCENTAGE);
        // 平台总手续费 = 商品价格 * 平台手续费比例 不计分拥金额
        BigDecimal independentPlatformPrice = payPrice.multiply(platformDivide).setScale(2, RoundingMode.HALF_UP);
        // 分账对象
        SohuOrderIndependentPriceVo orderIndependentPrice = new SohuOrderIndependentPriceVo();
        // 平台分账金额
        orderIndependentPrice.setAdminPrice(independentPlatformPrice);
        // 分销单状态
        orderIndependentPrice.setIndependentOrder(true);
        log.info("calculateBusyOrderDistribution 计算结果：{}", JSONUtil.toJsonStr(orderIndependentPrice));
        // 组装分账信息
        Map<String, BigDecimal> calculateMap = new HashMap<>();
        addIfValid(calculateMap, SohuIndependentObject.platform.getKey(), orderIndependentPrice.getAdminPrice());
        addIfValid(calculateMap, SohuIndependentObject.agency.getKey(), orderIndependentPrice.getAgencyPrice());
        addIfValid(calculateMap, SohuIndependentObject.distributionInvite.getKey(), orderIndependentPrice.getDistributorInvitePrice());
        addIfValid(calculateMap, SohuIndependentObject.country.getKey(), orderIndependentPrice.getCountryPrice());
        addIfValid(calculateMap, SohuIndependentObject.city.getKey(), orderIndependentPrice.getCityPrice());
        addIfValid(calculateMap, SohuIndependentObject.distribution.getKey(), orderIndependentPrice.getDistributorPrice());
        addIfValid(calculateMap, SohuIndependentObject.invite.getKey(), orderIndependentPrice.getInvitePrice());
        log.warn("calculateMap : {}", JsonUtils.toJsonString(calculateMap));
        return calculateMap;
    }


    /**
     * 组装map校验
     *
     * @param map
     * @param key
     * @param value
     */
    private void addIfValid(Map<String, BigDecimal> map, String key, BigDecimal value) {
        if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
            map.put(key, value);
        }
    }

    /**
     * 根据对象获取id集合
     *
     * @param bo
     */
    public static List<Long> getAllIds(SohuIndependentIdPayBo bo) {
        return Stream.of(bo.getAdminId(), bo.getCountryId(), bo.getCityId(), bo.getDistributorId(), bo.getInviteId(),
                bo.getDistributorInviteId(), bo.getMerUserId(), bo.getReceiveId()).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 计算每个子单的手续费
     *
     * @param yourObjects 分账对象
     * @param totalAmount 总交易金额
     * @param fee         总手续费
     */
    private static void distributeFeeOfShort(List<SohuIndependentOrderVo> yourObjects, BigDecimal totalAmount, BigDecimal fee) {
        // 计算每个对象的金额占比
        for (SohuIndependentOrderVo obj : yourObjects) {
            BigDecimal price = obj.getIndependentPrice();
            BigDecimal ratio = price.divide(totalAmount, 4, RoundingMode.HALF_UP);
            BigDecimal allocatedFee = ratio.multiply(fee).setScale(2, RoundingMode.HALF_UP);
            obj.setIndependentPrice(CalUtils.sub(obj.getIndependentPrice(), allocatedFee));
            obj.setChargePrice(allocatedFee);
        }
    }

    /**
     * 计算每个子单的手续费
     * 1、没有非平台对象：平台承担所有手续费。
     * 2、有非平台对象且总金额小于总手续费：由平台和非平台共同承担。
     * 3、非平台对象的总金额大于或等于总手续费，则由非平台对象共同承担
     *
     * @param yourObjects 分账对象
     * @param totalAmount 总交易金额
     * @param fee         总手续费
     */
    private static void newDistributeFeeOfShort(List<SohuIndependentOrderVo> yourObjects, BigDecimal totalAmount, BigDecimal fee) {
        // 确保平台对象存在，并找到平台对象
        SohuIndependentOrderVo platformObject = null;
        // 计算非平台对象的总金额
        BigDecimal totalNonPlatformAmount = BigDecimal.ZERO;

        for (SohuIndependentOrderVo obj : yourObjects) {
            if (obj.getIndependentObject().equals("PLATFORM")) {
                platformObject = obj; // 找到平台对象
            } else {
                totalNonPlatformAmount = totalNonPlatformAmount.add(obj.getIndependentPrice());
            }
        }

        // 没有非平台对象，平台承担所有手续费
        if (totalNonPlatformAmount.compareTo(BigDecimal.ZERO) == 0) {
            if (platformObject != null) {
                platformObject.setChargePrice(fee);
            }
            return; // 直接返回
        }

        // 有非平台对象且总金额小于总手续费
        if (totalNonPlatformAmount.compareTo(fee) < 0) {
            // 平台和非平台一起承担
            if (platformObject != null) {
                // 平台对象承担剩余手续费
                platformObject.setChargePrice(fee.subtract(totalNonPlatformAmount));
            }
            for (SohuIndependentOrderVo obj : yourObjects) {
                if (obj.getIndependentObject().equals("PLATFORM")) {
                    continue; // 跳过平台对象
                }

                BigDecimal price = obj.getIndependentPrice();
                // 计算比例
                BigDecimal ratio = price.divide(totalNonPlatformAmount, 4, RoundingMode.HALF_UP);
                // 计算分配的手续费
                BigDecimal allocatedFee = ratio.multiply(fee).setScale(2, RoundingMode.HALF_UP);

                // 更新对象的独立价格和手续费
                obj.setIndependentPrice(CalUtils.sub(obj.getIndependentPrice(), allocatedFee));
                obj.setChargePrice(allocatedFee);
            }
            return;
        }

        // 正常分配手续费给非平台对象
        for (SohuIndependentOrderVo obj : yourObjects) {
            if (obj.getIndependentObject().equals("PLATFORM")) {
                // 平台对象不承担手续费
                obj.setChargePrice(BigDecimal.ZERO);
                continue;
            }

            BigDecimal price = obj.getIndependentPrice();
            // 计算比例
            BigDecimal ratio = price.divide(totalNonPlatformAmount, 4, RoundingMode.HALF_UP);
            // 计算分配的手续费
            BigDecimal allocatedFee = ratio.multiply(fee).setScale(2, RoundingMode.HALF_UP);

            // 更新对象的独立价格和手续费
            obj.setIndependentPrice(CalUtils.sub(obj.getIndependentPrice(), allocatedFee));
            obj.setChargePrice(allocatedFee);
        }
    }

}
