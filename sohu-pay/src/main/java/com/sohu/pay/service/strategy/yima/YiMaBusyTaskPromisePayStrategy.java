package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.busyorder.api.domain.SohuBusyOrderSaveOrderPayReqBo;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.SohuBusyOrderPayModel;
import com.sohu.busyorder.api.model.SohuBusyTaskModel;
import com.sohu.busyorder.api.model.SohuBusyTaskReceiveModel;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.SohuImGroupCreateBo;
import com.sohu.im.api.bo.SohuImGroupUserBo;
import com.sohu.im.api.enums.ImGroupPermissionType;
import com.sohu.im.api.enums.ImGroupTaskRole;
import com.sohu.im.api.enums.ImGroupType;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.service.RemoteMiddleCategoryService;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.vo.SohuCategoryVo;
import com.sohu.middle.api.vo.SohuTradeRecordVo;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.vo.SohuMasterPayOrderVo;
import com.sohu.pay.service.ISohuMasterPayOrderService;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteUserService;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 商单任务保证金支付
 */
@Component
@Slf4j
public class YiMaBusyTaskPromisePayStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    @DubboReference
    private RemoteBusyOrderService remoteBusyOrderService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    private RemoteImService remoteImService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;
    @Resource
    private ISohuMasterPayOrderService sohuMasterPayOrderService;
    @DubboReference
    private RemoteBusyTaskReceiveService remoteBusyTaskReceiveService;
    @DubboReference
    private RemoteUserService remoteUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 商单任务保证金支付 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - 商单任务保证金退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        String busyOrder = refundPayBo.getMasterOrderNo();
        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryReceiveByTaskNoAndBackup(busyOrder, Boolean.TRUE);
        SohuBusyOrderPayModel orderPayModel = remoteBusyOrderService.queryBusyOrderPay(busyOrder, PayObject.BusyTaskPromise.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
        if (orderPayModel.getPayAmount() != null && CalUtils.isZero(taskReceiveModel.getAmount()) &&
                StrUtil.equalsAnyIgnoreCase(orderPayModel.getPayStatus(), PayStatus.Paid.name())) {
            remoteBusyOrderService.updateBusyOrderAndRece(orderPayModel.getBusyOrderReceiveId(), false);
            return Boolean.TRUE;
        }
        try {
            SohuBusyTaskSiteModel sohuBusyTaskSiteModel = remoteBusyOrderService.querySiteByTaskNo(taskReceiveModel.getTaskNumber());
            if (Objects.nonNull(sohuBusyTaskSiteModel)) {
                remoteImService.deleteGroupTaskNoLogin(sohuBusyTaskSiteModel.getUserId(), taskReceiveModel.getTaskNumber());
                log.info("任务群解散,taskNumber：{}", taskReceiveModel.getTaskNumber());
            }
        } catch (Exception e) {
            log.error("任务群解散失败,taskNumber：{}", taskReceiveModel.getTaskNumber());
        }

        SohuMasterPayOrderVo masterPayOrderVo = sohuMasterPayOrderService.queryByMasterPayNumberAndPayType("M" + orderPayModel.getOrderNo(), orderPayModel.getPayType());
        YiMaPayConfig yiMaPayConfig = getPayConfig();
        // 组装微信小程序退款请求参数
        BarcodeReverseRequest request = getBarcodeReverseRequest();
        request.setPosId(yiMaPayConfig.getPosId());
        request.setIsspid(yiMaPayConfig.getIssPid());
        request.setSystemId(yiMaPayConfig.getSystemId());
        request.setStoreId(yiMaPayConfig.getStoreId());
        // 退款参数封装
        BarcodeReverse reverse = new BarcodeReverse();
        reverse.setPayType(masterPayOrderVo.getChildPayType());
        // 请求退款单号拼接
        request.setPosSeq("R" + System.nanoTime());
        // 支付请求流水号
        reverse.setOrgPosSeq(orderPayModel.getOrderNo());
        reverse.setTxAmt(BigDecimalUtils.yuanToFen(orderPayModel.getPayAmount()));
        request.setBarcodeReverseRequest(reverse);
        log.info("商单预付款退款请求:{}", JSONUtil.toJsonStr(request));

        BarcodeReverseResponse response = Client.getClient().execute(request);
        List<String> resultList = Lists.newArrayList("9998", "0000");
        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
            log.error("商单预付款退款异常：{}", JSONUtil.toJsonStr(response));
            throw new RuntimeException(response.getResult().getComment());
        }
        orderPayModel.setPayableAmount(orderPayModel.getPayAmount());
        // 保存商单预付款退款记录
        remoteBusyOrderService.savePrepaymentRefundBillRecord(orderPayModel);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 商单任务保证金回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        // 校验支付状态
        checkPayStatus(response.getStatus());
        SohuBusyOrderPayModel payModel = remoteBusyOrderService.queryBusyOrderPayByOrderNo(outTradeNo);
        if (Objects.isNull(payModel)) {
            throw new ServiceException("任务支付保证金记录不存在");
        }
        // 支付成功,修改相关状态
        remoteBusyOrderService.updateBusyOrderSuccess(outTradeNo, response.getTradeNo());
        boolean exists = RedisUtils.isExistsObject(outTradeNo);
        Long receiveId = 0L;
        if (exists) {
            receiveId = RedisUtils.getCacheObject(outTradeNo);
        } else {
            SohuTradeRecordVo recordVo = remoteMiddleTradeRecordService.queryByPayNumber(outTradeNo);
            receiveId = Long.valueOf(recordVo.getConsumeCode());
        }
        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryBusyTaskRece(receiveId);
        SohuBusyTaskSiteModel sohuBusyTaskSiteModel = remoteBusyOrderService.querySiteByTaskNo(taskReceiveModel.getTaskNumber());
        String receiverTaskNickName = this.getReceiverTaskNickName(taskReceiveModel.getUserId());
        // 发送发单方任务接单通知
        CompletableFuture.runAsync(() -> {
            remoteBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSiteModel.getTaskNumber(), TaskNoticeEnum.TASK_BEEN_APPLY,
                    sohuBusyTaskSiteModel.getUserId(), null, receiverTaskNickName, Boolean.FALSE);
        });
        // 走修改其他子单、主单逻辑
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(taskReceiveModel), MqKeyEnum.BUSY_TASK_RECEIVE.getKey());
        remoteStreamMqService.sendDelayMsg(mqMessaging, 1L);
        this.createGroup(taskReceiveModel);
        // 更新流水记录成功
        remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, response.getTradeNo(), PayStatus.Paid, null);
        updatePayOrder(outTradeNo, CalUtils.centToYuan(new BigDecimal(response.getChargeAmount())), PayStatus.Paid.name(), response.getTradeNo());
        // 接单方申请接单，已选定其他接单人极光推送
        remoteBusyTaskReceiveService.pushApplyTaskOutNotice(sohuBusyTaskSiteModel);
        return "success";
    }

    private String getReceiverTaskNickName(Long userId){
        LoginUser receiverTaskUser = remoteUserService.queryById(userId);
        if (Objects.nonNull(receiverTaskUser)) {
            return DesensitizedUtil.chineseName(receiverTaskUser.getNickname());
        }
        return StrUtil.EMPTY;
    }

    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON +
                payBo.getMasterOrderNo() + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + payBo.getUserId();
        if (StringUtils.isNotBlank(payBo.getPayChannel()) && payBo.getPayChannel().equals("mobile")) {
            boolean exists = RedisUtils.isExistsObject(key);
            // 如果存在直接唤醒
            if (exists) {
                log.info("任务保证金支付订单存在，直接唤醒支付，订单号：{}，缓存值：{}", payBo.getMasterOrderNo(), RedisUtils.getCacheObject(key));
                return RedisUtils.getCacheObject(key);
            }
        }
        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryBusyTaskRece(payBo.getMasterId());
        if (ObjectUtil.isNull(taskReceiveModel)) {
            throw new ServiceException("order receive does not exist");
        }
        if (!StrUtil.equalsAnyIgnoreCase(taskReceiveModel.getState(), CommonState.WaitApprove.name())) {
            throw new ServiceException("Payment status is incorrect");
        }
        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        ScanpayRequest scanpayRequest = getScanpayRequest();
        //第三方支付单号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.BUSY_TASK_PROMISE_SUBUNIT);
        // 备注
        unifiedorderRequest.setMemo("任务支付保证金");
        scanpayRequest.setMemo("任务支付保证金");
        // 唯一订单号
        unifiedorderRequest.setPosSeq(posSeq);
        scanpayRequest.setPosSeq(posSeq);
        // 总金额
        Unifiedorder unifiedOrder = getUnifiedorder(payBo.getUserId());
        Scanpay scanpay = getScanpay();
        unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(taskReceiveModel.getAmount()));
        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(taskReceiveModel.getAmount()));
        // 不分账 -- R实时分账 --D延时分账
        ExtendParams extendParams = new ExtendParams();
        extendParams.setSplitFlag(ExtendParams.N);
        unifiedOrder.setExtendParams(extendParams);
        scanpay.setExtendParams(extendParams);
        // 设置请求参数
        unifiedorderRequest.setUnifiedorderRequest(unifiedOrder);
        scanpayRequest.setScanpayRequest(scanpay);

        SohuBusyOrderSaveOrderPayReqBo saveOrderPayModel = new SohuBusyOrderSaveOrderPayReqBo()
                .setOrderId(taskReceiveModel.getTaskNumber())
                .setOrderNo(posSeq)
                .setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus())
                .setPayStatus(PayStatus.WaitPay.name())
                .setAmount(taskReceiveModel.getAmount())
                .setBusyType(PayObject.BusyTaskPromise.name())
                .setPayeeId(0L)
                .setUserId(taskReceiveModel.getUserId())
                .setDeliveryId(0L)
                .setPayChannel(payBo.getPayChannel());
        remoteBusyOrderService.saveBusyOrderPay(saveOrderPayModel);

        payBo.setAmount(taskReceiveModel.getAmount());
        // 保存主支付单
        saveMasterPayOrder(payBo, posSeq, PayStatus.WaitPay.name());
        // 保存子支付单
        savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + posSeq, posSeq, PayStatus.WaitPay.name());

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = buildRecord(payBo);
        recordBo.type(SohuTradeRecordEnum.Type.BusyTaskPromise.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.BusyTaskPromise.getCode());
        recordBo.consumeCode(String.valueOf(payBo.getMasterId()));
        recordBo.msg(SohuTradeRecordEnum.Type.BusyTaskPromise.getMsg());
        recordBo.amountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordBo.payNumber(posSeq);
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        // 保存流水记录 - 钱 - 支出
        saveTradeRecord(recordBo.build());

        // 将接单id根据支付流水号保存到缓存里面
        RedisUtils.setCacheObject(posSeq, payBo.getMasterId(), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-任务支付保证金支付请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
            ScanpayResponse response = Client.getClient().execute(scanpayRequest);
            log.info("翼码支付 - PC-任务支付保证金支付请求response返回：{}", JSONUtil.toJsonStr(response));
//            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- 任务支付保证金支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        log.info("翼码支付 -MOBILE- 任务支付保证金支付请求response返回：{}", JSONUtil.toJsonStr(response));
        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);
    }


    public void createGroup(SohuBusyTaskReceiveModel taskReceive) {
        log.info("开始创建任务匿名群:{}", JSONUtil.toJsonStr(taskReceive));
        // 接单人ID
        Long userPull = taskReceive.getUserId();
        SohuBusyTaskSiteModel sohuBusyTaskSiteModel = remoteBusyOrderService.querySiteByTaskNo(taskReceive.getTaskNumber());
        SohuBusyTaskModel sohuBusyTaskModel = remoteBusyOrderService.queryByTaskNo(sohuBusyTaskSiteModel.getMasterTaskNumber());
        // sohu_category表主键ID
        Long type = sohuBusyTaskModel.getType();
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(type);
        String constMark = sohuCategoryVo.getConstMark();

        // 发单人ID
        Long userPublish = sohuBusyTaskModel.getUserId();

        SohuImGroupCreateBo imGroupCreateBo = new SohuImGroupCreateBo();

        imGroupCreateBo.setUnq(System.nanoTime());
        imGroupCreateBo.setUserId(userPublish);
        imGroupCreateBo.setGroupHeaderName("发单方 ");
        imGroupCreateBo.setLogo(SohuImGroupCreateBo.GROUP_PRIVACY_LOGO);
        imGroupCreateBo.setGroupNotice(SohuImGroupCreateBo.ANONYMOUS_NOTICE);
        //imGroupCreateBo.setName((StrUtil.isBlankIfStr(user.getNickname()) ? user.getUsername() : user.getNickname()) + "的任务群");

        List<SohuImGroupUserBo> groupUsers = new ArrayList<>();
        // 接单人
        SohuImGroupUserBo groupUserPull = SohuImGroupUserBo.builder().userId(userPull).permissionType(ImGroupPermissionType.group_user.name()).build();

        Map<String, Object> map = new HashMap<>();
        map.put("groupTaskRole", ImGroupTaskRole.taskRece.getCode());
        // 标识接单人是接单方
        groupUserPull.setExt(JSONUtil.toJsonStr(map));
        groupUsers.add(groupUserPull);

        imGroupCreateBo.setGroupUsers(groupUsers);
        // 流量服务商单
//        if (StrUtil.equalsAnyIgnoreCase(constMark, BusyTaskTypeEnum.FLOW.getCode())) {
//            imGroupCreateBo.setName(StringUtils.substring(sohuBusyTaskModel.getTitle(), 0, 10));
//            imGroupCreateBo.setGroupType(ImGroupType.groupTaskCustom.name());
//        } else {
//            imGroupCreateBo.setName(StringUtils.substring(sohuBusyTaskModel.getTitle(), 0, 24) + "的任务群");
//            imGroupCreateBo.setGroupType(ImGroupType.groupTask.name());
//        }
        imGroupCreateBo.setName(StringUtils.substring(sohuBusyTaskModel.getTitle(), 0, 24) + "的任务群");
        imGroupCreateBo.setGroupType(ImGroupType.groupTask.name());

        // 群拓展字段设置
        Map<String, Object> groupExtMap = new HashMap<>();
        groupExtMap.put("groupTaskChildNumber", taskReceive.getTaskNumber());
        groupExtMap.put(ImGroupTaskRole.taskPublish.getCode() + "UserId", userPublish);
        groupExtMap.put(ImGroupTaskRole.taskRece.getCode() + "UserId", userPull);
        imGroupCreateBo.setGroupExt(JSONUtil.toJsonStr(groupExtMap));
        imGroupCreateBo.setSendFirstGroupMsg(true);
        // 创建任务群
        remoteImService.createGroup(imGroupCreateBo);
    }
}
