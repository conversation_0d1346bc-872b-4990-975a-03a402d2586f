package com.sohu.pay.service.strategy.paypal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.paypal.orders.*;
import com.paypal.payments.RefundRequest;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.enums.SohuTradeRecordEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.vo.SohuTradeRecordVo;
import com.sohu.pay.api.domain.SohuPayQueryBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.model.SohuPayResultModel;
import com.sohu.pay.service.ISohuMasterPayOrderService;
import com.sohu.pay.service.SohuPaySyncService;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.model.SohuProductModel;
import com.sohu.shoporder.api.*;
import com.sohu.shoporder.api.model.SohuShopMasterOrderModel;
import com.sohu.shoporder.api.model.SohuShopOrderModel;
import com.sohu.shoporder.api.model.SohuShopRefundOrderModel;
import com.sohu.shoporder.api.vo.SohuShopOrderInfoVo;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品PayPal支付
 *
 * @author: zc
 * @date: 2024/9/12 18:44
 * @version: 1.0.0
 */
@Slf4j
@Component
public class PayPalGoodsStrategy extends PayPalAbs {

    @DubboReference
    private RemoteMasterOrderService remoteMasterOrderService;
    @DubboReference
    private RemoteShopRefundOrderService remoteShopRefundOrderService;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private SohuPaySyncService paySyncService;
    @DubboReference
    private RemoteShopOrderStatusService remoteShopOrderStatusService;
    @DubboReference
    private RemoteShopRefundOrderStatusService remoteShopRefundOrderStatusService;
    @DubboReference
    private RemoteShopOrderInfoService remoteShopOrderInfoService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @Resource
    private ISohuMasterPayOrderService sohuMasterPayOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        Objects.requireNonNull(payBo, "请求参数为空");
        log.info("PalPay支付 - 商品支付 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    public SohuPayResultModel paySuccess(SohuPayQueryBo payQueryBo) {
        return null;
    }

    @Override
    public Object paySuccessQuery(SohuPayQueryBo payQueryBo) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("PayPal支付 - 商城商品退款 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        SohuShopRefundOrderModel refundOrder = remoteShopRefundOrderService.getByRefundOrderNo(refundPayBo.getMasterOrderNo());
        if (refundPayBo.getUserId() != null && refundPayBo.getUserId() > 0L) {
            List<SohuMerchantModel> sohuMerchantModels = remoteMerchantService.selectByUserId(refundPayBo.getUserId());
            List<Long> merIds = sohuMerchantModels.stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
            if (!merIds.contains(refundOrder.getMerId())) {
                throw new ServiceException("无法操作非自己商户的订单");
            }
        }
        SohuShopOrderModel storeOrder = remoteShopOrderService.getByOrderNo(refundOrder.getShopOrderNo());
        if (!storeOrder.getPaid()) {
            throw new ServiceException("未支付无法退款");
        }
        try {
            SohuTradeRecordVo sohuTradeRecordVo = remoteMiddleTradeRecordService.queryConsume(storeOrder.getPayType(), SohuTradeRecordEnum.Type.Good.getCode(), storeOrder.getMasterOrderNo());
            RefundRequest refundRequest = new RefundRequest();
            com.paypal.payments.Money money = new com.paypal.payments.Money();
            money.currencyCode(DEFAULT_CURRENCY_CODE);
            money.value(String.valueOf(CalUtils.multiply(refundOrder.getRefundPrice(), new BigDecimal("0.95"))));
            refundRequest.amount(money);
            refundRequest.invoiceId(sohuTradeRecordVo.getPayNumber());
            //refundRequest.noteToPayer(refundOrder.getRefundReason());
            // 请求退款
            com.paypal.payments.Refund refundResponse = payPalService.refundPayOrder(sohuTradeRecordVo.getCaptureId(), refundRequest);
            if ("COMPLETED" .equals(refundResponse.status())) {
                log.info("退款成功");
                // todo 退款逻辑
                // todo PayPal申请退款+返回状态
                // 修改订单退款状态
                storeOrder.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_REFUNDING);
                refundOrder.setRefundStatus(OrderConstants.MERCHANT_REFUND_ORDER_STATUS_REFUNDING);
                refundOrder.setRefundTime(new Date());
                Boolean execute = transactionTemplate.execute(e -> {
                    remoteShopRefundOrderService.updateById(refundOrder);
                    remoteShopOrderService.updateById(storeOrder);
                    // 新增日志
                    remoteShopOrderStatusService.saveRefund(storeOrder.getOrderNo(), refundOrder.getRefundPrice(), "退款中");
                    remoteShopRefundOrderStatusService.createLog(refundOrder.getRefundOrderNo(), OrderConstants.REFUND_ORDER_LOG_TYPE_APPLY,
                            OrderConstants.ORDER_LOG_MESSAGE_REFUND_PRICE.replace("{amount}", refundOrder.getRefundPrice().toString()));
                    return Boolean.TRUE;
                });
                if (Boolean.FALSE.equals(execute)) {
                    remoteShopOrderStatusService.saveRefund(storeOrder.getOrderNo(), storeOrder.getPayPrice(), "失败");
                    throw new ServiceException("订单更新失败");
                }
                paySyncService.refundOrder(refundOrder);
                // todo 队列未部署
                // 2s后延迟 消息队列去做退款回调的事情-主动查询退款结果、订单状态、商品回滚
                MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(refundOrder.getRefundOrderNo()), MqKeyEnum.YI_MA_REFUND_PAY.getKey());
                remoteStreamMqService.sendDelayMsg(mqMessaging, 2L);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return Boolean.TRUE;

//        // todo 退款逻辑
//        Amount amount = new Amount();
//        amount.setCurrency("USD");
//        amount.setTotal(String.format("%.2f", new BigDecimal("0.11").abs()));
//        refund.setAmount(amount);
//        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refundSuccess(String refundOrderNo) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        // 翼码支付回掉逻辑
        log.info("PalPay支付 - 商城商品支付回调 ：{}", callbackResponse);
        CallbackRequest response = JSONUtil.toBean(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        // 校验支付状态
//        checkPayStatus(response.getStatus());
        SohuTradeRecordVo sohuTradeRecordVo = remoteMiddleTradeRecordService.queryByPayNumberOne(response.getPosSeq(), PayTypeEnum.PAY_TYPE_PAYPAL.getStatus());
        SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(sohuTradeRecordVo.getConsumeCode());
        masterOrder.setOutTradeNo(outTradeNo).setTransactionId(response.getTradeNo()).setPaid(true);
//                .setChargePrice(CalUtils.centToYuan(new BigDecimal(response.getChargeAmount())));
        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(masterOrder.getOrderNo());
//        log.warn("storeOrderList pay now 计算每个子单的手续费之前: {}", JSONObject.toJSONString(storeOrderList));
        // 计算每个子单的手续费 todo 暂时没有分销
//        distributeFee(storeOrderList, masterOrder.getPayPrice(), masterOrder.getChargePrice());
//        log.warn("storeOrderList pay update 计算每个子单的手续费: {}", JSONObject.toJSONString(storeOrderList));
        // 添加支付成功修改订单状态  --弃用redis队列
        Boolean execute = transactionTemplate.execute(e -> {
            for (SohuShopOrderModel storeOrder : storeOrderList) {
                storeOrder.setPaid(true);
                storeOrder.setPayTime(new Date());
                storeOrder.setPayType(masterOrder.getPayType());
                storeOrder.setStatus(OrderConstants.ORDER_STATUS_SHIPPING);
            }
            remoteShopOrderService.updateBatchById(storeOrderList);
            remoteMasterOrderService.updateById(masterOrder);
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            log.error("商城商品支付回调 订单更新失败==》" + masterOrder.getOutTradeNo());
            throw new ServiceException("订单回调修改数据失败");
        }
        // 异步更新订单、商品等数据
        paySyncService.payOrder(masterOrder);

        // 更新支付单
        updatePayOrder(outTradeNo, masterOrder.getChargePrice(), PayStatus.Paid.name(), response.getTradeNo());

        SohuTradeRecordBo recordBo = SohuTradeRecordBo.builder().build();
        recordBo.setPayType(PayTypeEnum.PAY_TYPE_PAYPAL.getStatus());
        recordBo.setConsumeType(SohuTradeRecordEnum.Type.Good.getCode());
        recordBo.setPayNumber(outTradeNo);
        SohuTradeRecordVo recordVo = remoteMiddleTradeRecordService.queryOne(recordBo);
        recordBo.setId(recordVo.getId());
        recordBo.setPayStatus(PayStatus.Paid.name());
        recordBo.setTransactionId(response.getTradeNo());
        recordBo.setCaptureId(response.getCaptureId());
        // 更新流水明细状态
        // Boolean successTrade = remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, response.getTradeNo(), PayStatus.Paid);
        Boolean successTrade = remoteMiddleTradeRecordService.updateByBo(recordBo);

        if (successTrade) {
            // 修改支付订单状态
            BigDecimal chargeAmount = StrUtil.isBlankIfStr(response.getChargeAmount()) ? BigDecimal.ZERO : CalUtils.centToYuan(new BigDecimal(response.getChargeAmount()));
            // 更新支付单
            updatePayOrder(outTradeNo, chargeAmount, PayStatus.Paid.name(), response.getTradeNo());
        }
        return successTrade ? "success" : "fail";
    }

    @Override
    public Boolean close(String outTradeNo, String payType) {
        return null;
    }

    /**
     * 支付
     *
     * @param payBo 预支付请求对象
     * @return {@link String}
     */
    public String getPay(SohuPrePayBo payBo) {
        String masterOrderNo = payBo.getMasterOrderNo();
        // 通过缓存获取预下单对象
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_PAYPAL.getStatus() + StrPool.COLON + masterOrderNo;
        boolean exists = RedisUtils.isExistsObject(key);
        // 如果存在直接唤醒
        if (exists) {
            log.info("商品订单存在，直接唤醒支付，订单号：{}，缓存值：{}", masterOrderNo, RedisUtils.getCacheObject(key));
            return RedisUtils.getCacheObject(key);
        }
        SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(masterOrderNo);
        // 状态判断
        if (ObjectUtil.isNull(masterOrder)) {
            throw new ServiceException("order does not exist");
        }
        if (masterOrder.getIsCancel()) {
            throw new ServiceException("order is cancelled");
        }
        if (masterOrder.getPaid()) {
            throw new ServiceException("order not paid");
        }
        // 剩余组装信息
        Long merId = remoteShopOrderService.getListByMasterNo(masterOrderNo).get(0).getMerId();
        SohuMerchantModel merchantModel = remoteMerchantService.selectById(merId);
        StringBuilder body = new StringBuilder();
        body.append(merchantModel.getName());
        if (StringUtils.isBlank(body)) {
            body.append("用户下单");
        }
        // 总金额
        payBo.setAmount(masterOrder.getPayPrice());
        // 保存支付流水
//        saveAmountTradeRecord(payBo, masterOrderNo, SohuTradeRecordEnum.Type.Good.getCode(), SohuTradeRecordEnum.Type.Good.getCode(),
//                masterOrderNo, SohuTradeRecordEnum.Type.Good.getMsg(), PayStatus.WaitPay.name(), SohuTradeRecordEnum.AmountType.Expend.getCode());

        // 子订单表
        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(masterOrderNo);
//        // 保存主单
//        saveMasterPayOrder(payBo, masterOrderNo, PayStatus.WaitPay.name());
//        for (SohuShopOrderModel sohuShopOrderModel : storeOrderList) {
//            payBo.setAmount(sohuShopOrderModel.getPayPrice());
//            // 保存子单
//            savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + masterOrderNo, sohuShopOrderModel.getOrderNo(), PayStatus.WaitPay.name());
//            // todo 发送消息通知
////            sendMsgOfWaitSend(sohuShopOrderModel);
//        }
        // PayPal支付
        //第三方支付单号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_PLATFORM);
        // 总金额
        BigDecimal amount = masterOrder.getPayPrice();
        // 支付渠道、信息
        String type = SohuTradeRecordEnum.Type.Good.getCode();
        String msg = SohuTradeRecordEnum.Type.Good.getMsg();
        log.info("==================PayPal=======================");
        payBo.setAmount(amount);
        // 付款组装
        OrderRequest orderRequest = getInitOrderRequest();

        List<PurchaseUnitRequest> purchaseUnitRequests = new ArrayList<PurchaseUnitRequest>();
        // 信息组装
        PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest();
        purchaseUnitRequest.description("商品下单").customId(posSeq).invoiceId(posSeq);
        // 金额对象
        AmountWithBreakdown breakdown = new AmountWithBreakdown()
                .currencyCode(DEFAULT_CURRENCY_CODE)
                .value(String.valueOf(amount))
                .amountBreakdown(new AmountBreakdown()
                        .itemTotal(new Money().currencyCode(DEFAULT_CURRENCY_CODE).value(String.valueOf(masterOrder.getProductTotalPrice())))
                        .shipping(new Money().currencyCode(DEFAULT_CURRENCY_CODE).value(String.valueOf(masterOrder.getTotalPostage())))
                );
        purchaseUnitRequest.amountWithBreakdown(breakdown);
        // 子单数据集合
//        purchaseUnitRequest.items(new ArrayList<Item>() {{
//
//            add(new Item().name("Recharge Coin").description("Recharge Coin").unitAmount(new Money().currencyCode(DEFAULT_CURRENCY_CODE).value(String.valueOf(amount))).quantity("1"));
//        }});
        List<Item> itemList = this.exchangeGoodsDetailList(masterOrderNo);
        purchaseUnitRequest.items(itemList);

        purchaseUnitRequest.shippingDetail(new ShippingDetail()
                .name(new Name().fullName("Recharge Coin"))
                .addressPortable(new AddressPortable()
                        .addressLine1("minglereels")
                        .addressLine2("minglereels")
                        .adminArea2("minglereels")
                        .adminArea1("minglereels")
                        .postalCode("minglereels")
                        .countryCode("CN")));
        purchaseUnitRequests.add(purchaseUnitRequest);
        orderRequest.purchaseUnits(purchaseUnitRequests);
        // 预支付返回对象
        Order payOrder = payPalService.createPayOrder(orderRequest);
        requireNotNullOrder(payOrder);

        String paymentId = payOrder.id();

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = buildRecord(payBo);
        recordBo.payNumber(posSeq);
        recordBo.type(type);
        recordBo.consumeType(type);
        recordBo.consumeCode(payBo.getMasterOrderNo());
        recordBo.msg(msg);
        recordBo.amountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.getCode());
        recordBo.transactionId(paymentId);

        // 保存流水记录 - 钱 - 支出
        saveTradeRecord(recordBo.build());

        // 是否存在主支付单
        Boolean isMasterExcite = sohuMasterPayOrderService.selectCount(posSeq);
        if (!isMasterExcite) {
            // 保存主支付单
            saveMasterPayOrderSpecial(payBo, posSeq, PayStatus.WaitPay.name(), paymentId, payBo.getUuid());
        }
        // 保存子单
        for (SohuShopOrderModel sohuShopOrderModel : storeOrderList) {
            payBo.setAmount(sohuShopOrderModel.getPayPrice());
            // 是否存在子支付单
            if (Objects.isNull(sohuPayOrderService.queryByPayNumber(OrderConstants.ORDER_PREFIX_MERCHANT + sohuShopOrderModel.getOrderNo()))) {
                // 保存子单
                savePayOrder(payBo, posSeq, OrderConstants.ORDER_PREFIX_MERCHANT + sohuShopOrderModel.getOrderNo(), PayStatus.WaitPay.name());
            }
            // 未支付到期时间
            RedisUtils.setCacheObject(sohuShopOrderModel.getMasterOrderNo(), System.currentTimeMillis() + (30 * 60 * 1000), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            // todo 发送消息通知
//            sendMsgOfWaitSend(sohuShopOrderModel);
        }

        // 获取付款单跳转链接
        String href = getPayOrderHref(payOrder);
        // 校验跳转链接
        requireNotPayOrderHref(href);
        // PayPal支付参数计入缓存
        String paypalParams = JSONUtil.toJsonStr(buildPayResponse(paymentId, href));
        RedisUtils.setCacheObject(key, paypalParams, Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        // 跳转链接
        RedisUtils.setCacheObject(PayTypeEnum.PAY_TYPE_PAYPAL.getStatus() + StrPool.COLON + paymentId, JSONUtil.toJsonStr(payBo));
        return paypalParams;
    }

    /**
     * 根据主订单号查询所有商品详情并组装商品详细数据
     *
     * @param orderNo 主订单号
     * @return WechatPayUnifiedOrderRequest.DiscountDetail
     */
    private List<Item> exchangeGoodsDetailList(String orderNo) {
        // 商户商品订单表
        // 微信支付请求需要的商品详情集合
        List<Item> goodsDetailList = Lists.newArrayList();
        // 商户商品订单表
        List<SohuShopOrderModel> storeOrderList = remoteShopOrderService.getListByMasterNo(orderNo);
        if (CollUtil.isEmpty(storeOrderList)) {
            return goodsDetailList;
        }
        for (SohuShopOrderModel storeOrder : storeOrderList) {
            // 订单详情表
            List<SohuShopOrderInfoVo> storeOrderInfos = remoteShopOrderInfoService.getListByOrderNo(storeOrder.getOrderNo());
            List<Long> productIds = new ArrayList<>();
            for (SohuShopOrderInfoVo orderInfo : storeOrderInfos) {
                productIds.add(orderInfo.getProductId());
            }
            // 商品详情-当前订单所有购买的商品id
            List<SohuProductModel> storeProductList = remoteProductService.listByIds(productIds);
            if (CollUtil.isEmpty(storeOrderInfos)) {
                return goodsDetailList;
            }
            Map<Long, SohuProductModel> productModelMap = storeProductList.stream().collect(Collectors.toMap(SohuProductModel::getId, v -> v));
            for (SohuShopOrderInfoVo info : storeOrderInfos) {
                SohuProductModel storeProduct = productModelMap.get(info.getProductId());
                // 微信支付请商品详情
                Item goodsDetail = new Item();

                goodsDetail.sku(storeProduct.getId().toString());
                goodsDetail.name(storeProduct.getStoreName());
//                goodsDetail.unitAmount(BigDecimalUtils.yuanToFen(storeProduct.getPrice()));
                goodsDetail.unitAmount(new Money().currencyCode(DEFAULT_CURRENCY_CODE).value(String.valueOf(storeProduct.getPrice())));
                // 根据商品id与订单详情表商品id匹配，设置商品购买数量
                goodsDetail.quantity(info.getPayNum().toString());
                goodsDetail.description(storeProduct.getStoreName());
                goodsDetailList.add(goodsDetail);
            }
        }
        return goodsDetailList;
    }

}
