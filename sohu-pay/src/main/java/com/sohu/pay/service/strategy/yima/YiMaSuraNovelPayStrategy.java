package com.sohu.pay.service.strategy.yima;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.PayStatus;
import com.sohu.common.core.enums.PayTypeEnum;
import com.sohu.common.core.enums.SohuTradeRecordEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.vo.SohuTradeRecordVo;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.pay.api.bo.SohuMasterPayOrderBo;
import com.sohu.pay.api.bo.SohuPayOrderBo;
import com.sohu.pay.api.bo.SohuRefundPayOrderBo;
import com.sohu.pay.api.domain.SohuPrePayBo;
import com.sohu.pay.api.domain.SohuRefundPayBo;
import com.sohu.pay.api.vo.SohuMasterPayOrderVo;
import com.sohu.pay.domain.SohuPayOrder;
import com.sohu.pay.service.ISohuRefundPayOrderService;
import com.sohu.pay.service.strategy.AbsTradeRecordStrategy;
import com.sohu.pay.service.strategy.PaymentStrategy;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.scanpay.Scanpay;
import com.wangcaio2o.ipossa.sdk.request.scanpay.ScanpayRequest;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.Unifiedorder;
import com.wangcaio2o.ipossa.sdk.request.unifiedorder.UnifiedorderRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.callback.CallbackRequest;
import com.wangcaio2o.ipossa.sdk.response.scanpay.ScanpayResponse;
import com.wangcaio2o.ipossa.sdk.response.unifiedorder.UnifiedorderResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

import static com.sohu.common.core.constant.OrderConstants.ORDER_STATUS_H5_REFUNDED;

/**
 * 购买小说单章节
 */
@Slf4j
@Component
public class YiMaSuraNovelPayStrategy extends AbsTradeRecordStrategy implements YiMaPayStrategy {

    @Resource
    private ISohuRefundPayOrderService sohuRefundPayOrderService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payment(SohuPrePayBo payBo) {
        log.info("翼码支付 - 购买小说单章节 - 支付来源：{}", JSONUtil.toJsonStr(payBo));
        return getPay(payBo);
    }

    @Override
    public Boolean refund(SohuRefundPayBo refundPayBo) {
        log.info("翼码支付 - 购买小说单章节 - 退款来源：{}", JSONUtil.toJsonStr(refundPayBo));
        SohuPayOrder sohuPayOrder = sohuPayOrderService.queryByPayNumber(refundPayBo.getPayNumber());
        if (StrUtil.equalsAnyIgnoreCase(sohuPayOrder.getPayStatus(), PayStatus.WaitPay.name())) {
            throw new RuntimeException("该订单未支付");
        }
        if (StrUtil.equalsAnyIgnoreCase(sohuPayOrder.getPayStatus(), PayStatus.Refund.name())) {
            throw new RuntimeException("该订单已退款");
        }
        if (sohuPayOrder.getPayAmount().compareTo(sohuPayOrder.getRefundAmount()) < 1) {
            throw new RuntimeException("子订单已退款金额不能超过实际付款金额");
        }
        SohuTradeRecordVo sohuTradeRecordVo = remoteMiddleTradeRecordService.queryByPayNumber(refundPayBo.getPayNumber());
        SohuMasterPayOrderVo sohuMasterPayOrderVo = sohuMasterPayOrderService.queryByMasterPayNumber(sohuPayOrder.getMasterOrderNo());

        // 退款单
        SohuRefundPayOrderBo refundPayOrder = new SohuRefundPayOrderBo();
        refundPayOrder.setUserId(sohuPayOrder.getUserId());

        refundPayOrder.setTransactionId(sohuPayOrder.getPayNumber());
        refundPayOrder.setMasterOrderNo(sohuPayOrder.getMasterOrderNo());
        refundPayOrder.setOrderNo(sohuPayOrder.getOrderNo());
        refundPayOrder.setPayNumber(sohuPayOrder.getPayNumber());
        refundPayOrder.setPayType(sohuPayOrder.getPayType());
        refundPayOrder.setPayableAmount(sohuPayOrder.getPayAmount());
        refundPayOrder.setRefundAmount(sohuPayOrder.getPayAmount());

        YiMaPayConfig yiMaPayConfig = getPayConfig();
        // 组装微信小程序退款请求参数
        BarcodeReverseRequest request = getBarcodeReverseRequest();
        request.setPosId(yiMaPayConfig.getPosId());
        request.setIsspid(yiMaPayConfig.getIssPid());
        request.setSystemId(yiMaPayConfig.getSystemId());
        request.setStoreId(yiMaPayConfig.getStoreId());
        // 退款参数封装
        BarcodeReverse reverse = new BarcodeReverse();
        String payType = getPayType(sohuPayOrder.getSourceType());
        reverse.setPayType(payType);
        // 请求退款单号拼接
        request.setPosSeq("R" + System.nanoTime());
        // 支付请求流水号
        reverse.setOrgPosSeq(sohuPayOrder.getPayNumber());
        reverse.setTxAmt(BigDecimalUtils.yuanToFen(sohuPayOrder.getPayAmount()));
        request.setBarcodeReverseRequest(reverse);
        log.info("购买小说单章节退款请求:{}", JSONUtil.toJsonStr(request));

        refundPayOrder.setRefundOrderNo(request.getPosSeq());

        BarcodeReverseResponse response = Client.getClient().execute(request);
        List<String> resultList = Lists.newArrayList("9998", "0000");
        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
            log.error("购买小说单章节退款异常：{}", JSONUtil.toJsonStr(response));
            throw new RuntimeException(response.getResult().getComment());
        } else {
            refundPayOrder.setRefundStatus(ORDER_STATUS_H5_REFUNDED);
        }
        // 保存退款单
        this.sohuRefundPayOrderService.insertByBo(refundPayOrder);
        // 修改主支付单状态
        SohuMasterPayOrderBo sohuMasterPayOrderBo = SohuMasterPayOrderBo.builder()
                .id(sohuMasterPayOrderVo.getId())
                .payStatus(PayStatus.Refund.name())
                .refundAmount(sohuPayOrder.getPayAmount())
                .build();
        sohuMasterPayOrderService.updateByBo(sohuMasterPayOrderBo);
        // 修改子支付单状态
        SohuPayOrderBo sohuPayOrderBo = SohuPayOrderBo.builder()
                .id(sohuPayOrder.getId())
                .payStatus(PayStatus.Refund.name())
                .refundAmount(sohuPayOrder.getPayAmount())
                .build();
        sohuPayOrderService.updateByBo(sohuPayOrderBo);

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = SohuTradeRecordBo.builder();
        recordBo.userId(sohuPayOrder.getUserId());
        recordBo.amount(sohuPayOrder.getPayAmount());
        recordBo.payType(sohuPayOrder.getPayType());
        recordBo.payStatus(PayStatus.Refund.name());
        recordBo.operateChannel(sohuPayOrder.getSourceType());
        recordBo.type(SohuTradeRecordEnum.Type.NOVEL.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.BUY_SURA_NOVEL.getCode());
        recordBo.parentConsumeCode(sohuTradeRecordVo.getParentConsumeCode());
        recordBo.consumeCode(sohuTradeRecordVo.getConsumeCode());
        recordBo.msg("购买小说单章节退款");
        recordBo.amountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
        recordBo.payNumber(refundPayOrder.getRefundOrderNo());
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        // 保存流水记录
        saveTradeRecord(recordBo.build());

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payCallback(String callbackResponse) {
        log.info("翼码支付 - 购买小说单章节支付回调 ：{}", callbackResponse);
        CallbackRequest response = JSONObject.parseObject(callbackResponse, CallbackRequest.class);
        String outTradeNo = response.getPosSeq();
        // 校验支付状态
        checkPayStatus(response.getStatus());

        if (StrUtil.equalsAnyIgnoreCase(response.getStatus(), "1")) {
            // 更新流水明细状态
            remoteMiddleTradeRecordService.updatePayStatus(outTradeNo, response.getTradeNo(), PayStatus.Paid, null);
            // 更新支付单
            updatePayOrder(outTradeNo, CalUtils.centToYuan(new BigDecimal(response.getChargeAmount())), PayStatus.Paid.name(), response.getTradeNo());
            // 发送钱包通知
            sendWalletNotice(response.getTradeNo());
        }
        return StrUtil.equalsAnyIgnoreCase(response.getStatus(), "1") ? "success" : "fail";
    }

    /**
     * 购买小说单章节
     *
     * @return {@link String}
     */
    @Transactional(rollbackFor = Exception.class)
    public String getPay(SohuPrePayBo payBo) {
        Long masterId = payBo.getMasterId();
        String key = CacheConstants.ORDER_PAY_TWO + PayTypeEnum.PAY_TYPE_YI_MA.getStatus() + StrPool.COLON + payBo.getPaySource() + StrPool.COLON +
                masterId + StrPool.COLON + getOperateChannel(payBo.getPayChannel()) + StrPool.COLON + payBo.getUserId();
        boolean exists = RedisUtils.isExistsObject(key);
        // 如果存在直接唤醒
        if (exists) {
            log.info("单个小说单章节订单存在，直接唤醒支付，订单号：{}，缓存值：{}", payBo.getMasterId(), RedisUtils.getCacheObject(key));
            return RedisUtils.getCacheObject(key);
        }
        SohuTradeRecordVo existPaid = remoteMiddleTradeRecordService.queryOne(payBo.getUserId(), SohuTradeRecordEnum.Type.NOVEL.getCode(),
                SohuTradeRecordEnum.Type.BUY_SURA_NOVEL.getCode(), String.valueOf(masterId), PayStatus.Paid.name());
        if (Objects.nonNull(existPaid)) {
            throw new ServiceException("该小说单章节已付过费!");
        }
        UnifiedorderRequest unifiedorderRequest = getUnifiedorderRequest();
        ScanpayRequest scanpayRequest = getScanpayRequest();
        if (Objects.nonNull(payBo.getAppletType()) && payBo.getAppletType() == 2) {
            unifiedorderRequest.setStoreId("80380113");
        }
        // 备注
        unifiedorderRequest.setMemo("单个小说单章节付费");
        scanpayRequest.setMemo("单个小说单章节付费");
        //第三方支付单号
        String posSeq = NumberUtil.getOrderNo(OrderConstants.BUY_SURA_NOVEL_PREFIX);
        // 唯一订单号
        unifiedorderRequest.setPosSeq(posSeq);
        scanpayRequest.setPosSeq(posSeq);
        Unifiedorder unifiedOrder = getUnifiedorder(payBo.getUserId());
        Scanpay scanpay = getScanpay();
        // 总金额
        unifiedOrder.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getAmount().abs()));
        scanpay.setTxAmt(BigDecimalUtils.yuanToFen(payBo.getAmount().abs()));
        // 不分账 -- R实时分账 --D延时分账
        ExtendParams extendParams = new ExtendParams();
        extendParams.setSplitFlag(ExtendParams.N);
        unifiedOrder.setExtendParams(extendParams);
        // 设置请求参数
        unifiedorderRequest.setUnifiedorderRequest(unifiedOrder);
        scanpayRequest.setScanpayRequest(scanpay);

        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = buildRecord(payBo);
        recordBo.parentConsumeCode(String.valueOf(payBo.getParentConsumeCode()));
        recordBo.type(SohuTradeRecordEnum.Type.NOVEL.getCode());
        recordBo.consumeType(SohuTradeRecordEnum.Type.BUY_SURA_NOVEL.getCode());
        recordBo.consumeCode(Objects.requireNonNull(masterId).toString());
        recordBo.msg(SohuTradeRecordEnum.Type.BUY_SURA_NOVEL.getMsg());
        recordBo.amountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
        recordBo.payNumber(posSeq);
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        recordBo.port(payBo.getPort());
        // 保存流水记录 购买者 - 钱 - 支出
        saveTradeRecord(recordBo.build());

        // 保存主支付单
        saveMasterPayOrder(payBo, posSeq, PayStatus.WaitPay.name());
        // 保存子支付单
        savePayOrder(payBo, OrderConstants.ORDER_PREFIX_PLATFORM + posSeq, posSeq, PayStatus.WaitPay.name());

        // 加入自动未支付自动取消队列
        MqMessaging mqCancelMessaging = new MqMessaging(JSONObject.toJSONString(recordBo.build()), MqKeyEnum.CANCEL_NOVEL_ORDER.getKey());
        remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 16L);
        log.info("已发送延时取消队列-cancel_novel_order:{}", JSONObject.toJSONString(recordBo.build()));

        if (StrUtil.equalsAnyIgnoreCase(payBo.getPayChannel(), Constants.CHANNEL_PC)) {
            log.info("翼码支付 - PC-小说单章节付费支付请求request：{}", JSONUtil.toJsonStr(scanpayRequest));
            ScanpayResponse response = Client.getClient().execute(scanpayRequest);
            log.info("翼码支付 - PC-小说单章节付费支付请求response返回：{}", JSONUtil.toJsonStr(response));
            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
            return JSONUtil.toJsonStr(response);
        }
        log.info("翼码支付 -MOBILE- 小说单章节付费支付请求request：{}", JSONUtil.toJsonStr(unifiedorderRequest));
        UnifiedorderResponse response = Client.getClient().execute(unifiedorderRequest);
        log.info("翼码支付 -MOBILE- 小说单章节付费支付请求response返回：{}", JSONUtil.toJsonStr(response));
        RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(response), Duration.ofMinutes(PaymentStrategy.PAY_TIME_OUT));
        return JSONUtil.toJsonStr(response);

    }
}
