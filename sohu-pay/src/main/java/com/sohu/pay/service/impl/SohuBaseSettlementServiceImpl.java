package com.sohu.pay.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.sohu.busyorder.api.RemoteBusyTaskNoticeService;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.RemoteBusyTaskSiteService;
import com.sohu.busyorder.api.bo.SohuBusyTaskPayBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskRefundBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.vo.SohuBusyTaskPayVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.utils.*;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.noticeContent.NoticeContentDetail;
import com.sohu.im.api.noticeContent.NoticeSystemContent;
import com.sohu.middle.api.bo.SohuBillRecordBo;
import com.sohu.middle.api.bo.SohuMaterialPromotionOrderCmdBo;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.notice.RemoteMiddleSystemNoticeService;
import com.sohu.middle.api.vo.*;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.bo.DelayConfirmqueryBo;
import com.sohu.pay.api.bo.SohuIndependentOrderBo;
import com.sohu.pay.api.bo.SohuWithdrawBo;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.model.SohuSplitInfoModel;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.pay.domain.SohuIndependentLevelConfig;
import com.sohu.pay.service.*;
import com.sohu.shoporder.api.bo.SohuIndependentTempBo;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.RemoteSysRoleService;
import com.sohu.system.api.RemoteUserService;
import com.sohu.system.api.domain.SysDictData;
import com.sohu.third.wechat.pay.constant.WechatPayConstants;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.model.SplitInfo;
import com.wangcaio2o.ipossa.sdk.model.SplitList;
import com.wangcaio2o.ipossa.sdk.request.barcodecancel.BarcodeCancel;
import com.wangcaio2o.ipossa.sdk.request.barcodecancel.BarcodeCancelRequest;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverse;
import com.wangcaio2o.ipossa.sdk.request.barcodereverse.BarcodeReverseRequest;
import com.wangcaio2o.ipossa.sdk.request.delayconfirm.DelayConfirm;
import com.wangcaio2o.ipossa.sdk.request.delayconfirm.DelayConfirmRequest;
import com.wangcaio2o.ipossa.sdk.request.delayconfirmquery.DelayConfirmquery;
import com.wangcaio2o.ipossa.sdk.request.delayconfirmquery.DelayConfirmqueryRequest;
import com.wangcaio2o.ipossa.sdk.request.delayconfirmrefund.DelayConfirmrefund;
import com.wangcaio2o.ipossa.sdk.request.delayconfirmrefund.DelayConfirmrefundRequest;
import com.wangcaio2o.ipossa.sdk.request.merchantwithdraw.MerchantWithdraw;
import com.wangcaio2o.ipossa.sdk.request.merchantwithdraw.MerchantWithdrawRequest;
import com.wangcaio2o.ipossa.sdk.response.barcodecancel.BarcodeCancelResponse;
import com.wangcaio2o.ipossa.sdk.response.barcodereverse.BarcodeReverseResponse;
import com.wangcaio2o.ipossa.sdk.response.delayconfirm.DelayConfirmResponse;
import com.wangcaio2o.ipossa.sdk.response.delayconfirmquery.DelayConfirmqueryResponse;
import com.wangcaio2o.ipossa.sdk.response.delayconfirmrefund.DelayConfirmrefundResponse;
import com.wangcaio2o.ipossa.sdk.response.merchantwithdraw.MerchantWithdrawResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/6 12:05
 */
@Service
@Slf4j
public abstract class SohuBaseSettlementServiceImpl implements SohuBaseSettlementService {

    protected static final String TASK_SETTLE_EXECUTE = "task_settle_execute:";
    protected static final Long PLATFORM_USER_ID = 2L;

    @Resource
    private AsyncConfig asyncConfig;

    @Resource
    protected ISohuIndependentOrderService sohuIndependentOrderService;
    @Resource
    protected ISohuAccountBankService accountBankService;
    @Resource
    protected ISohuAccountService sohuAccountService;
    @Resource
    protected ISohuIndependentLevelConfigService levelConfigService;
    @Resource
    protected ISohuIndependentTemplateService templateService;
    @DubboReference
    protected RemoteMiddleBillRecordService remoteMiddleBillRecordService;
    @DubboReference
    protected RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    protected RemoteMiddleIndependentMaterialService independentMaterialService;
    @DubboReference
    protected RemoteBusyTaskSiteService remoteBusyTaskSiteService;
    @DubboReference
    protected RemoteUserService remoteUserService;
    @DubboReference
    protected RemoteDictService remoteDictService;
    @DubboReference
    protected RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    protected RemoteMiddleInviteService remoteMiddleInviteService;
    @DubboReference
    protected RemoteStreamMqService remoteStreamMqService;
    @DubboReference
    protected RemoteMiddleMaterialPromotionOrderService materialPromotionOrderService;
    @DubboReference
    protected RemoteBusyTaskReceiveService remoteBusyTaskReceiveService;
    @DubboReference
    protected RemoteBusyTaskNoticeService remoteBusyTaskNoticeService;
    @DubboReference
    protected RemoteMiddleUserBehaviorRecordService remoteMiddleUserBehaviorRecordService;
    @DubboReference
    private RemoteMiddleSystemNoticeService remoteMiddleSystemNoticeService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemotePlatformIndustryService remotePlatformIndustryService;
    @DubboReference
    private RemoteSysRoleService remoteSysRoleService;
    @DubboReference
    private RemoteBusyBlackService remoteBusyBlackService;

    public Boolean delayConfirmquery(String posSeq, String taskNumber, Boolean isSuccess, String busyType) {
        //避免重复提交
        if (!RedisUtils.setObjectIfAbsent(TASK_SETTLE_EXECUTE + taskNumber + ":" + posSeq, 1, Duration.ofSeconds(10))) {
            throw new RuntimeException("正在处理中，请稍后再试");
        }
        List<SohuIndependentOrderVo> independentOrderList = new ArrayList<>();
        if (!isSuccess) {
            DelayConfirmqueryResponse response = sendConfirmquery(posSeq);
            //判断yi-ma延时分账状态
            List<String> resultList = Lists.newArrayList();
            resultList.add("0000");
            if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
                //延时队列5s后查询分账是否成功-分账成功修改分账订单记录表状态,客户已绑卡，发起提现流程
                sendConfirmqueryMQ(posSeq, taskNumber, isSuccess, busyType, 2L);
            }
            //查询分账订单记录表
            independentOrderList = sohuIndependentOrderService.queryListByTradeNo(posSeq, IndependentStatusEnum.DISTRIBUTING.getCode());
            if (CollUtil.isEmpty(independentOrderList)) {
                log.info("延时交易确认查询未查询到分账订单记录信息......");
                return true;
            }
            // 更新账单表订单状态
            sohuIndependentOrderService.updateIndependentStatusByTradeNo(posSeq, IndependentStatusEnum.DISTRIBUTED.getCode());
        } else {
            independentOrderList = sohuIndependentOrderService.queryListByTradeNo(posSeq, IndependentStatusEnum.DISTRIBUTED.getCode());
        }
        //判断用户是否绑定银行卡，若绑定发起提现流程
        List<Long> userIds = independentOrderList.stream().map(SohuIndependentOrderVo::getUserId).collect(Collectors.toList());
        List<SohuAccountBankVo> sohuAccountBankList = accountBankService.queryListByUserId(userIds);
        if (CollUtil.isEmpty(sohuAccountBankList)) {
            log.info("延时交易确认查询未查询到银行卡信息......");
            return true;
        }
        Map<Long, Long> userIdMap = sohuAccountBankList.stream().collect(Collectors.toMap(SohuAccountBankVo::getUserId, SohuAccountBankVo::getId));
        List<String> validObject = List.of(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
        for (SohuIndependentOrderVo item : independentOrderList) {
            if (validObject.contains(item.getIndependentObject()) && userIdMap.get(item.getUserId()) != null) {
                //判断该任务单是否存在提现记录
//                SohuBillRecordVo billRecord = remoteMiddleBillRecordService.queryByTaskNumber(item.getTaskNumber(), item.getUserId());
//                if (billRecord != null) {
//                    continue;
//                }
                this.handleWithdrawal(item.getUserId(), item.getIndependentObject(), item.getIndependentPrice(), item.getTaskNumber(), item.getSiteType(), item.getSiteId());
            }
        }
        return true;
    }

    public void checkPayPassword(Long userId, String password) {
        //校验支付密码
        LoginUser user = remoteUserService.queryById(userId);
        String payPassword = user.getPayPassword();
        if (!BCrypt.checkpw(password, payPassword)) {
            throw new RuntimeException("密码错误");
        }
    }

    public void checkPayPasswordV2(Long userId, String password) {
        String newPassword = SecretUtil.desEncrypt(password, SecretUtil.KEY);
        if (StrUtil.isEmpty(newPassword)) {
            throw new RuntimeException("密码解析失败");
        }
        //校验支付密码
        LoginUser user = remoteUserService.queryById(userId);
        String payPassword = user.getPayPassword();
        if (!BCrypt.checkpw(newPassword, payPassword)) {
            throw new RuntimeException("密码错误");
        }
    }

    /**
     * 查询商单未分账剩余金额
     *
     * @param distributorAmount
     * @param independentOrderList
     * @return
     */
    protected BigDecimal getBalance(BigDecimal distributorAmount, List<SohuIndependentOrderVo> independentOrderList) {
        BigDecimal totalIndependentPrice = getTotalIndepentOrderPrice(independentOrderList);
        return distributorAmount.subtract(totalIndependentPrice);
    }

    /**
     * 查询商单分销佣金剩余金额
     *
     * @param shareDistributorBalance
     * @param independentOrderList
     * @return
     */
    protected BigDecimal getShareBalance(BigDecimal shareDistributorBalance, List<SohuIndependentOrderVo> independentOrderList) {
        BigDecimal shareBalance = BigDecimal.ZERO;
        if (shareDistributorBalance.compareTo(BigDecimal.ZERO) > 0) {
            List<String> distributionObject = List.of(SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
            shareBalance = CalUtils.sub(shareDistributorBalance, independentOrderList.stream().filter(item -> distributionObject.contains(item.getIndependentObject()))
                    .map(SohuIndependentOrderVo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return shareBalance;
    }

    /**
     * 查询商单分账金额之和
     *
     * @param independentOrderList
     * @return
     */
    protected BigDecimal getTotalIndepentOrderPrice(List<SohuIndependentOrderVo> independentOrderList) {
        BigDecimal totalIndependentPrice = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(independentOrderList)) {
            totalIndependentPrice = independentOrderList.stream().filter(vo -> vo.getIndependentObject().equals(SohuIndependentObject.rece.getKey()))
                    .map(SohuIndependentOrderVo::getIndependentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return totalIndependentPrice;
    }

    /**
     * 获取分销记录流水
     *
     * @param taskNumber
     * @return
     */
    protected List<SohuIndependentOrderVo> getIndependentOrderList(String taskNumber) {
        List<SohuIndependentOrderVo> independentOrderList = sohuIndependentOrderService.queryListByOrderNo(taskNumber);
        return independentOrderList;
    }

    /**
     * 获取翼码支付配置
     *
     * @return
     */
    protected YiMaPayConfig getYiMaPayConfig() {
        SysDictData dictData = remoteDictService.getDictData(DictEnum.payConfig.getKey(), DictEnum.YMPayConfig.getKey());
        Objects.requireNonNull(dictData, "翼码支付配置为空");
        String dictValue = dictData.getDictValue();
        return JSONUtil.toBean(dictValue, YiMaPayConfig.class);
    }

    /**
     * 封装翼码账户信息
     *
     * @param splitInfoList
     */
    protected void setMechantInfo(List<SohuSplitInfoModel> splitInfoList) {
        List<Long> userIds = splitInfoList.stream().map(SohuSplitInfoModel::getUserId).collect(Collectors.toList());
        //查询用户的翼码账户
        List<SohuAccountBankVo> sohuAccountVos = accountBankService.queryPassListByUserId(userIds);
        Map<Long, String> merchantMap = sohuAccountVos.stream().collect(Collectors.toMap(SohuAccountBankVo::getUserId, SohuAccountBankVo::getMerchantId));
        if (CollUtil.isEmpty(sohuAccountVos)) {
            throw new RuntimeException("未查询到分账用户绑定银行卡记录");
        }
        splitInfoList.forEach(item -> {
            item.setMerchantId(merchantMap.get(item.getUserId()));
        });
    }


    protected Long getInviteId(Long userId) {
        SohuIndependentLevelConfig levelConfig = levelConfigService.queryByStatusAndSiteId(Constants.ONE, null);
        Integer level = ObjectUtils.isNull(levelConfig) ? levelConfig.getLevel() : Constants.ONE;
        //查询是否有拉新人-上级拉新人
        Long regUserId = remoteMiddleInviteService.selectByInviteCount(userId, level);
        return regUserId;
    }

    /**
     * 分账
     *
     * @param posSeq     流水号
     * @param payNumber  支付流水号
     * @param mergedList 分账对象信息
     */
    protected Boolean distribution(String posSeq, String payNumber, List<SohuSplitInfoModel> mergedList, String keepAmt) {
        // 获取翼码支付配置
        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
        // 延时分账请求参数对象
        DelayConfirmRequest delayConfirmRequest = new DelayConfirmRequest();
        delayConfirmRequest.setPosId(yiMaPayConfig.getPosId());
        delayConfirmRequest.setIsspid(yiMaPayConfig.getIssPid());
        delayConfirmRequest.setSystemId(yiMaPayConfig.getSystemId());
        delayConfirmRequest.setStoreId(yiMaPayConfig.getStoreId());
        delayConfirmRequest.setPosSeq(posSeq);
        delayConfirmRequest.setMemo("流量商单支付酬金");
        DelayConfirm delayConfirm = new DelayConfirm();
        delayConfirm.setOrgPosSeq(payNumber);
        ExtendParams extendParams = new ExtendParams();
        // 分账信息
        SplitInfo splitInfo = new SplitInfo();
        splitInfo.setKeepAmt(keepAmt);
        if (CollUtil.isNotEmpty(mergedList)) {
            List<SplitList> splitLists = Lists.newArrayList();
            //累加同一商户分账金额
            Map<String, BigDecimal> result = mergedList.stream()
                    .collect(Collectors.groupingBy(SohuSplitInfoModel::getMerchantId,
                            Collectors.mapping(s -> new BigDecimal(s.getDivAmt()), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            result.forEach((key, value) -> {
                SplitList splitList = new SplitList();
                splitList.setMerchantId(key);
                splitList.setDivAmt(value.toString());
                splitLists.add(splitList);
            });
            splitInfo.setSplitList(splitLists);
        }
        extendParams.setSplitInfo(splitInfo);
        delayConfirm.setExtendParams(extendParams);
        delayConfirmRequest.setDelayConfirmRequest(delayConfirm);
        log.info("确认分账请求request={}", JSONObject.toJSONString(delayConfirmRequest));
        DelayConfirmResponse delayConfirmResponse = Client.getClient().execute(delayConfirmRequest);
        log.info("确认分账请求response={}", JSONObject.toJSONString(delayConfirmResponse));
        //判断yi-ma延时分账状态
        List<String> resultList = Lists.newArrayList();
        resultList.add("9998");
        resultList.add("0000");
        if (ObjectUtils.isNull(delayConfirmResponse) || !resultList.contains(delayConfirmResponse.getResult().getId())) {
            log.error("yi-ma延时分账异常{}", delayConfirmResponse.getResult().getComment());
            if (delayConfirmResponse.getResult().getComment().equals("交易失败：50202-未找到支付订单")) {
                throw new RuntimeException("回账正在处理中，请稍后结算");
            } else {
                throw new RuntimeException(delayConfirmResponse.getResult().getComment());
            }
        }
        if (delayConfirmResponse.getResult().getId().equals("0000")) {
            return true;
        }
        return false;
    }

    /**
     * 接单人分账对象
     *
     * @param receiveDistributorAmount 分账金额
     * @param receiveUserId            接单人Id
     * @param splitInfoList            分账对象
     * @param receiveId                接单记录Id
     * @param childTaskNumber          子任务Id
     */
    protected void receiveSplitInfo(BigDecimal receiveDistributorAmount, Long receiveUserId, List<SohuSplitInfoModel> splitInfoList, Long receiveId, String childTaskNumber, String posSeq) {
        SohuSplitInfoModel receiveSplit = new SohuSplitInfoModel();
        receiveSplit.setDivAmt(String.valueOf(CalUtils.yuanToCent(receiveDistributorAmount).intValue()));
        receiveSplit.setAmount(receiveDistributorAmount);
        receiveSplit.setIndependentObject(SohuIndependentObject.rece.getKey());
        receiveSplit.setUserId(receiveUserId);
        receiveSplit.setReceiveId(receiveId);
        receiveSplit.setChildTaskNumber(childTaskNumber);
        receiveSplit.setPosSeq(posSeq);
        splitInfoList.add(receiveSplit);
    }

    /**
     * 接单人拉新人分账对象
     *
     * @param receiveInviteId            接单人拉新人Id
     * @param receiveInviteAllAmount     接单人拉新人分账总额
     * @param receiveInviteBalanceAmount 接单人拉新人分账余额
     * @param receiveInviteRadio         接单人拉新人分账比例
     * @param splitInfoList              分账对象
     * @param receiveId                  接单记录Id
     * @param childTaskNumber            子任务Id
     * @return
     */
    protected BigDecimal receiveInviteSplitInfo(Long receiveInviteId, BigDecimal receiveInviteAllAmount, BigDecimal receiveInviteBalanceAmount, BigDecimal receiveInviteRadio, List<SohuSplitInfoModel> splitInfoList, Long receiveId, String childTaskNumber, String posSeq, Date createTime) {
        BigDecimal receiveInviteAmount = BigDecimal.ZERO;
        if (receiveInviteId != null) {
            receiveInviteAmount = CalUtils.multiply(receiveInviteAllAmount, CalUtils.divide(receiveInviteRadio, CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
            if (receiveInviteBalanceAmount.compareTo(receiveInviteAmount) < 0) {
                receiveInviteAmount = receiveInviteBalanceAmount;
            }
        }
        //判断当前账号是否已冻结，冻结不参与分账，分账金额给与平台
        Map<Long, Boolean> map = sohuAccountService.afterFreezeMap(Arrays.asList(receiveInviteId), createTime);
        boolean isFreeze = false;
        if (map.get(receiveInviteId) != null) {
            isFreeze = map.get(receiveInviteId);
        }
        if (receiveInviteId != null && receiveInviteAmount.compareTo(BigDecimal.ZERO) > 0) {
            SohuSplitInfoModel receiveInviteSplit = new SohuSplitInfoModel();
            receiveInviteSplit.setDivAmt(String.valueOf(CalUtils.yuanToCent(receiveInviteAmount).intValue()));
            receiveInviteSplit.setAmount(receiveInviteAmount);
            receiveInviteSplit.setIndependentObject(SohuIndependentObject.invite.getKey());
            receiveInviteSplit.setUserId(isFreeze ? PLATFORM_USER_ID : receiveInviteId);
            receiveInviteSplit.setReceiveId(receiveId);
            receiveInviteSplit.setChildTaskNumber(childTaskNumber);
            receiveInviteSplit.setPosSeq(posSeq);
            splitInfoList.add(receiveInviteSplit);
        }
        return receiveInviteAmount;
    }

    /**
     * 分销人分账对象
     * * 1 分销商单且达标人数已达上限，1 存在上级拉新人 按照比例计算 2 不存在上级拉新人 独享分销佣金
     * * 2 分销商单且达标人数未达上限，无论是否存在上级拉新，都是按照比例计算
     *
     * @param isShare                是否分销
     * @param shareUserId            分销人Id
     * @param shareInviteId          分销人拉新人Id
     * @param shareDistributorAmount 分销金额
     * @param distributorRatio       分销比例
     * @param splitInfoList          分账对象
     * @param receiveId              接单记录Id
     * @param receiveUserId          接单人Id
     * @param childTaskNumber        子任务Id
     * @return
     */
    protected BigDecimal shareSplitInfo(Boolean isShare, Long shareUserId, Long shareInviteId, BigDecimal shareDistributorAmount, BigDecimal distributorRatio, List<SohuSplitInfoModel> splitInfoList, Long receiveId, Long receiveUserId, String childTaskNumber, String posSeq, Date createTime) {
        BigDecimal shareAmount = CalUtils.multiply(shareDistributorAmount, BigDecimalUtils.divide(distributorRatio, CalUtils.PERCENTAGE)).setScale(2, RoundingMode.HALF_UP);
        if (shareInviteId == null) {
            shareAmount = shareDistributorAmount;
        }
        //判断当前账号是否已冻结，冻结不参与分账，分账金额给与平台
        Map<Long, Boolean> map = sohuAccountService.afterFreezeMap(Arrays.asList(shareUserId), createTime);
        boolean isFreeze = false;
        if (map.get(shareUserId) != null) {
            isFreeze = map.get(shareUserId);
        }
        if (isShare && shareAmount.compareTo(BigDecimal.ZERO) > 0) {
            SohuSplitInfoModel shareSplit = new SohuSplitInfoModel();
            shareSplit.setDivAmt(String.valueOf(CalUtils.yuanToCent(shareAmount).intValue()));
            shareSplit.setAmount(shareAmount);
            shareSplit.setIndependentObject(SohuIndependentObject.distribution.getKey());
            shareSplit.setUserId(isFreeze ? PLATFORM_USER_ID : shareUserId);
            shareSplit.setReceiveId(receiveId);
            shareSplit.setChildTaskNumber(childTaskNumber);
            shareSplit.setReceiveUserId(receiveUserId);
            shareSplit.setPosSeq(posSeq);
            splitInfoList.add(shareSplit);
        }
        return shareAmount;
    }

    /**
     * 接单人拉新人分账对象
     *
     * @param shareInviteId            接单人拉新人Id
     * @param shareInviteAmount        接单人拉新人分账金额
     * @param shareInviteBalanceAmount 接单人拉新人分账余额
     * @param splitInfoList            分账对象
     * @param receiveId                接单记录Id
     * @param childTaskNumber          接单子任务Id
     * @return
     */
    protected BigDecimal shareInviteSplitInfo(Long shareInviteId, BigDecimal shareInviteAmount, BigDecimal shareInviteBalanceAmount, List<SohuSplitInfoModel> splitInfoList, Long receiveId, String childTaskNumber, String posSeq, Date createTime) {
        if (shareInviteId != null) {
            if (shareInviteBalanceAmount.compareTo(shareInviteAmount) < 0) {
                shareInviteAmount = shareInviteBalanceAmount;
            }
        }
        //判断当前账号是否已冻结，冻结不参与分账，分账金额给与平台
        Map<Long, Boolean> map = sohuAccountService.afterFreezeMap(Arrays.asList(shareInviteId), createTime);
        boolean isFreeze = false;
        if (map.get(shareInviteId) != null) {
            isFreeze = map.get(shareInviteId);
        }
        if (shareInviteId != null && shareInviteAmount.compareTo(BigDecimal.ZERO) > 0) {
            SohuSplitInfoModel shareInvateSplit = new SohuSplitInfoModel();
            shareInvateSplit.setDivAmt(String.valueOf(CalUtils.yuanToCent(shareInviteAmount).intValue()));
            shareInvateSplit.setAmount(shareInviteAmount);
            shareInvateSplit.setIndependentObject(SohuIndependentObject.distributionInvite.getKey());
            shareInvateSplit.setUserId(isFreeze ? PLATFORM_USER_ID : shareInviteId);
            shareInvateSplit.setReceiveId(receiveId);
            shareInvateSplit.setChildTaskNumber(childTaskNumber);
            shareInvateSplit.setPosSeq(posSeq);
            splitInfoList.add(shareInvateSplit);
        }
        return shareInviteAmount;
    }

    protected void accountPlatformSplitInfo(List<SohuIndependentTempBo> platformObjects, List<SohuSplitInfoModel> splitInfoList, Long receiveId, String childTaskNumber, String posSeq) {
        for (SohuIndependentTempBo platformObject : platformObjects) {
            SohuSplitInfoModel platformSplit = new SohuSplitInfoModel();
            platformSplit.setDivAmt(String.valueOf(CalUtils.yuanToCent(platformObject.getIndependentPrice()).intValue()));
            platformSplit.setAmount(platformObject.getIndependentPrice());
            platformSplit.setIndependentObject(platformObject.getIndependentObject());
            platformSplit.setUserId(platformObject.getUserId());
            platformSplit.setReceiveId(receiveId);
            platformSplit.setChildTaskNumber(childTaskNumber);
            platformSplit.setPosSeq(posSeq);
            platformSplit.setSiteId(platformObject.getSiteId());
            platformSplit.setSiteType(platformObject.getSiteType());
            splitInfoList.add(platformSplit);
        }
    }

    /**
     * 平台分账对象
     *
     * @param platformAmount 平台分账金额
     * @param splitInfoList  分账对象
     * @param receiveId      接单记录Id
     * @param posSeq         流水号
     */
    protected void platformSplitInfo(BigDecimal platformAmount, List<SohuSplitInfoModel> splitInfoList, Long receiveId, String posSeq) {
        SohuSplitInfoModel adminSplitList = new SohuSplitInfoModel();
        adminSplitList.setAmount(platformAmount);
        adminSplitList.setIndependentObject(SohuIndependentObject.platform.getKey());
        adminSplitList.setUserId(PLATFORM_USER_ID);
        adminSplitList.setPosSeq(posSeq);
        adminSplitList.setReceiveId(receiveId);
        splitInfoList.add(adminSplitList);
    }

    public Boolean handleWithdrawal(Long userId, String independentObject, BigDecimal amount, String taskNumber, Integer siteType, Long siteId) {
        String userRole = UserRoleEnum.PERSONAL.getType();
        //判断提现人的身份
        List<String> personRoles = Arrays.asList(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
        List<String> siteRoles = Arrays.asList(SohuIndependentObject.country.getKey(), SohuIndependentObject.city.getKey(), SohuIndependentObject.entrance.getKey(), SohuIndependentObject.industrysite.getKey(), SohuIndependentObject.invitecity.getKey());
        List<String> agencyRoles = Arrays.asList(SohuIndependentObject.agency.getKey());
        if (siteRoles.contains(independentObject)) {
            userRole = UserRoleEnum.CITYSTATION.getType();
        }
        if (agencyRoles.contains(independentObject)) {
            userRole = UserRoleEnum.AGENCY.getType();
        }
        //提现-记录流水
        SohuWithdrawBo withdrawBo = new SohuWithdrawBo();
        withdrawBo.setUserId(userId);
        withdrawBo.setWithdrawalUserType(userRole);
        withdrawBo.setWithdrawalAmount(amount);
        withdrawBo.setTaskNumber(taskNumber);
        withdrawBo.setSiteType(siteType);
        withdrawBo.setSiteId(siteId);
        accountBankService.withdrawal(withdrawBo);
        // 流水号
//        String tradeNo = NumberUtil.getOrderNo(OrderConstants.BANK_ORDER_PREFIX);
//        // 发起翼码提现
//        int totalPrice = CalUtils.yuanToCent(amount).intValue();
//        MerchantWithdrawResponse response = sendWithdrawal(userId, tradeNo, totalPrice, "提现");
//        String code = response.getTransStatus();
//        //如果提现失败，抛出错误信息
//        if (StringUtils.equals(code, OrderConstants.BANK_FAIL)) {
//            throw new RuntimeException(response.getErrorMsg());
//        }
//        //查询该用户的银行卡号
//        SohuAccountBankVo accountBank = accountBankService.queryListByUserId(List.of(userId)).get(0);
//        // 账单流水记录-发单方余额支出记录
//        SohuBillRecordBo billRecordDO = new SohuBillRecordBo();
//        billRecordDO.setPayerId(userId);
//        billRecordDO.setPayeeId(0L);
//        billRecordDO.setAmount(amount);
//        billRecordDO.setTransactionType(SohuTransactionTypeEnum.withdrawal.name());
//        billRecordDO.setBusyCode(0L);
//        billRecordDO.setTitle("提现");
//        billRecordDO.setState(PayStatus.Processing.name());
//        billRecordDO.setBillType(SohuBillType.Balance.name());
//        billRecordDO.setTradeNo(tradeNo);
//        billRecordDO.setUserId(userId);
//        billRecordDO.setTaskNumber(taskNumber);
//        billRecordDO.setWalletBalance(BigDecimal.ZERO);
//        billRecordDO.setPayType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus());
//        billRecordDO.setWithdrawId(response.getWithdrawSeq());
//        billRecordDO.setWithdrawTime(new Date());
//        billRecordDO.setWithdrawBankNo(accountBank.getCardNo());
//        billRecordDO.setWithdrawBankName(accountBank.getParentBankName());
//        billRecordDO.setSiteType(siteType);
//        billRecordDO.setSiteId(siteId);
//        billRecordDO.setWithdrawalUserType(userRole);
//        remoteMiddleBillRecordService.insertByBo(billRecordDO);
//        //计入用户流水明细表
//        SohuTradeRecordBo tradeRecordDO = SohuTradeRecordBo.builder().build();
//        tradeRecordDO.setUserId(userId);
//        tradeRecordDO.setType("Withdrawal");
//        tradeRecordDO.setAmount(amount);
//        tradeRecordDO.setConsumeType("Withdrawal");
//        tradeRecordDO.setConsumeCode(tradeNo);
//        tradeRecordDO.setMsg("翼码余额提现");
//        tradeRecordDO.setAmountType(SohuTradeRecordEnum.AmountType.Expend.getCode());
//        tradeRecordDO.setPayType(SohuBillType.Balance.name());
//        tradeRecordDO.setPayStatus(PayStatus.Paid.name());
//        tradeRecordDO.setAccountType(SohuTradeRecordEnum.AccountType.Amount.name());
//        tradeRecordDO.setPayNumber(tradeNo);
//        tradeRecordDO.setUnq(System.nanoTime() + RandomUtils.randomString(3));
//        tradeRecordDO.setOperateChannel(Constants.CHANNEL_MOBILE);
//        tradeRecordDO.setPayTime(new Date());
//        tradeRecordDO.setSiteId(siteId);
//        tradeRecordDO.setSiteType(siteType);
//        remoteMiddleTradeRecordService.insertByBo(tradeRecordDO);
//        //发送提现成功通知
//        CompletableFuture.runAsync(() -> {
//            NoticeSystemContent content = new NoticeSystemContent();
//            content.setTitle(SystemNoticeEnum.withdraw);
//            content.setNoticeTime(DateUtils.getTime());
//            content.setType(SystemNoticeEnum.SubType.withdrawPass.name());
//            content.setDetailId(userId);
//            NoticeContentDetail detail = new NoticeContentDetail();
//            detail.setUserId(userId);
//            detail.setDesc(SystemNoticeEnum.withdrawDesc);
//            content.setContent(detail);
//            String contentJson = JSONUtil.toJsonStr(content);
//            remoteMiddleSystemNoticeService.sendAdminNotice(userId, SystemNoticeEnum.withdraw, contentJson, SystemNoticeEnum.Type.withdraw);
//        }, asyncConfig.getAsyncExecutor());
        return true;
    }

    /**
     * 提现
     *
     * @param userId
     * @param posSeq
     * @param amount
     * @param remark
     * @return
     */
    protected MerchantWithdrawResponse sendWithdrawal(Long userId, String posSeq, int amount, String remark) {
        SohuAccountVo accountVo = sohuAccountService.queryByUserIdOfPass(userId);
        if (ObjectUtil.isEmpty(accountVo)) {
            throw new RuntimeException(MessageUtils.message("WRONG_INFO"));
        }
        MerchantWithdrawRequest request = new MerchantWithdrawRequest();
        MerchantWithdraw withdraw = new MerchantWithdraw();
        withdraw.setMerchantId(accountVo.getMerchantId());
        withdraw.setAmount(amount);
        withdraw.setRemark(remark);
        request.setPosSeq(posSeq);
        request.setMerchantWithdrawRequest(withdraw);
        log.info("提现请求request={}", JSONObject.toJSONString(request));
        MerchantWithdrawResponse response = Client.getClient().execute(request);
        log.info("提现请求返回response={}", JSONObject.toJSONString(response));
        return response;
    }

    /**
     * 延时交易确认查询
     *
     * @param posSeq
     * @return
     */
    protected DelayConfirmqueryResponse sendConfirmquery(String posSeq) {
        //获取翼码支付配置
        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
        // 支付参数组装
        DelayConfirmqueryRequest confirmqueryRequest = new DelayConfirmqueryRequest();
        confirmqueryRequest.setPosId(yiMaPayConfig.getPosId());
        confirmqueryRequest.setIsspid(yiMaPayConfig.getIssPid());
        confirmqueryRequest.setSystemId(yiMaPayConfig.getSystemId());
        confirmqueryRequest.setStoreId(yiMaPayConfig.getStoreId());
        confirmqueryRequest.setPosSeq(NumberUtil.getOrderNo("QR"));
        DelayConfirmquery confirmquery = new DelayConfirmquery();
        confirmquery.setOrgPosSeq(posSeq);
        confirmqueryRequest.setDelayConfirmqueryRequest(confirmquery);
        log.info("延时交易确认查询request={}", JSONObject.toJSONString(confirmqueryRequest));
        DelayConfirmqueryResponse response = Client.getClient().execute(confirmqueryRequest);
        log.info("延时交易确认查询response返回={}", JSONObject.toJSONString(response));
        return response;
    }


    /**
     * 发送延时交易确认退回请求
     *
     * @param posSeq
     */
    protected void sendConfirmrefund(String posSeq) {
        // 获取翼码支付配置
        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
        DelayConfirmrefundRequest confirmrefundRequest = new DelayConfirmrefundRequest();
        confirmrefundRequest.setPosId(yiMaPayConfig.getPosId());
        confirmrefundRequest.setIsspid(yiMaPayConfig.getIssPid());
        confirmrefundRequest.setSystemId(yiMaPayConfig.getSystemId());
        confirmrefundRequest.setStoreId(yiMaPayConfig.getStoreId());
        confirmrefundRequest.setPosSeq(NumberUtil.getOrderNo("TH"));
        DelayConfirmrefund confirmrefund = new DelayConfirmrefund();
        ExtendParams extendParams = new ExtendParams();
        confirmrefund.setExtendParams(extendParams);
        confirmrefund.setOrgPosSeq(posSeq);
        confirmrefundRequest.setDelayConfirmrefundRequest(confirmrefund);
        log.info("延时交易确认退回请求request={}", JSONObject.toJSONString(confirmrefundRequest));
        DelayConfirmrefundResponse response = Client.getClient().execute(confirmrefundRequest);
        log.info("延时交易确认退回请求response返回={}", JSONObject.toJSONString(response));
    }


    /**
     * 发送退款请求
     *
     * @param refundNumber
     * @param totalPrice
     * @param payNumber
     */
    protected void sendRefund(String refundNumber, Integer totalPrice, String payNumber) {
        // 获取翼码支付配置
        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
        //翼码退款
        BarcodeReverseRequest barcodeReverseRequest = new BarcodeReverseRequest();
        barcodeReverseRequest.setPosId(yiMaPayConfig.getPosId());
        barcodeReverseRequest.setIsspid(yiMaPayConfig.getIssPid());
        barcodeReverseRequest.setSystemId(yiMaPayConfig.getSystemId());
        barcodeReverseRequest.setStoreId(yiMaPayConfig.getStoreId());
        barcodeReverseRequest.setPosSeq(refundNumber);
        BarcodeReverse barcodeReverse = new BarcodeReverse();
        barcodeReverse.setTxAmt(totalPrice);
        barcodeReverse.setOrgPosSeq(payNumber);
        barcodeReverseRequest.setBarcodeReverseRequest(barcodeReverse);
        log.info("退款请求request={}", JSONObject.toJSONString(barcodeReverseRequest));
        BarcodeReverseResponse barcodeReverseResponse = Client.getClient().execute(barcodeReverseRequest);
        log.info("退款请求response返回={}", JSONObject.toJSONString(barcodeReverseResponse));
        List<String> resultList = com.google.common.collect.Lists.newArrayList("9998", "0000");
        if (ObjectUtils.isNull(barcodeReverseResponse) || !resultList.contains(barcodeReverseResponse.getResult().getId())) {
            log.error("流量商单退款异常：{}", JSONUtil.toJsonStr(barcodeReverseResponse));
            throw new RuntimeException(barcodeReverseResponse.getResult().getComment());
        }
    }

    /**
     * 撤销交易
     *
     * @param posSeq
     */
    protected void sendBarcodeCancel(String posSeq) {
        //获取翼码支付配置
        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
        // 支付参数组装
        BarcodeCancelRequest cancelRequest = new BarcodeCancelRequest();
        cancelRequest.setPosId(yiMaPayConfig.getPosId());
        cancelRequest.setIsspid(yiMaPayConfig.getIssPid());
        cancelRequest.setSystemId(yiMaPayConfig.getSystemId());
        cancelRequest.setStoreId(yiMaPayConfig.getStoreId());
        cancelRequest.setPosSeq("C" + System.nanoTime());
        BarcodeCancel cancel = new BarcodeCancel();
        cancel.setOrgPosSeq(posSeq);
        cancelRequest.setBarcodeCancelRequest(cancel);
        log.info("撤销交易request={}", JSONObject.toJSONString(cancelRequest));
        BarcodeCancelResponse barcodeCancelResponse = Client.getClient().execute(cancelRequest);
        log.info("撤销交易请求response返回={}", JSONObject.toJSONString(barcodeCancelResponse));
    }

    protected void saveIndependentOrder(String taskNumber, List<SohuSplitInfoModel> splitList, Boolean success, List<SohuIndependentOrderBo> boList, String title, BigDecimal fullAmount,Long consumerUserId) {
        splitList.forEach(vo -> {
            SohuIndependentOrderBo independentOrderBo = new SohuIndependentOrderBo();
            independentOrderBo.setOrderNo(taskNumber);
            independentOrderBo.setTradeType(BusyType.BusyOrder.name());
            independentOrderBo.setIndependentStatus(success ? IndependentStatusEnum.DISTRIBUTED.getCode() : IndependentStatusEnum.DISTRIBUTING.getCode());
            independentOrderBo.setIndependentObject(vo.getIndependentObject());
            independentOrderBo.setTradeNo(vo.getPosSeq());
            independentOrderBo.setUserId(vo.getUserId());
            independentOrderBo.setIndependentPrice(vo.getAmount());
            independentOrderBo.setTaskNumber(vo.getChildTaskNumber());
            independentOrderBo.setTaskTitle(title);
            independentOrderBo.setTaskFullAmount(fullAmount);
            independentOrderBo.setSiteId(vo.getSiteId());
            independentOrderBo.setSiteType(vo.getSiteType());
            independentOrderBo.setConsumerUserId(consumerUserId);
            boList.add(independentOrderBo);
        });
    }

    protected void saveTradeRecord(String taskNumber, String payChannel, List<SohuSplitInfoModel> splitList, List<SohuTradeRecordBo> boList, String busyType) {
        splitList.forEach(vo -> {
            SohuTradeRecordBo tradeRecordBo = SohuTradeRecordBo.builder().
                    userId(vo.getUserId()).
                    type(busyType).
                    consumeType(busyType).
                    consumeCode(vo.getReceiveId() == null ? "0" : vo.getReceiveId().toString()).
                    amount(vo.getAmount()).
                    amountType(SohuTradeRecordEnum.AmountType.InCome.getCode()).
                    payType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus()).
                    operateChannel(payChannel).
                    payNumber(taskNumber).
                    payStatus(PayStatus.Paid.name()).
                    msg("流量商单支付酬金").
                    independent(true).
                    independentObject(vo.getIndependentObject()).
                    independentStatus(IndependentStatusEnum.DISTRIBUTED.getCode()).
                    payTime(new Date()).
                    accountType(SohuTradeRecordEnum.AccountType.Amount.name()).
                    templateType(2).
                    userType(1).
                    sourceType(1).
                    unq(System.nanoTime() + RandomUtils.randomString(3)).
                    siteId(vo.getSiteId()).
                    siteType(vo.getSiteType()).
                    build();
            boList.add(tradeRecordBo);
        });
    }

    protected void saveMaterialOrder(Long shareUserId, Long receiveUserId, String taskNumber, String transactionId, BigDecimal amount, SohuMaterialPromotionOrderCmdBo cmdBo, String busyType) {
        String mpOrderNo = NumberUtil.getOrderNo(OrderConstants.MATERIAL_PAY_PREFIX);
        SohuIndependentMaterialVo materialVo = independentMaterialService.queryByFlowTask(taskNumber, busyType);
        Objects.requireNonNull(materialVo, "分销素材不存在");
        //查询用户信息
        LoginUser user = remoteUserService.queryById(receiveUserId);
        cmdBo.setShareUserId(shareUserId);
        cmdBo.setBuyUserId(receiveUserId);
        cmdBo.setBuyUserName(user.getNickname());
        cmdBo.setMaterialId(materialVo.getId());
        cmdBo.setPayPrice(materialVo.getPrice());
        cmdBo.setMaterialType(busyType);
        cmdBo.setOrderNo(mpOrderNo);
        cmdBo.setTradeNo(taskNumber);
        cmdBo.setTransactionId(transactionId);
        cmdBo.setIndependentPrice(amount);
        cmdBo.setIndependentStatus(IndependentStatusEnum.DISTRIBUTED.getCode());
        cmdBo.setTradeType(BusyType.BusyTask.name());
    }

    /**
     * 保存退款记录
     *
     * @param taskNumber        主任务编号
     * @param refundNumber      退款编号
     * @param refundAmount      退款金额
     * @param sohuBusyTaskPayVo 支付对象
     * @param busyType          退款类型
     * @param msg               退款信息
     */
    protected void saveRefundRecord(String taskNumber, String refundNumber, BigDecimal refundAmount, SohuBusyTaskPayVo sohuBusyTaskPayVo, String busyType, String msg, String refundReason) {
        //新增退款记录
        SohuBusyTaskRefundBo refundBo = new SohuBusyTaskRefundBo();
        refundBo.setTaskNumber(taskNumber);
        refundBo.setRefundOrderNo(refundNumber);
        refundBo.setRefundNumber(refundNumber);
        refundBo.setTransactionId(sohuBusyTaskPayVo.getTransactionId());
        refundBo.setRefundAmount(refundAmount);
        refundBo.setRefundStatus(WechatPayConstants.RefundStatus.SUCCESS);
        refundBo.setUserId(sohuBusyTaskPayVo.getUserId());
        refundBo.setRefundReason(refundReason);
        remoteBusyTaskService.saveBusyTaskRefund(refundBo);
        //更新支付表中退款信息
        SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
        payBo.setId(sohuBusyTaskPayVo.getId());
        payBo.setRefundAmount(CalUtils.add(refundAmount, sohuBusyTaskPayVo.getRefundAmount()));
        payBo.setRefundId(refundNumber);
        if (refundAmount.compareTo(sohuBusyTaskPayVo.getPayAmount()) == 0 || CalUtils.add(refundAmount, sohuBusyTaskPayVo.getRefundAmount()).compareTo(sohuBusyTaskPayVo.getPayAmount()) == 0) {
            payBo.setPayStatus(PayStatus.Refund.name());
        }
        remoteBusyTaskService.updateBusyTaskPay(payBo);
        //新增退款流水
        SohuTradeRecordBo.SohuTradeRecordBoBuilder recordBo = SohuTradeRecordBo.builder();
        recordBo.userId(sohuBusyTaskPayVo.getUserId());
        recordBo.amount(refundAmount);
        recordBo.payType(sohuBusyTaskPayVo.getPayType());
        recordBo.payStatus(PayStatus.Refund.name());
        recordBo.operateChannel(sohuBusyTaskPayVo.getPayChannel());
        recordBo.type(busyType);
        recordBo.consumeType(busyType);
        recordBo.consumeCode("0");
        recordBo.msg(msg);
        recordBo.amountType(SohuTradeRecordEnum.AmountType.InCome.getCode());
        recordBo.payNumber(sohuBusyTaskPayVo.getTaskNumber());
        recordBo.accountType(SohuTradeRecordEnum.AccountType.Amount.name());
        recordBo.payTime(new Date());
        remoteMiddleTradeRecordService.insertByBo(recordBo.build());
    }

    /**
     * 发送延时队列查询分账是否成功
     *
     * @param posSeq          流水号
     * @param childTaskNumber 子单号
     * @param success         是否已分账成功
     */
    protected void sendConfirmqueryMQ(String posSeq, String childTaskNumber, Boolean success, String busyType, Long level) {
        //延时队列5s后查询分账是否成功-分账成功修改分账订单记录表状态,客户已绑卡，发起提现流程
        DelayConfirmqueryBo delayConfirmqueryBo = new DelayConfirmqueryBo(posSeq, childTaskNumber, success, busyType);
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(delayConfirmqueryBo), MqKeyEnum.DELAY_CONFIRMQUERY.getKey());
        remoteStreamMqService.sendDelayMsg(mqMessaging, level);
    }

    protected void syncSettleUserBehavior(Long userId, String nickName, SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute) {
        SohuUserBehaviorRecordPointBo pointBo = new SohuUserBehaviorRecordPointBo();
        pointBo.setUserId(userId);
        pointBo.setUserName(nickName);
        pointBo.setEventSign("sd_settlement_success");
        pointBo.setEventName("愿望结算成功");
        pointBo.setBusinessType(BehaviorBusinessTypeEnum.BUSY_ORDER.getCode());
        pointBo.setOperaType(OperaTypeEnum.ADD.getOperaType());
        pointBo.setOperaSource(Constants.ZERO);
        pointBo.setSourceType("HSS");
        pointBo.setRequestId(UUID.randomUUID().toString());
        pointBo.setEventAttribute(eventAttribute);
        remoteMiddleUserBehaviorRecordService.addList(Arrays.asList(pointBo));
    }
}
