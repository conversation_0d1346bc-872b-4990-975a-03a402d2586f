package com.sohu.pay.service;

import com.sohu.pay.api.bo.AccountDistributionBo;
import com.sohu.pay.api.bo.AccountPlatformBo;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.shoporder.api.bo.SohuIndependentTempBo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分账逻辑处理
 *
 * <AUTHOR>
 * @date 2025/6/3 19:57
 */
public interface AccountProcessService {
    /**
     * 获取平台分账对象集合
     *
     * @param busyType       业务类型
     * @param busyCode       业务Id
     * @param siteType       站点类型
     * @param entranceSiteId 入口站点Id
     * @param citySiteId     城市站点Id
     * @param industrySiteId 行业站点Id
     * @param payPrice       支付金额
     * @param chargeFee      分摊手续费
     * @return
     */
    List<SohuIndependentTempBo> accountPlatformObjects(AccountPlatformBo bo);

    /**
     * 获取分销分账对象
     *
     * @param bo
     * @return
     */
    List<SohuIndependentTempBo> accountDistributionObjects(AccountDistributionBo bo);
}
