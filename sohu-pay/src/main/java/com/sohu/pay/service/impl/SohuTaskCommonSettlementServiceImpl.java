package com.sohu.pay.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.sohu.busyorder.api.bo.SohuBusyTaskPayBo;
import com.sohu.busyorder.api.bo.SohuPayBusyBo;
import com.sohu.busyorder.api.bo.SohuRefundBusyBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.NumberUtil;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuMaterialPromotionOrderCmdBo;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.pay.api.bo.*;
import com.sohu.pay.api.enums.PaySceceTypeEnum;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.model.SohuSplitInfoModel;
import com.sohu.pay.api.vo.SohuIndependentOrderVo;
import com.sohu.pay.service.AccountProcessService;
import com.sohu.pay.service.ISohuIndependentTemplateService;
import com.sohu.pay.service.SohuTaskCommonSettlementService;
import com.sohu.shoporder.api.bo.SohuIndependentTempBo;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025/3/4 12:29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuTaskCommonSettlementServiceImpl extends SohuBaseSettlementServiceImpl implements SohuTaskCommonSettlementService {

    private final AsyncConfig asyncConfig;
    private final AccountProcessService accountProcessService;
    private final ISohuIndependentTemplateService independentTemplateService;

    @Override
    public Boolean settle(SohuPaySettlementBo bo) {
        Long receiveId = bo.getReceiveId();
        String password = bo.getPassword();
        //避免重复提交
        if (!RedisUtils.setObjectIfAbsent(TASK_SETTLE_EXECUTE + receiveId, 1, Duration.ofSeconds(3))) {
            throw new RuntimeException("请稍后再试");
        }
        //校验支付密码
        checkPayPassword(LoginHelper.getUserId(), password);
        return handleSettle(receiveId);
    }

    @Override
    public Boolean settleV2(SohuPaySettlementBo bo) {
        Long receiveId = bo.getReceiveId();
        String password = bo.getPassword();
        //避免重复提交
        if (!RedisUtils.setObjectIfAbsent(TASK_SETTLE_EXECUTE + receiveId, 1, Duration.ofSeconds(3))) {
            throw new RuntimeException("请稍后再试");
        }
        //校验支付密码
        checkPayPasswordV2(LoginHelper.getUserId(), password);
        return handleSettle(receiveId);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean batchSettle(SohuPayBatchSettlementBo bo) {
        String taskNumber = bo.getTaskNumber();
        //查询支付订单
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.Paid.name());
        if (CollUtil.isEmpty(payList)) {
            throw new RuntimeException("该商单不存在已支付记录");
        }
        SohuBusyTaskVo busyTaskVo = remoteBusyTaskService.getByTaskNo(taskNumber);
        if (ObjectUtils.isNull(busyTaskVo)) {
            throw new RuntimeException("任务商单不存在");
        }
        //修改商单状态->已完结
        boolean b = remoteBusyTaskService.settleBusyTask(taskNumber);
        log.info("修改商单状态 待结算->已完结 {}", b);
        payList.forEach(payVo -> {
            //退款流水号
            String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
            sendRefund(refundNumber, CalUtils.yuanToCent(payVo.getPayAmount()).intValue(), payVo.getPayNumber());
            saveRefundRecord(taskNumber, refundNumber, payVo.getPayAmount(), payVo, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(), "通用商单-" + PaySceceTypeEnum.getByCode(payVo.getPaySceneType()).getDesc() + "退款", "平台终止商单");
            if (payVo.getPaySceneType().equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode())) {
                //发送终止通知 -> 发单方
                remoteBusyTaskNoticeService.sendTaskNotice(busyTaskVo.getId(), TaskNoticeEnum.TASK_STOP,
                        busyTaskVo.getUserId(), null, null, Boolean.TRUE);
                //发送终止通知 -> 接单方
                remoteBusyTaskNoticeService.sendTaskNotice(busyTaskVo.getId(), TaskNoticeEnum.TASK_STOP_RECEIVER,
                        payVo.getReceiveUserId(), null, null, Boolean.TRUE);
            }
        });
        return true;
    }

    @Override
    public Boolean batchSettleV2(SohuPayBatchSettlementBo bo) {
        return null;
    }

    @Override
    public Boolean delayConfirmquery(DelayConfirmqueryBo delayConfirmqueryBo) {
        return this.delayConfirmquery(delayConfirmqueryBo.getPosSeq(), delayConfirmqueryBo.getTaskNumber(), delayConfirmqueryBo.getSuscess(), delayConfirmqueryBo.getBusyType());
    }

    @Override
    public TableDataInfo<SohuPayBusyVo> payList(SohuPayBusyBo bo, PageQuery pageQuery) {
        return remoteBusyTaskService.busyAggrPayList(bo, pageQuery);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean refund(SohuRefundBusyBo bo) {
        String taskNumber = bo.getTaskNumber();
        SohuBusyTaskPayVo payVo = remoteBusyTaskService.queryByPayScene(taskNumber, PayStatus.Paid.name(), bo.getPaySceneType());
        if (Objects.isNull(payVo)) {
            throw new RuntimeException("支付记录不存在");
        }
        //退还保证金
        return this.operRefund(payVo, bo.getRefundAmount(), bo.getRefundReason());
    }

    /**
     * 处理结算逻辑
     *
     * @param receiveId
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean handleSettle(Long receiveId) {
        //查询接单记录
        SohuBusyTaskReceiveVo receiveModel = remoteBusyTaskReceiveService.selectById(receiveId);
        Objects.requireNonNull(receiveModel, "接单记录不存在");
        List<String> validStatus = List.of(SohuBusyTaskState.WaitSettle.name(), SohuBusyTaskState.Execute.name(), SohuBusyTaskState.WaitApproveSettle.name());
        if (!validStatus.contains(receiveModel.getState())) {
            throw new RuntimeException("任务状态异常");
        }
        Long receiveUserId = receiveModel.getUserId();
        Long shareUserId = receiveModel.getSharePerson();
        //查询商单子任务
        String childTaskNumber = receiveModel.getTaskNumber();
        SohuBusyTaskSiteVo taskSiteModel = remoteBusyTaskSiteService.queryByTaskNumber(childTaskNumber);
        Objects.requireNonNull(taskSiteModel, "商单子任务不存在");
        //判断接单状态 待结算，执行中->结算
        if (!validStatus.contains(taskSiteModel.getState())) {
            throw new RuntimeException("任务状态异常");
        }
        //查询商单主任务
        String taskNumber = taskSiteModel.getMasterTaskNumber();
        SohuBusyTaskVo sohuBusyTaskModel = remoteBusyTaskService.getByTaskNo(taskNumber);
        Objects.requireNonNull(sohuBusyTaskModel, "商单主任务不存在");

        String lockKey = String.format("sohu:lock:pass_user_master_task_number:%s", taskNumber);
        if (!RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(10))) {
            throw new RuntimeException("该订单正在结算中，请稍后再试");
        }
        //查询支付订单
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.Paid.name());
        if (CollUtil.isEmpty(payList)) {
            throw new RuntimeException("支付订单不存在");
        }
        //如果是分销
        String busyPosSeq = "";
        String sharePosSeq = "";
        Boolean busyOrderSuccess = false;
        Boolean shareOrderSuccess = false;
        try {
            //查询商单支付记录
            SohuBusyTaskPayVo busyOrderPay = payList.stream().filter(pay -> pay.getPaySceneType().equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode())).findFirst().orElse(null);
            SohuBusyTaskPayVo shareOrderPay = payList.stream().filter(pay -> pay.getPaySceneType().equals(PaySceceTypeEnum.BUSY_SHARE_PAY.getCode())).findFirst().orElse(null);
            SohuBusyTaskPayVo busyBondPay = payList.stream().filter(pay -> pay.getPaySceneType().equals(PaySceceTypeEnum.BUSY_BOND_PAY.getCode()) && pay.getSettleTime() == null).findFirst().orElse(null);
            //查询待分账记录
            List<SohuIndependentOrderVo> independentOrderList = getIndependentOrderList(taskNumber);
            if (CollUtil.isNotEmpty(independentOrderList)) {
                Map<String, List<SohuIndependentOrderVo>> map = independentOrderList.stream().collect(Collectors.groupingBy(SohuIndependentOrderVo::getTradeNo));
                for (Map.Entry<String, List<SohuIndependentOrderVo>> entry : map.entrySet()) {
                    String k = entry.getKey();
                    List<SohuIndependentOrderVo> v = entry.getValue();
                    List<SohuSplitInfoModel> splitInfoModels = new ArrayList<>();
                    for (SohuIndependentOrderVo item : v) {
                        SohuSplitInfoModel sohuSplitInfoModel = new SohuSplitInfoModel();
                        sohuSplitInfoModel.setUserId(item.getUserId());
                        sohuSplitInfoModel.setDivAmt(String.valueOf(CalUtils.yuanToCent(item.getIndependentPrice()).intValue()));
                        splitInfoModels.add(sohuSplitInfoModel);
                        if (item.getIndependentObject().equals(SohuIndependentObject.rece.getKey())) {
                            busyPosSeq = item.getTradeNo();
                        }
                        if (item.getIndependentObject().equals(SohuIndependentObject.distribution.getKey())) {
                            sharePosSeq = item.getTradeNo();
                        }
                    }
                    setMechantInfo(splitInfoModels);
                    // 接单分账
                    if (StrUtil.isNotEmpty(busyPosSeq)) {
                        busyOrderSuccess = distribution(busyPosSeq, busyOrderPay.getPayNumber(), splitInfoModels, "0");
                    }
                    if (StrUtil.isNotEmpty(sharePosSeq)) {
                        shareOrderSuccess = distribution(sharePosSeq, shareOrderPay.getPayNumber(), splitInfoModels, "0");
                    }
                }
            }
            //修改商单状态->已完结
            boolean b = remoteBusyTaskService.settleBusyTask(taskNumber);
            log.info("修改商单状态 待结算->已完结 {}", b);
            sendConfirmqueryMQ(busyPosSeq, childTaskNumber, busyOrderSuccess, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(), 2L);
            if (StrUtil.isNotEmpty(sharePosSeq)) {
                sendConfirmqueryMQ(sharePosSeq, childTaskNumber, shareOrderSuccess, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(), 2L);
            }
            //如果发单方支付分销佣金，但无分销，则原路退回分销佣金
            if (!StrUtil.isNotEmpty(sharePosSeq) && shareOrderPay != null) {
                int totalPrice = CalUtils.yuanToCent(shareOrderPay.getPayAmount()).intValue();
                //退款流水号
                String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
                sendRefund(refundNumber, totalPrice, shareOrderPay.getPayNumber());
                saveRefundRecord(taskNumber, refundNumber, shareOrderPay.getPayAmount(), shareOrderPay, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(), "通用商单-" + PaySceceTypeEnum.getByCode(shareOrderPay.getPaySceneType()).getDesc() + "退款", "结算退回");
            }
            if (busyBondPay != null) {
                //更新保证金结算时间，7天后退回给发单方
                SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
                payBo.setId(busyBondPay.getId());
                payBo.setSettleTime(new Date());
                remoteBusyTaskService.updateBusyTaskPay(payBo);
            }
            //更新接单分账状态
            if (busyOrderSuccess) {
                sohuIndependentOrderService.updateIndependentStatusByTradeNo(busyPosSeq, IndependentStatusEnum.DISTRIBUTED.getCode());
            }
            //更新分销分账状态
            if (shareOrderSuccess) {
                sohuIndependentOrderService.updateIndependentStatusByTradeNo(sharePosSeq, IndependentStatusEnum.DISTRIBUTED.getCode());
                materialPromotionOrderService.updateByTradeNo(taskNumber, shareOrderPay.getTransactionId(), IndependentStatusEnum.DISTRIBUTED.getCode());
            }
            //同步收入统计
            MqMessaging mqCancelMessaging = new MqMessaging(taskNumber, MqKeyEnum.INCOME_INFO.getKey());
            remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);

            LoginUser loginUser = LoginHelper.getLoginUser();
            //异步新增埋点数据
            CompletableFuture.runAsync(() -> {
                SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
                eventAttribute.setContentNo(taskNumber);
                eventAttribute.setContentName(sohuBusyTaskModel.getTitle());
                eventAttribute.setOrderNo(busyOrderPay.getOrderNo());
                eventAttribute.setPayAmount(busyOrderPay.getPayAmount());
                eventAttribute.setAmount(sohuBusyTaskModel.getFullAmount());
                eventAttribute.setContentType("通用");
                syncSettleUserBehavior(loginUser.getUserId(), loginUser.getNickname(), eventAttribute);
            }, asyncConfig.getAsyncExecutor());
        } catch (Exception e) {
            log.error("结算异常", e);
            //延时分账退回
            sendConfirmrefund(busyPosSeq);
            sendConfirmrefund(sharePosSeq);
            throw new RuntimeException(e);
        } finally {
            //释放锁
            RedisUtils.deleteObject(lockKey);
        }
        return true;
    }

    @Override
    public Boolean cancelTaskSettle(String taskNumber, Integer paySceneType) {
        //查询支付订单
        List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskNumber, PayStatus.Paid.name());
        if (CollUtil.isEmpty(payList)) {
            return true;
        }
        SohuBusyTaskPayVo busyOrderPay = payList.stream().filter(pay -> pay.getPaySceneType().equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode())).findFirst().orElse(null);
        SohuBusyTaskPayVo shareOrderPay = payList.stream().filter(pay -> pay.getPaySceneType().equals(PaySceceTypeEnum.BUSY_SHARE_PAY.getCode())).findFirst().orElse(null);
        SohuBusyTaskPayVo busyBondPay = payList.stream().filter(pay -> pay.getPaySceneType().equals(PaySceceTypeEnum.BUSY_BOND_PAY.getCode()) && pay.getSettleTime() == null).findFirst().orElse(null);
        //原路退回商单金额
        if (paySceneType.equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode()) && busyOrderPay != null) {
            //退款流水号
            String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
            sendRefund(refundNumber, CalUtils.yuanToCent(busyOrderPay.getPayAmount()).intValue(), busyOrderPay.getPayNumber());
            saveRefundRecord(taskNumber, refundNumber, busyOrderPay.getPayAmount(), busyOrderPay, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(), "通用商单-" + PaySceceTypeEnum.getByCode(busyOrderPay.getPaySceneType()).getDesc() + "退款", "取消商单");
            //更新分账记录状态->退款
            sohuIndependentOrderService.updateIndependentStatus(taskNumber, IndependentStatusEnum.REFUNDED.getCode());
            remoteMiddleTradeRecordService.updatePayStatus(busyOrderPay.getTaskNumber(), null, PayStatus.Paid.name(), PayStatus.Refund.name());
            if (Objects.nonNull(shareOrderPay)) {
                materialPromotionOrderService.updateByTradeNo(taskNumber, shareOrderPay.getTransactionId(), IndependentStatusEnum.REFUNDED.getCode());
            }
        }
        if (paySceneType.equals(PaySceceTypeEnum.BUSY_SHARE_PAY.getCode()) && shareOrderPay != null) {
            //退款流水号
            String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
            sendRefund(refundNumber, CalUtils.yuanToCent(shareOrderPay.getPayAmount()).intValue(), shareOrderPay.getPayNumber());
            saveRefundRecord(taskNumber, refundNumber, shareOrderPay.getPayAmount(), shareOrderPay, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(), "通用商单-" + PaySceceTypeEnum.getByCode(shareOrderPay.getPaySceneType()).getDesc() + "退款", "商单下架");
        }
        if (busyBondPay != null) {
            //更新保证金结算时间，7天后退回给发单方
            SohuBusyTaskPayBo payBo = new SohuBusyTaskPayBo();
            payBo.setId(busyBondPay.getId());
            payBo.setSettleTime(new Date());
            remoteBusyTaskService.updateBusyTaskPay(payBo);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean operRefund(SohuBusyTaskPayVo payVo, BigDecimal refundAmount, String refundReason) {
        String taskNumber = payVo.getTaskNumber();
        SohuBusyTaskVo sohuBusyTaskModel = remoteBusyTaskService.getByTaskNo(taskNumber);
        Objects.requireNonNull(sohuBusyTaskModel, "商单主任务不存在");
        //退款流水号
        String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
        int totalPrice = CalUtils.yuanToCent(refundAmount).intValue();
        sendRefund(refundNumber, totalPrice, payVo.getPayNumber());
        saveRefundRecord(taskNumber, refundNumber, refundAmount, payVo, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(), refundReason, refundReason);
        //给接单方发送退还保证金通知
        if (payVo.getPaySceneType().equals(PaySceceTypeEnum.BUSY_BOND_PAY.getCode())) {
            if (payVo.getPayAmount().compareTo(refundAmount) > 0) {
                remoteBusyTaskNoticeService.sendTaskNotice(sohuBusyTaskModel.getId(), TaskNoticeEnum.REFUND_DEPOSIT,
                        payVo.getUserId(), StrUtil.format("\n扣除金额:{}元\n退款金额:{}元\n扣除原因:{}", CalUtils.sub(payVo.getPayAmount(), refundAmount), refundAmount, refundReason), null, Boolean.TRUE);
            } else {
                remoteBusyTaskNoticeService.sendTaskNotice(sohuBusyTaskModel.getId(), TaskNoticeEnum.REFUND_DEPOSIT,
                        payVo.getUserId(), "", null, Boolean.TRUE);
            }
        }
        //给发单方、接单方发送接单方缴纳保证金超时通知
        if (payVo.getPaySceneType().equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode())) {
            remoteBusyTaskNoticeService.sendTaskNotice(sohuBusyTaskModel.getId(), TaskNoticeEnum.RECEIVER_PAY_DEPOSIT_OVERTIME,
                    payVo.getUserId(), null, null, Boolean.TRUE);
            remoteBusyTaskNoticeService.sendTaskNotice(sohuBusyTaskModel.getId(), TaskNoticeEnum.PAY_DEPOSIT_OVERTIME,
                    payVo.getReceiveUserId(), null, null, Boolean.TRUE);
            //删除分账信息
            SohuBusyTaskPayVo sharePayVo = remoteBusyTaskService.queryByPayScene(taskNumber, PayStatus.Paid.name(), PaySceceTypeEnum.BUSY_SHARE_PAY.getCode());
            //更新分账记录状态->退款
            sohuIndependentOrderService.updateIndependentStatus(taskNumber, IndependentStatusEnum.REFUNDED.getCode());
            remoteMiddleTradeRecordService.updatePayStatus(taskNumber, null, PayStatus.Paid.name(), PayStatus.Refund.name());
            if (Objects.nonNull(sharePayVo)) {
                materialPromotionOrderService.updateByTradeNo(taskNumber, sharePayVo.getTransactionId(), IndependentStatusEnum.REFUNDED.getCode());
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean delayTaskUpdate(String payNumber) {
        String key = "delayTaskUpdate:" + payNumber;
        if (!RedisUtils.setObjectIfAbsent(key, 1, Duration.ofSeconds(3))) {
            log.info("任务商单正在处理中，请稍后再试");
            return true;
//            throw new ServiceException("任务商单正在处理中，请稍后再试");
        }
        SohuBusyTaskPayVo sohuBusyTaskPayVo = remoteBusyTaskService.queryBusyTaskPay(payNumber, PayStatus.Paid.name());
        Objects.requireNonNull(sohuBusyTaskPayVo, "任务商单支付记录不存在");
        SohuBusyTaskVo busyTaskVo = remoteBusyTaskService.getByTaskNo(sohuBusyTaskPayVo.getTaskNumber());
        if (ObjectUtils.isNull(busyTaskVo)) {
            throw new RuntimeException("任务商单不存在");
        }
        int paySceneType = sohuBusyTaskPayVo.getPaySceneType();
        //支付商单金额更新商单状态  商单金额->执行中 分销金额->待接单
        if (paySceneType == PaySceceTypeEnum.BUSY_ORDER_PAY.getCode()) {
            //如果开启保证金缴纳并商单金额>500 商单状态->WaitPromisePay,给接单方发送缴纳保证金通知;否则，商单状态->Execute
            if (busyTaskVo.getIsReceiveDeposit() && sohuBusyTaskPayVo.getPayAmount().compareTo(BigDecimal.valueOf(0.2)) >= Constants.ZERO) {
                remoteBusyTaskService.updateTaskStateAfterPay(sohuBusyTaskPayVo.getTaskNumber(), SohuBusyTaskState.WaitPromisePay.name(), sohuBusyTaskPayVo.getReceiveUserId());
                remoteBusyTaskNoticeService.sendTaskNotice(busyTaskVo.getId(), TaskNoticeEnum.PAY_DEPOSIT,
                        sohuBusyTaskPayVo.getReceiveUserId(), null, null, Boolean.TRUE);
            } else {
                remoteBusyTaskService.updateTaskStateAfterPay(sohuBusyTaskPayVo.getTaskNumber(), SohuBusyTaskState.Execute.name(), sohuBusyTaskPayVo.getReceiveUserId());
            }
            //给接单方发送发单方支付成功通知
            remoteBusyTaskNoticeService.sendTaskNotice(busyTaskVo.getId(), TaskNoticeEnum.TASK_PAYMENT,
                    sohuBusyTaskPayVo.getReceiveUserId(), sohuBusyTaskPayVo.getPayAmount() + "元", null, Boolean.TRUE);
        }
        if (paySceneType == PaySceceTypeEnum.BUSY_SHARE_PAY.getCode()) {
            remoteBusyTaskService.updateTaskStateAfterPay(sohuBusyTaskPayVo.getTaskNumber(), SohuBusyTaskState.WaitReceive.name(), null);
            //发单方发送任务审核通过通知
            remoteBusyTaskNoticeService.sendTaskNotice(sohuBusyTaskPayVo.getId(), TaskNoticeEnum.TASK_PUBLISH_SUCCESS, sohuBusyTaskPayVo.getUserId(), null, null, Boolean.TRUE);
        }
        if (paySceneType == PaySceceTypeEnum.BUSY_BOND_PAY.getCode()) {
            remoteBusyTaskService.updateTaskStateAfterPay(sohuBusyTaskPayVo.getTaskNumber(), SohuBusyTaskState.Execute.name(), sohuBusyTaskPayVo.getUserId());
            //发送缴纳保证金成功通知->接单方
            remoteBusyTaskNoticeService.sendTaskNotice(busyTaskVo.getId(), TaskNoticeEnum.PAY_DEPOSIT_SUCCESS,
                    sohuBusyTaskPayVo.getUserId(), null, null, Boolean.TRUE);
            //发送缴纳保证金成功通知->发单方
            remoteBusyTaskNoticeService.sendTaskNotice(busyTaskVo.getId(), TaskNoticeEnum.RECEIVER_PAY_DEPOSIT_SUCCESS,
                    busyTaskVo.getUserId(), null, null, Boolean.TRUE);
        }
//        }
//        catch (Exception e) {
//            //退款流水号
//            String refundNumber = NumberUtil.getOrderNo(OrderConstants.ORDER_PREFIX_REFUND);
//            int totalPrice = CalUtils.yuanToCent(sohuBusyTaskPayVo.getPayAmount()).intValue();
//            sendRefund(refundNumber, totalPrice, sohuBusyTaskPayVo.getPayNumber());
//            saveRefundRecord(sohuBusyTaskPayVo.getTaskNumber(), refundNumber, sohuBusyTaskPayVo.getPayAmount(), sohuBusyTaskPayVo, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode(), "通用商单-" + PaySceceTypeEnum.getByCode(sohuBusyTaskPayVo.getPaySceneType()).getDesc() + "退款", "支付异常退款");
//        }finally {
//            RedisUtils.deleteObject(key);
//        }
        return true;
    }

    @Override
    public Boolean applySettle(String taskNumber, String reason) {
        String lockKey = String.format("sohu:lock:pass_user_master_task_number:%s", taskNumber);
        if (!RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(10))) {
            throw new RuntimeException("该订单正在结算中，请稍后再试");
        }
        try {
            //修改商单状态 待结算->已完结
            boolean b = remoteBusyTaskService.applySettleBusyTask(taskNumber, reason);
            log.info("审核结算,商单号:{}，状态:{}", taskNumber, b);
            //查询未结算的接单任务
            List<String> validStatus = Arrays.asList(SohuBusyTaskState.WaitSettle.name(), SohuBusyTaskState.Execute.name(), SohuBusyTaskState.WaitApproveSettle.name());
            List<SohuBusyTaskReceiveVo> receiveList = remoteBusyTaskReceiveService.queryListByMasterTaskNumberAndStateList(taskNumber, validStatus);
            if (CollUtil.isNotEmpty(receiveList)) {
                handleSettle(receiveList.get(0).getId());
            }
        } catch (Exception e) {
            log.error("结算异常", e);
            throw new RuntimeException(e.getMessage());
        } finally {
            RedisUtils.deleteObject(lockKey);
        }
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean waitIncomeProcess(String busyType, Long busyCode) {
        if (BusyType.BusyTask.getType().equals(busyType)) {
            SohuBusyTaskReceiveVo receiveModel = remoteBusyTaskReceiveService.selectById(busyCode);
            SohuBusyTaskSiteVo taskSiteModel = remoteBusyTaskSiteService.queryByTaskNumber(receiveModel.getTaskNumber());
            SohuIndependentTemplateModel templateInfo = independentTemplateService.queryTemplateInfo(receiveModel.getSiteType(), receiveModel.getSiteId(), 2);
            //查询支付订单
            List<SohuBusyTaskPayVo> payList = remoteBusyTaskService.queryByTaskNumber(taskSiteModel.getMasterTaskNumber(), PayStatus.Paid.name());
            if (CollUtil.isEmpty(payList)) {
                throw new RuntimeException("支付订单不存在");
            }
            //查询商单子任务
            String childTaskNumber = receiveModel.getTaskNumber();
            //查询商单主任务
            String taskNumber = taskSiteModel.getMasterTaskNumber();
            SohuBusyTaskVo sohuBusyTaskModel = remoteBusyTaskService.getByTaskNo(taskNumber);
            SohuBusyTaskPayVo busyOrderPay = payList.stream().filter(pay -> pay.getPaySceneType().equals(PaySceceTypeEnum.BUSY_ORDER_PAY.getCode())).findFirst().orElse(null);
            SohuBusyTaskPayVo shareOrderPay = payList.stream().filter(pay -> pay.getPaySceneType().equals(PaySceceTypeEnum.BUSY_SHARE_PAY.getCode())).findFirst().orElse(null);
            //如果是分销
            String busyPosSeq = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
            String sharePosSeq = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
            Long shareUserId = receiveModel.getSharePerson();
            Boolean isShare = false;
            //封装分账对象信息
            List<SohuSplitInfoModel> splitInfoList = new ArrayList<>();
            //平台分账对象分账
            AccountPlatformBo bo = new AccountPlatformBo();
            bo.setBusyType(busyType);
            bo.setBusyCode(busyCode);
            bo.setUserId(receiveModel.getUserId());
            bo.setSiteType(receiveModel.getSiteType());
            bo.setEntranceSiteId(receiveModel.getSiteId());
            bo.setCitySiteId(taskSiteModel.getSiteId());
            bo.setIndustryType(taskSiteModel.getIndustryType());
            bo.setPayPrice(busyOrderPay.getPayAmount());
            bo.setChargeFee(busyOrderPay.getChargeAmount());
            bo.setTemplateModel(templateInfo);
            bo.setOrderTime(receiveModel.getCreateTime());
            List<SohuIndependentTempBo> platformObjects = accountProcessService.accountPlatformObjects(bo);
            accountPlatformSplitInfo(platformObjects, splitInfoList, busyCode, childTaskNumber, busyPosSeq);
            //接单人分账
            receiveSplitInfo(busyOrderPay.getReceiveDistributorAmount(), receiveModel.getUserId(), splitInfoList, busyCode, childTaskNumber, busyPosSeq);
            //分销分账
            BigDecimal shareAmount = BigDecimal.ZERO;
            if (shareOrderPay != null && shareUserId != null && shareUserId > 0) {
                AccountDistributionBo distributionBo = new AccountDistributionBo();
                distributionBo.setShareUserId(shareUserId);
                distributionBo.setShareAmount(shareOrderPay.getPayAmount());
                distributionBo.setChargeFee(shareOrderPay.getChargeAmount());
                distributionBo.setTemplateModel(templateInfo);
                List<SohuIndependentTempBo> distributionObjects = accountProcessService.accountDistributionObjects(distributionBo);
                accountPlatformSplitInfo(distributionObjects, splitInfoList, busyCode, childTaskNumber, sharePosSeq);
                isShare = true;
                shareAmount = distributionObjects.stream().filter(temp -> temp.getIndependentObject().equals(SohuIndependentObject.distribution.getKey())).findFirst().get().getIndependentPrice();
            }
            boolean success = false;
            List<SohuIndependentOrderBo> boList = Lists.newArrayList();
            List<SohuTradeRecordBo> tradeList = Lists.newArrayList();
            SohuMaterialPromotionOrderCmdBo cmdBo = new SohuMaterialPromotionOrderCmdBo();
            //插入sohu_independent_order
            saveIndependentOrder(taskNumber, splitInfoList, success, boList, sohuBusyTaskModel.getTitle(), sohuBusyTaskModel.getFullAmount(), sohuBusyTaskModel.getUserId());
            //插入sohu_trade_record
            saveTradeRecord(taskNumber, busyOrderPay.getPayChannel(), splitInfoList, tradeList, SohuTradeRecordEnum.Type.BusyTaskCommon.getCode());
            //插入素材流水订单表
            if (isShare) {
                saveMaterialOrder(shareUserId, receiveModel.getUserId(), taskNumber, shareOrderPay.getTransactionId(), shareAmount, cmdBo, BusyTaskTypeEnum.COMMON_TASK.getCode());
            }
            if (CollUtil.isNotEmpty(boList)) {
                sohuIndependentOrderService.insertByBoList(boList);
            }
            if (CollUtil.isNotEmpty(tradeList)) {
                remoteMiddleTradeRecordService.insertBatch(tradeList);
            }
            if (cmdBo != null && cmdBo.getShareUserId() != null) {
                materialPromotionOrderService.save(cmdBo);
            }
            //同步收入统计
            MqMessaging mqCancelMessaging = new MqMessaging(taskNumber, MqKeyEnum.INCOME_INFO.getKey());
            remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);
            return true;
        }
        return true;
    }
}
