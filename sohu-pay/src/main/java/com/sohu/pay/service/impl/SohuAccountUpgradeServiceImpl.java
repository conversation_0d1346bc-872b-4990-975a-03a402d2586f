package com.sohu.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.admin.api.vo.SohuUserCondVo;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.SohuBusyTaskState;
import com.sohu.common.core.enums.SysNoticeEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.enums.AuditState;
import com.sohu.pay.api.bo.SohuAccountAuditBo;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.bo.SohuAccountHistoryBo;
import com.sohu.pay.api.bo.SohuAccountUpgradeBo;
import com.sohu.pay.api.enums.AccountEnum;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.api.vo.SohuAccountHistoryVo;
import com.sohu.pay.api.vo.SohuAccountUpgradeVo;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.pay.domain.SohuAccount;
import com.sohu.pay.domain.SohuAccountBank;
import com.sohu.pay.service.*;
import com.sohu.shoporder.api.RemoteShopOrderService;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteUserService;
import com.wangcaio2o.ipossa.sdk.response.accountbalancequery.AccountBalanceQueryList;
import com.wangcaio2o.ipossa.sdk.response.merchantquery.MerchantQueryResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/23 9:34
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuAccountUpgradeServiceImpl extends SohuAccountBaseServiceImpl implements SohuAccountUpgradeService {
    private final ISohuAccountService sohuAccountService;
    private final ISohuAccountHistoryService sohuAccountHistoryService;
    private final AsyncConfig asyncConfig;
    private final YmBaseService ymBaseService;
    private final ISohuAccountAuditLogService sohuAccountAuditLogService;
    @DubboReference
    private final RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteBusyTaskReceiveService remoteBusyTaskReceiveService;
    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;
    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

    @Override
    public Boolean upgrage(SohuAccountUpgradeBo upgradeBo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            throw new ServiceException("当前用户未登录");
        }
        //查询认证记录是否存在
        SohuAccountVo sohuAccountVo = sohuAccountService.queryById(upgradeBo.getId());
        log.info("查询认证记录：{}", sohuAccountVo);
        if (Objects.isNull(sohuAccountVo)) {
            throw new ServiceException("当前用户未查询到账户信息");
        }
        validateCertPass(upgradeBo.getIdentityNo(), upgradeBo.getLicenseCode());
        //校验是否是个人账户->企业账户
        checkAccountType(upgradeBo.getAccountType(), sohuAccountVo.getAccountType());
        //校验用户是否绑定银行卡、是否存在资金或待结算的资金
        Boolean existBalance = checkAccount(sohuAccountVo.getUserId());
        //封装更新对象
        SohuAccountBo updateBo = BeanUtil.copyProperties(upgradeBo, SohuAccountBo.class);
        if (existBalance) {
            //删除翼码商户
            ymBaseService.deleteMerchant(sohuAccountVo.getMerchantId());
            //更新认证信息
            this.updateByBoOfCommon(updateBo, sohuAccountVo, null);
            //异步创建翼码企业分账商户
            CompletableFuture.runAsync(() -> sohuAccountService.audit(updateBo), asyncConfig.getAsyncExecutor());
            //实名认证提交发送消息通知
            CompletableFuture.runAsync(() -> this.sendMsgOfEntrySubmit(sohuAccountVo.getId(), sohuAccountVo.getUserId()), asyncConfig.getAsyncExecutor());
        } else {
            //认证记录状态变更为冻结升级中，更新认证信息
            SohuAccount updateEntity = new SohuAccount();
            updateEntity.setId(sohuAccountVo.getId());
//            BeanUtil.copyProperties(updateBo, updateEntity);
            updateEntity.setState(AccountEnum.AccountStatusEnum.FreezeUpgrade.name());
            updateEntity.setFreezeTime(new Date());
            updateEntity.setIsUpgrage(Constants.ONE);
            baseMapper.updateById(updateEntity);
        }
        //保存历史实名信息
        SohuAccountHistoryVo historyVo = sohuAccountHistoryService.queryByAccountId(sohuAccountVo.getId());
        if (Objects.isNull(historyVo)) {
            SohuAccountHistoryBo historyBo = BeanUtil.copyProperties(updateBo, SohuAccountHistoryBo.class);
            historyBo.setAccountId(sohuAccountVo.getId());
            sohuAccountHistoryService.insertByBo(historyBo);
        }
        //记录审核日志
        sohuAccountAuditLogService.save(sohuAccountVo.getId(), sohuAccountVo.getAccountType(), CommonState.WaitApprove.getCode(), null);
        return Boolean.TRUE;
    }

    @Override
    public Boolean cancel(Long accountId) {
        //查询认证记录是否存在
        SohuAccount sohuAccount = baseMapper.selectById(accountId);
        if (Objects.isNull(sohuAccount) || !AccountEnum.AccountStatusEnum.FreezeUpgrade.name().equals(sohuAccount.getState())) {
            throw new ServiceException("当前用户未查询到账户信息");
        }
        //恢复账户之前数据
        SohuAccountHistoryVo historyVo = sohuAccountHistoryService.queryByAccountId(accountId);
//        if (Objects.nonNull(historyVo)) {
//            BeanUtil.copyProperties(historyVo, sohuAccount);
//        }
        sohuAccount.setState(AccountEnum.AccountStatusEnum.Pass.name());
        sohuAccount.setIsUpgrage(Constants.ZERO);
        LambdaUpdateWrapper<SohuAccount> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuAccount::getId, accountId);
        luw.set(SohuAccount::getFreezeTime, null);
        baseMapper.update(sohuAccount, luw);
        //删除历史实名信息
        sohuAccountHistoryService.deleteById(historyVo.getId());
        return Boolean.TRUE;
    }

    @Override
    public Boolean audit(SohuAccountAuditBo bo) {
        Long accountId = bo.getId();
        //查询认证记录是否存在
        SohuAccountVo sohuAccountVo = sohuAccountService.queryById(accountId);
        if (Objects.isNull(sohuAccountVo)) {
            throw new ServiceException("当前用户未查询到账户信息");
        }
        if (!List.of(AccountEnum.AccountStatusEnum.WaitApprove.name(), AccountEnum.AccountStatusEnum.FreezeUpgrade.name()).contains(sohuAccountVo.getState())) {
            throw new ServiceException("当前账户状态不允许审核");
        }
        if (AccountEnum.AccountStatusEnum.Pass.name().equals(bo.getAuditState())) {
            //查询账户是否已经删除
            MerchantQueryResponse response = ymBaseService.merchantQuery(sohuAccountVo.getMerchantId());
            List<String> resultList = Lists.newArrayList("9998", "0000");
            if (ObjectUtils.isNotEmpty(response) && resultList.contains(response.getResult().getId())) {
                //删除翼码商户
                ymBaseService.deleteMerchant(sohuAccountVo.getMerchantId());
            }
            SohuAccountHistoryVo historyVo = sohuAccountHistoryService.queryByAccountId(accountId);
            SohuAccountBo updateBo = BeanUtil.copyProperties(historyVo, SohuAccountBo.class);
            updateBo.setId(historyVo.getAccountId());
            //更新认证信息
            this.updateByBoOfCommon(updateBo, sohuAccountVo, bo.getVoucherUrl());
            //异步创建企业分账商户
            CompletableFuture.runAsync(() -> sohuAccountService.audit(updateBo), asyncConfig.getAsyncExecutor());
            //实名认证提交发送消息通知
            CompletableFuture.runAsync(() -> this.sendMsgOfEntrySubmit(sohuAccountVo.getId(), sohuAccountVo.getUserId()), asyncConfig.getAsyncExecutor());
            //更改升级店铺状态(延时消息)
            MqMessaging upgradeMessaging = new MqMessaging(String.valueOf(sohuAccountVo.getId()), MqKeyEnum.ACCOUNT_UPGRADE.getKey());
            remoteStreamMqService.sendDelayMsg(upgradeMessaging, 3L);
        }
        if (AccountEnum.AccountStatusEnum.Refuse.name().equals(bo.getAuditState())) {
            SohuAccount updateEntity = new SohuAccount();
            updateEntity.setId(sohuAccountVo.getId());
            updateEntity.setState(AccountEnum.AccountStatusEnum.Refuse.name());
            updateEntity.setRejectReason(bo.getRejectReason());
            this.baseMapper.updateById(updateEntity);
        }
        //记录审核日志
        sohuAccountAuditLogService.save(accountId, sohuAccountVo.getAccountType(), bo.getAuditState(), bo.getRejectReason());
        return Boolean.TRUE;
    }

    @Override
    public SohuAccountUpgradeVo detail(Long accountId) {
        SohuAccountUpgradeVo upgradeVo = new SohuAccountUpgradeVo();
        //查询认证记录是否存在
        SohuAccountVo sohuAccountVo = sohuAccountService.queryById(accountId);
        if (Objects.isNull(sohuAccountVo)) {
            throw new ServiceException("当前用户未查询到账户信息");
        }
        long userId = sohuAccountVo.getUserId();
        //查询用户头像
        LoginUser loginUser = remoteUserService.selectById(userId);
        upgradeVo.setAvatar(Objects.nonNull(loginUser) ? loginUser.getAvatar() : "");
        //查询用户银行卡号
        SohuAccountBankVo sohuAccountBankVo = sohuAccountBankMapper.selectVoOne(new LambdaQueryWrapper<SohuAccountBank>()
                .eq(SohuAccountBank::getUserId, userId));
        upgradeVo.setBankNo(Objects.nonNull(sohuAccountBankVo) ? sohuAccountBankVo.getCardNo() : "");
        upgradeVo.setPersonalInfo(BeanUtil.copyProperties(sohuAccountVo, SohuAccountUpgradeVo.AccountInfo.class));
        //查询历史实名信息
        SohuAccountHistoryVo historyVo = sohuAccountHistoryService.queryByAccountId(accountId);
        if (Objects.nonNull(historyVo)) {
            upgradeVo.setBusinessInfo(BeanUtil.copyProperties(historyVo, SohuAccountUpgradeVo.AccountInfo.class));
        }
        return upgradeVo;
    }

    @Override
    public SohuUserCondVo getUserCond(Long userId,Date freezeTime) {
        SohuUserCondVo sohuUserCondVo = new SohuUserCondVo();
        List<SohuMerchantModel> merchantModels = remoteMerchantService.selectByUserId(userId);
        if (CollUtil.isNotEmpty(merchantModels)) {
            for (SohuMerchantModel merchantModel : merchantModels
            ) {
                if (merchantModel.getAuditStatus().equals(AccountEnum.AccountStatusEnum.Pass.name())
                        && !CommonState.ClosePass.getCode().equals(merchantModel.getCloseStatus())
                        && merchantModel.getMerchantType().equals(AccountEnum.AccountTypeEnum.PERSONAL.getCode())) {
                    sohuUserCondVo.setExistPersonMerchant(true);
                    break;
                }
            }
        }
        //查询用户余额
        int balance = getYmBalance(userId);
        sohuUserCondVo.setBalance(CalUtils.centToYuan(BigDecimal.valueOf(balance)));
        //查询在途商单
        List<String> taskStates = Arrays.asList(SohuBusyTaskState.WaitPay.name(),
                SohuBusyTaskState.WaitApprove.name(),
                SohuBusyTaskState.ReceiveTask.name(),
                SohuBusyTaskState.WaitApproveSettle.name(),
                SohuBusyTaskState.WaitSettle.name(),
                SohuBusyTaskState.Execute.name(),
                SohuBusyTaskState.WaitIndependentPay.name(),
                SohuBusyTaskState.WaitFullAmountPay.name(),
                SohuBusyTaskState.WaitPromisePay.name());
        long taskCount = remoteBusyTaskReceiveService.inTransitTask(userId, taskStates, freezeTime);
        //查询该用户下所有的店铺
        Long shopCount = 0L;
        List<SohuMerchantModel> sohuMerchants = remoteMerchantService.selectByUserId(userId);
        if (CollUtil.isNotEmpty(sohuMerchants)) {
            List<String> validShopstates = Arrays.asList(OrderConstants.MERCHANT_ORDER_STATUS_UNPAID, OrderConstants.ORDER_STATUS_SHIPPING, OrderConstants.ORDER_STATUS_AWAIT_RECEIVING, OrderConstants.ORDER_STATUS_RECEIVE);
            shopCount = remoteShopOrderService.inTransitOrder(sohuMerchants.stream().map(SohuMerchantModel::getId).collect(Collectors.toList()), validShopstates);
        }
        long orderCount = taskCount + shopCount;
        sohuUserCondVo.setOrderCount(orderCount);
        return sohuUserCondVo;
    }

    @Override
    public Boolean accountUpgradeJobHandler() {
        //查询所有冻结升级中的账户
        List<SohuAccountVo> accountList = baseMapper.selectVoList(new LambdaQueryWrapper<SohuAccount>().eq(SohuAccount::getState, AccountEnum.AccountStatusEnum.FreezeUpgrade.name()));
        if (CollUtil.isEmpty(accountList)) {
            log.info("当前没有冻结升级中的账户");
            return Boolean.TRUE;
        }
        for (SohuAccountVo item : accountList
        ) {
            //查询账户是否有在途订单与余额
            SohuUserCondVo sohuUserCondVo = getUserCond(item.getUserId(),  item.getFreezeTime());
            if (sohuUserCondVo.getBalance().compareTo(BigDecimal.ZERO) > 0 && sohuUserCondVo.getOrderCount() == 0) {
                //发送系统通知提现
                CompletableFuture.runAsync(
                        () -> remoteMiddleSystemNoticeService.sendSystemNotice(item.getUserId(), item.getUserId(), SysNoticeEnum.ACCOUNT_UPGRADE_WITHDRAWAL, null),
                        asyncConfig.getAsyncExecutor()
                );
            }
            if (sohuUserCondVo.getBalance().compareTo(BigDecimal.ZERO) > 0 || sohuUserCondVo.getOrderCount() > 0) {
                log.info("账户{}有在途订单或余额，请处理", item.getId());
                continue;
            }
            //修改账户为待审核
            SohuAccount updateEntity = new SohuAccount();
            updateEntity.setId(item.getId());
            updateEntity.setState(AccountEnum.AccountStatusEnum.WaitApprove.name());
            baseMapper.updateById(updateEntity);
        }
        return Boolean.TRUE;
    }

    private void checkAccountType(String upgradeAccountType, String accountType) {
        if (!AccountEnum.AccountTypeEnum.BUSINESS.getCode().equals(upgradeAccountType) || !AccountEnum.AccountTypeEnum.PERSONAL.getCode().equals(accountType)) {
            throw new ServiceException("升级失败:升级需从个人升级到企业");
        }
    }

    private Boolean checkAccount(Long userId) {
        //查询是否绑定银行卡
        SohuAccountBankVo sohuAccountBankVo = sohuAccountBankMapper.selectVoOne(new LambdaQueryWrapper<SohuAccountBank>()
                .eq(SohuAccountBank::getUserId, userId));
        if (Objects.nonNull(sohuAccountBankVo) && sohuAccountBankVo.getState().equals(AuditState.Pass.name())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    protected Boolean updateByEntity(SohuAccount updateEntity, SohuAccountBo bo) {
        LambdaUpdateWrapper<SohuAccount> luw = new LambdaUpdateWrapper<>();
        luw.eq(SohuAccount::getId, bo.getId())
                .set(SohuAccount::getRejectReason, null)
                .set(SohuAccount::getAuditTime, null)
                .set(SohuAccount::getMerchantId, null)
                .set(SohuAccount::getHasMerchant, Boolean.FALSE);
        return baseMapper.update(updateEntity, luw) > 0;
    }

    public Boolean updateByBoOfCommon(SohuAccountBo bo, SohuAccountVo vo, String voucherUrl) {
        if (!Objects.equals(vo.getId(), bo.getId())) {
            throw new RuntimeException("非法修改");
        }
        SohuAccount updateEntity = new SohuAccount();
        //构建更新对象
        this.buildAccountOfEdit(bo, updateEntity);
        updateEntity.setState(AccountEnum.AccountStatusEnum.WaitApprove.name());
        updateEntity.setIsUpgrage(Constants.ONE);
        updateEntity.setVoucherUrl(voucherUrl);
        return this.updateByEntity(updateEntity, bo);
    }

    protected Integer getYmBalance(Long userId) {
        int totalAvlAmt = 0;
        List<AccountBalanceQueryList> balanceQueryList = ymBaseService.queryUserBalance(userId);
        if (CollUtil.isNotEmpty(balanceQueryList)) {
            totalAvlAmt = balanceQueryList.stream().mapToInt(AccountBalanceQueryList::getBalanceAmt).sum();
        }
        return totalAvlAmt;
    }
}
