package com.sohu.pay.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 第三方分账单对象 sohu_independent_order
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_independent_order")
public class SohuIndependentOrder extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 系统-用户id
     */
    private Long userId;
    /**
     * 站点类型 1 城市站点 2 行业站点
     */
    private Integer siteType;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 订单号-子单
     */
    private String orderNo;
    /**
     * 第三方交易单号-唯一
     */
    private String tradeNo;
    /**
     * 分账方对象：平台、分销人、拉新人、国家站长、城市站长
     * {@link com.sohu.common.core.enums.SohuIndependentObject}
     */
    private String independentObject;
    /**
     * 分账金额-0.00
     */
    private BigDecimal independentPrice;
    /**
     * 分账总金额-0.00
     */
    private BigDecimal independentTotalPrice;
    /**
     * 分账状态：0 未分账  1 已分账  2 分账处理中  3 分账异常  4 已退款
     */
    private Integer independentStatus;
    /**
     * 交易类型（商品，商单）
     */
    private String tradeType;
    /**
     * 商户ID
     */
    private Long merId;

    /**
     * 品牌方名称
     */
    private String taskUserName;
    /**
     * 商单名称
     */
    private String taskTitle;
    /**
     * 子任务编号
     */
    private String taskNumber;
    /**
     * 任务金额
     */
    private BigDecimal taskFullAmount;

    /**
     * 第三方服务费-0.00
     */
    private BigDecimal chargePrice;

    /**
     * 消费者ID
     */
    private BigDecimal consumerUserId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
