package com.sohu.pay.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.bo.SohuIndependentPlayetTemplateBo;
import com.sohu.pay.api.bo.SohuIndependentTemplateBo;
import com.sohu.pay.api.vo.SohuIndependentTemplateVo;
import com.sohu.pay.service.ISohuIndependentTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 分账模版控制器
 * 前端访问路由地址为:/pay/independentTemplate
 *
 * <AUTHOR>
 * @date 2023-10-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/independentTemplate")
public class SohuIndependentTemplateController extends BaseController {

    private final ISohuIndependentTemplateService iSohuIndependentTemplateService;

    /**
     * 查询分账模版列表
     */
    @SaCheckPermission(value = "pay:independentTemplate:list", orRole = {"kefuchaoguan"})
    @GetMapping("/list")
    public TableDataInfo<SohuIndependentTemplateVo> list(SohuIndependentTemplateBo bo, PageQuery pageQuery) {
        return iSohuIndependentTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 校验分账模版重复
     */
    @SaCheckPermission(value = "pay:independentTemplate:list", orRole = {"kefuchaoguan"})
    @GetMapping("/exit")
    public R<SohuIndependentTemplateVo> exitData(SohuIndependentTemplateBo bo) {
        SohuIndependentTemplateVo sohuIndependentTemplateVo = iSohuIndependentTemplateService.exitData(bo.getSiteId(), bo.getTemplateType());
        return R.ok(sohuIndependentTemplateVo);
    }

    /**
     * 导出分账模版列表
     */
    @SaCheckPermission(value = "pay:independentTemplate:export", orRole = {"kefuchaoguan"})
    @Log(title = "分账模版", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuIndependentTemplateBo bo, HttpServletResponse response) {
        List<SohuIndependentTemplateVo> list = iSohuIndependentTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "分账模版", SohuIndependentTemplateVo.class, response);
    }

    /**
     * 获取分账模版详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission(value = "pay:independentTemplate:query", orRole = {"kefuchaoguan"})
    @GetMapping("/{id}")
    public R<SohuIndependentTemplateVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuIndependentTemplateService.queryById(id));
    }

    /**
     * 获取短剧分账模版详细信息
     */
    @GetMapping("/getPlayletInfo")
    public R<SohuIndependentTemplateVo> getPlayletInfo() {
        return R.ok(iSohuIndependentTemplateService.getPlayletInfo());
    }

    /**
     * 新增分账模版
     */
    @SaCheckPermission(value = "pay:independentTemplate:add", orRole = {"kefuchaoguan"})
    @Log(title = "分账模版", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuIndependentTemplateBo bo) {
        return R.ok(iSohuIndependentTemplateService.insertByBo(bo));
    }


    /**
     * 新增短剧分账模版
     */
    @PostMapping("/addPlaylet")
    public R<Boolean> addPlaylet(@RequestBody SohuIndependentPlayetTemplateBo bo) {
        return R.ok(iSohuIndependentTemplateService.addPlaylet(bo));
    }

    /**
     * 修改分账模版
     */
    @SaCheckPermission(value = "pay:independentTemplate:edit", orRole = {"kefuchaoguan"})
    @Log(title = "分账模版", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuIndependentTemplateBo bo) {
        return toAjax(iSohuIndependentTemplateService.updateByBo(bo));
    }

    /**
     * 删除分账模版
     *
     * @param ids 主键串
     */
    @SaCheckPermission(value = "pay:independentTemplate:remove", orRole = {"kefuchaoguan"})
    @Log(title = "分账模版", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuIndependentTemplateService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
