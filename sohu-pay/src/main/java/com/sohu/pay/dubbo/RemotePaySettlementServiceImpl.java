package com.sohu.pay.dubbo;

import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.vo.SohuBusyTaskPayVo;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.pay.api.RemotePaySettlementService;
import com.sohu.pay.api.bo.DelayConfirmqueryBo;
import com.sohu.pay.api.model.SohuBusyTaskPayNewModel;
import com.sohu.pay.service.SohuPaySettlementService;
import com.sohu.pay.service.SohuTaskCommonSettlementService;
import com.sohu.pay.service.SohuTaskFlowSettlementService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/16 16:48
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemotePaySettlementServiceImpl implements RemotePaySettlementService {

    private final SohuPaySettlementService sohuPaySettlementService;
    private final SohuTaskFlowSettlementService sohuTaskFlowSettlementService;
    private final SohuTaskCommonSettlementService sohuTaskCommonSettlementService;

    @Override
    public Boolean overSettle(String taskNumber) {
        return sohuTaskFlowSettlementService.overSettle(taskNumber);
    }

    @Override
    public Boolean barcodeCancelPay(String taskNumber) {
        return sohuTaskFlowSettlementService.barcodeCancelPay(taskNumber);
    }

    @Override
    public Boolean delayConfirmquery(DelayConfirmqueryBo delayConfirmqueryBo) {
        return sohuPaySettlementService.delayConfirmquery(delayConfirmqueryBo);
    }

    @Override
    public Boolean delayConfirmPlatform(String taskNumber) {
        return sohuTaskFlowSettlementService.delayConfirmPlatform(taskNumber);
    }

    @Override
    public Boolean delayConfirmRefund(String taskNumber) {
        return sohuTaskFlowSettlementService.delayConfirmRefund(taskNumber);
    }

    @Override
    public Boolean delayTaskUpdate(String payNumber) {
        return sohuTaskCommonSettlementService.delayTaskUpdate(payNumber);
    }

    @Override
    public Boolean handleSettle(Long receiveId) {
        return sohuTaskFlowSettlementService.handleSettle(receiveId);
    }

    @Override
    public Boolean onDistributing(Long userId, String childTaskNumber) {
        return sohuTaskFlowSettlementService.onDistributing(userId, childTaskNumber);
    }

    @Override
    public Boolean handleBatchSettle(String taskNumber) {
        return sohuTaskFlowSettlementService.handleBatchSettle(taskNumber);
    }

    @Override
    public Boolean applySettle(String taskNumber, String reason,String busyType) {
        if (busyType.equals(BusyTaskTypeEnum.FLOW_TASK.getCode())){
           return sohuTaskFlowSettlementService.applySettle(taskNumber, reason);
        }
        return sohuTaskCommonSettlementService.applySettle(taskNumber, reason);
    }

    @Override
    public Boolean cancelTaskSettle(String taskNumber, Integer paySceneType) {
        return sohuTaskCommonSettlementService.cancelTaskSettle(taskNumber, paySceneType);
    }

    @Override
    public Boolean operRefund(SohuBusyTaskPayNewModel payVo, BigDecimal refundAmount, String refundReason) {
        return sohuTaskCommonSettlementService.operRefund(BeanCopyUtils.copy(payVo, SohuBusyTaskPayVo.class), refundAmount, refundReason);
    }

    @Override
    public Boolean waitIncomeProcess(String busyType, Long busyCode) {
        return sohuTaskCommonSettlementService.waitIncomeProcess(busyType, busyCode);
    }
}
