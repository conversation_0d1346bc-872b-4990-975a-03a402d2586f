package com.sohu.pay.dubbo;

import cn.hutool.json.JSONUtil;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.bo.SohuAccountBo;
import com.sohu.pay.api.bo.SohuAccountUpgradeBo;
import com.sohu.pay.api.model.SohuAccountBankModel;
import com.sohu.pay.api.vo.SohuAccountBankVo;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.pay.domain.SohuAccountBank;
import com.sohu.pay.service.ISohuAccountBankService;
import com.sohu.pay.service.ISohuAccountService;
import com.sohu.pay.service.SohuAccountUpgradeService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付
 * <p>
 * 注意: 此Service内不允许调用标注`数据权限`注解的方法
 * 例如: deptMapper.selectList 此 selectList 方法标注了`数据权限`注解 会出现循环解析的问题
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteAccountServiceImpl implements RemoteAccountService {

    private final ISohuAccountService accountService;
    private final ISohuAccountBankService accountBankService;

    private final SohuAccountUpgradeService sohuAccountUpgradeService;

//    @Override
//    public String userAccount(Long userId) {
//        SohuAccountVo sohuAccountVo = accountService.userList(userId);
//        return Objects.nonNull(sohuAccountVo) ? sohuAccountVo.getMerchantId() : null;
//    }

    @Override
    public String getMerchantId(Long userId) {
        SohuAccountVo sohuAccountVo = accountService.queryByUserId(userId);
        return Objects.nonNull(sohuAccountVo) ? sohuAccountVo.getMerchantId() : null;
    }

    @Override
    public SohuAccountVo queryByUserId(Long userId) {
        return accountService.queryByUserId(userId);
    }

//    @Override
//    public String getBusinessAccountByUserId(Long userId) {
//        return accountService.getBusinessAccountByUserId(userId);
//    }

//    @Override
//    public SohuAccountModel selectAccountByUserId(Long userId) {
//        SohuAccountVo vo = accountService.queryByUserId(userId);
//        return BeanCopyUtils.copy(vo, SohuAccountModel.class);
//    }

//    @Override
//    public SohuAccountModel selectPassAccountByUserId(Long userId) {
//        SohuAccountVo vo = accountService.selectPassAccountByUserId(userId);
//        return BeanCopyUtils.copy(vo, SohuAccountModel.class);
//    }

    @Override
    public TableDataInfo<SohuAccountVo> selectPassAccountList(PageQuery pageQuery) {
        return accountService.selectPassAccountList(pageQuery);
    }

//    @Override
//    public Map<Long, SohuAccountModel> selectAccountMapByUserIds(Collection<Long> userIds) {
//        Map<Long, SohuAccountModel> resultMap = new HashMap<>();
//        if (CollectionUtils.isEmpty(userIds)) {
//            return resultMap;
//        }
//        List<SohuAccount> list = accountService.selectListByUserIds(userIds);
//        if (CollectionUtils.isEmpty(list)) {
//            return resultMap;
//        }
//        for (SohuAccount entity : list) {
//            SohuAccountModel model = BeanCopyUtils.copy(entity, SohuAccountModel.class);
//            resultMap.put(model.getUserId(), model);
//        }
//        return resultMap;
//    }

    @Override
    public Map<Long, SohuAccountVo> selectAccountMapByUserIdsOfPass(Collection<Long> userIds) {
        List<SohuAccountVo> list = accountService.selectListByUserIdsOfPass(userIds);
        return list.stream().collect(Collectors.toMap(SohuAccountVo::getUserId, p -> p));
    }

    @Override
    public Map<Long, SohuAccountVo> selectAccountMapByUserIds(Collection<Long> userIds) {
        return accountService.selectAccountMapByUserIds(userIds);
    }

    @Override
    public List<SohuAccountBankModel> queryListByUserId(List<Long> userIds) {
        List<SohuAccountBankVo> sohuAccountBankVos = accountBankService.queryListByUserId(userIds);
        return BeanCopyUtils.copyList(sohuAccountBankVos, SohuAccountBankModel.class);
    }

    @Override
    public List<SohuAccountBankVo> queryPassListByUserId(List<Long> userId) {
        return accountBankService.queryPassListByUserId(userId);
    }

    @Override
    public SohuAccountBankModel queryAccountBankByUserId(Long userId) {
        SohuAccountBankVo sohuAccountBankVo = accountBankService.queryByUserId(userId);
        return BeanCopyUtils.copy(sohuAccountBankVo, SohuAccountBankModel.class);
    }

    @Override
    public Boolean YiMaBankAdd(String msg) {
        SohuAccountBank bean = JSONUtil.toBean(msg, SohuAccountBank.class);
        if (Objects.isNull(bean)) {
            return Boolean.FALSE;
        }
        return accountBankService.YiMaBankAdd(bean);
    }

    @Override
    public long selectPassAccountCount() {
        return accountService.selectPassAccountCount();
    }

    @Override
    public Boolean updateStateOfTimeout() {
        return accountService.updateStateOfTimeout();
    }

    @Override
    public Boolean update(SohuAccountBo bo) {
        return accountService.update(bo);
    }

    @Override
    public Boolean save(SohuAccountBo bo) {
        return accountService.save(bo);
    }

    @Override
    public Boolean saveByBo(SohuAccountBo bo) {
        return accountService.saveByBo(bo);
    }

    @Override
    public String auditByThirdParty(SohuAccountBo bo) {
        return accountService.auditByThirdParty(bo);
    }

    @Override
    public Map<String, String> sendYmWithdrawal(Long userId, String tradeNo, BigDecimal amount, String remark, Long billId) {
        return accountBankService.sendYmWithdrawal(userId, tradeNo, amount, remark, billId);
    }

    @Override
    public String queryWithdrawal(String tradeNo, Long billId) {
        return accountBankService.queryResWithdrawal(tradeNo, billId);
    }

    @Override
    public Long insertByBo(SohuAccountBo bo) {
        return accountService.insertByBo(bo);
    }

    @Override
    public Boolean updateByBo(SohuAccountBo bo) {
        return accountService.updateByBo(bo);
    }

    @Override
    public Boolean upgrage(SohuAccountUpgradeBo upgradeBo) {
        return sohuAccountUpgradeService.upgrage(upgradeBo);
    }

    @Override
    public Boolean accountUpgradeJobHandler() {
        return sohuAccountUpgradeService.accountUpgradeJobHandler();
    }

    @Override
    public List<SohuAccountVo> queryList(SohuAccountBo bo) {
        return accountService.queryList(bo);
    }

    @Override
    public Map<Long, Boolean> afterFreezeMap(List<Long> userIds, Date freezeTime) {
        return accountService.afterFreezeMap(userIds, freezeTime);
    }

//    @Override
//    public void saveAccount(SohuAccountModel sohuAccountModel) {
//        accountService.saveAccount(BeanCopyUtils.copy(sohuAccountModel, SohuAccount.class));
//    }

    @Override
    public SohuAccountVo queryByUserIdOfPass(Long userId) {
        return accountService.queryByUserIdOfPass(userId);
    }

    @Override
    public SohuAccountVo queryByUserIdOfPassOrWaitApprove(Long userId) {
        return accountService.queryByUserIdOfPassOrWaitApprove(userId);
    }

//    @Override
//    public SohuAccountModel checkAccountTypeByUserId(Long userId) {
//        return accountService.checkAccountTypeByUserId(userId);
//    }
}
