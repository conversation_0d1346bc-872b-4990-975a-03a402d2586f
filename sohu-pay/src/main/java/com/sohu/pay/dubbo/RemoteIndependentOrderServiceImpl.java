package com.sohu.pay.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.pay.api.RemoteIndependentOrderService;
import com.sohu.pay.api.bo.SohuIndependentOrderBo;
import com.sohu.pay.api.model.SohuIndependentOrderModel;
import com.sohu.pay.domain.SohuIndependentOrder;
import com.sohu.pay.mapper.SohuIndependentOrderMapper;
import com.sohu.pay.service.ISohuIndependentOrderService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 第三方分账单
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteIndependentOrderServiceImpl implements RemoteIndependentOrderService {

    private final ISohuIndependentOrderService sohuIndependentOrderService;
    private final SohuIndependentOrderMapper sohuIndependentOrderMapper;


    @Override
    public Boolean insert(SohuIndependentOrderModel model) {
        SohuIndependentOrderBo bo = BeanUtil.copyProperties(model, SohuIndependentOrderBo.class);
        return sohuIndependentOrderService.insertByBo(bo);
    }

    @Override
    public Boolean batchInsert(List<SohuIndependentOrderModel> model) {
        List<SohuIndependentOrderBo> boList = BeanUtil.copyToList(model, SohuIndependentOrderBo.class);
        return sohuIndependentOrderService.insertByBoList(boList);
    }

    @Override
    public void updateIndependentStatus(String orderNo, String tradeType, int independentStatus) {
        sohuIndependentOrderMapper.updateIndependentStatus(orderNo, tradeType, independentStatus);
    }

    @Override
    public BigDecimal queryAmount(String orderNo, String tradeType, String independentObject, Long userId) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentOrder::getOrderNo, orderNo).eq(SohuIndependentOrder::getTradeType, tradeType).eq(SohuIndependentOrder::getUserId, userId);
        if (StrUtil.isNotBlank(independentObject)) {
            lqw.eq(SohuIndependentOrder::getIndependentObject, independentObject);
        }
        SohuIndependentOrder sohuIndependentOrder = sohuIndependentOrderMapper.selectOne(lqw);
        return Objects.nonNull(sohuIndependentOrder) ? sohuIndependentOrder.getIndependentPrice() : null;
    }

    @Override
    public List<SohuIndependentOrderModel> queryByOrderNo(String orderNo, String tradeType) {
        List<SohuIndependentOrder> orders = sohuIndependentOrderMapper.selectList(SohuIndependentOrder::getOrderNo, orderNo, SohuIndependentOrder::getTradeType, tradeType);
        List<SohuIndependentOrderModel> result = new ArrayList<>();
        if (CollUtil.isEmpty(orders)) {
            return result;
        }
        for (SohuIndependentOrder order : orders) {
            SohuIndependentOrderModel item = new SohuIndependentOrderModel();
            BeanUtil.copyProperties(order, item);
            result.add(item);
        }
        return result;
    }

    @Override
    public List<SohuIndependentOrderModel> queryByOrderNo(String orderNo, String tradeType, Integer independentStatus) {
        LambdaQueryWrapper<SohuIndependentOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuIndependentOrder::getOrderNo, orderNo);
        lqw.eq(StrUtil.isNotEmpty(tradeType),  SohuIndependentOrder::getTradeType, tradeType);
        if (independentStatus != null) {
            lqw.eq(SohuIndependentOrder::getIndependentStatus, independentStatus);
        }
        List<SohuIndependentOrder> orders = sohuIndependentOrderMapper.selectList(lqw);
        List<SohuIndependentOrderModel> result = new ArrayList<>();
        if (CollUtil.isEmpty(orders)) {
            return result;
        }
        for (SohuIndependentOrder order : orders) {
            SohuIndependentOrderModel item = new SohuIndependentOrderModel();
            BeanUtil.copyProperties(order, item);
            result.add(item);
        }
        return result;
    }

    @Override
    public BigDecimal selectIndependentingByUserId(Long userId, Integer siteType, Long siteId, List<String> selectRoles, String independentObject, Integer independentStatus, Date startTime, Date endTime) {
        return sohuIndependentOrderService.selectIndependentingByUserId(userId, siteType, siteId, selectRoles, independentObject, independentStatus, startTime, endTime);
    }

    @Override
    public BigDecimal selectIndependentInviteByUserId(Long userId) {
        return sohuIndependentOrderService.selectIndependentInviteByUserId(userId);
    }

    @Override
    public Map<String, Object> queryByUserId(Long userId, String independentObject, String startTime, String endTime, Integer pageNum, Integer pageSize) {
        Page<SohuIndependentOrderModel> data = sohuIndependentOrderService.queryByUserId(userId, independentObject, startTime, endTime, pageNum, pageSize);
        Map<String, Object> map = new HashMap<>();
        map.put("total", data.getTotal());
        map.put("records", data.getRecords());
        return map;
    }

    @Override
    public List<SohuIndependentOrderModel> inviteAmount(Long userId) {
        List<SohuIndependentOrder> sohuIndependentOrders = sohuIndependentOrderService.inviteAmount(userId);
        List<SohuIndependentOrderModel> result = new ArrayList<>();
        if (CollUtil.isEmpty(sohuIndependentOrders)) {
            return result;
        }
        for (SohuIndependentOrder order : sohuIndependentOrders) {
            SohuIndependentOrderModel item = new SohuIndependentOrderModel();
            BeanUtil.copyProperties(order, item);
            result.add(item);
        }
        return result;
    }

    @Override
    public List<SohuIndependentOrderModel> groupIndependentByUserId(Long userId, Long siteId, List<String> selectRoles) {
        return sohuIndependentOrderService.groupIndependentByUserId(userId, siteId, selectRoles);
    }

    @Override
    public List<SohuIndependentOrderModel> queryByInviteUser(Long consumerUserId, Long inviteUserId, String independentObject, List<Integer> independentStatus) {
        return sohuIndependentOrderService.queryByInviteUser(consumerUserId, inviteUserId, independentObject, independentStatus);
    }

    @Override
    public List<SohuIndependentOrderModel> queryListForId(Long id) {
        return sohuIndependentOrderService.queryListForId(id);
    }
}
