package com.sohu.pay.dubbo;

import com.sohu.pay.api.RemoteAccountProcessService;
import com.sohu.pay.api.bo.AccountPlatformBo;
import com.sohu.pay.service.AccountProcessService;
import com.sohu.shoporder.api.bo.SohuIndependentTempBo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 15:55
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteAccountProcessServiceImpl implements RemoteAccountProcessService {

    private final AccountProcessService accountProcessService;
    @Override
    public Boolean waitIncomeProcess(String busyType, Long busyCode) {
        return null;
    }

    @Override
    public List<SohuIndependentTempBo> accountPlatformObjects(AccountPlatformBo bo) {
        return accountProcessService.accountPlatformObjects(bo);
    }
}
