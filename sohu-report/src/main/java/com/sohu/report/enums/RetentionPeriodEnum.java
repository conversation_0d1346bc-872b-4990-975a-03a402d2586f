import com.sohu.report.api.vo.SohuAgentRetentionVo;

import java.util.function.BiConsumer;

package com.sohu.report.enum;

import com.sohu.report.api.vo.SohuAgentRetentionVo;

import java.util.function.BiConsumer;

public enum RetentionPeriodEnum {
    DAY_1_AFTER(1, "1天后", SohuAgentRetentionVo::setDay1After),
    DAY_2_AFTER(2, "2天后", SohuAgentRetentionVo::setDay2After),
    DAY_3_AFTER(3, "3天后", SohuAgentRetentionVo::setDay3After),
    DAY_4_AFTER(4, "4天后", SohuAgentRetentionVo::setDay4After),
    DAY_5_AFTER(5, "5天后", SohuAgentRetentionVo::setDay5After),
    DAY_6_AFTER(6, "6天后", SohuAgentRetentionVo::setDay6After),
    DAY_7_AFTER(7, "7天后", SohuAgentRetentionVo::setDay7After),
    DAY_14_AFTER(14, "14天后", SohuAgentRetentionVo::setDay14After),
    DAY_30_AFTER(30, "30天后", SohuAgentRetentionVo::setDay30After);

    private final int days;
    private final String description;
    // 使用 BiConsumer 来直接引用 SohuAgentRetentionVo 的 setter 方法
    private final BiConsumer<SohuAgentRetentionVo, Long> setter;

    RetentionPeriodEnum(int days, String description, BiConsumer<SohuAgentRetentionVo, Long> setter) {
        this.days = days;
        this.description = description;
        this.setter = setter;
    }

    public int getDays() {
        return days;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 将留存数量设置到对应的 RetentionVo 字段上
     * @param retentionVo 留存数据VO
     * @param count 留存数量
     */
    public void setRetentionValue(SohuAgentRetentionVo retentionVo, Long count) {
        this.setter.accept(retentionVo, count);
    }
}