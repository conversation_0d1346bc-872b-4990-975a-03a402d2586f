package com.sohu.report.mapper;

import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo;
import com.sohu.report.domain.SohuUserIncomeStatisticsInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 用户收益明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface SohuUserIncomeStatisticsInfoMapper extends BaseMapperPlus<SohuUserIncomeStatisticsInfoMapper, SohuUserIncomeStatisticsInfo, SohuUserIncomeStatisticsInfoVo> {

    List<SohuUserIncomeStatisticsInfoVo> getUserTop(@Param("userId") Long userId, @Param("stationId")Long stationId, @Param("stationType")Integer stationType, @Param("startDate")Date startDate,@Param("endDate") Date endDate);

    List<SohuUserIncomeStatisticsInfoVo> getBusyTop(@Param("userId")Long userId, @Param("stationId")Long stationId,@Param("stationType") Integer stationType, @Param("startDate")Date startDate,@Param("endDate") Date endDate);

    List<SohuUserIncomeStatisticsInfoVo> getChannelTop(@Param("userId")Long userId, @Param("stationId")Long stationId, @Param("stationType")Integer stationType,@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}
