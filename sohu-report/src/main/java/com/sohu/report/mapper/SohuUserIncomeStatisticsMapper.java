package com.sohu.report.mapper;

import com.sohu.common.mybatis.core.mapper.BaseMapperPlus;
import com.sohu.report.api.vo.SohuShopGoodsStatVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsVo;
import com.sohu.report.domain.SohuUserIncomeStatistics;
import org.apache.ibatis.annotations.Mapper;
import com.sohu.report.vo.UserIncomeVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 用户收益统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Mapper
public interface SohuUserIncomeStatisticsMapper extends BaseMapperPlus<SohuUserIncomeStatisticsMapper, SohuUserIncomeStatistics, SohuUserIncomeStatisticsVo> {

    List<SohuShopGoodsStatVo> selectTopProducts(
            @Param("stationId") Long stationId,
            @Param("stationType") Integer stationType,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("productCategoryIds") List<Long> productCategoryIds);

    /**
     * 基于时间查询用户收益
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<UserIncomeVO> queryUserIncomeByTime(@Param("startTime")Date startTime,@Param("endTime")Date endTime);

    /**
     * 查询收益概览
     *
     * @param stationId
     * @param stationType
     * @param startDate
     * @param endDate
     * @param userId
     * @return
     */
    List<UserIncomeVO> queryIncomeOverview(@Param("stationId") Long stationId,
                                           @Param("stationType") Integer stationType,
                                           @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate,
                                           @Param("userId") Long userId);
}
