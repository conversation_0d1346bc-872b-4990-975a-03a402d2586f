package com.sohu.report.util;

import cn.hutool.core.util.StrUtil;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.enums.SohuIndependentObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 定义角色类型映射规则
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuReportRoleUtil {

    private static final List<String> PERSON_ROLES = List.of(
            SohuIndependentObject.rece.getKey(),
            SohuIndependentObject.invite.getKey(),
            SohuIndependentObject.distribution.getKey(),
            SohuIndependentObject.distributionInvite.getKey()
    );

    private static final List<String> SITE_ROLES = List.of(
            SohuIndependentObject.country.getKey(),
            SohuIndependentObject.city.getKey(),
            SohuIndependentObject.entrance.getKey(),
            SohuIndependentObject.industrysite.getKey(),
            SohuIndependentObject.invitecity.getKey()
    );

    private static final List<String> AGENCY_ROLES = List.of(
            SohuIndependentObject.agency.getKey()
    );

    /**
     * 获取角色类型
     *
     * @param independentObject 独立对象标识
     * @return 返回 person / site / agency 三种类型之一
     * @throws IllegalArgumentException 如果 independentObject 无法识别
     */
    public String getIndependentObjectRoleType(String independentObject) {
        if (PERSON_ROLES.contains(independentObject)) {
            return "person";
        } else if (SITE_ROLES.contains(independentObject)) {
            return "site";
        } else if (AGENCY_ROLES.contains(independentObject)) {
            return "agency";
        } else {
            log.warn("Unrecognized independentObject: {}", independentObject);
            throw new IllegalArgumentException("Unknown independentObject: " + independentObject);
        }
    }

    public String getRoleType(String roleTypeBo) {
        if (StrUtil.equalsAnyIgnoreCase(roleTypeBo, RoleCodeEnum.CityStationAgent.getCode())) {
            return "site";
        }
        if (StrUtil.equalsAnyIgnoreCase(roleTypeBo, RoleCodeEnum.Agent.getCode())) {
            return "agent";
        }
        return null;
    }
}
