package com.sohu.report.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.SohuIndependentObject;
import com.sohu.report.api.vo.*;
import com.sohu.report.vo.GroupedIncomeVO;
import com.sohu.report.vo.SiteKey;
import com.sohu.report.vo.UserIncomeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 站点报表数据转换工具类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuReportConvertUtil {

    private final SohuReportGroupSumUtil sohuReportGroupSumUtil;

    /**
     * 周收益数据转换为饼图数据
     *
     * @param list
     * @return
     */
    public List<SohuPieChartStatVo> convertWeekToPieChartData(List<SohuUserIncomeStatisticsWeekVo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<SohuPieChartStatVo>();
        }
        BigDecimal totalProfit = list.stream().map(SohuUserIncomeStatisticsWeekVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        return list.stream().map(agg -> {
            SohuPieChartStatVo vo = new SohuPieChartStatVo();
            vo.setType(agg.getBusyType());
            vo.setName(BusyType.mapBusyDesc.get(agg.getBusyType().toLowerCase()));
            vo.setProfit(agg.getTotalIncome());

            String ratio = "0.0%";
            if (totalProfit.compareTo(BigDecimal.ZERO) > 0) {
                ratio = agg.getTotalIncome().multiply(BigDecimal.valueOf(100)).divide(totalProfit, 2, RoundingMode.HALF_UP).toPlainString() + "%";
            }
            vo.setRadio(ratio);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 月收益数据转换为饼图数据
     *
     * @param list
     * @return
     */
    public List<SohuPieChartStatVo> convertMonthToPieChartData(List<SohuUserIncomeStatisticsMonthVo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<SohuPieChartStatVo>();
        }
        BigDecimal totalProfit = list.stream().map(SohuUserIncomeStatisticsMonthVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        return list.stream().map(agg -> {
            SohuPieChartStatVo vo = new SohuPieChartStatVo();
            vo.setType(agg.getBusyType());
            vo.setName(BusyType.mapBusyDesc.get(agg.getBusyType().toLowerCase()));
            vo.setProfit(agg.getTotalIncome());

            String ratio = "0.0%";
            if (totalProfit.compareTo(BigDecimal.ZERO) > 0) {
                ratio = agg.getTotalIncome().multiply(BigDecimal.valueOf(100)).divide(totalProfit, 2, RoundingMode.HALF_UP).toPlainString() + "%";
            }
            vo.setRadio(ratio);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 日收益数据转换为饼图数据
     *
     * @param list
     * @return
     */
    public List<SohuPieChartStatVo> convertDayToPieChartData(List<SohuUserIncomeStatisticsDayVo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<SohuPieChartStatVo>();
        }
        BigDecimal totalProfit = list.stream().map(SohuUserIncomeStatisticsDayVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<SohuPieChartStatVo> collect = list.stream().map(agg -> {
            SohuPieChartStatVo vo = new SohuPieChartStatVo();
            vo.setType(agg.getBusyType());
            vo.setName(BusyType.mapBusyDesc.get(agg.getBusyType().toLowerCase()));
            vo.setProfit(agg.getTotalIncome());

            String ratio = "0.0%";
            if (totalProfit.compareTo(BigDecimal.ZERO) > 0) {
                ratio = agg.getTotalIncome().multiply(BigDecimal.valueOf(100)).divide(totalProfit, 2, RoundingMode.HALF_UP).toPlainString() + "%";
            }
            vo.setRadio(ratio);
            return vo;
        }).collect(Collectors.toList());
        // 所有应有的业务类型
        List<String> busyTypes = Arrays.asList(
                BusyType.Playlet.getType(),
                BusyType.Goods.getType(),
                BusyType.Novel.getType(),
                BusyType.BusyOrder.getType()
        );

        // 转成小写类型 map，便于比较和补全
        Map<String, SohuPieChartStatVo> resultMap = collect.stream()
                .collect(Collectors.toMap(vo -> vo.getType().toLowerCase(), vo -> vo, (a, b) -> a));

        // 补全缺失类型
        for (String busyType : busyTypes) {
            String key = busyType.toLowerCase();
            if (!resultMap.containsKey(key)) {
                SohuPieChartStatVo vo = new SohuPieChartStatVo();
                vo.setType(key);
                vo.setName(BusyType.mapBusyDesc.getOrDefault(key, "未知类型"));
                vo.setProfit(BigDecimal.ZERO);
                vo.setRadio("0.00%");
                resultMap.put(key, vo);
            }
        }

        // 按 busyTypes 顺序输出（LinkedList 可保序）
        List<SohuPieChartStatVo> result = new LinkedList<>();
        for (String busyType : busyTypes) {
            SohuPieChartStatVo vo = resultMap.get(busyType.toLowerCase());
            if (vo != null) {
                result.add(vo);
            }
        }
        return result;
    }

    /**
     * 日收益数据转换,根据日期聚合
     *
     * @param list
     * @return
     */
    /**
     * 根据 dayDate 对统计数据按天聚合
     */
    public List<SohuLineChartVo> convertDayToLineChartData(List<SohuUserIncomeStatisticsDayVo> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<SohuUserIncomeStatisticsDayVo> groupList = sohuReportGroupSumUtil.groupSumDayByDate(dataList);
        return groupList.stream().map(entry -> {
            SohuLineChartVo item = SohuLineChartVo.builder().build();
            item.setX(entry.getDayDate());
            item.setY(entry.getTotalIncome().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineChartVo::getX)).collect(Collectors.toList());
    }

    /**
     * 周收益数据转换,根据周聚合
     *
     * @param list
     * @return
     */
    /**
     * 根据 weekNum 对统计数据按周聚合
     */
    public List<SohuLineChartVo> convertWeekToLineChartData(List<SohuUserIncomeStatisticsWeekVo> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<SohuUserIncomeStatisticsWeekVo> groupList = sohuReportGroupSumUtil.groupSumWeekByDate(dataList);
        return groupList.stream().map(entry -> {
            SohuLineChartVo item = SohuLineChartVo.builder().build();
            item.setX(String.valueOf(entry.getWeekDate()));
            item.setY(entry.getTotalIncome().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineChartVo::getX)).collect(Collectors.toList());
    }

    /**
     * 月收益数据转换,根据月聚合
     *
     * @param list
     * @return
     */
    /**
     * 根据 monthNum 对统计数据按月聚合
     */
    public List<SohuLineChartVo> convertMonthToLineChartData(List<SohuUserIncomeStatisticsMonthVo> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<SohuUserIncomeStatisticsMonthVo> groupList = sohuReportGroupSumUtil.groupSumMonthByDate(dataList);

        return groupList.stream().map(entry -> {

            SohuLineChartVo item = SohuLineChartVo.builder().build();
            item.setX(String.valueOf(entry.getMonthDate()));
            item.setY(entry.getTotalIncome().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineChartVo::getX)).collect(Collectors.toList());
    }


    /**
     * 分组逻辑：
     * 1. 按用户id分组
     * 2. 按站点id + 站点类型分组
     * 3. 按 independentObject 分组
     * 4. 在同一个 independentObject 下，再按 tradeType 分组
     * 5. 在同一个 independentObject + tradeType 下，将 status=0/2 和 status=1/4 分别汇总
     */
    public List<GroupedIncomeVO> groupIncomeData(List<UserIncomeVO> userIncomeList, boolean needGroupByTradeType) {
        // 定义角色类型映射规则
        List<String> personRoles = List.of(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
        List<String> siteRoles = List.of(SohuIndependentObject.country.getKey(), SohuIndependentObject.city.getKey(), SohuIndependentObject.entrance.getKey(), SohuIndependentObject.industrysite.getKey(), SohuIndependentObject.invitecity.getKey());
        List<String> agencyRoles = List.of(SohuIndependentObject.agency.getKey());
        String ALL_TRADE_TYPE = "ALL";
        List<GroupedIncomeVO> result = new ArrayList<>();
        // 四层嵌套Map：userId → siteKey → roleType → tradeType → GroupedIncomeVO
        Map<Long, Map<SiteKey, Map<String, Map<String, GroupedIncomeVO>>>> tempMap = new HashMap<>();
        for (UserIncomeVO income : userIncomeList) {
            Long userId = income.getUserId();
            // 处理站点组合键
            SiteKey siteKey = new SiteKey((income.getSiteId() == null || income.getSiteId() == 0) ? 0 : income.getSiteId(), income.getSiteType());
            // 根据independentObject映射角色类型
            String roleType;
            String independentObject = income.getIndependentObject();
            if (personRoles.contains(independentObject)) {
                roleType = "person";
            } else if (siteRoles.contains(independentObject)) {
                roleType = "site";
            } else if (agencyRoles.contains(independentObject)) {
                roleType = "agency";
            } else {
                continue; // 或抛异常，根据业务需求
            }
            // 动态tradeType控制
            String tradeType = needGroupByTradeType ? income.getTradeType() : ALL_TRADE_TYPE;
            int status = Integer.parseInt(income.getIndependentStatus());
            BigDecimal amount = income.getTotalAmount();
            // 初始化分组结构
            tempMap.computeIfAbsent(userId, k -> new HashMap<>()).computeIfAbsent(siteKey, k -> new HashMap<>()).computeIfAbsent(roleType, k -> new HashMap<>()) // 关键调整：角色类型作为第三层分组键
                    .computeIfAbsent(tradeType, k -> {
                        GroupedIncomeVO vo = new GroupedIncomeVO();
                        vo.setUserId(userId);
                        vo.setSiteId(siteKey.getSiteId());
                        vo.setSiteType(siteKey.getSiteType());
                        vo.setIndependentObject(roleType); // 新增角色类型字段
                        vo.setTradeType(tradeType);
                        vo.setWaitEntryAmount(BigDecimal.ZERO);
                        vo.setEntryAmount(BigDecimal.ZERO);
                        return vo;
                    });

            // 金额汇总逻辑
            GroupedIncomeVO groupedIncome = tempMap.get(userId).get(siteKey).get(roleType).get(tradeType);
            if (status == 0 || status == 2) {
                groupedIncome.setWaitEntryAmount(groupedIncome.getWaitEntryAmount().add(amount));
            } else if (status == 1 || status == 4) {
                groupedIncome.setEntryAmount(groupedIncome.getEntryAmount().add(amount));
            }
        }
        // 结果转换
        tempMap.values().forEach(siteMap -> siteMap.values().forEach(roleMap -> roleMap.values().forEach(tradeTypeMap -> result.addAll(tradeTypeMap.values()))));
        return result;
    }

    /**
     * 服务商拉新概览折线图-转换-日
     *
     * @param dayVoList
     * @param startDate
     * @param endDate
     * @return {@link List}
     */
    public List<SohuLineInviteVo> agentDayStationInviteBoard(List<SohuUserIncomeStatisticsDayVo> dayVoList, String startDate, String endDate) {
        if (CollUtil.isNotEmpty(dayVoList)) {
            return dayVoList.stream().map(entry -> {
                SohuLineInviteVo item = SohuLineInviteVo.builder().build();
                item.setX(String.valueOf(entry.getDayDate()));
                item.setInviteY(Optional.ofNullable(entry.getInviteNum()).orElse(0L).toString());
                item.setBandY(Optional.ofNullable(entry.getInviteBindNum()).orElse(0L).toString());
                return item;
            }).sorted(Comparator.comparing(SohuLineInviteVo::getX)).collect(Collectors.toList());
        }

        return generateDefaultInviteData(startDate, endDate);
    }

    /**
     * 服务商拉新概览折线图-转换-周
     *
     * @param weekVoList
     * @param startDate
     * @param endDate
     * @return {@link List}
     */
    public List<SohuLineInviteVo> agentWeekStationInviteBoard(List<SohuUserIncomeStatisticsWeekVo> weekVoList, String startDate, String endDate) {
        if (CollUtil.isNotEmpty(weekVoList)) {
            return weekVoList.stream().map(entry -> {
                SohuLineInviteVo item = SohuLineInviteVo.builder().build();
                item.setX(String.valueOf(entry.getWeekDate()));
                item.setInviteY(Optional.ofNullable(entry.getInviteNum()).orElse(0L).toString());
                item.setBandY(Optional.ofNullable(entry.getInviteBindNum()).orElse(0L).toString());
                return item;
            }).sorted(Comparator.comparing(SohuLineInviteVo::getX)).collect(Collectors.toList());
        }

        return generateDefaultInviteData(startDate, endDate);
    }

    /**
     * 服务商拉新概览折线图-转换-月
     *
     * @param monthVoList
     * @param startDate
     * @param endDate
     * @return {@link List}
     */
    public List<SohuLineInviteVo> agentMonthStationInviteBoard(List<SohuUserIncomeStatisticsMonthVo> monthVoList, String startDate, String endDate) {
        if (CollUtil.isNotEmpty(monthVoList)) {
            return monthVoList.stream().map(entry -> {
                SohuLineInviteVo item = SohuLineInviteVo.builder().build();
                item.setX(String.valueOf(entry.getMonthDate()));
                item.setInviteY(Optional.ofNullable(entry.getInviteNum()).orElse(0L).toString());
                item.setBandY(Optional.ofNullable(entry.getInviteBindNum()).orElse(0L).toString());
                return item;
            }).sorted(Comparator.comparing(SohuLineInviteVo::getX)).collect(Collectors.toList());
        }

        // 兜底处理：生成默认数据
        return generateDefaultInviteData(startDate, endDate);
    }

    /**
     * 兜底默认数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 默认数据列表
     */
    private List<SohuLineInviteVo> generateDefaultInviteData(String startDate, String endDate) {
        List<SohuLineInviteVo> defaultList = new ArrayList<>();

        // 如果没有时间范围，返回空列表或者默认当前月份
        if (StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
            return Collections.emptyList();
        }

        try {
            // 解析时间范围，生成每个月的默认数据
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            LocalDate current = start.withDayOfMonth(1);
            while (!current.isAfter(end)) {
                SohuLineInviteVo item = SohuLineInviteVo.builder()
                        .x(current.format(DateTimeFormatter.ofPattern("yyyy-MM")))
                        .inviteY("0")
                        .bandY("0")
                        .build();
                defaultList.add(item);
                current = current.plusMonths(1);
            }
        } catch (Exception e) {
            // 时间解析失败，返回空列表
            return Collections.emptyList();
        }

        return defaultList;
    }

}
