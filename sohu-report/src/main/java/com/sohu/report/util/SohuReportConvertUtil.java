package com.sohu.report.util;

import cn.hutool.core.collection.CollUtil;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.SohuIndependentObject;
import com.sohu.report.api.vo.*;
import com.sohu.report.vo.GroupedIncomeVO;
import com.sohu.report.vo.SiteKey;
import com.sohu.report.vo.UserIncomeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 站点报表数据转换工具类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuReportConvertUtil {

    private final SohuReportGroupSumUtil sohuReportGroupSumUtil;

    /**
     * 周收益数据转换为饼图数据
     *
     * @param list
     * @return
     */
    public List<SohuPieChartStatVo> convertWeekToPieChartData(List<SohuUserIncomeStatisticsWeekVo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<SohuPieChartStatVo>();
        }
        BigDecimal totalProfit = list.stream().map(SohuUserIncomeStatisticsWeekVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        return list.stream().map(agg -> {
            SohuPieChartStatVo vo = new SohuPieChartStatVo();
            vo.setType(agg.getBusyType());
            vo.setName(BusyType.mapBusyDesc.get(agg.getBusyType().toLowerCase()));
            vo.setProfit(agg.getTotalIncome());

            String ratio = "0.0%";
            if (totalProfit.compareTo(BigDecimal.ZERO) > 0) {
                ratio = agg.getTotalIncome().multiply(BigDecimal.valueOf(100)).divide(totalProfit, 2, RoundingMode.HALF_UP).toPlainString() + "%";
            }
            vo.setRadio(ratio);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 月收益数据转换为饼图数据
     *
     * @param list
     * @return
     */
    public List<SohuPieChartStatVo> convertMonthToPieChartData(List<SohuUserIncomeStatisticsMonthVo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<SohuPieChartStatVo>();
        }
        BigDecimal totalProfit = list.stream().map(SohuUserIncomeStatisticsMonthVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        return list.stream().map(agg -> {
            SohuPieChartStatVo vo = new SohuPieChartStatVo();
            vo.setType(agg.getBusyType());
            vo.setName(BusyType.mapBusyDesc.get(agg.getBusyType().toLowerCase()));
            vo.setProfit(agg.getTotalIncome());

            String ratio = "0.0%";
            if (totalProfit.compareTo(BigDecimal.ZERO) > 0) {
                ratio = agg.getTotalIncome().multiply(BigDecimal.valueOf(100)).divide(totalProfit, 2, RoundingMode.HALF_UP).toPlainString() + "%";
            }
            vo.setRadio(ratio);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 日收益数据转换为饼图数据
     *
     * @param list
     * @return
     */
    public List<SohuPieChartStatVo> convertDayToPieChartData(List<SohuUserIncomeStatisticsDayVo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<SohuPieChartStatVo>();
        }
        BigDecimal totalProfit = list.stream().map(SohuUserIncomeStatisticsDayVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        return list.stream().map(agg -> {
            SohuPieChartStatVo vo = new SohuPieChartStatVo();
            vo.setType(agg.getBusyType());
            vo.setName(BusyType.mapBusyDesc.get(agg.getBusyType().toLowerCase()));
            vo.setProfit(agg.getTotalIncome());

            String ratio = "0.0%";
            if (totalProfit.compareTo(BigDecimal.ZERO) > 0) {
                ratio = agg.getTotalIncome().multiply(BigDecimal.valueOf(100)).divide(totalProfit, 2, RoundingMode.HALF_UP).toPlainString() + "%";
            }
            vo.setRadio(ratio);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 日收益数据转换,根据日期聚合
     *
     * @param list
     * @return
     */
    /**
     * 根据 dayDate 对统计数据按天聚合
     */
    public List<SohuLineChartVo> convertDayToLineChartData(List<SohuUserIncomeStatisticsDayVo> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<SohuUserIncomeStatisticsDayVo> groupList = sohuReportGroupSumUtil.groupSumDayByDate(dataList);
        return groupList.stream().map(entry -> {
            SohuLineChartVo item = SohuLineChartVo.builder().build();
            item.setX(entry.getDayDate());
            item.setY(entry.getTotalIncome().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineChartVo::getX)).collect(Collectors.toList());
    }

    /**
     * 周收益数据转换,根据周聚合
     *
     * @param list
     * @return
     */
    /**
     * 根据 weekNum 对统计数据按周聚合
     */
    public List<SohuLineChartVo> convertWeekToLineChartData(List<SohuUserIncomeStatisticsWeekVo> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<SohuUserIncomeStatisticsWeekVo> groupList = sohuReportGroupSumUtil.groupSumWeekByDate(dataList);
        return groupList.stream().map(entry -> {
            SohuLineChartVo item = SohuLineChartVo.builder().build();
            item.setX(String.valueOf(entry.getWeekDate()));
            item.setY(entry.getTotalIncome().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineChartVo::getX)).collect(Collectors.toList());
    }

    /**
     * 月收益数据转换,根据月聚合
     *
     * @param list
     * @return
     */
    /**
     * 根据 monthNum 对统计数据按月聚合
     */
    public List<SohuLineChartVo> convertMonthToLineChartData(List<SohuUserIncomeStatisticsMonthVo> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<SohuUserIncomeStatisticsMonthVo> groupList = sohuReportGroupSumUtil.groupSumMonthByDate(dataList);

        return groupList.stream().map(entry -> {

            SohuLineChartVo item = SohuLineChartVo.builder().build();
            item.setX(String.valueOf(entry.getMonthDate()));
            item.setY(entry.getTotalIncome().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineChartVo::getX)).collect(Collectors.toList());
    }


    /**
     * 分组逻辑：
     * 1. 按用户id分组
     * 2. 按站点id + 站点类型分组
     * 3. 按 independentObject 分组
     * 4. 在同一个 independentObject 下，再按 tradeType 分组
     * 5. 在同一个 independentObject + tradeType 下，将 status=0/2 和 status=1/4 分别汇总
     */
    public List<GroupedIncomeVO> groupIncomeData(List<UserIncomeVO> userIncomeList, boolean needGroupByTradeType) {
        // 定义角色类型映射规则
        List<String> personRoles = List.of(SohuIndependentObject.rece.getKey(), SohuIndependentObject.invite.getKey(), SohuIndependentObject.distribution.getKey(), SohuIndependentObject.distributionInvite.getKey());
        List<String> siteRoles = List.of(SohuIndependentObject.country.getKey(), SohuIndependentObject.city.getKey(), SohuIndependentObject.entrance.getKey(), SohuIndependentObject.industrysite.getKey(), SohuIndependentObject.invitecity.getKey());
        List<String> agencyRoles = List.of(SohuIndependentObject.agency.getKey());
        String ALL_TRADE_TYPE = "ALL";
        List<GroupedIncomeVO> result = new ArrayList<>();
        // 四层嵌套Map：userId → siteKey → roleType → tradeType → GroupedIncomeVO
        Map<Long, Map<SiteKey, Map<String, Map<String, GroupedIncomeVO>>>> tempMap = new HashMap<>();
        for (UserIncomeVO income : userIncomeList) {
            Long userId = income.getUserId();
            // 处理站点组合键
            SiteKey siteKey = new SiteKey((income.getSiteId() == null || income.getSiteId() == 0) ? 0 : income.getSiteId(), income.getSiteType());
            // 根据independentObject映射角色类型
            String roleType;
            String independentObject = income.getIndependentObject();
            if (personRoles.contains(independentObject)) {
                roleType = "person";
            } else if (siteRoles.contains(independentObject)) {
                roleType = "site";
            } else if (agencyRoles.contains(independentObject)) {
                roleType = "agency";
            } else {
                continue; // 或抛异常，根据业务需求
            }
            // 动态tradeType控制
            String tradeType = needGroupByTradeType ? income.getTradeType() : ALL_TRADE_TYPE;
            int status = Integer.parseInt(income.getIndependentStatus());
            BigDecimal amount = income.getTotalAmount();
            // 初始化分组结构
            tempMap.computeIfAbsent(userId, k -> new HashMap<>()).computeIfAbsent(siteKey, k -> new HashMap<>()).computeIfAbsent(roleType, k -> new HashMap<>()) // 关键调整：角色类型作为第三层分组键
                    .computeIfAbsent(tradeType, k -> {
                        GroupedIncomeVO vo = new GroupedIncomeVO();
                        vo.setUserId(userId);
                        vo.setSiteId(siteKey.getSiteId());
                        vo.setSiteType(siteKey.getSiteType());
                        vo.setIndependentObject(roleType); // 新增角色类型字段
                        vo.setTradeType(tradeType);
                        vo.setWaitEntryAmount(BigDecimal.ZERO);
                        vo.setEntryAmount(BigDecimal.ZERO);
                        return vo;
                    });

            // 金额汇总逻辑
            GroupedIncomeVO groupedIncome = tempMap.get(userId).get(siteKey).get(roleType).get(tradeType);
            if (status == 0 || status == 2) {
                groupedIncome.setWaitEntryAmount(groupedIncome.getWaitEntryAmount().add(amount));
            } else if (status == 1 || status == 4) {
                groupedIncome.setEntryAmount(groupedIncome.getEntryAmount().add(amount));
            }
        }
        // 结果转换
        tempMap.values().forEach(siteMap -> siteMap.values().forEach(roleMap -> roleMap.values().forEach(tradeTypeMap -> result.addAll(tradeTypeMap.values()))));
        return result;
    }

    /**
     * 服务商拉新概览折线图-转换-日
     *
     * @param dayVoList
     * @return {@link List}
     */
    public List<SohuLineInviteVo> agentDayStationInviteBoard(List<SohuUserIncomeStatisticsDayVo> dayVoList) {
        if (CollUtil.isEmpty(dayVoList)) {
            return Collections.emptyList();
        }
        return dayVoList.stream().map(entry -> {
            SohuLineInviteVo item = SohuLineInviteVo.builder().build();
            item.setX(String.valueOf(entry.getDayDate()));
            item.setInviteY(entry.getInviteNum().toString());
            item.setBandY(entry.getInviteBindNum().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineInviteVo::getX)).collect(Collectors.toList());
    }

    /**
     * 服务商拉新概览折线图-转换-周
     *
     * @param weekVoList
     * @return {@link List}
     */
    public List<SohuLineInviteVo> agentWeekStationInviteBoard(List<SohuUserIncomeStatisticsWeekVo> weekVoList) {
        if (CollUtil.isEmpty(weekVoList)) {
            return Collections.emptyList();
        }
        return weekVoList.stream().map(entry -> {
            SohuLineInviteVo item = SohuLineInviteVo.builder().build();
            item.setX(String.valueOf(entry.getWeekDate()));
            item.setInviteY(entry.getInviteNum().toString());
            item.setBandY(entry.getInviteBindNum().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineInviteVo::getX)).collect(Collectors.toList());
    }

    /**
     * 服务商拉新概览折线图-转换-月
     *
     * @param monthVoList
     * @return {@link List}
     */
    public List<SohuLineInviteVo> agentMonthStationInviteBoard(List<SohuUserIncomeStatisticsMonthVo> monthVoList) {
        if (CollUtil.isEmpty(monthVoList)) {
            return Collections.emptyList();
        }
        return monthVoList.stream().map(entry -> {
            SohuLineInviteVo item = SohuLineInviteVo.builder().build();
            item.setX(String.valueOf(entry.getMonthDate()));
            item.setInviteY(entry.getInviteNum().toString());
            item.setBandY(entry.getInviteBindNum().toString());
            return item;
        }).sorted(Comparator.comparing(SohuLineInviteVo::getX)).collect(Collectors.toList());
    }

}
