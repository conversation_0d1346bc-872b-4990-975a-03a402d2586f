package com.sohu.report.util;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Date;
import java.util.Locale;

/**
 * @Author: leibo
 * @Date: 2025/5/30 16:17
 **/
public class DateUtils {

    /**
     * 获取当天的 0 点 0 分 0 秒
     * @param date
     * @return
     */
    public static Date getStartOfDay(Date date) {
        LocalDateTime startOfDay = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .atStartOfDay();
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取今天 0 点 0 分 0 秒 的前一天时间（昨天 0 点）
     * @return Date 类型的前一天 0 点时间
     */
    public static Date getYesterdayStartOfDay(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate previousDay = localDate.minusDays(1);
        LocalDateTime previousDayStart = previousDay.atStartOfDay();
        return Date.from(previousDayStart.atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 判断当前日期是否是本周的第一天（周一）
     */
    public static boolean isStartOfWeek(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.getDayOfWeek() == DayOfWeek.MONDAY;
    }

    /**
     * 获取本周或上周的第一天（周一 0 点）
     * - 如果今天是周一，返回上周一
     * - 否则返回本周一
     */
    public static Date getStartOfWeekOrPrevious(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate startOfWeek = localDate.with(DayOfWeek.MONDAY);
        if (isStartOfWeek(date)) {
            startOfWeek = startOfWeek.minusWeeks(1);
        }
        return Date.from(startOfWeek.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取本周或上周的最后一天（周日 23:59:59）
     * - 如果今天是周一，返回上周日
     * - 否则返回本周日
     */
    public static Date getEndOfWeekOrPrevious(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endOfWeek = localDate.with(DayOfWeek.SUNDAY);
        if (isStartOfWeek(date)) {
            endOfWeek = endOfWeek.minusWeeks(1);
        }
        return Date.from(endOfWeek.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 判断当前日期是否是本月的第一天（1号）
     */
    public static boolean isStartOfMonth(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.getDayOfMonth() == 1;
    }

    /**
     * 获取本月或上月的第一天（1号 0 点）
     * - 如果今天是1号，返回上个月1号
     * - 否则返回本月1号
     */
    public static Date getStartOfMonthOrPrevious(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate startOfMonth = localDate.withDayOfMonth(1);
        if (isStartOfMonth(date)) {
            startOfMonth = startOfMonth.minusMonths(1);
        }
        return Date.from(startOfMonth.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取本月或上月的最后一天（23:59:59）
     * - 如果今天是1号，返回上个月最后一天
     * - 否则返回本月最后一天
     */
    public static Date getEndOfMonthOrPrevious(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endOfMonth = localDate.with(TemporalAdjusters.lastDayOfMonth());
        if (isStartOfMonth(date)) {
            endOfMonth = endOfMonth.minusMonths(1);
        }
        return Date.from(endOfMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 返回年月日
     * @param date
     * @return
     */
    public static String formatToDate(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return localDate.format(formatter);
    }

    /**
     * 返回年周
     *
     * @param date
     * @return
     */
    public static String formatToYearWeek(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 中国标准（周一为周起始）
        WeekFields weekFields = WeekFields.of(Locale.CHINA);
        int week = localDate.get(weekFields.weekOfWeekBasedYear());
        return String.format("%d%02d", localDate.getYear(), week);
    }

    /**
     * 返回年月
     *
     * @param date
     * @return
     */
    public static String formatToYearMonth(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        return localDate.format(formatter);
    }

}
