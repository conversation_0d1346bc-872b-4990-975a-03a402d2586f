package com.sohu.report.util;

import cn.hutool.core.collection.CollUtil;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsDayVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsMonthVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsWeekVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 按业务类型busyType分组并聚合为汇总对象
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuReportGroupSumUtil {

    /**
     * 按业务类型busyType分组并聚合为汇总对象 -日
     */
    public List<SohuUserIncomeStatisticsDayVo> groupSumDayByBusyType(List<SohuUserIncomeStatisticsDayVo> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().filter(vo -> vo.getBusyType() != null).collect(Collectors.groupingBy(SohuUserIncomeStatisticsDayVo::getBusyType)).entrySet().stream().map(entry -> {
            String busyType = entry.getKey();
            List<SohuUserIncomeStatisticsDayVo> item = entry.getValue();

            SohuUserIncomeStatisticsDayVo aggregated = new SohuUserIncomeStatisticsDayVo();
            aggregated.setBusyType(busyType);
            aggregated.setTotalIncome(item.stream().map(SohuUserIncomeStatisticsDayVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setInviteIncome(item.stream().map(SohuUserIncomeStatisticsDayVo::getInviteIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setTradeAmount(item.stream().map(SohuUserIncomeStatisticsDayVo::getTradeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setOrderNum(item.stream().map(SohuUserIncomeStatisticsDayVo::getOrderNum).filter(Objects::nonNull).reduce(0L, Long::sum));
            return aggregated;
        }).collect(Collectors.toList());
    }

    /**
     * 按业务类型busyType分组并聚合为汇总对象 -周
     */
    public List<SohuUserIncomeStatisticsWeekVo> groupSumWeekByBusyType(List<SohuUserIncomeStatisticsWeekVo> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().filter(vo -> vo.getBusyType() != null).collect(Collectors.groupingBy(SohuUserIncomeStatisticsWeekVo::getBusyType)).entrySet().stream().map(entry -> {
            String busyType = entry.getKey();
            List<SohuUserIncomeStatisticsWeekVo> item = entry.getValue();

            SohuUserIncomeStatisticsWeekVo aggregated = new SohuUserIncomeStatisticsWeekVo();
            aggregated.setBusyType(busyType);
            aggregated.setTotalIncome(item.stream().map(SohuUserIncomeStatisticsWeekVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setInviteIncome(item.stream().map(SohuUserIncomeStatisticsWeekVo::getInviteIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setTradeAmount(item.stream().map(SohuUserIncomeStatisticsWeekVo::getTradeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setOrderNum(item.stream().map(SohuUserIncomeStatisticsWeekVo::getOrderNum).filter(Objects::nonNull).reduce(0L, Long::sum));
            return aggregated;
        }).collect(Collectors.toList());
    }

    /**
     * 按业务类型busyType分组并聚合为汇总对象  -月
     */
    public List<SohuUserIncomeStatisticsMonthVo> groupSumMonthByBusyType(List<SohuUserIncomeStatisticsMonthVo> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().filter(vo -> vo.getBusyType() != null).collect(Collectors.groupingBy(SohuUserIncomeStatisticsMonthVo::getBusyType)).entrySet().stream().map(entry -> {
            String busyType = entry.getKey();
            List<SohuUserIncomeStatisticsMonthVo> item = entry.getValue();

            SohuUserIncomeStatisticsMonthVo aggregated = new SohuUserIncomeStatisticsMonthVo();
            aggregated.setBusyType(busyType);
            aggregated.setTotalIncome(item.stream().map(SohuUserIncomeStatisticsMonthVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setInviteIncome(item.stream().map(SohuUserIncomeStatisticsMonthVo::getInviteIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setTradeAmount(item.stream().map(SohuUserIncomeStatisticsMonthVo::getTradeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setOrderNum(item.stream().map(SohuUserIncomeStatisticsMonthVo::getOrderNum).filter(Objects::nonNull).reduce(0L, Long::sum));
            return aggregated;
        }).collect(Collectors.toList());
    }

    /**
     * 按业务类型busyType分组并聚合为汇总对象 -日
     */
    public List<SohuUserIncomeStatisticsDayVo> groupSumDayByDate(List<SohuUserIncomeStatisticsDayVo> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().filter(vo -> vo.getDayDate() != null).collect(Collectors.groupingBy(SohuUserIncomeStatisticsDayVo::getDayDate)).entrySet().stream().map(entry -> {
            String dayDate = entry.getKey();
            List<SohuUserIncomeStatisticsDayVo> item = entry.getValue();

            SohuUserIncomeStatisticsDayVo aggregated = new SohuUserIncomeStatisticsDayVo();
            aggregated.setDayDate(dayDate);
            aggregated.setTotalIncome(item.stream().map(SohuUserIncomeStatisticsDayVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setInviteIncome(item.stream().map(SohuUserIncomeStatisticsDayVo::getInviteIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setTradeAmount(item.stream().map(SohuUserIncomeStatisticsDayVo::getTradeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setOrderNum(item.stream().map(SohuUserIncomeStatisticsDayVo::getOrderNum).filter(Objects::nonNull).reduce(0L, Long::sum));
            aggregated.setInviteNum(item.stream().map(SohuUserIncomeStatisticsDayVo::getInviteNum).reduce(0L, Long::sum));
            aggregated.setInviteBindNum(item.stream().map(SohuUserIncomeStatisticsDayVo::getInviteBindNum).reduce(0L, Long::sum));
            return aggregated;
        }).collect(Collectors.toList());
    }

    /**
     * 按业务类型busyType分组并聚合为汇总对象 -周
     */
    public List<SohuUserIncomeStatisticsWeekVo> groupSumWeekByDate(List<SohuUserIncomeStatisticsWeekVo> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().filter(vo -> vo.getWeekDate() != null).collect(Collectors.groupingBy(SohuUserIncomeStatisticsWeekVo::getWeekDate)).entrySet().stream().map(entry -> {
            String weekDate = entry.getKey();
            List<SohuUserIncomeStatisticsWeekVo> item = entry.getValue();

            SohuUserIncomeStatisticsWeekVo aggregated = new SohuUserIncomeStatisticsWeekVo();
            aggregated.setWeekDate(weekDate);
            aggregated.setTotalIncome(item.stream().map(SohuUserIncomeStatisticsWeekVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setInviteIncome(item.stream().map(SohuUserIncomeStatisticsWeekVo::getInviteIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setTradeAmount(item.stream().map(SohuUserIncomeStatisticsWeekVo::getTradeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setOrderNum(item.stream().map(SohuUserIncomeStatisticsWeekVo::getOrderNum).filter(Objects::nonNull).reduce(0L, Long::sum));
            return aggregated;
        }).collect(Collectors.toList());
    }

    /**
     * 按业务类型busyType分组并聚合为汇总对象  -月
     */
    public List<SohuUserIncomeStatisticsMonthVo> groupSumMonthByDate(List<SohuUserIncomeStatisticsMonthVo> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().filter(vo -> vo.getMonthDate() != null).collect(Collectors.groupingBy(SohuUserIncomeStatisticsMonthVo::getMonthDate)).entrySet().stream().map(entry -> {
            String monthDate = entry.getKey();
            List<SohuUserIncomeStatisticsMonthVo> item = entry.getValue();

            SohuUserIncomeStatisticsMonthVo aggregated = new SohuUserIncomeStatisticsMonthVo();
            aggregated.setMonthDate(monthDate);
            aggregated.setTotalIncome(item.stream().map(SohuUserIncomeStatisticsMonthVo::getTotalIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setInviteIncome(item.stream().map(SohuUserIncomeStatisticsMonthVo::getInviteIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setTradeAmount(item.stream().map(SohuUserIncomeStatisticsMonthVo::getTradeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            aggregated.setOrderNum(item.stream().map(SohuUserIncomeStatisticsMonthVo::getOrderNum).filter(Objects::nonNull).reduce(0L, Long::sum));
            return aggregated;
        }).collect(Collectors.toList());
    }

}
