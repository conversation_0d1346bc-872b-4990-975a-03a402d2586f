package com.sohu.report.util;

import cn.hutool.core.collection.CollUtil;
import com.sohu.common.core.enums.SohuDateEnum;
import com.sohu.report.api.vo.SohuLineChartVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class ChartStatUtil {

    public List<SohuLineChartVo> fillMissingDates(List<SohuLineChartVo> original,
                                                  LocalDate startDate,
                                                  LocalDate endDate,
                                                  SohuDateEnum granularity) {
        // 防空
        if (CollUtil.isEmpty(original)) {
            original = Collections.emptyList();
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 构建已有数据的 map
        Map<String, String> dataMap = original.stream()
                .filter(Objects::nonNull)
                .filter(vo -> vo.getX() != null) // x不能为空
                .collect(Collectors.toMap(
                        SohuLineChartVo::getX,
                        vo -> Optional.ofNullable(vo.getY()).orElse("0"), // y为空设为"0"
                        (a, b) -> b
                ));

        List<SohuLineChartVo> filled = new ArrayList<>();

        LocalDate date = startDate;

        while (!date.isAfter(endDate)) {
            String key;
            if (granularity == SohuDateEnum.WEEK) {
                // 年 + 周，例如 202523
                WeekFields weekFields = WeekFields.ISO;
                int weekNumber = date.get(weekFields.weekOfWeekBasedYear());
                int year = date.get(weekFields.weekBasedYear());
                key = String.format("%d%02d", year, weekNumber);
            } else if (granularity == SohuDateEnum.MONTH) {
                // 年月，例如 202501
                key = date.format(DateTimeFormatter.ofPattern("yyyyMM"));
            } else {
                // 默认使用 yyyy-MM-dd（天）
                key = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }

            String yVal = dataMap.getOrDefault(key, "0");

            filled.add(SohuLineChartVo.builder()
                    .x(key)
                    .y(yVal)
                    .build());

            // 移动到下一个时间单位
            if (granularity == SohuDateEnum.WEEK) {
                date = date.plusWeeks(1);
            } else if (granularity == SohuDateEnum.MONTH) {
                date = date.plusMonths(1);
            } else {
                date = date.plusDays(1);
            }
        }
        return filled;
    }
}
