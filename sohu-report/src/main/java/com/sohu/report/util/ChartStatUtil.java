package com.sohu.report.util;

import cn.hutool.core.collection.CollUtil;
import com.sohu.common.core.enums.SohuDateEnum;
import com.sohu.report.api.vo.SohuLineChartVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class ChartStatUtil {

    public List<SohuLineChartVo> fillMissingDates(List<SohuLineChartVo> original,
                                                  LocalDate startDate,
                                                  LocalDate endDate,
                                                  SohuDateEnum granularity) {
        // 防空
        if (CollUtil.isEmpty(original)) {
            original = Collections.emptyList();
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 构建已有数据的 map
        Map<String, String> dataMap = original.stream()
                .filter(Objects::nonNull)
                .filter(vo -> vo.getX() != null) // x不能为空
                .collect(Collectors.toMap(
                        SohuLineChartVo::getX,
                        vo -> Optional.ofNullable(vo.getY()).orElse("0"), // y为空设为"0"
                        (a, b) -> b
                ));

        List<SohuLineChartVo> filled = new ArrayList<>();

        LocalDate date = startDate;
        while (!date.isAfter(endDate)) {
            String key = date.format(formatter);
            String yVal = dataMap.getOrDefault(key, "0");

            filled.add(SohuLineChartVo.builder()
                    .x(key)
                    .y(yVal)
                    .build());

            // 时间粒度跳转
            switch (granularity) {
                case WEEK:
                    date = date.plusWeeks(1);
                    break;
                case MONTH:
                    date = date.plusMonths(1);
                    break;
                default:
                    date = date.plusDays(1);
            }
        }

        return filled;
    }
}
