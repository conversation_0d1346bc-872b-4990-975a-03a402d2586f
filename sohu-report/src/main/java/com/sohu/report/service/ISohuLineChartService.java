package com.sohu.report.service;

import com.sohu.report.api.bo.SohuBaseDateBo;
import com.sohu.report.api.bo.SohuInviteReportBo;
import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.api.vo.SohuLineInviteVo;

import java.util.List;

/**
 * 饼图服务
 *
 * <AUTHOR>
 */
public interface ISohuLineChartService {

    /**
     * 站长总体收益折线图
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineChartVo> stationAllLineStat(SohuStationReportBo bo);

    /**
     * 站长拉新收益折线图
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineChartVo> stationInviteLineStat(SohuStationReportBo bo);

    /**
     * 服务商拉新概览折线图
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineInviteVo> agentStationInviteBoard(SohuInviteReportBo bo);

}
