package com.sohu.report.service;


import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsWeekBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.api.vo.SohuPieChartStatVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsWeekVo;

import java.util.List;

/**
 * 用户收益周统计Service接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface ISohuUserIncomeStatisticsWeekService {

    /**
     * 查询用户收益周统计列表
     */
    List<SohuUserIncomeStatisticsWeekVo> queryList(SohuUserIncomeStatisticsWeekBo bo);

    /**
     * 修改用户收益周统计
     */
    Boolean insertByBo(SohuUserIncomeStatisticsWeekBo bo);

    /**
     * 修改用户收益周统计
     */
    Boolean updateByBo(SohuUserIncomeStatisticsWeekBo bo);

    /**
     * 站长总体收益饼图
     * 统计各类型收益数据，以饼图形式展现，统计某段时间范围内的 愿望，商品，小说，短剧 收益
     *
     * @param bo 入参
     * @return 饼图数据
     */
    List<SohuPieChartStatVo> stationAllPieStat(SohuStationReportBo bo);

    /**
     * 站长总体收益折线图
     *
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineChartVo> stationAllLineStat(SohuStationReportBo bo);

    /**
     * 站长拉新收益折线图
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineChartVo> stationInviteLineStat(SohuStationReportBo bo);

    /**
     * 基于日期用户查询用户数据统计
     *
     * @param userId
     * @param roleType
     * @param siteId
     * @param busyType
     * @param week
     * @return
     */
    SohuUserIncomeStatisticsWeekVo queryByParam(Long userId, String roleType, Long siteId, String busyType, String week);
}
