package com.sohu.report.service;


import com.sohu.report.api.bo.SohuReportBillDetailBo;
import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsMonthBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.api.vo.SohuPieChartStatVo;
import com.sohu.report.api.vo.SohuReportBillDetailVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsMonthVo;

import java.util.List;

/**
 * 用户收益月统计Service接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface ISohuUserIncomeStatisticsMonthService {

    /**
     * 查询用户收益月统计列表
     */
    List<SohuUserIncomeStatisticsMonthVo> queryList(SohuUserIncomeStatisticsMonthBo bo);

    /**
     * 修改用户收益月统计
     */
    Boolean insertByBo(SohuUserIncomeStatisticsMonthBo bo);

    /**
     * 修改用户收益月统计
     */
    Boolean updateByBo(SohuUserIncomeStatisticsMonthBo bo);

    /**
     * 站长总体收益饼图
     * 统计各类型收益数据，以饼图形式展现，统计某段时间范围内的 愿望，商品，小说，短剧 收益
     *
     * @param bo 入参
     * @return 饼图数据
     */
    List<SohuPieChartStatVo> stationAllPieStat(SohuStationReportBo bo);

    /**
     * 站长总体收益折线图
     *
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineChartVo> stationAllLineStat(SohuStationReportBo bo);

    /**
     * 站长拉新收益折线图
     *
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineChartVo> stationInviteLineStat(SohuStationReportBo bo);

    /**
     * 基于时间用户查询统计数据
     *
     * @param userId
     * @param independentObject
     * @param siteId
     * @param tradeType
     * @param month
     * @return
     */
    SohuUserIncomeStatisticsMonthVo queryByParam(Long userId, String independentObject, Long siteId, String tradeType, String month);

    /**
     * 月账单详情
     *
     * @param bo
     * @return {@link SohuReportBillDetailVo}
     */
    SohuReportBillDetailVo billDetail(SohuReportBillDetailBo bo);

    /**
     * 季度账单详情
     *
     * @param bo
     * @return {@link SohuReportBillDetailVo}
     */
    SohuReportBillDetailVo quarterBillDetail(SohuReportBillDetailBo bo);

    /**
     * 年度月账单详情
     *
     * @param bo
     * @return {@link SohuReportBillDetailVo}
     */
    SohuReportBillDetailVo yearBillDetail(SohuReportBillDetailBo bo);

}
