package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.enums.SohuDateEnum;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsDayBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.api.vo.SohuPieChartStatVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsDayVo;
import com.sohu.report.domain.SohuUserIncomeStatisticsDay;
import com.sohu.report.mapper.SohuUserIncomeStatisticsDayMapper;
import com.sohu.report.service.ISohuUserIncomeStatisticsDayService;
import com.sohu.report.util.ChartStatUtil;
import com.sohu.report.util.SohuReportConvertUtil;
import com.sohu.report.util.SohuReportGroupSumUtil;
import com.sohu.report.util.SohuReportRoleUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户收益日统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@RequiredArgsConstructor
@Service
public class SohuUserIncomeStatisticsDayServiceImpl implements ISohuUserIncomeStatisticsDayService {

    private final SohuUserIncomeStatisticsDayMapper baseMapper;
    private final SohuReportConvertUtil sohuReportConvertUtil;
    private final SohuReportGroupSumUtil sohuReportGroupSumUtil;
    private final SohuReportRoleUtil sohuReportRoleUtil;
    private final ChartStatUtil chartStatUtil;

    /**
     * 查询用户收益日统计列表
     */
    @Override
    public List<SohuUserIncomeStatisticsDayVo> queryList(SohuUserIncomeStatisticsDayBo bo) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsDay> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserIncomeStatisticsDay> buildQueryWrapper(SohuUserIncomeStatisticsDayBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserIncomeStatisticsDay> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserIncomeStatisticsDay::getUserId, bo.getUserId());
        lqw.eq(bo.getStationId() != null, SohuUserIncomeStatisticsDay::getStationId, bo.getStationId());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleType()), SohuUserIncomeStatisticsDay::getRoleType, bo.getRoleType());
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuUserIncomeStatisticsDay::getBusyType, bo.getBusyType());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteRole()), SohuUserIncomeStatisticsDay::getInviteRole, bo.getInviteRole());
        lqw.eq(bo.getTotalIncome() != null, SohuUserIncomeStatisticsDay::getTotalIncome, bo.getTotalIncome());
        lqw.eq(bo.getInviteIncome() != null, SohuUserIncomeStatisticsDay::getInviteIncome, bo.getInviteIncome());
        lqw.eq(bo.getTradeAmount() != null, SohuUserIncomeStatisticsDay::getTradeAmount, bo.getTradeAmount());
        lqw.eq(bo.getOrderNum() != null, SohuUserIncomeStatisticsDay::getOrderNum, bo.getOrderNum());
        lqw.between(StringUtils.isNotBlank(bo.getDayDate()), SohuUserIncomeStatisticsDay::getDayDate, bo.getStartDate(), bo.getEndDate());
        if (StrUtil.isNotBlank(bo.getStartDate()) && StrUtil.isNotBlank(bo.getEndDate())) {
            lqw.between(SohuUserIncomeStatisticsDay::getDayDate, bo.getStartDate(), bo.getEndDate());
        }
        return lqw;
    }

    /**
     * 新增用户收益日统计
     */
    @Override
    public Boolean insertByBo(SohuUserIncomeStatisticsDayBo bo) {
        SohuUserIncomeStatisticsDay add = BeanUtil.toBean(bo, SohuUserIncomeStatisticsDay.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户收益日统计
     */
    @Override
    public Boolean updateByBo(SohuUserIncomeStatisticsDayBo bo) {
        SohuUserIncomeStatisticsDay update = BeanUtil.toBean(bo, SohuUserIncomeStatisticsDay.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserIncomeStatisticsDay entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<SohuPieChartStatVo> stationAllPieStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsDayBo statisticsWeekBo = new SohuUserIncomeStatisticsDayBo();
        statisticsWeekBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsWeekBo.setStationId(bo.getSiteId());
        List<SohuUserIncomeStatisticsDayVo> list = queryList(statisticsWeekBo);
        List<SohuPieChartStatVo> result = new LinkedList<SohuPieChartStatVo>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        List<SohuUserIncomeStatisticsDayVo> aggregatedList = sohuReportGroupSumUtil.groupSumDayByBusyType(list);
        return sohuReportConvertUtil.convertDayToPieChartData(aggregatedList);
    }

    @Override
    public List<SohuLineChartVo> stationAllLineStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsDayBo statisticsDayBo = new SohuUserIncomeStatisticsDayBo();
        statisticsDayBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsDayBo.setStationId(bo.getSiteId());
        statisticsDayBo.setStartDate(bo.getStartDate());
        statisticsDayBo.setEndDate(bo.getEndDate());
        List<SohuUserIncomeStatisticsDayVo> list = queryList(statisticsDayBo);
        List<SohuLineChartVo> result = new LinkedList<SohuLineChartVo>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        LocalDate start = LocalDate.parse(bo.getStartDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate end = LocalDate.parse(bo.getEndDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
        return chartStatUtil.fillMissingDates(sohuReportConvertUtil.convertDayToLineChartData(list), start, end, SohuDateEnum.DAY);
    }

    @Override
    public List<SohuLineChartVo> stationInviteLineStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsDayBo statisticsWeekBo = new SohuUserIncomeStatisticsDayBo();
        statisticsWeekBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsWeekBo.setStationId(bo.getSiteId());
        List<SohuUserIncomeStatisticsDayVo> list = queryList(statisticsWeekBo);
        List<SohuLineChartVo> result = new LinkedList<SohuLineChartVo>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        List<SohuUserIncomeStatisticsDayVo> groupList = sohuReportGroupSumUtil.groupSumDayByDate(list);
        return groupList
                .stream()
                .map(entry -> {
                    SohuLineChartVo item = SohuLineChartVo.builder().build();
                    item.setX(entry.getDayDate());
                    item.setY(entry.getInviteIncome().toString());
                    return item;
                })
                .sorted(Comparator.comparing(SohuLineChartVo::getY))
                .collect(Collectors.toList());
    }

    @Override
    public SohuUserIncomeStatisticsDayVo queryByParam(Long userId, String roleType, Long siteId, String busyType, String day) {
        return baseMapper.selectVoOne(Wrappers.<SohuUserIncomeStatisticsDay>lambdaQuery()
                .eq(SohuUserIncomeStatisticsDay::getUserId, userId)
                .eq(SohuUserIncomeStatisticsDay::getRoleType, roleType)
                .eq(SohuUserIncomeStatisticsDay::getStationId, siteId)
                .eq(SohuUserIncomeStatisticsDay::getBusyType, busyType)
                .eq(SohuUserIncomeStatisticsDay::getDayDate, day));
    }

    @Override
    public List<SohuUserIncomeStatisticsDayVo> queryOneMonthDayIncome(Long siteId, String roleType, String preDate) {
        if (siteId == null || preDate == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 解析传入的字符串日期
        LocalDate baseDate = LocalDate.parse(preDate, formatter);

        // 获取该月第一天和最后一天
        LocalDate monthStart = baseDate.withDayOfMonth(1);
        LocalDate monthEnd = monthStart.with(TemporalAdjusters.lastDayOfMonth());

        // 构建查询条件对象
        SohuUserIncomeStatisticsDayBo statisticsDayBo = new SohuUserIncomeStatisticsDayBo();
        statisticsDayBo.setStationId(siteId);
        statisticsDayBo.setStartDate(monthStart.format(formatter));  // 转回 yyyyMMdd 字符串
        statisticsDayBo.setEndDate(monthEnd.format(formatter));
        // 角色类型设置（如果需要）
        statisticsDayBo.setRoleType(sohuReportRoleUtil.getRoleType(roleType));
        List<SohuUserIncomeStatisticsDayVo> rawList = this.queryList(statisticsDayBo);

        // 按 dayDate 聚合
        Map<String, SohuUserIncomeStatisticsDayVo> groupedMap = rawList.stream()
                .collect(Collectors.groupingBy(SohuUserIncomeStatisticsDayVo::getDayDate,
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            SohuUserIncomeStatisticsDayVo vo = new SohuUserIncomeStatisticsDayVo();
                            vo.setDayDate(list.get(0).getDayDate());

                            vo.setTotalIncome(list.stream()
                                    .map(SohuUserIncomeStatisticsDayVo::getTotalIncome)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));

                            vo.setInviteIncome(list.stream()
                                    .map(SohuUserIncomeStatisticsDayVo::getInviteIncome)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));

                            vo.setTradeAmount(list.stream()
                                    .map(SohuUserIncomeStatisticsDayVo::getTradeAmount)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));

                            vo.setOrderNum(list.stream()
                                    .map(SohuUserIncomeStatisticsDayVo::getOrderNum)
                                    .filter(Objects::nonNull)
                                    .reduce(0L, Long::sum));

                            return vo;
                        })
                ));

        // 排序返回
        return groupedMap.values().stream()
                .sorted(Comparator.comparing(SohuUserIncomeStatisticsDayVo::getDayDate))
                .collect(Collectors.toList());
    }

}
