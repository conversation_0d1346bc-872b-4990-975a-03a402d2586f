package com.sohu.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.report.api.bo.SohuReportBillDayDetailBo;
import com.sohu.report.api.bo.SohuReportBillDetailBo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsDayBo;
import com.sohu.report.api.vo.SohuAgentIncomeStatisticsVo;
import com.sohu.report.api.vo.SohuReportBillDetailVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsDayVo;
import com.sohu.report.service.ISohuReportService;
import com.sohu.report.service.ISohuUserIncomeStatisticsDayService;
import com.sohu.report.service.ISohuUserIncomeStatisticsMonthService;
import com.sohu.report.util.SohuReportCheckUtil;
import com.sohu.report.util.SohuReportGroupSumUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-6-6 09:00:29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ISohuReportServiceImpl implements ISohuReportService {

    private final ISohuUserIncomeStatisticsMonthService monthService;
    private final ISohuUserIncomeStatisticsDayService dayService;
    private final SohuReportCheckUtil sohuReportCheckUtil;
    private final SohuReportGroupSumUtil sohuReportGroupSumUtil;

    @Override
    public SohuReportBillDetailVo billDetail(SohuReportBillDetailBo bo) {
        // 检查入参时间
        sohuReportCheckUtil.checkReportDate(bo.getStartDate(), bo.getEndDate());
        if (CalUtils.isNullOrZero(bo.getSiteId())) {
            throw new RuntimeException("站点ID为空");
        }
        SohuReportBillDetailVo result = new SohuReportBillDetailVo();
        // 账单类型（3=月度账单 4=季度账单 5=年度账单）
        Integer type = bo.getType();
        // 起始时间，如，月=20250501，季=20250401，年=20250101
        String preDate = bo.getPreDate();
        if (type == 4) {
            // 季度账单：返回当前季度的三个月
            result = monthService.quarterBillDetail(bo);
            result.setMonthOptions(buildMonthOptions(type, preDate));
            return result;
        } else if (type == 5) {
            result = monthService.yearBillDetail(bo);
            result.setMonthOptions(buildMonthOptions(type, preDate));
            return result;
        }
        // 月度账单：仅当前月
        result = monthService.billDetail(bo);
        result.setMonthOptions(buildMonthOptions(type, preDate));
        return result;
    }

    @Override
    public List<SohuUserIncomeStatisticsDayVo> billDayDetail(SohuReportBillDayDetailBo bo) {
        log.info("每天收益数据（当前月份）,参数：{}", JSONUtil.toJsonStr(bo));
        return dayService.queryOneMonthDayIncome(bo.getSiteId(), bo.getRoleType(), bo.getPreDate());
    }

    @Override
    public SohuAgentIncomeStatisticsVo agentBoard() {
        SohuAgentIncomeStatisticsVo result = new SohuAgentIncomeStatisticsVo();
        Long loginId = LoginHelper.getUserId();
        // 获取当天日期（格式：yyyyMMdd）
        String today = today = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        // 获取昨天日期（格式：yyyyMMdd）
        String yesterday = DateUtil.format(DateUtil.yesterday(), "yyyyMMdd");
        SohuUserIncomeStatisticsDayBo dayBo = new SohuUserIncomeStatisticsDayBo();
        dayBo.setStartDate(yesterday);
        dayBo.setEndDate(today);
        dayBo.setRoleType(RoleCodeEnum.Agent.getCode());
        dayBo.setUserId(loginId);
        List<SohuUserIncomeStatisticsDayVo> dayVoList = dayService.queryList(dayBo);
        List<SohuUserIncomeStatisticsDayVo> daySum = sohuReportGroupSumUtil.groupSumDayByDate(dayVoList);
        Map<String, SohuUserIncomeStatisticsDayVo> daySumMap = CollUtil.isEmpty(daySum) ? new HashMap<>() : daySum.stream().collect(
                Collectors.toMap(SohuUserIncomeStatisticsDayVo::getDayDate, entry -> entry));
        SohuUserIncomeStatisticsDayVo yesterdayVo = daySumMap.get(yesterday);
        SohuUserIncomeStatisticsDayVo todayVo = daySumMap.get(today);
        // 设置今天的
        result.setTodayTotalIncome(Objects.nonNull(todayVo) ? todayVo.getTotalIncome() : BigDecimal.ZERO);
        result.setTodayInviteNum(Objects.nonNull(todayVo) ? todayVo.getInviteNum() : 0);
        result.setTodayInviteBindNum(Objects.nonNull(todayVo) ? todayVo.getInviteBindNum() : 0);

        // 设置昨日
        result.setYesterdayTotalIncome(Objects.nonNull(yesterdayVo) ? yesterdayVo.getTotalIncome() : BigDecimal.ZERO);
        result.setYesterdayInviteNum(Objects.nonNull(yesterdayVo) ? yesterdayVo.getInviteNum() : 0);
        result.setYesterdayInviteBindNum(Objects.nonNull(yesterdayVo) ? yesterdayVo.getInviteBindNum() : 0);

        // todo 查余额
        return result;
    }

    private List<SohuReportBillDetailVo.MonthOption> buildMonthOptions(int type, String preDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate baseDate = LocalDate.parse(preDate, formatter);
        List<SohuReportBillDetailVo.MonthOption> options = new ArrayList<>();

        if (type == 3) {
            // 月度账单：当前月
            options.add(buildSingleMonthOption(baseDate));
        } else if (type == 4) {
            // 季度账单：当前季度三个月
            int startMonth = ((baseDate.getMonthValue() - 1) / 3) * 3 + 1;
            for (int i = 0; i < 3; i++) {
                LocalDate month = baseDate.withMonth(startMonth + i).withDayOfMonth(1);
                options.add(buildSingleMonthOption(month));
            }
        } else if (type == 5) {
            // 年度账单：全年12个月
            for (int i = 1; i <= 12; i++) {
                LocalDate month = baseDate.withMonth(i).withDayOfMonth(1);
                options.add(buildSingleMonthOption(month));
            }
        }
        return options;
    }

    private SohuReportBillDetailVo.MonthOption buildSingleMonthOption(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        SohuReportBillDetailVo.MonthOption option = new SohuReportBillDetailVo.MonthOption();
        option.setLabel(date.getMonthValue() + "月");
        option.setValue(date.format(formatter));
        return option;
    }

}
