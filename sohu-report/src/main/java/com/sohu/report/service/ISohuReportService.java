package com.sohu.report.service;

import com.sohu.report.api.bo.SohuReportBillDayDetailBo;
import com.sohu.report.api.bo.SohuReportBillDetailBo;
import com.sohu.report.api.vo.SohuAgentIncomeStatisticsVo;
import com.sohu.report.api.vo.SohuReportBillDetailVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsDayVo;

import java.util.List;

public interface ISohuReportService {

    /**
     * 账单详情
     *
     * @param bo
     * @return {@link SohuReportBillDetailVo}
     */
    SohuReportBillDetailVo billDetail(SohuReportBillDetailBo bo);

    /**
     * 查询每天收益（支持月、季、年，但返回一个月的数据）
     *
     * @return 每天收益数据（当前月份）
     */
    List<SohuUserIncomeStatisticsDayVo> billDayDetail(SohuReportBillDayDetailBo bo);

    /**
     * 服务商看板统计
     *
     * @return {@link SohuAgentIncomeStatisticsVo}
     */
    SohuAgentIncomeStatisticsVo agentBoard();

}
