package com.sohu.report.service;


import com.sohu.report.api.bo.SohuBaseDateBo;
import com.sohu.report.api.bo.SohuInviteReportBo;
import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsDayBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.api.vo.SohuLineInviteVo;
import com.sohu.report.api.vo.SohuPieChartStatVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsDayVo;

import java.util.List;

/**
 * 用户收益日统计Service接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface ISohuUserIncomeStatisticsDayService {

    /**
     * 查询用户收益日统计列表
     */
    List<SohuUserIncomeStatisticsDayVo> queryList(SohuUserIncomeStatisticsDayBo bo);

    /**
     * 修改用户收益日统计
     */
    Boolean insertByBo(SohuUserIncomeStatisticsDayBo bo);

    /**
     * 修改用户收益日统计
     */
    Boolean updateByBo(SohuUserIncomeStatisticsDayBo bo);

    /**
     * 站长总体收益饼图
     * 统计各类型收益数据，以饼图形式展现，统计某段时间范围内的 愿望，商品，小说，短剧 收益
     *
     * @param bo 入参
     * @return 饼图数据
     */
    List<SohuPieChartStatVo> stationAllPieStat(SohuStationReportBo bo);

    /**
     * 站长总体收益折线图
     *
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineChartVo> stationAllLineStat(SohuStationReportBo bo);

    /**
     * 站长拉新收益折线图
     *
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineChartVo> stationInviteLineStat(SohuStationReportBo bo);

    /**
     * 查询处理数据
     *
     * @param userId
     * @param roleType
     * @param siteId
     * @param busyType
     * @param day
     * @return
     */
    SohuUserIncomeStatisticsDayVo queryByParam(Long userId, String roleType, Long siteId, String busyType, String day);

    /**
     * 查询每天收益（支持月、季、年，但返回一个月的数据）
     *
     * @param siteId   站点ID
     * @param roleType 角色类型
     * @param preDate  开始日期（格式为 yyyyMMdd）
     * @return 每天收益数据（当前月份）
     */
    List<SohuUserIncomeStatisticsDayVo> queryOneMonthDayIncome(Long siteId, String roleType, String preDate);

    /**
     * 服务商拉新概览折线图
     * @param bo 入参
     * @return {@link List}
     */
    List<SohuLineInviteVo> agentStationInviteBoard(SohuInviteReportBo bo);

}
