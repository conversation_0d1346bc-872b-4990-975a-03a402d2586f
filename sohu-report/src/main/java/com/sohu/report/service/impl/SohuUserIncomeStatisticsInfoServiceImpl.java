package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsInfoBo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo;
import com.sohu.report.api.vo.SohuUserIncomeTopVo;
import com.sohu.report.domain.SohuUserIncomeStatisticsInfo;
import com.sohu.report.mapper.SohuUserIncomeStatisticsInfoMapper;
import com.sohu.report.service.ISohuUserIncomeStatisticsInfoService;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户收益明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@RequiredArgsConstructor
@Service
public class SohuUserIncomeStatisticsInfoServiceImpl implements ISohuUserIncomeStatisticsInfoService {

    private final SohuUserIncomeStatisticsInfoMapper baseMapper;
    @DubboReference
    private RemoteUserService userService;

    @Override
    public TableDataInfo<SohuUserIncomeStatisticsInfoVo> queryPageList(SohuUserIncomeStatisticsInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsInfo> lqw = buildQueryWrapper(bo);
        Page<SohuUserIncomeStatisticsInfoVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询用户收益明细列表
     */
    @Override
    public List<SohuUserIncomeStatisticsInfoVo> queryList(SohuUserIncomeStatisticsInfoBo bo) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserIncomeStatisticsInfo> buildQueryWrapper(SohuUserIncomeStatisticsInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserIncomeStatisticsInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserIncomeStatisticsInfo::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleType()), SohuUserIncomeStatisticsInfo::getRoleType, bo.getRoleType());
        lqw.eq(bo.getStationId() != null, SohuUserIncomeStatisticsInfo::getStationId, bo.getStationId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuUserIncomeStatisticsInfo::getBusyType, bo.getBusyType());
        lqw.like(StringUtils.isNotBlank(bo.getBusyInfoName()), SohuUserIncomeStatisticsInfo::getBusyInfoName, bo.getBusyInfoName());
        lqw.eq(StringUtils.isNotBlank(bo.getIncomeType()), SohuUserIncomeStatisticsInfo::getIncomeType, bo.getIncomeType());
        lqw.eq(bo.getIncome() != null, SohuUserIncomeStatisticsInfo::getIncome, bo.getIncome());
        lqw.eq(bo.getConsumerUserId() != null, SohuUserIncomeStatisticsInfo::getConsumerUserId, bo.getConsumerUserId());
        lqw.eq(bo.getConsumerChannelId() != null, SohuUserIncomeStatisticsInfo::getConsumerChannelId, bo.getConsumerChannelId());
        lqw.like(StringUtils.isNotBlank(bo.getConsumerChannelName()), SohuUserIncomeStatisticsInfo::getConsumerChannelName, bo.getConsumerChannelName());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderAmount()), SohuUserIncomeStatisticsInfo::getOrderAmount, bo.getOrderAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), SohuUserIncomeStatisticsInfo::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeNo()), SohuUserIncomeStatisticsInfo::getTradeNo, bo.getTradeNo());
        lqw.eq(bo.getOrderTime() != null, SohuUserIncomeStatisticsInfo::getOrderTime, bo.getOrderTime());
        lqw.eq(bo.getIncomeTime() != null, SohuUserIncomeStatisticsInfo::getIncomeTime, bo.getIncomeTime());
        lqw.orderByDesc(SohuUserIncomeStatisticsInfo::getIncomeTime);
        return lqw;
    }

    /**
     * 新增用户收益明细
     */
    @Override
    public Boolean insertByBo(SohuUserIncomeStatisticsInfoBo bo) {
        SohuUserIncomeStatisticsInfo add = BeanUtil.toBean(bo, SohuUserIncomeStatisticsInfo.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户收益明细
     */
    @Override
    public Boolean updateByBo(SohuUserIncomeStatisticsInfoBo bo) {
        SohuUserIncomeStatisticsInfo update = BeanUtil.toBean(bo, SohuUserIncomeStatisticsInfo.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public SohuUserIncomeTopVo getIncomeTop(Long stationId, Integer stationType, Date startDate, Date endDate) {
        // 查询用户top
        List<SohuUserIncomeStatisticsInfoVo> userList = baseMapper.getUserTop(LoginHelper.getUserId(), stationId, stationType, startDate, endDate);
        // 查询业务top
        List<SohuUserIncomeStatisticsInfoVo> busyList = baseMapper.getBusyTop(LoginHelper.getUserId(), stationId, stationType, startDate, endDate);
        // 查询渠道top
        List<SohuUserIncomeStatisticsInfoVo> channelList = baseMapper.getChannelTop(LoginHelper.getUserId(), stationId, stationType, startDate, endDate);
        SohuUserIncomeTopVo incomeTopVo = new SohuUserIncomeTopVo();
        List<SohuUserIncomeTopVo.IncomeUserTopVo> incomeUserTopList = new ArrayList<>();
        List<SohuUserIncomeTopVo.IncomeBusyTopVo> incomeBusyTopList = new ArrayList<>();
        List<SohuUserIncomeTopVo.IncomeChannelTopVo> incomeChannelTopList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userList)) {
            Map<Long, LoginUser> longLoginUserMap = userService.selectMap(userList.stream()
                    .map(SohuUserIncomeStatisticsInfoVo::getUserId).collect(Collectors.toList()));
            for (SohuUserIncomeStatisticsInfoVo userIncome : userList) {
                SohuUserIncomeTopVo.IncomeUserTopVo incomeUserTopVo = new SohuUserIncomeTopVo.IncomeUserTopVo();
                incomeUserTopVo.setUserId(userIncome.getUserId());
                incomeUserTopVo.setUserName(
                        longLoginUserMap.containsKey(userIncome.getUserId()) ? longLoginUserMap.get(userIncome.getUserId()).getUsername() : "未知");
                incomeUserTopVo.setAmount(userIncome.getIncome());
                incomeUserTopList.add(incomeUserTopVo);
            }
        }
        if (CollectionUtils.isNotEmpty(busyList)) {
            for (SohuUserIncomeStatisticsInfoVo busyIncome : busyList) {
                SohuUserIncomeTopVo.IncomeBusyTopVo incomeBusyTopVo = new SohuUserIncomeTopVo.IncomeBusyTopVo();
                incomeBusyTopVo.setBusyType(busyIncome.getBusyType());
                incomeBusyTopVo.setBusyName(busyIncome.getBusyInfoName());
                incomeBusyTopVo.setAmount(busyIncome.getIncome());
                incomeBusyTopList.add(incomeBusyTopVo);
            }
        }
        if (CollectionUtils.isNotEmpty(channelList)) {
            for (SohuUserIncomeStatisticsInfoVo channelIncome : channelList) {
                SohuUserIncomeTopVo.IncomeChannelTopVo incomeChannelTopVo = new SohuUserIncomeTopVo.IncomeChannelTopVo();
                incomeChannelTopVo.setChannelId(channelIncome.getConsumerChannelId());
                incomeChannelTopVo.setChannelName(channelIncome.getConsumerChannelName());
                incomeChannelTopVo.setAmount(channelIncome.getIncome());
                incomeChannelTopList.add(incomeChannelTopVo);
            }
        }
        incomeTopVo.setIncomeUserTopList(incomeUserTopList);
        incomeTopVo.setIncomeBusyTopList(incomeBusyTopList);
        incomeTopVo.setIncomeChannelTopList(incomeChannelTopList);
        return incomeTopVo;
    }

    @Override
    public SohuUserIncomeStatisticsInfoVo queryIncomeById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public List<SohuUserIncomeStatisticsInfoVo> incomeNewest() {
        LambdaQueryWrapper<SohuUserIncomeStatisticsInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuUserIncomeStatisticsInfo::getUserId, LoginHelper.getUserId());
        lqw.eq(SohuUserIncomeStatisticsInfo::getRoleType, RoleCodeEnum.Agent.getCode());
        lqw.orderByDesc(SohuUserIncomeStatisticsInfo::getIncomeTime);
        lqw.last(" limit 10");
        List<SohuUserIncomeStatisticsInfoVo> result = this.baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(result)) {
            return List.of();
        }
        // 消费用户ID集合
        Set<Long> consumerUserIds = result.stream().map(SohuUserIncomeStatisticsInfoVo::getConsumerUserId).collect(Collectors.toSet());
        Map<Long, LoginUser> consumerUserMap = userService.selectMap(consumerUserIds);
        for (SohuUserIncomeStatisticsInfoVo model : result) {
            LoginUser user = consumerUserMap.get(model.getConsumerUserId());
            if (Objects.isNull(user)) {
                continue;
            }
            model.setConsumerUserName(StringUtils.getValidString(user.getNickname(), user.getUsername()));
            model.setConsumerUserAvatar(user.getAvatar());
            if (StrUtil.equalsAnyIgnoreCase(model.getBusyType(), BusyType.Goods.getType())) {
                model.setConsumerSummary(model.getConsumerUserName() + "购买了商品," + model.getIncome() + "元");
            } else if (StrUtil.equalsAnyIgnoreCase(model.getBusyType(), BusyType.BusyOrder.getType(),
                    BusyType.BusyTask.getType(), BusyType.BusyTaskFlow.getType(), BusyType.BusyTaskCommon.getType())) {
                model.setConsumerSummary(model.getConsumerUserName() + "完成了商单,分佣" + model.getIncome() + "元");
            } else if (StrUtil.equalsAnyIgnoreCase(model.getBusyType(), BusyType.BusyPlaylet.getType(), BusyType.Video.getType())) {
                model.setConsumerSummary(model.getConsumerUserName() + "短剧充值,分佣" + model.getIncome() + "元");
            }
        }
        return result;
    }

    @Override
    public List<SohuUserIncomeStatisticsInfoVo> queryUserIncomeByTime(Long userId, Date startTime, Date endTime) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuUserIncomeStatisticsInfo::getUserId, userId);
        lqw.between(SohuUserIncomeStatisticsInfo::getOrderTime, startTime, endTime);
        return List.of();
    }
}
