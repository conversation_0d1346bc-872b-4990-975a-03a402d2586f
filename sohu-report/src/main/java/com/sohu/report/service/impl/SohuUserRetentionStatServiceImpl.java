package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserRetentionStatBo;
import com.sohu.report.api.vo.SohuUserRetentionStatVo;
import com.sohu.report.domain.SohuUserRetentionStat;
import com.sohu.report.mapper.SohuUserRetentionStatMapper;
import com.sohu.report.service.ISohuUserRetentionStatService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户留存统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RequiredArgsConstructor
@Service
public class SohuUserRetentionStatServiceImpl implements ISohuUserRetentionStatService {

    private final SohuUserRetentionStatMapper baseMapper;

    /**
     * 查询用户留存统计
     */
    @Override
    public SohuUserRetentionStatVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询用户留存统计列表
     */
    @Override
    public TableDataInfo<SohuUserRetentionStatVo> queryPageList(SohuUserRetentionStatBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuUserRetentionStat> lqw = buildQueryWrapper(bo);
        Page<SohuUserRetentionStatVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询用户留存统计列表
     */
    @Override
    public List<SohuUserRetentionStatVo> queryList(SohuUserRetentionStatBo bo) {
        LambdaQueryWrapper<SohuUserRetentionStat> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserRetentionStat> buildQueryWrapper(SohuUserRetentionStatBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserRetentionStat> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserRetentionStat::getUserId, bo.getUserId());
        lqw.eq(bo.getInviteDate() != null, SohuUserRetentionStat::getInviteDate, bo.getInviteDate());
        lqw.eq(bo.getRetentionDay() != null, SohuUserRetentionStat::getRetentionDay, bo.getRetentionDay());
        lqw.eq(bo.getRetainedCount() != null, SohuUserRetentionStat::getRetainedCount, bo.getRetainedCount());
        return lqw;
    }

    /**
     * 新增用户留存统计
     */
    @Override
    public Boolean insertByBo(SohuUserRetentionStatBo bo) {
        SohuUserRetentionStat add = BeanUtil.toBean(bo, SohuUserRetentionStat.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户留存统计
     */
    @Override
    public Boolean updateByBo(SohuUserRetentionStatBo bo) {
        SohuUserRetentionStat update = BeanUtil.toBean(bo, SohuUserRetentionStat.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserRetentionStat entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除用户留存统计
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
