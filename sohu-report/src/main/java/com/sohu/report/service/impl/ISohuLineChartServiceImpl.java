package com.sohu.report.service.impl;

import com.sohu.common.core.enums.SohuDateEnum;
import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.service.ISohuLineChartService;
import com.sohu.report.service.ISohuUserIncomeStatisticsDayService;
import com.sohu.report.service.ISohuUserIncomeStatisticsMonthService;
import com.sohu.report.service.ISohuUserIncomeStatisticsWeekService;
import com.sohu.report.util.SohuReportCheckUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 折线图服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class ISohuLineChartServiceImpl implements ISohuLineChartService {

    private final SohuReportCheckUtil sohuReportCheckUtil;
    private final ISohuUserIncomeStatisticsDayService dayService;
    private final ISohuUserIncomeStatisticsWeekService weekService;
    private final ISohuUserIncomeStatisticsMonthService monthService;

    @Override
    public List<SohuLineChartVo> stationAllLineStat(SohuStationReportBo bo) {
        // 初始化时间
        bo.initDateIfNull();
        // 检查入参
        sohuReportCheckUtil.stationReportBoCheck(bo);
        SohuDateEnum dateEnum = SohuDateEnum.getGranularityEnum(bo.getStartDate(), bo.getEndDate());
        if(dateEnum==SohuDateEnum.WEEK){
            return weekService.stationAllLineStat(bo);
        }
        if(dateEnum==SohuDateEnum.MONTH){
            return monthService.stationAllLineStat(bo);
        }
        return dayService.stationAllLineStat(bo);
    }

    @Override
    public List<SohuLineChartVo> stationInviteLineStat(SohuStationReportBo bo) {
        // 初始化时间
        bo.initDateIfNull();
        // 检查入参
        sohuReportCheckUtil.stationReportBoCheck(bo);
        SohuDateEnum dateEnum = SohuDateEnum.getGranularityEnum(bo.getStartDate(), bo.getEndDate());
        if(dateEnum==SohuDateEnum.WEEK){
            return weekService.stationInviteLineStat(bo);
        }
        if(dateEnum==SohuDateEnum.MONTH){
            return monthService.stationInviteLineStat(bo);
        }
        return dayService.stationInviteLineStat(bo);
    }

}
