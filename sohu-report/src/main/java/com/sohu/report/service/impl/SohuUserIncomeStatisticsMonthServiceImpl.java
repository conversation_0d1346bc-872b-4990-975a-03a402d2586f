package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.SohuDateEnum;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.report.api.bo.SohuReportBillDetailBo;
import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsMonthBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.api.vo.SohuPieChartStatVo;
import com.sohu.report.api.vo.SohuReportBillDetailVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsMonthVo;
import com.sohu.report.domain.SohuUserIncomeStatisticsMonth;
import com.sohu.report.mapper.SohuUserIncomeStatisticsMonthMapper;
import com.sohu.report.service.ISohuUserIncomeStatisticsMonthService;
import com.sohu.report.util.ChartStatUtil;
import com.sohu.report.util.SohuReportConvertUtil;
import com.sohu.report.util.SohuReportGroupSumUtil;
import com.sohu.report.util.SohuReportRoleUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户收益月统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuUserIncomeStatisticsMonthServiceImpl implements ISohuUserIncomeStatisticsMonthService {

    private final SohuUserIncomeStatisticsMonthMapper baseMapper;
    private final SohuReportConvertUtil sohuReportConvertUtil;
    private final SohuReportGroupSumUtil sohuReportGroupSumUtil;
    private final SohuReportRoleUtil sohuReportRoleUtil;
    private final ChartStatUtil chartStatUtil;

    /**
     * 查询用户收益月统计列表
     */
    @Override
    public List<SohuUserIncomeStatisticsMonthVo> queryList(SohuUserIncomeStatisticsMonthBo bo) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsMonth> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserIncomeStatisticsMonth> buildQueryWrapper(SohuUserIncomeStatisticsMonthBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserIncomeStatisticsMonth> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserIncomeStatisticsMonth::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleType()), SohuUserIncomeStatisticsMonth::getRoleType, bo.getRoleType());
        lqw.eq(bo.getStationId() != null, SohuUserIncomeStatisticsMonth::getStationId, bo.getStationId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuUserIncomeStatisticsMonth::getBusyType, bo.getBusyType());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteRole()), SohuUserIncomeStatisticsMonth::getInviteRole, bo.getInviteRole());
        lqw.eq(bo.getTotalIncome() != null, SohuUserIncomeStatisticsMonth::getTotalIncome, bo.getTotalIncome());
        lqw.eq(bo.getInviteIncome() != null, SohuUserIncomeStatisticsMonth::getInviteIncome, bo.getInviteIncome());
        lqw.eq(bo.getTradeAmount() != null, SohuUserIncomeStatisticsMonth::getTradeAmount, bo.getTradeAmount());
        lqw.eq(bo.getOrderNum() != null, SohuUserIncomeStatisticsMonth::getOrderNum, bo.getOrderNum());
        lqw.between(StringUtils.isNotBlank(bo.getMonthDate()), SohuUserIncomeStatisticsMonth::getMonthDate, bo.getStartDate(), bo.getEndDate());
        return lqw;
    }

    /**
     * 新增用户收益月统计
     */
    @Override
    public Boolean insertByBo(SohuUserIncomeStatisticsMonthBo bo) {
        SohuUserIncomeStatisticsMonth add = BeanUtil.toBean(bo, SohuUserIncomeStatisticsMonth.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户收益月统计
     */
    @Override
    public Boolean updateByBo(SohuUserIncomeStatisticsMonthBo bo) {
        SohuUserIncomeStatisticsMonth update = BeanUtil.toBean(bo, SohuUserIncomeStatisticsMonth.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserIncomeStatisticsMonth entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<SohuPieChartStatVo> stationAllPieStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsMonthBo statisticsMonthBo = new SohuUserIncomeStatisticsMonthBo();
        statisticsMonthBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsMonthBo.setStationId(bo.getSiteId());
        List<SohuUserIncomeStatisticsMonthVo> list = queryList(statisticsMonthBo);
        List<SohuPieChartStatVo> result = new LinkedList<SohuPieChartStatVo>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        List<SohuUserIncomeStatisticsMonthVo> aggregatedList = sohuReportGroupSumUtil.groupSumMonthByBusyType(list);
        return sohuReportConvertUtil.convertMonthToPieChartData(aggregatedList);
    }

    @Override
    public List<SohuLineChartVo> stationAllLineStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsMonthBo statisticsMonthBo = new SohuUserIncomeStatisticsMonthBo();
        statisticsMonthBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsMonthBo.setStationId(bo.getSiteId());
        statisticsMonthBo.setStartDate(bo.getStartDate());
        statisticsMonthBo.setEndDate(bo.getEndDate());
        List<SohuUserIncomeStatisticsMonthVo> list = queryList(statisticsMonthBo);
        List<SohuLineChartVo> result = new LinkedList<SohuLineChartVo>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        LocalDate start = LocalDate.parse(bo.getStartDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate end = LocalDate.parse(bo.getEndDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
        return chartStatUtil.fillMissingDates(sohuReportConvertUtil.convertMonthToLineChartData(list), start, end, SohuDateEnum.DAY);
    }

    @Override
    public List<SohuLineChartVo> stationInviteLineStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsMonthBo statisticsMonthBo = new SohuUserIncomeStatisticsMonthBo();
        statisticsMonthBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsMonthBo.setStationId(bo.getSiteId());
        List<SohuUserIncomeStatisticsMonthVo> list = queryList(statisticsMonthBo);
        List<SohuLineChartVo> result = new LinkedList<SohuLineChartVo>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        List<SohuUserIncomeStatisticsMonthVo> groupList = sohuReportGroupSumUtil.groupSumMonthByDate(list);

        return groupList
                .stream()
                .map(entry -> {

                    SohuLineChartVo item = SohuLineChartVo.builder().build();
                    item.setX(entry.getMonthDate());
                    item.setY(entry.getInviteIncome().toString());
                    return item;
                })
                .sorted(Comparator.comparing(SohuLineChartVo::getY))
                .collect(Collectors.toList());
    }

    @Override
    public SohuUserIncomeStatisticsMonthVo queryByParam(Long userId, String roleType, Long siteId, String busyType, String month) {
        return baseMapper.selectVoOne(Wrappers.<SohuUserIncomeStatisticsMonth>lambdaQuery()
                .eq(SohuUserIncomeStatisticsMonth::getUserId, userId)
                .eq(SohuUserIncomeStatisticsMonth::getRoleType, roleType)
                .eq(SohuUserIncomeStatisticsMonth::getStationId, siteId)
                .eq(SohuUserIncomeStatisticsMonth::getBusyType, busyType)
                .eq(SohuUserIncomeStatisticsMonth::getMonthDate, month));
    }

    @Override
    public SohuReportBillDetailVo billDetail(SohuReportBillDetailBo bo) {
        log.info("查询月度账单");
        return buildReportBillDetailVo(bo);
    }

    @Override
    public SohuReportBillDetailVo quarterBillDetail(SohuReportBillDetailBo bo) {
        log.info("查询季度账单");
        return buildReportBillDetailVo(bo);
    }

    @Override
    public SohuReportBillDetailVo yearBillDetail(SohuReportBillDetailBo bo) {
        log.info("查询年度账单");
        return buildReportBillDetailVo(bo);
    }

    private SohuReportBillDetailVo buildReportBillDetailVo(SohuReportBillDetailBo bo) {
        log.info("账单入参：{}", JSONUtil.toJsonStr(bo));
        SohuUserIncomeStatisticsMonthBo statisticsMonthBo = new SohuUserIncomeStatisticsMonthBo();
        statisticsMonthBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsMonthBo.setStationId(bo.getSiteId());
        statisticsMonthBo.setStartDate(bo.getStartDate());
        statisticsMonthBo.setEndDate(bo.getEndDate());
        List<SohuUserIncomeStatisticsMonthVo> dataList = queryList(statisticsMonthBo);

        if (CollUtil.isEmpty(dataList)) {
            return new SohuReportBillDetailVo();
        }

        // 总收益
        BigDecimal totalIncome = dataList.stream()
                .map(SohuUserIncomeStatisticsMonthVo::getTotalIncome)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 总订单数
        long totalOrderNum = dataList.stream()
                .map(SohuUserIncomeStatisticsMonthVo::getOrderNum)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);

        // 按业务类型聚合
        Map<String, List<SohuUserIncomeStatisticsMonthVo>> groupedByType = dataList.stream()
                .collect(Collectors.groupingBy(SohuUserIncomeStatisticsMonthVo::getBusyType));

        List<SohuPieChartStatVo> pieList = new ArrayList<>();
        for (Map.Entry<String, List<SohuUserIncomeStatisticsMonthVo>> entry : groupedByType.entrySet()) {
            String busyType = entry.getKey();
            List<SohuUserIncomeStatisticsMonthVo> groupList = entry.getValue();

            BigDecimal income = groupList.stream()
                    .map(SohuUserIncomeStatisticsMonthVo::getTotalIncome)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            String ratio = "0.0%";
            if (totalIncome.compareTo(BigDecimal.ZERO) > 0) {
                ratio = income.multiply(BigDecimal.valueOf(100))
                        .divide(totalIncome, 2, RoundingMode.HALF_UP)
                        .toPlainString() + "%";
            }

            SohuPieChartStatVo vo = new SohuPieChartStatVo();
            vo.setType(busyType);
            vo.setName(BusyType.mapBusyDesc.get(busyType.toLowerCase()));
            vo.setProfit(income);
            vo.setRadio(ratio);

            pieList.add(vo);
        }

        // 选出收益最高的类型
        SohuPieChartStatVo maxProfit = pieList.stream()
                .max(Comparator.comparing(SohuPieChartStatVo::getProfit, Comparator.nullsFirst(BigDecimal::compareTo)))
                .orElse(null);

        // 构造返回对象
        SohuReportBillDetailVo result = new SohuReportBillDetailVo();
        result.setTotalIncome(totalIncome.setScale(2, RoundingMode.HALF_UP).toPlainString());
        result.setOrderNum(totalOrderNum);
        result.setMaxProfit(maxProfit);

        return result;
    }

}
