package com.sohu.report.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsInfoBo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo;
import com.sohu.report.api.vo.SohuUserIncomeTopVo;

import java.util.Date;
import java.util.List;

/**
 * 用户收益明细Service接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface ISohuUserIncomeStatisticsInfoService {
    /**
     * 分页查询用户收益
     * @param bo
     * @return
     */
    TableDataInfo<SohuUserIncomeStatisticsInfoVo> queryPageList(SohuUserIncomeStatisticsInfoBo bo, PageQuery pageQuery);
    /**
     * 查询用户收益明细列表
     */
    List<SohuUserIncomeStatisticsInfoVo> queryList(SohuUserIncomeStatisticsInfoBo bo);

    /**
     * 修改用户收益明细
     */
    Boolean insertByBo(SohuUserIncomeStatisticsInfoBo bo);

    /**
     * 修改用户收益明细
     */
    Boolean updateByBo(SohuUserIncomeStatisticsInfoBo bo);

    /**
     * 查询用户收益top
     *
     * @param stationId
     * @param stationType
     * @param startDate
     * @param endDate
     * @return
     */
    SohuUserIncomeTopVo getIncomeTop(Long stationId, Integer stationType, Date startDate, Date endDate);

    /**
     * 获取用户收益详情
     * @param id
     * @return
     */
    SohuUserIncomeStatisticsInfoVo queryIncomeById(Long id);

    /**
     * 代理服务商最近到账收益
     *
     * @return {@link List}
     */
    List<SohuUserIncomeStatisticsInfoVo> incomeNewest();

    /**
     * 基于分账单id查询记录
     *
     * @param independentOrderId
     * @return
     */
    SohuUserIncomeStatisticsInfoVo queryByIndependentOrderId(Long independentOrderId);
    /**
     * 基于时间查询用户收益
     *
     * @param userId    站长ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 用户收益列表
     */
    List<SohuUserIncomeStatisticsInfoVo> queryUserIncomeByTime(Long userId, Date startTime, Date endTime);
}
