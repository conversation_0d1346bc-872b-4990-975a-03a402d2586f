package com.sohu.report.service;


import com.sohu.report.api.bo.SohuUserIncomeStatisticsInfoBo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo;
import com.sohu.report.api.vo.SohuUserIncomeTopVo;

import java.util.Date;
import java.util.List;

/**
 * 用户收益明细Service接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface ISohuUserIncomeStatisticsInfoService {


    /**
     * 查询用户收益明细列表
     */
    List<SohuUserIncomeStatisticsInfoVo> queryList(SohuUserIncomeStatisticsInfoBo bo);

    /**
     * 修改用户收益明细
     */
    Boolean insertByBo(SohuUserIncomeStatisticsInfoBo bo);

    /**
     * 修改用户收益明细
     */
    Boolean updateByBo(SohuUserIncomeStatisticsInfoBo bo);

    /**
     * 查询用户收益top
     *
     * @param stationId
     * @param stationType
     * @param startDate
     * @param endDate
     * @return
     */
    SohuUserIncomeTopVo getIncomeTop(Long stationId, Integer stationType, Date startDate, Date endDate);
}
