package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.core.enums.SohuDateEnum;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsWeekBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.api.vo.SohuPieChartStatVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsWeekVo;
import com.sohu.report.domain.SohuUserIncomeStatisticsWeek;
import com.sohu.report.mapper.SohuUserIncomeStatisticsWeekMapper;
import com.sohu.report.service.ISohuUserIncomeStatisticsWeekService;
import com.sohu.report.util.ChartStatUtil;
import com.sohu.report.util.SohuReportConvertUtil;
import com.sohu.report.util.SohuReportGroupSumUtil;
import com.sohu.report.util.SohuReportRoleUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户收益周统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@RequiredArgsConstructor
@Service
public class SohuUserIncomeStatisticsWeekServiceImpl implements ISohuUserIncomeStatisticsWeekService {

    private final SohuUserIncomeStatisticsWeekMapper baseMapper;
    private final SohuReportConvertUtil sohuReportConvertUtil;
    private final SohuReportGroupSumUtil sohuReportGroupSumUtil;
    private final SohuReportRoleUtil sohuReportRoleUtil;
    private final ChartStatUtil chartStatUtil;

    /**
     * 查询用户收益周统计列表
     */
    @Override
    public List<SohuUserIncomeStatisticsWeekVo> queryList(SohuUserIncomeStatisticsWeekBo bo) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsWeek> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuUserIncomeStatisticsWeek> buildQueryWrapper(SohuUserIncomeStatisticsWeekBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserIncomeStatisticsWeek> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserIncomeStatisticsWeek::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleType()), SohuUserIncomeStatisticsWeek::getRoleType, bo.getRoleType());
        lqw.eq(bo.getStationId() != null, SohuUserIncomeStatisticsWeek::getStationId, bo.getStationId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusyType()), SohuUserIncomeStatisticsWeek::getBusyType, bo.getBusyType());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteRole()), SohuUserIncomeStatisticsWeek::getInviteRole, bo.getInviteRole());
        lqw.eq(bo.getTotalIncome() != null, SohuUserIncomeStatisticsWeek::getTotalIncome, bo.getTotalIncome());
        lqw.eq(bo.getInviteIncome() != null, SohuUserIncomeStatisticsWeek::getInviteIncome, bo.getInviteIncome());
        lqw.eq(bo.getTradeAmount() != null, SohuUserIncomeStatisticsWeek::getTradeAmount, bo.getTradeAmount());
        lqw.eq(bo.getOrderNum() != null, SohuUserIncomeStatisticsWeek::getOrderNum, bo.getOrderNum());
        lqw.between(StringUtils.isNotBlank(bo.getWeekDate()), SohuUserIncomeStatisticsWeek::getWeekDate, bo.getStartDate(), bo.getEndDate());
        return lqw;
    }

    /**
     * 新增用户收益周统计
     */
    @Override
    public Boolean insertByBo(SohuUserIncomeStatisticsWeekBo bo) {
        SohuUserIncomeStatisticsWeek add = BeanUtil.toBean(bo, SohuUserIncomeStatisticsWeek.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户收益周统计
     */
    @Override
    public Boolean updateByBo(SohuUserIncomeStatisticsWeekBo bo) {
        SohuUserIncomeStatisticsWeek update = BeanUtil.toBean(bo, SohuUserIncomeStatisticsWeek.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserIncomeStatisticsWeek entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<SohuPieChartStatVo> stationAllPieStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsWeekBo statisticsWeekBo = new SohuUserIncomeStatisticsWeekBo();
        statisticsWeekBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsWeekBo.setStationId(bo.getSiteId());
        List<SohuUserIncomeStatisticsWeekVo> weekVoList = queryList(statisticsWeekBo);
        List<SohuPieChartStatVo> result = new LinkedList<SohuPieChartStatVo>();
        if (CollUtil.isEmpty(weekVoList)) {
            return result;
        }
        List<SohuUserIncomeStatisticsWeekVo> aggregatedList = sohuReportGroupSumUtil.groupSumWeekByBusyType(weekVoList);
        return sohuReportConvertUtil.convertWeekToPieChartData(aggregatedList);
    }

    @Override
    public List<SohuLineChartVo> stationAllLineStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsWeekBo statisticsWeekBo = new SohuUserIncomeStatisticsWeekBo();
        statisticsWeekBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsWeekBo.setStationId(bo.getSiteId());
        statisticsWeekBo.setStartDate(bo.getStartDate());
        statisticsWeekBo.setEndDate(bo.getEndDate());
        List<SohuUserIncomeStatisticsWeekVo> list = queryList(statisticsWeekBo);
        List<SohuLineChartVo> result = new LinkedList<SohuLineChartVo>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        LocalDate start = LocalDate.parse(bo.getStartDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate end = LocalDate.parse(bo.getEndDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
        return chartStatUtil.fillMissingDates(sohuReportConvertUtil.convertWeekToLineChartData(list), start, end, SohuDateEnum.DAY);
    }

    @Override
    public List<SohuLineChartVo> stationInviteLineStat(SohuStationReportBo bo) {
        SohuUserIncomeStatisticsWeekBo statisticsWeekBo = new SohuUserIncomeStatisticsWeekBo();
        statisticsWeekBo.setRoleType(sohuReportRoleUtil.getRoleType(bo.getRoleType()));
        statisticsWeekBo.setStationId(bo.getSiteId());
        List<SohuUserIncomeStatisticsWeekVo> list = queryList(statisticsWeekBo);
        List<SohuLineChartVo> result = new LinkedList<SohuLineChartVo>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        List<SohuUserIncomeStatisticsWeekVo> groupList = sohuReportGroupSumUtil.groupSumWeekByDate(list);
        return groupList
                .stream()
                .map(entry -> {
                    SohuLineChartVo item = SohuLineChartVo.builder().build();
                    item.setX(String.valueOf(entry.getWeekDate()));
                    item.setY(entry.getInviteIncome().toString());
                    return item;
                })
                .sorted(Comparator.comparing(SohuLineChartVo::getY))
                .collect(Collectors.toList());
    }

    @Override
    public SohuUserIncomeStatisticsWeekVo queryByParam(Long userId, String roleType, Long siteId, String busyType, String week) {
        return baseMapper.selectVoOne(Wrappers.<SohuUserIncomeStatisticsWeek>lambdaQuery()
                .eq(SohuUserIncomeStatisticsWeek::getUserId, userId)
                .eq(SohuUserIncomeStatisticsWeek::getRoleType, roleType)
                .eq(SohuUserIncomeStatisticsWeek::getStationId, siteId)
                .eq(SohuUserIncomeStatisticsWeek::getBusyType, busyType)
                .eq(SohuUserIncomeStatisticsWeek::getWeekDate, week));
    }


}
