package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.IndependentStatusEnum;
import com.sohu.common.core.enums.SohuDateEnum;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.service.RemotePlatformIndustryService;
import com.sohu.middle.api.vo.SohuPlatformIndustryInfoVo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsBo;
import com.sohu.report.api.vo.*;
import com.sohu.report.domain.SohuUserIncomeStatistics;
import com.sohu.report.domain.SohuUserIncomeStatisticsDay;
import com.sohu.report.domain.SohuUserIncomeStatisticsMonth;
import com.sohu.report.domain.SohuUserIncomeStatisticsWeek;
import com.sohu.report.mapper.SohuUserIncomeStatisticsDayMapper;
import com.sohu.report.mapper.SohuUserIncomeStatisticsMapper;
import com.sohu.report.mapper.SohuUserIncomeStatisticsMonthMapper;
import com.sohu.report.mapper.SohuUserIncomeStatisticsWeekMapper;
import com.sohu.report.service.ISohuUserIncomeStatisticsService;
import com.sohu.report.util.SohuReportRoleUtil;
import com.sohu.report.utils.DateUtils;
import com.sohu.report.vo.UserIncomeVO;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户收益统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuUserIncomeStatisticsServiceImpl implements ISohuUserIncomeStatisticsService {

    // 业务类型常量
    private static final String BUSY_ORDER = "BusyOrder";
    private static final String GOODS = "Goods";
    private static final String SHORT_PLAY = "ShortPlay";
    private static final String NOVEL = "Novel";

    // 日期格式常量
    private static final DateTimeFormatter DAY_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    private static final DateTimeFormatter DISPLAY_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DISPLAY_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    private final SohuUserIncomeStatisticsMapper baseMapper;
    private final SohuUserIncomeStatisticsDayMapper dayMapper;
    private final SohuUserIncomeStatisticsWeekMapper weekMapper;
    private final SohuUserIncomeStatisticsMonthMapper monthMapper;
    private final SohuReportRoleUtil sohuReportRoleUtil;
    @DubboReference
    private RemotePlatformIndustryService remotePlatformIndustryService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询站长收益统计
     */
    @Override
    public SohuUserIncomeStatisticsVo getInfo(Long stationId, String roleType, Integer stationType) {
        Long userId = LoginHelper.getUserId();
        roleType = sohuReportRoleUtil.getRoleType(roleType);
        SohuUserIncomeStatisticsVo sohuUserIncomeStatisticsVo = this.queryByUserIdAndRoleTypeAndStationId(userId, roleType, stationId);
        if (Objects.isNull(sohuUserIncomeStatisticsVo)) {
            return new SohuUserIncomeStatisticsVo();
        }
        Date startOfDay = com.sohu.report.utils.DateUtils.getStartOfDay(new Date());
        // 今日收益
        BigDecimal todayIncome = remoteUserService.getUserIncome(userId, stationType, stationId, roleType, IndependentStatusEnum.DISTRIBUTED.getCode(), startOfDay, new Date());
        sohuUserIncomeStatisticsVo.setTodayIncome(todayIncome);
        // 昨日收益
        Date yesterdayStartOfDay = com.sohu.report.utils.DateUtils.getYesterdayStartOfDay(new Date());
        List<SohuUserIncomeStatisticsDayVo> dayList = dayMapper.selectVoList(Wrappers.<SohuUserIncomeStatisticsDay>lambdaQuery()
                .eq(SohuUserIncomeStatisticsDay::getUserId, userId)
                .eq(SohuUserIncomeStatisticsDay::getRoleType, roleType)
                .eq(SohuUserIncomeStatisticsDay::getStationId, stationId)
                .eq(SohuUserIncomeStatisticsDay::getDayDate, yesterdayStartOfDay));

        BigDecimal yesterdayIncome = BigDecimal.ZERO;
        BigDecimal yesterdayTradeAmount = BigDecimal.ZERO;
        Long yesterdayOrderNum = 0L;
        if (CollectionUtils.isNotEmpty(dayList)) {
            for (SohuUserIncomeStatisticsDayVo dayVo : dayList) {
                yesterdayIncome = CalUtils.add(yesterdayIncome, dayVo.getTotalIncome());
                yesterdayTradeAmount = CalUtils.add(yesterdayTradeAmount, dayVo.getTradeAmount());
                yesterdayOrderNum = yesterdayOrderNum + dayVo.getOrderNum();
            }
        }
        sohuUserIncomeStatisticsVo.setYesterdayIncome(yesterdayIncome);
        sohuUserIncomeStatisticsVo.setYesterdayTradeAmount(yesterdayTradeAmount);
        sohuUserIncomeStatisticsVo.setYesterdayOrderNum(yesterdayOrderNum);
        return sohuUserIncomeStatisticsVo;
    }

    private LambdaQueryWrapper<SohuUserIncomeStatistics> buildQueryWrapper(SohuUserIncomeStatisticsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserIncomeStatistics> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserIncomeStatistics::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleType()), SohuUserIncomeStatistics::getRoleType, bo.getRoleType());
        lqw.eq(bo.getStationId() != null, SohuUserIncomeStatistics::getStationId, bo.getStationId());
        lqw.eq(bo.getReceiveIncome() != null, SohuUserIncomeStatistics::getReceiveIncome, bo.getReceiveIncome());
        lqw.eq(bo.getWaitIncome() != null, SohuUserIncomeStatistics::getWaitIncome, bo.getWaitIncome());
        lqw.eq(bo.getWaitWithdrawal() != null, SohuUserIncomeStatistics::getWaitWithdrawal, bo.getWaitWithdrawal());
        lqw.eq(bo.getAlreadyWithdrawal() != null, SohuUserIncomeStatistics::getAlreadyWithdrawal, bo.getAlreadyWithdrawal());
        return lqw;
    }

    /**
     * 新增用户收益统计
     */
    @Override
    public Boolean insertByBo(SohuUserIncomeStatisticsBo bo) {
        SohuUserIncomeStatistics add = BeanUtil.toBean(bo, SohuUserIncomeStatistics.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户收益统计
     */
    @Override
    public Boolean updateByBo(SohuUserIncomeStatisticsBo bo) {
        SohuUserIncomeStatistics update = BeanUtil.toBean(bo, SohuUserIncomeStatistics.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserIncomeStatistics entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<OrderTradeAnalysisVo> orderTradeAnalysis(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        // 转换为Date类型
        Date start = DateUtil.parse(startDate);
        Date end = DateUtil.parse(endDate);
        // 调用枚举方法获取时间粒度
        SohuDateEnum granularityEnum = SohuDateEnum.getGranularityEnum(start, end);

        // 根据时间粒度进行处理
        switch (granularityEnum) {
            case DAY:
                return processDayGranularity(stationId, roleType, busyType, startDate, endDate);
            case WEEK:
                return processWeekGranularity(stationId, roleType, busyType, startDate, endDate);
            case MONTH:
                return processMonthGranularity(stationId, roleType, busyType, startDate, endDate);
            default:
                throw new IllegalArgumentException("不支持的时间粒度: " + granularityEnum);
        }
    }

    @Override
    public List<SohuShopGoodsStatVo> goodsAnalysis(Long stationId, Integer stationType, String startDate, String endDate) {
        switch (stationType) {
            case Constants.ONE:
                // 城市站点：获取站点下所有成交的订单中销量前十的商品
                return baseMapper.selectTopProducts(stationId, stationType, startDate, endDate, null);

            case Constants.TWO:
                // 行业站点：根据产品分类获取销量前十的商品
                return getIndustryTopProducts(stationId, stationType, startDate, endDate);

            default:
                return Collections.emptyList();
        }
    }

    /**
     * 获取行业站点的热门商品
     */
    private List<SohuShopGoodsStatVo> getIndustryTopProducts(Long stationId, Integer stationType,
                                                            String startDate, String endDate) {
        SohuPlatformIndustryInfoVo industryInfo = remotePlatformIndustryService.getInfo(stationId);
        if (Objects.isNull(industryInfo) || CollectionUtils.isEmpty(industryInfo.getProductCategoryIds())) {
            return Collections.emptyList();
        }

        return baseMapper.selectTopProducts(stationId, stationType, startDate, endDate,
                industryInfo.getProductCategoryIds());
    }

    @Override
    public List<UserIncomeVO> queryUserIncomeByTime(Date startTime, Date endTime) {
        return baseMapper.queryUserIncomeByTime(startTime, endTime);
    }

    @Override
    public SohuUserIncomeStatisticsVo queryByUserIdAndRoleTypeAndStationId(Long userId, String independentObject, Long siteId) {
        return baseMapper.selectVoOne(Wrappers.<SohuUserIncomeStatistics>lambdaQuery()
                .eq(SohuUserIncomeStatistics::getUserId, userId)
                .eq(SohuUserIncomeStatistics::getRoleType, independentObject)
                .eq(SohuUserIncomeStatistics::getStationId, siteId));
    }

    /**
     * 处理日粒度统计
     */
    private List<OrderTradeAnalysisVo> processDayGranularity(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        // 构建查询条件
        LambdaQueryWrapper<SohuUserIncomeStatisticsDay> dayLqw = buildDayQuery(stationId, roleType, busyType, startDate, endDate);
        List<SohuUserIncomeStatisticsDayVo> dayStats = dayMapper.selectVoList(dayLqw);

        // 将现有数据按日期分组
        Map<String, List<SohuUserIncomeStatisticsDayVo>> groupedStats = dayStats.stream()
                .collect(Collectors.groupingBy(SohuUserIncomeStatisticsDayVo::getDayDate));

        // 生成完整的日期范围并填充数据
        return generateCompleteTimeSeriesData(groupedStats, generateDayKeys(startDate, endDate), this::aggregateOrdersByBusinessType);
    }

    /**
     * 构建日查询条件
     */
    private LambdaQueryWrapper<SohuUserIncomeStatisticsDay> buildDayQuery(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        return new LambdaQueryWrapper<SohuUserIncomeStatisticsDay>()
                .eq(SohuUserIncomeStatisticsDay::getStationId, stationId)
                .eq(SohuUserIncomeStatisticsDay::getRoleType, sohuReportRoleUtil.getRoleType(roleType))
                .eq(StringUtils.isNotBlank(busyType), SohuUserIncomeStatisticsDay::getBusyType, busyType)
                .eq(SohuUserIncomeStatisticsDay::getUserId, LoginHelper.getUserId())
                .between(SohuUserIncomeStatisticsDay::getDayDate, startDate, endDate)
                .orderByAsc(SohuUserIncomeStatisticsDay::getDayDate);
    }

    /**
     * 生成完整的时间序列数据（通用方法）
     */
    private <T> List<OrderTradeAnalysisVo> generateCompleteTimeSeriesData(
            Map<String, List<T>> groupedData,
            List<String> timeKeys,
            java.util.function.Function<List<T>, OrderTradeAnalysisVo> aggregator) {

        return timeKeys.stream()
                .map(timeKey -> {
                    if (groupedData.containsKey(timeKey)) {
                        OrderTradeAnalysisVo vo = aggregator.apply(groupedData.get(timeKey));
                        vo.setDisplayDate(formatDisplayDate(timeKey));
                        return vo;
                    } else {
                        return createOrderAnalysisVo(timeKey);
                    }
                })
                .sorted(Comparator.comparing(OrderTradeAnalysisVo::getDisplayDate))
                .collect(Collectors.toList());
    }

    /**
     * 处理周粒度统计
     */
    private List<OrderTradeAnalysisVo> processWeekGranularity(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsWeek> weekLqw = buildWeekQuery(stationId, roleType, busyType, startDate, endDate);
        List<SohuUserIncomeStatisticsWeek> weekStats = weekMapper.selectList(weekLqw);

        // 聚合现有周数据
        Map<String, OrderTradeAnalysisVo> weekDataMap = weekStats.stream()
                .collect(Collectors.groupingBy(
                        SohuUserIncomeStatisticsWeek::getWeekDate,
                        LinkedHashMap::new,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                this::aggregateWeekStats
                        )
                ));

        // 生成完整的周数据
        List<String> allWeekKeys = generateWeekKeys(startDate, endDate);
        return allWeekKeys.stream()
                .map(weekKey -> weekDataMap.getOrDefault(weekKey, createOrderAnalysisVo(weekKey)))
                .sorted(Comparator.comparing(OrderTradeAnalysisVo::getDisplayDate))
                .collect(Collectors.toList());
    }

    /**
     * 聚合周统计数据
     */
    private OrderTradeAnalysisVo aggregateWeekStats(List<SohuUserIncomeStatisticsWeek> weekStats) {
        if (weekStats.isEmpty()) {
            return createOrderAnalysisVo("");
        }

        OrderTradeAnalysisVo vo = createOrderAnalysisVo(weekStats.get(0).getWeekDate());
        weekStats.forEach(stat -> updateOrderAnalysisVo(vo, stat.getBusyType(), stat.getOrderNum(), stat.getTradeAmount()));
        return vo;
    }

    /**
     * 处理月粒度统计
     */
    private List<OrderTradeAnalysisVo> processMonthGranularity(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsMonth> monthLqw = buildMonthQuery(stationId, roleType, busyType, startDate, endDate);
        List<SohuUserIncomeStatisticsMonth> monthStats = monthMapper.selectList(monthLqw);

        // 聚合现有月数据
        Map<String, OrderTradeAnalysisVo> monthDataMap = monthStats.stream()
                .collect(Collectors.groupingBy(
                        SohuUserIncomeStatisticsMonth::getMonthDate,
                        LinkedHashMap::new,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                this::aggregateMonthStats
                        )
                ));

        // 生成完整的月数据
        List<String> allMonthKeys = generateMonthKeys(startDate, endDate);
        return allMonthKeys.stream()
                .map(monthKey -> monthDataMap.getOrDefault(monthKey, createOrderAnalysisVo(monthKey)))
                .sorted(Comparator.comparing(OrderTradeAnalysisVo::getDisplayDate))
                .collect(Collectors.toList());
    }

    /**
     * 聚合月统计数据
     */
    private OrderTradeAnalysisVo aggregateMonthStats(List<SohuUserIncomeStatisticsMonth> monthStats) {
        if (monthStats.isEmpty()) {
            return createOrderAnalysisVo("");
        }

        OrderTradeAnalysisVo vo = createOrderAnalysisVo(monthStats.get(0).getMonthDate());
        monthStats.forEach(stat -> updateOrderAnalysisVo(vo, stat.getBusyType(), stat.getOrderNum(), stat.getTradeAmount()));
        return vo;
    }

    /**
     * 构建周查询条件
     */
    private LambdaQueryWrapper<SohuUserIncomeStatisticsWeek> buildWeekQuery(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        return new LambdaQueryWrapper<SohuUserIncomeStatisticsWeek>()
                .select(SohuUserIncomeStatisticsWeek::getBusyType,
                        SohuUserIncomeStatisticsWeek::getOrderNum,
                        SohuUserIncomeStatisticsWeek::getTradeAmount,
                        SohuUserIncomeStatisticsWeek::getWeekDate)
                .eq(SohuUserIncomeStatisticsWeek::getStationId, stationId)
                .eq(SohuUserIncomeStatisticsWeek::getRoleType, sohuReportRoleUtil.getRoleType(roleType))
                .eq(SohuUserIncomeStatisticsWeek::getUserId, LoginHelper.getUserId())
                .eq(StringUtils.isNotBlank(busyType), SohuUserIncomeStatisticsWeek::getBusyType, busyType)
                .between(SohuUserIncomeStatisticsWeek::getWeekDate,
                        DateUtils.formatToYearWeek(DateUtil.parse(startDate)),
                        DateUtils.formatToYearWeek(DateUtil.parse(endDate)))
                .orderByAsc(SohuUserIncomeStatisticsWeek::getWeekDate);
    }

    /**
     * 构建月查询条件
     */
    private LambdaQueryWrapper<SohuUserIncomeStatisticsMonth> buildMonthQuery(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        return new LambdaQueryWrapper<SohuUserIncomeStatisticsMonth>()
                .select(SohuUserIncomeStatisticsMonth::getBusyType,
                        SohuUserIncomeStatisticsMonth::getOrderNum,
                        SohuUserIncomeStatisticsMonth::getTradeAmount,
                        SohuUserIncomeStatisticsMonth::getMonthDate)
                .eq(SohuUserIncomeStatisticsMonth::getStationId, stationId)
                .eq(SohuUserIncomeStatisticsMonth::getRoleType, sohuReportRoleUtil.getRoleType(roleType))
                .eq(SohuUserIncomeStatisticsMonth::getUserId, LoginHelper.getUserId())
                .eq(StringUtils.isNotBlank(busyType), SohuUserIncomeStatisticsMonth::getBusyType, busyType)
                .between(SohuUserIncomeStatisticsMonth::getMonthDate,
                        DateUtils.formatToYearMonth(DateUtil.parse(startDate)),
                        DateUtils.formatToYearMonth(DateUtil.parse(endDate)))
                .orderByAsc(SohuUserIncomeStatisticsMonth::getMonthDate);
    }

    /**
     * 创建OrderAnalysisVo对象
     */
    private OrderTradeAnalysisVo createOrderAnalysisVo(String date) {
        OrderTradeAnalysisVo vo = new OrderTradeAnalysisVo();
        vo.setDisplayDate(formatDisplayDate(date));
        initializeOrderAnalysisVo(vo);
        return vo;
    }

    /**
     * 格式化显示日期
     */
    private String formatDisplayDate(String date) {
        if (date.length() == 8) {
            // yyyyMMdd 格式
            return LocalDate.parse(date, DAY_FORMATTER).format(DISPLAY_DATE_FORMATTER);
        } else if (date.length() == 6 && date.matches("\\d{6}")) {
            try {
                // 尝试解析为 yyyyMM (月)
                YearMonth ym = YearMonth.parse(date, MONTH_FORMATTER);
                return ym.format(DISPLAY_MONTH_FORMATTER);
            } catch (Exception e) {
                // 解析为周格式
                return date.substring(0, 4) + "-W" + date.substring(4);
            }
        }
        return date;
    }

    /**
     * 初始化OrderAnalysisVo的默认值
     */
    private void initializeOrderAnalysisVo(OrderTradeAnalysisVo vo) {
        vo.setWishOrderNum(0);
        vo.setProductOrderNum(0);
        vo.setShortDramaOrderNum(0);
        vo.setNovelOrderNum(0);
        vo.setNovelIncome(BigDecimal.ZERO);
        vo.setShortDramaIncome(BigDecimal.ZERO);
        vo.setProductIncome(BigDecimal.ZERO);
        vo.setWishIncome(BigDecimal.ZERO);
    }

    /**
     * 根据业务类型更新订单分析对象
     */
    private void updateOrderAnalysisVo(OrderTradeAnalysisVo vo, String busyType, Long orderNum, BigDecimal tradeAmount) {
        int orderCount = Optional.ofNullable(orderNum).orElse(0L).intValue();
        BigDecimal amount = Optional.ofNullable(tradeAmount).orElse(BigDecimal.ZERO);

        switch (busyType) {
            case BUSY_ORDER:
                vo.setWishOrderNum(vo.getWishOrderNum() + orderCount);
                vo.setWishIncome(vo.getWishIncome().add(amount));
                break;
            case GOODS:
                vo.setProductOrderNum(vo.getProductOrderNum() + orderCount);
                vo.setProductIncome(vo.getProductIncome().add(amount));
                break;
            case SHORT_PLAY:
                vo.setShortDramaOrderNum(vo.getShortDramaOrderNum() + orderCount);
                vo.setShortDramaIncome(vo.getShortDramaIncome().add(amount));
                break;
            case NOVEL:
                vo.setNovelOrderNum(vo.getNovelOrderNum() + orderCount);
                vo.setNovelIncome(vo.getNovelIncome().add(amount));
                break;
            default:
                log.warn("未知的业务类型: {}", busyType);
                break;
        }
    }

    /**
     * 聚合订单数据（按业务类型）
     */
    private OrderTradeAnalysisVo aggregateOrdersByBusinessType(List<SohuUserIncomeStatisticsDayVo> stats) {
        if (stats.isEmpty()) {
            return createOrderAnalysisVo("");
        }

        OrderTradeAnalysisVo vo = createOrderAnalysisVo(stats.get(0).getDayDate());
        stats.forEach(stat -> updateOrderAnalysisVo(vo, stat.getBusyType(), stat.getOrderNum(), stat.getTradeAmount()));
        return vo;
    }

    /**
     * 生成指定日期范围内的所有日期（yyyyMMdd格式）
     */
    private List<String> generateDayKeys(String startDateStr, String endDateStr) {
        LocalDate start = LocalDate.parse(startDateStr);
        LocalDate end = LocalDate.parse(endDateStr);

        return start.datesUntil(end.plusDays(1))
                .map(date -> date.format(DAY_FORMATTER))
                .collect(Collectors.toList());
    }

    /**
     * 生成指定日期范围内的所有月份（yyyyMM格式）
     */
    private List<String> generateMonthKeys(String startDateStr, String endDateStr) {
        YearMonth start = YearMonth.from(LocalDate.parse(startDateStr));
        YearMonth end = YearMonth.from(LocalDate.parse(endDateStr));

        List<String> months = new ArrayList<>();
        YearMonth current = start;
        while (!current.isAfter(end)) {
            months.add(current.format(MONTH_FORMATTER));
            current = current.plusMonths(1);
        }
        return months;
    }

    /**
     * 生成指定日期范围内的所有周（yyyyWW格式）
     */
    private List<String> generateWeekKeys(String startDateStr, String endDateStr) {
        Date current = DateUtil.parse(startDateStr);
        Date end = DateUtil.parse(endDateStr);

        // 使用 LinkedHashSet 保持顺序并确保键的唯一性
        Set<String> uniqueWeeks = new LinkedHashSet<>();

        // 逐天循环，提取周信息，并添加到集合中
        while (!current.after(end)) {
            uniqueWeeks.add(DateUtils.formatToYearWeek(current));
            current = DateUtil.offsetDay(current, 1);
        }
        return new ArrayList<>(uniqueWeeks);
    }
}
