package com.sohu.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.IndependentStatusEnum;
import com.sohu.common.core.enums.SohuDateEnum;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.service.RemotePlatformIndustryService;
import com.sohu.middle.api.vo.SohuPlatformIndustryInfoVo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsBo;
import com.sohu.report.api.vo.*;
import com.sohu.report.domain.SohuUserIncomeStatistics;
import com.sohu.report.domain.SohuUserIncomeStatisticsDay;
import com.sohu.report.domain.SohuUserIncomeStatisticsMonth;
import com.sohu.report.domain.SohuUserIncomeStatisticsWeek;
import com.sohu.report.mapper.SohuUserIncomeStatisticsDayMapper;
import com.sohu.report.mapper.SohuUserIncomeStatisticsMapper;
import com.sohu.report.mapper.SohuUserIncomeStatisticsMonthMapper;
import com.sohu.report.mapper.SohuUserIncomeStatisticsWeekMapper;
import com.sohu.report.service.ISohuUserIncomeStatisticsService;
import com.sohu.report.util.SohuReportRoleUtil;
import com.sohu.report.utils.DateUtils;
import com.sohu.report.vo.UserIncomeVO;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户收益统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuUserIncomeStatisticsServiceImpl implements ISohuUserIncomeStatisticsService {

    private final SohuUserIncomeStatisticsMapper baseMapper;
    private final SohuUserIncomeStatisticsDayMapper dayMapper;
    private final SohuUserIncomeStatisticsWeekMapper weekMapper;
    private final SohuUserIncomeStatisticsMonthMapper monthMapper;
    private final SohuReportRoleUtil sohuReportRoleUtil;
    @DubboReference
    private RemotePlatformIndustryService remotePlatformIndustryService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询站长收益统计
     */
    @Override
    public SohuUserIncomeStatisticsVo getInfo(Long stationId, String roleType, Integer stationType) {
        Long userId = LoginHelper.getUserId();
        SohuUserIncomeStatisticsVo sohuUserIncomeStatisticsVo = this.queryByUserIdAndRoleTypeAndStationId(userId, roleType, stationId);
        if (Objects.isNull(sohuUserIncomeStatisticsVo)) {
            return new SohuUserIncomeStatisticsVo();
        }
        Date startOfDay = com.sohu.report.utils.DateUtils.getStartOfDay(new Date());
        // 今日收益
        BigDecimal todayIncome = remoteUserService.getUserIncome(userId, stationType, stationId, roleType, IndependentStatusEnum.DISTRIBUTED.getCode(), startOfDay, new Date());
        sohuUserIncomeStatisticsVo.setTodayIncome(todayIncome);
        // 昨日收益
        Date yesterdayStartOfDay = com.sohu.report.utils.DateUtils.getYesterdayStartOfDay(new Date());
        List<SohuUserIncomeStatisticsDayVo> dayList = dayMapper.selectVoList(Wrappers.<SohuUserIncomeStatisticsDay>lambdaQuery()
                .eq(SohuUserIncomeStatisticsDay::getUserId, userId)
                .eq(SohuUserIncomeStatisticsDay::getRoleType, roleType)
                .eq(SohuUserIncomeStatisticsDay::getStationId, stationId)
                .eq(SohuUserIncomeStatisticsDay::getDayDate, yesterdayStartOfDay));

        BigDecimal yesterdayIncome = BigDecimal.ZERO;
        BigDecimal yesterdayTradeAmount = BigDecimal.ZERO;
        Long yesterdayOrderNum = 0L;
        if (CollectionUtils.isNotEmpty(dayList)) {
            for (SohuUserIncomeStatisticsDayVo dayVo : dayList) {
                yesterdayIncome = CalUtils.add(yesterdayIncome, dayVo.getTotalIncome());
                yesterdayTradeAmount = CalUtils.add(yesterdayTradeAmount, dayVo.getTradeAmount());
                yesterdayOrderNum = yesterdayOrderNum + dayVo.getOrderNum();
            }
        }
        sohuUserIncomeStatisticsVo.setYesterdayIncome(yesterdayIncome);
        sohuUserIncomeStatisticsVo.setYesterdayTradeAmount(yesterdayTradeAmount);
        sohuUserIncomeStatisticsVo.setYesterdayOrderNum(yesterdayOrderNum);
        return sohuUserIncomeStatisticsVo;
    }

    private LambdaQueryWrapper<SohuUserIncomeStatistics> buildQueryWrapper(SohuUserIncomeStatisticsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuUserIncomeStatistics> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuUserIncomeStatistics::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleType()), SohuUserIncomeStatistics::getRoleType, bo.getRoleType());
        lqw.eq(bo.getStationId() != null, SohuUserIncomeStatistics::getStationId, bo.getStationId());
        lqw.eq(bo.getReceiveIncome() != null, SohuUserIncomeStatistics::getReceiveIncome, bo.getReceiveIncome());
        lqw.eq(bo.getWaitIncome() != null, SohuUserIncomeStatistics::getWaitIncome, bo.getWaitIncome());
        lqw.eq(bo.getWaitWithdrawal() != null, SohuUserIncomeStatistics::getWaitWithdrawal, bo.getWaitWithdrawal());
        lqw.eq(bo.getAlreadyWithdrawal() != null, SohuUserIncomeStatistics::getAlreadyWithdrawal, bo.getAlreadyWithdrawal());
        return lqw;
    }

    /**
     * 新增用户收益统计
     */
    @Override
    public Boolean insertByBo(SohuUserIncomeStatisticsBo bo) {
        SohuUserIncomeStatistics add = BeanUtil.toBean(bo, SohuUserIncomeStatistics.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户收益统计
     */
    @Override
    public Boolean updateByBo(SohuUserIncomeStatisticsBo bo) {
        SohuUserIncomeStatistics update = BeanUtil.toBean(bo, SohuUserIncomeStatistics.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuUserIncomeStatistics entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<OrderTradeAnalysisVo> orderTradeAnalysis(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        // 转换为Date类型
        Date start = DateUtil.parse(startDate);
        Date end = DateUtil.parse(endDate);
        // 调用枚举方法获取时间粒度
        SohuDateEnum granularityEnum = SohuDateEnum.getGranularityEnum(start, end);

        // 根据时间粒度进行处理
        switch (granularityEnum) {
            case DAY:
                return processDayGranularity(stationId, roleType, busyType, startDate, endDate);
            case WEEK:
                return processWeekGranularity(stationId, roleType, busyType, startDate, endDate);
            case MONTH:
                return processMonthGranularity(stationId, roleType, busyType, startDate, endDate);
            default:
                throw new IllegalArgumentException("不支持的时间粒度: " + granularityEnum);
        }
    }

    @Override
    public List<SohuShopGoodsStatVo> goodsAnalysis(Long stationId, Integer stationType, String startDate, String endDate) {
        // 站点类型等于1标识是城市站点
        if (stationType.equals(Constants.ONE)) {
            // 获取站点下所有成交的订单中销量前十的商品(只要支付成功了,销量+1)
            return baseMapper.selectTopProducts(stationId, stationType, startDate, endDate, null);
        }
        // 站点类型等于1标识是行业站点
        if (stationType.equals(Constants.TWO)) {
            SohuPlatformIndustryInfoVo sohuPlatformIndustryInfoVo = remotePlatformIndustryService.getInfo(stationId);
            if (Objects.isNull(sohuPlatformIndustryInfoVo) || Objects.isNull(sohuPlatformIndustryInfoVo.getProductCategoryIds())) {
                return new ArrayList<>();
            }

            List<Long> productCategoryIds = sohuPlatformIndustryInfoVo.getProductCategoryIds();
            return baseMapper.selectTopProducts(stationId, stationType, startDate, endDate, productCategoryIds);
        }

        return new ArrayList<>();
    }

    @Override
    public List<UserIncomeVO> queryUserIncomeByTime(Date startTime, Date endTime) {
        return baseMapper.queryUserIncomeByTime(startTime, endTime);
    }

    @Override
    public SohuUserIncomeStatisticsVo queryByUserIdAndRoleTypeAndStationId(Long userId, String independentObject, Long siteId) {
        return baseMapper.selectVoOne(Wrappers.<SohuUserIncomeStatistics>lambdaQuery()
                .eq(SohuUserIncomeStatistics::getUserId, userId)
                .eq(SohuUserIncomeStatistics::getRoleType, independentObject)
                .eq(SohuUserIncomeStatistics::getStationId, siteId));
    }

    /**
     * 处理日粒度统计
     */
    private List<OrderTradeAnalysisVo> processDayGranularity(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsDay> dayLqw = new LambdaQueryWrapper<>();
        dayLqw.eq(SohuUserIncomeStatisticsDay::getStationId, stationId)
                .eq(SohuUserIncomeStatisticsDay::getRoleType, sohuReportRoleUtil.getRoleType(roleType))
                .eq(StringUtils.isNotBlank(busyType), SohuUserIncomeStatisticsDay::getBusyType, busyType)
                .eq(SohuUserIncomeStatisticsDay::getUserId, LoginHelper.getUserId())
                .between(SohuUserIncomeStatisticsDay::getDayDate, startDate, endDate)
                .orderByAsc(SohuUserIncomeStatisticsDay::getDayDate);

        List<SohuUserIncomeStatisticsDayVo> dayStats = dayMapper.selectVoList(dayLqw);
        if (dayStats.isEmpty()) {
            return Collections.emptyList();
        }

        // 按日期聚合数据
        Map<LocalDate, OrderTradeAnalysisVo> dailyAnalysisMap = dayStats.stream()
                .collect(Collectors.groupingBy(
                        stat -> LocalDate.parse(stat.getDayDate(), DateTimeFormatter.ofPattern("yyyyMMdd")),
                        LinkedHashMap::new,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                this::aggregateOrdersByBusinessType
                        )
                ));

        return dailyAnalysisMap.values().stream()
                .sorted(Comparator.comparing(OrderTradeAnalysisVo::getDisplayDate))
                .collect(Collectors.toList());
    }

    /**
     * 处理周粒度统计
     */
    private List<OrderTradeAnalysisVo> processWeekGranularity(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsWeek> weekLqw = buildWeekQuery(stationId, roleType, busyType, startDate, endDate);
        List<SohuUserIncomeStatisticsWeek> weekStats = weekMapper.selectList(weekLqw);

        if (weekStats.isEmpty()) {
            return Collections.emptyList();
        }

        return processWeekStats(weekStats);
    }

    /**
     * 处理月粒度统计
     */
    private List<OrderTradeAnalysisVo> processMonthGranularity(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsMonth> monthLqw = buildMonthQuery(stationId, roleType, busyType, startDate, endDate);
        List<SohuUserIncomeStatisticsMonth> monthStats = monthMapper.selectList(monthLqw);

        if (monthStats.isEmpty()) {
            return Collections.emptyList();
        }

        return processMonthStats(monthStats);
    }

    /**
     * 构建周查询条件
     */
    private LambdaQueryWrapper<SohuUserIncomeStatisticsWeek> buildWeekQuery(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsWeek> weekLqw = new LambdaQueryWrapper<>();
        weekLqw.select(SohuUserIncomeStatisticsWeek::getBusyType,
                        SohuUserIncomeStatisticsWeek::getOrderNum,
                        SohuUserIncomeStatisticsWeek::getWeekDate)
                .eq(SohuUserIncomeStatisticsWeek::getStationId, stationId)
                .eq(SohuUserIncomeStatisticsWeek::getRoleType, sohuReportRoleUtil.getRoleType(roleType))
                .eq(SohuUserIncomeStatisticsWeek::getUserId, LoginHelper.getUserId())
                .eq(StringUtils.isNotBlank(busyType), SohuUserIncomeStatisticsWeek::getBusyType, busyType)
                .between(SohuUserIncomeStatisticsWeek::getWeekDate, DateUtils.formatToYearWeek(DateUtil.parse(startDate)), DateUtils.formatToYearWeek(DateUtil.parse(endDate)));

        return weekLqw.orderByAsc(SohuUserIncomeStatisticsWeek::getWeekDate);
    }

    /**
     * 处理周统计数据
     */
    private List<OrderTradeAnalysisVo> processWeekStats(List<SohuUserIncomeStatisticsWeek> weekStats) {
        Map<String, OrderTradeAnalysisVo> weeklyAnalysisMap = new LinkedHashMap<>();

        for (SohuUserIncomeStatisticsWeek stat : weekStats) {
            OrderTradeAnalysisVo vo = weeklyAnalysisMap.computeIfAbsent(stat.getWeekDate(), k -> createOrderAnalysisVo(stat.getWeekDate()));
            updateOrderAnalysisVo(vo, stat.getBusyType(), stat.getOrderNum(), stat.getTradeAmount());
        }

        return new ArrayList<>(weeklyAnalysisMap.values());
    }

    /**
     * 构建月查询条件
     */
    private LambdaQueryWrapper<SohuUserIncomeStatisticsMonth> buildMonthQuery(Long stationId, String roleType, String busyType, String startDate, String endDate) {
        LambdaQueryWrapper<SohuUserIncomeStatisticsMonth> monthLqw = new LambdaQueryWrapper<>();
        monthLqw.select(SohuUserIncomeStatisticsMonth::getBusyType,
                        SohuUserIncomeStatisticsMonth::getOrderNum,
                        SohuUserIncomeStatisticsMonth::getMonthDate)
                .eq(SohuUserIncomeStatisticsMonth::getStationId, stationId)
                .eq(SohuUserIncomeStatisticsMonth::getRoleType, sohuReportRoleUtil.getRoleType(roleType))
                .eq(SohuUserIncomeStatisticsMonth::getUserId, LoginHelper.getUserId())
                .eq(StringUtils.isNotBlank(busyType), SohuUserIncomeStatisticsMonth::getBusyType, busyType)
                .between(SohuUserIncomeStatisticsMonth::getMonthDate, DateUtils.formatToYearMonth(DateUtil.parse(startDate)), DateUtils.formatToYearMonth(DateUtil.parse(endDate)));

        return monthLqw.orderByAsc(SohuUserIncomeStatisticsMonth::getMonthDate);
    }

    /**
     * 处理月统计数据
     */
    private List<OrderTradeAnalysisVo> processMonthStats(List<SohuUserIncomeStatisticsMonth> monthStats) {
        Map<String, OrderTradeAnalysisVo> monthlyAnalysisMap = new LinkedHashMap<>();

        for (SohuUserIncomeStatisticsMonth stat : monthStats) {
            OrderTradeAnalysisVo vo = monthlyAnalysisMap.computeIfAbsent(stat.getMonthDate(), k -> createOrderAnalysisVo(stat.getMonthDate()));
            updateOrderAnalysisVo(vo, stat.getBusyType(), stat.getOrderNum(), stat.getTradeAmount());
        }

        return new ArrayList<>(monthlyAnalysisMap.values());
    }

    /**
     * 创建OrderAnalysisVo对象
     */
    private OrderTradeAnalysisVo createOrderAnalysisVo(String date, String displayDate) {
        OrderTradeAnalysisVo vo = new OrderTradeAnalysisVo();
        vo.setDisplayDate(date);
        vo.setWishOrderNum(0);
        vo.setProductOrderNum(0);
        vo.setShortDramaOrderNum(0);
        vo.setNovelOrderNum(0);
        vo.setNovelIncome(BigDecimal.ZERO);
        vo.setShortDramaIncome(BigDecimal.ZERO);
        vo.setProductIncome(BigDecimal.ZERO);
        vo.setWishIncome(BigDecimal.ZERO);

        return vo;
    }

    /**
     * 创建OrderAnalysisVo对象（日粒度专用）
     */
    private OrderTradeAnalysisVo createOrderAnalysisVo(String date) {
        return createOrderAnalysisVo(date, null);
    }

    /**
     * 根据业务类型更新订单分析对象
     */
    private void updateOrderAnalysisVo(OrderTradeAnalysisVo vo, String busyType, Long orderNum, BigDecimal tradeAmount) {
        int orderCount = Optional.ofNullable(orderNum).orElse(0L).intValue();
        BigDecimal amount = Optional.ofNullable(tradeAmount).orElse(BigDecimal.ZERO);

        switch (busyType) {
            case "BusyOrder":
                vo.setWishOrderNum(vo.getWishOrderNum() + orderCount);
                vo.setWishIncome(vo.getWishIncome().add(amount));
                break;
            case "Goods":
                vo.setProductOrderNum(vo.getProductOrderNum() + orderCount);
                vo.setProductIncome(vo.getProductIncome().add(amount));
                break;
            case "ShortPlay":
                vo.setShortDramaOrderNum(vo.getShortDramaOrderNum() + orderCount);
                vo.setShortDramaIncome(vo.getShortDramaIncome().add(amount));
                break;
            case "Novel":
                vo.setNovelOrderNum(vo.getNovelOrderNum() + orderCount);
                vo.setNovelIncome(vo.getNovelIncome().add(amount));
                break;
            default:
                log.warn("未知的业务类型: {}", busyType);
                break;
        }
    }

    /**
     * 聚合订单数据（按业务类型）
     */
    private OrderTradeAnalysisVo aggregateOrdersByBusinessType(List<SohuUserIncomeStatisticsDayVo> stats) {
        OrderTradeAnalysisVo vo = createOrderAnalysisVo(stats.get(0).getDayDate());

        stats.forEach(stat -> updateOrderAnalysisVo(vo, stat.getBusyType(), stat.getOrderNum(), stat.getTradeAmount()));

        return vo;
    }

    /**
     * 生成指定日期范围内的所有日期（yyyyMMdd格式）
     */
    private List<String> generateDayKeys(String startDateStr, String endDateStr) {
        List<String> dates = new ArrayList<>();
        LocalDate start = LocalDate.parse(startDateStr);
        LocalDate end = LocalDate.parse(endDateStr);

        LocalDate current = start;
        while (!current.isAfter(end)) {
            dates.add(current.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            current = current.plusDays(1);
        }
        return dates;
    }

    /**
     * 生成指定日期范围内的所有月份（yyyyMM格式）
     */
    private List<String> generateMonthKeys(String startDateStr, String endDateStr) {
        List<String> months = new ArrayList<>();
        YearMonth start = YearMonth.from(LocalDate.parse(startDateStr));
        YearMonth end = YearMonth.from(LocalDate.parse(endDateStr));

        YearMonth current = start;
        while (!current.isAfter(end)) {
            months.add(current.format(DateTimeFormatter.ofPattern("yyyyMM")));
            current = current.plusMonths(1);
        }
        return months;
    }

    /**
     * 生成指定日期范围内的所有周（yyyyWW格式）
     */
    private List<String> generateWeekKeys(String startDateStr, String endDateStr) {
        List<String> weeks = new ArrayList<>();
        Date current = DateUtil.parse(startDateStr);
        Date end = DateUtil.parse(endDateStr);

        // 使用 LinkedHashSet 保持顺序并确保键的唯一性
        Set<String> uniqueWeeks = new LinkedHashSet<>();

        // 逐天循环，提取周信息，并添加到集合中。
        while (!current.after(end)) {
            uniqueWeeks.add(DateUtils.formatToYearWeek(current));
            current = DateUtil.offsetDay(current, 1);
        }
        return new ArrayList<>(uniqueWeeks);
    }
}
