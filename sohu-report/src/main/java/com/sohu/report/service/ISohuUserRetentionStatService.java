package com.sohu.report.service;


import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserRetentionStatBo;
import com.sohu.report.api.vo.SohuUserRetentionStatVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户留存统计Service接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface ISohuUserRetentionStatService {

    /**
     * 查询用户留存统计
     */
    SohuUserRetentionStatVo queryById(Long id);

    /**
     * 查询用户留存统计列表
     */
    TableDataInfo<SohuUserRetentionStatVo> queryPageList(SohuUserRetentionStatBo bo, PageQuery pageQuery);

    /**
     * 查询用户留存统计列表
     */
    List<SohuUserRetentionStatVo> queryList(SohuUserRetentionStatBo bo);

    /**
     * 修改用户留存统计
     */
    Boolean insertByBo(SohuUserRetentionStatBo bo);

    /**
     * 修改用户留存统计
     */
    Boolean updateByBo(SohuUserRetentionStatBo bo);

    /**
     * 校验并批量删除用户留存统计信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量保存或更新用户留存统计数据
     *
     * @param retentionStats 用户留存统计数据列表
     */
    void saveOrUpdateBatchRetentionStats(List<SohuUserRetentionStatVo> retentionStats);
}
