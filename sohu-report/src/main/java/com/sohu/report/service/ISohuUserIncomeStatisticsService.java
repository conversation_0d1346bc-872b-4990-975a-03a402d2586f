package com.sohu.report.service;


import com.sohu.report.api.bo.SohuUserIncomeStatisticsBo;
import com.sohu.report.api.vo.OrderTradeAnalysisVo;
import com.sohu.report.api.vo.SohuShopGoodsStatVo;
import com.sohu.report.api.vo.SohuUserIncomeOverviewVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsVo;
import com.sohu.report.vo.UserIncomeVO;

import java.util.Date;
import java.util.List;

/**
 * 用户收益统计Service接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface ISohuUserIncomeStatisticsService {

    /**
     * 修改用户收益统计
     */
    Boolean insertByBo(SohuUserIncomeStatisticsBo bo);

    /**
     * 修改用户收益统计
     */
    Boolean updateByBo(SohuUserIncomeStatisticsBo bo);

    /**
     * 查询首页看板数据总计
     *
     * @param stationId
     * @param roleType
     * @param stationType
     * @return
     */
    SohuUserIncomeStatisticsVo getInfo(Long stationId, String roleType, Integer stationType);


    /**
     * 订单分析
     *
     * @param stationId 站点ID
     * @param roleType 角色类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 订单分析结果列表
     */
    List<OrderTradeAnalysisVo> orderTradeAnalysis(Long stationId, String roleType, String busyType, String startDate, String endDate);

    /**
     * 商品分析
     *
     * @param stationId 站点ID
     * @param stationType 站点类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 商品分析结果列表
     */
    List<SohuShopGoodsStatVo> goodsAnalysis(Long stationId, Integer stationType, String startDate, String endDate);

    /**
     * 基于时间查询用户收益
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<UserIncomeVO> queryUserIncomeByTime(Date startTime, Date endTime);

    /**
     * 查询用户收益统计值
     *
     * @param userId
     * @param independentObject
     * @param siteId
     * @return
     */
    SohuUserIncomeStatisticsVo queryByUserIdAndRoleTypeAndStationId(Long userId, String independentObject, Long siteId);

    /**
     * 查询收益概览
     *
     * @param stationId
     * @param stationType
     * @param startDate
     * @param endDate
     * @return
     */
    SohuUserIncomeOverviewVo getIncomeOverview(Long stationId, Integer stationType, Date startDate, Date endDate);

}
