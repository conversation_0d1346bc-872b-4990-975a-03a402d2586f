package com.sohu.report.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.common.excel.utils.ExcelUtil;
import com.sohu.common.log.annotation.Log;
import com.sohu.common.log.enums.BusinessType;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserRetentionStatBo;
import com.sohu.report.api.vo.SohuUserRetentionStatVo;
import com.sohu.report.service.ISohuUserRetentionStatService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 用户留存统计控制器
 * 前端访问路由地址为:/system/userRetentionStat
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/userRetentionStat")
public class SohuUserRetentionStatController extends BaseController {

    private final ISohuUserRetentionStatService iSohuUserRetentionStatService;

    /**
     * 查询用户留存统计列表
     */
    @SaCheckPermission("system:userRetentionStat:list")
    @GetMapping("/list")
    public TableDataInfo<SohuUserRetentionStatVo> list(SohuUserRetentionStatBo bo, PageQuery pageQuery) {
        return iSohuUserRetentionStatService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户留存统计列表
     */
    @SaCheckPermission("system:userRetentionStat:export")
    @Log(title = "用户留存统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SohuUserRetentionStatBo bo, HttpServletResponse response) {
        List<SohuUserRetentionStatVo> list = iSohuUserRetentionStatService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户留存统计", SohuUserRetentionStatVo.class, response);
    }

    /**
     * 获取用户留存统计详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:userRetentionStat:query")
    @GetMapping("/{id}")
    public R<SohuUserRetentionStatVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iSohuUserRetentionStatService.queryById(id));
    }

    /**
     * 新增用户留存统计
     */
    @SaCheckPermission("system:userRetentionStat:add")
    @Log(title = "用户留存统计", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody SohuUserRetentionStatBo bo) {
        return toAjax(iSohuUserRetentionStatService.insertByBo(bo));
    }

    /**
     * 修改用户留存统计
     */
    @SaCheckPermission("system:userRetentionStat:edit")
    @Log(title = "用户留存统计", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Boolean> edit(@Validated(EditGroup.class) @RequestBody SohuUserRetentionStatBo bo) {
        return toAjax(iSohuUserRetentionStatService.updateByBo(bo));
    }

    /**
     * 删除用户留存统计
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:userRetentionStat:remove")
    @Log(title = "用户留存统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(iSohuUserRetentionStatService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
