package com.sohu.report.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.report.api.bo.SohuReportBillDayDetailBo;
import com.sohu.report.api.bo.SohuReportBillDetailBo;
import com.sohu.report.api.vo.SohuAgentIncomeStatisticsVo;
import com.sohu.report.api.vo.SohuReportBillDetailVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsDayVo;
import com.sohu.report.service.ISohuReportService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 折线统计
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/report")
public class SohuReportController extends BaseController {

    private final ISohuReportService sohuReportService;

    @Operation(summary = "账单详情", description = "负责人:柯真 账单详情")
    @GetMapping("/bill/detail")
    public R<SohuReportBillDetailVo> billDetail(SohuReportBillDetailBo bo) {
        return R.ok(sohuReportService.billDetail(bo));
    }

    @Operation(summary = "账单详情的每天收益列表", description = "负责人:柯真 账单详情的每天收益列表")
    @GetMapping("/bill/dayDetail")
    public R<List<SohuUserIncomeStatisticsDayVo>> billDayDetail(SohuReportBillDayDetailBo bo) {
        return R.ok(sohuReportService.billDayDetail(bo));
    }

    @Operation(summary = "服务商看板统计", description = "负责人:柯真 服务商看板统计")
    @GetMapping("/agent/board")
    public R<SohuAgentIncomeStatisticsVo> agentBoard() {
        return R.ok(sohuReportService.agentBoard());
    }

}
