package com.sohu.report.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.report.api.vo.OrderTradeAnalysisVo;
import com.sohu.report.api.vo.SohuShopGoodsStatVo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsVo;
import com.sohu.report.service.ISohuUserIncomeStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 站长看板统计管理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/income/board")
public class SohuUserIncomeBoardController extends BaseController {

    private final ISohuUserIncomeStatisticsService incomeStatisticsService;

    @Operation(summary = "获取站长看板统计", description = "负责人:汪伟 获取站长看板统计列表")
    @GetMapping
    public R<SohuUserIncomeStatisticsVo> info(@RequestParam(value = "stationId") Long stationId,
                                              @RequestParam(value = "roleType") String roleType,
                                              @RequestParam(value = "stationType") Integer stationType) {
        return R.ok(incomeStatisticsService.getInfo(stationId, roleType, stationType));
    }

    @Operation(summary = "获取站长站点订单量分析", description = "负责人:张良峰 获取站长站点订单量分析")
    @GetMapping("/order/analysis")
    public R<List<OrderTradeAnalysisVo>> orderAnalysis(@RequestParam(value = "stationId") Long stationId,
                                                       @RequestParam(value = "roleType") String roleType,
                                                       @RequestParam(value = "busyType", required = false) String busyType,
                                                       @RequestParam(value = "startDate") String startDate,
                                                       @RequestParam(value = "endDate") String endDate) {
        return R.ok(incomeStatisticsService.orderTradeAnalysis(stationId, roleType, busyType, startDate, endDate));
    }

    @Operation(summary = "获取站长站点热门内容", description = "负责人:张良峰 获取站长站点热门内容")
    @GetMapping("/goods/analysis")
    public R<List<SohuShopGoodsStatVo>> goodsAnalysis(@RequestParam(value = "stationId") Long stationId,
                                                      @RequestParam(value = "stationType") Integer stationType,
                                                      @RequestParam(value = "startDate") String startDate,
                                                      @RequestParam(value = "endDate") String endDate) {

        return R.ok(incomeStatisticsService.goodsAnalysis(stationId, stationType, startDate, endDate));
    }
}
