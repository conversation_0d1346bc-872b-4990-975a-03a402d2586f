package com.sohu.report.controller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.report.api.vo.*;
import com.sohu.report.service.ISohuUserIncomeStatisticsInfoService;
import com.sohu.report.service.ISohuUserIncomeStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 站长看板统计管理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/income/board")
public class SohuUserIncomeBoardController extends BaseController {

    private final ISohuUserIncomeStatisticsService incomeStatisticsService;
    private final ISohuUserIncomeStatisticsInfoService incomeStatisticsInfoService;

    @Operation(summary = "获取站长看板统计", description = "负责人:雷博 获取站长看板统计列表")
    @GetMapping
    public R<SohuUserIncomeStatisticsVo> info(@RequestParam(value = "stationId") Long stationId,
                                              @RequestParam(value = "roleType") String roleType,
                                              @RequestParam(value = "stationType") Integer stationType) {
        return R.ok(incomeStatisticsService.getInfo(stationId, roleType, stationType));
    }

    @Operation(summary = "获取站长站点订单量分析", description = "负责人:张良峰 获取站长站点订单量分析")
    @GetMapping("/order/analysis")
    public R<List<OrderTradeAnalysisVo>> orderAnalysis(@RequestParam(value = "stationId") Long stationId,
                                                       @RequestParam(value = "roleType") String roleType,
                                                       @RequestParam(value = "busyType", required = false) String busyType,
                                                       @RequestParam(value = "startDate") String startDate,
                                                       @RequestParam(value = "endDate") String endDate) {
        return R.ok(incomeStatisticsService.orderTradeAnalysis(stationId, roleType, busyType, startDate, endDate));
    }

    @Operation(summary = "获取站长站点热门内容", description = "负责人:张良峰 获取站长站点热门内容")
    @GetMapping("/goods/analysis")
    public R<List<SohuShopGoodsStatVo>> goodsAnalysis(@RequestParam(value = "stationId") Long stationId,
                                                      @RequestParam(value = "stationType") Integer stationType,
                                                      @RequestParam(value = "startDate") String startDate,
                                                      @RequestParam(value = "endDate") String endDate) {

        return R.ok(incomeStatisticsService.goodsAnalysis(stationId, stationType, startDate, endDate));
    }

    /**
     * 获取总收益概览
     *
     * @return
     */
    @Operation(summary = "获取总收益概览", description = "负责人:雷博 获取总收益概览")
    @GetMapping("/overview")
    public R<SohuUserIncomeOverviewVo> getIncomeOverview(@RequestParam(value = "stationId") Long stationId,
                                                         @RequestParam(value = "stationType") Integer stationType,
                                                         @RequestParam(value = "startDate") Date startDate,
                                                         @RequestParam(value = "endDate") Date endDate) {
        return R.ok(incomeStatisticsService.getIncomeOverview(stationId, stationType, startDate, endDate));
    }

    /**
     * 获取收益TOP
     *
     * @return
     */
    @Operation(summary = "获取收益TOP", description = "负责人:雷博 获取收益TOP")
    @GetMapping("/top")
    public R<SohuUserIncomeTopVo> getIncomeTop(@RequestParam(value = "stationId") Long stationId,
                                               @RequestParam(value = "stationType") Integer stationType,
                                               @RequestParam(value = "startDate") Date startDate,
                                               @RequestParam(value = "endDate") Date endDate) {
        return R.ok(incomeStatisticsInfoService.getIncomeTop(stationId, stationType, startDate, endDate));
    }

}
