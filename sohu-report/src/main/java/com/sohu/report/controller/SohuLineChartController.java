package com.sohu.report.controller;

import com.sohu.common.core.domain.R;
import com.sohu.common.core.web.controller.BaseController;
import com.sohu.report.api.bo.SohuBaseDateBo;
import com.sohu.report.api.bo.SohuInviteReportBo;
import com.sohu.report.api.bo.SohuStationReportBo;
import com.sohu.report.api.vo.SohuLineChartVo;
import com.sohu.report.api.vo.SohuLineInviteVo;
import com.sohu.report.service.ISohuLineChartService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 折线统计
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/line/chart")
public class SohuLineChartController extends BaseController {

    private final ISohuLineChartService sohuLineChartService;

    @Operation(summary = "站长总体收益折线图", description = "负责人:柯真 统计站长总体收益，x轴是时间，y轴是收益额")
    @GetMapping("/station/all")
    public R<List<SohuLineChartVo>> stationAllStat(SohuStationReportBo bo) {
        return R.ok(sohuLineChartService.stationAllLineStat(bo));
    }

    @Operation(summary = "站长拉新收益折线图", description = "负责人:柯真 统计站长拉新收益，x轴是时间，y轴是收益额")
    @GetMapping("/station/invite")
    public R<List<SohuLineChartVo>> stationInviteStat(SohuStationReportBo bo) {
        return R.ok(sohuLineChartService.stationInviteLineStat(bo));
    }

    @Operation(summary = "服务商拉新概览折线图", description = "负责人:柯真")
    @GetMapping("/station/agentInviteBoard")
    public R<List<SohuLineInviteVo>> agentStationInviteBoard(SohuInviteReportBo bo) {
        return R.ok(sohuLineChartService.agentStationInviteBoard(bo));
    }

}
