package com.sohu.report.dubbo;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.admin.api.vo.SohuAgentUserVo;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.enums.SohuIndependentObject;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.enums.InviteEnum;
import com.sohu.report.api.RemoteIncomeStatisticsService;
import com.sohu.report.api.bo.*;
import com.sohu.report.api.vo.*;
import com.sohu.report.service.*;
import com.sohu.report.util.DateUtils;
import com.sohu.report.util.SohuReportConvertUtil;
import com.sohu.report.vo.GroupedIncomeVO;
import com.sohu.report.vo.SiteKey;
import com.sohu.report.vo.UserIncomeVO;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: leibo
 * @Date: 2025/5/30 09:03
 **/
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteIncomeStatisticsServiceImpl implements RemoteIncomeStatisticsService {

    private final ISohuUserIncomeStatisticsService userIncomeStatisticsService;
    private final ISohuUserIncomeStatisticsDayService userIncomeStatisticsDayService;
    private final ISohuUserIncomeStatisticsWeekService userIncomeStatisticsWeekService;
    private final ISohuUserIncomeStatisticsMonthService userIncomeStatisticsMonthService;
    private final SohuReportConvertUtil sohuReportConvertUtil;
    private final ISohuUserIncomeStatisticsInfoService userIncomeStatisticsInfoService;
    @DubboReference
    private RemoteUserService userService;
    @DubboReference
    private RemoteAdminService remoteAdminService;

    @Override
    public void handleStatistics() {
        // 查询总数据
        List<UserIncomeVO> userIncomeList = userIncomeStatisticsService.queryUserIncomeByTime(null, null);
        if (CollectionUtils.isEmpty(userIncomeList)) {
            return;
        }
        List<GroupedIncomeVO> groupIncomeList = sohuReportConvertUtil.groupIncomeData(userIncomeList, Boolean.FALSE);
        // 处理汇总数据进总表
        for (GroupedIncomeVO groupedIncomeVO : groupIncomeList) {
            SohuUserIncomeStatisticsBo incomeStatisticsBo = new SohuUserIncomeStatisticsBo();
            incomeStatisticsBo.setUserId(groupedIncomeVO.getUserId());
            incomeStatisticsBo.setRoleType(groupedIncomeVO.getIndependentObject());
            incomeStatisticsBo.setStationId(groupedIncomeVO.getSiteId());
            incomeStatisticsBo.setReceiveIncome(groupedIncomeVO.getEntryAmount());
            incomeStatisticsBo.setWaitIncome(groupedIncomeVO.getWaitEntryAmount());
            // 查询已提现总额
            BigDecimal userWithdrawal = userService.getUserWithdrawal(groupedIncomeVO.getUserId(), groupedIncomeVO.getSiteType(), groupedIncomeVO.getSiteId(), groupedIncomeVO.getIndependentObject(), null, null);
            incomeStatisticsBo.setAlreadyWithdrawal(userWithdrawal);
            // 计算待提现总额
            incomeStatisticsBo.setWaitWithdrawal(CalUtils.sub(groupedIncomeVO.getEntryAmount(), userWithdrawal));
            SohuUserIncomeStatisticsVo incomeStatisticsVo = userIncomeStatisticsService.queryByUserIdAndRoleTypeAndStationId(groupedIncomeVO.getUserId(), groupedIncomeVO.getIndependentObject(), groupedIncomeVO.getSiteId());
            if (Objects.isNull(incomeStatisticsVo)) {
                // 新增
                userIncomeStatisticsService.insertByBo(incomeStatisticsBo);
            } else {
                // 编辑
                incomeStatisticsBo.setId(incomeStatisticsVo.getId());
                userIncomeStatisticsService.updateByBo(incomeStatisticsBo);
            }
        }
        // 按天查询收益收据
        Date now = new Date();
        Date startOfDay = DateUtils.getStartOfDay(now);
        Date yesterdayStartOfDay = DateUtils.getYesterdayStartOfDay(now);
        this.handleForDay(yesterdayStartOfDay, startOfDay);
        // 按周查询收益数据
        Date startOfWeek = DateUtils.getStartOfWeekOrPrevious(now);
        Date endOfWeek = DateUtils.getEndOfWeekOrPrevious(now);
        this.handleForWeek(startOfWeek, endOfWeek);
        // 按月查询收益数据
        Date startOfMonth = DateUtils.getStartOfMonthOrPrevious(now);
        Date endOfMonth = DateUtils.getEndOfMonthOrPrevious(now);
        this.handleForMonth(startOfMonth, endOfMonth);
    }

    @Override
    public void handleDayStatistics() {
        // 按天查询收益收据
        Date now = new Date();
        Date startOfDay = DateUtils.getStartOfDay(now);
        this.handleForDay(startOfDay, now);
    }

    @Override
    public TableDataInfo<SohuUserIncomeStatisticsInfoVo> queryIncomePageList(SohuUserIncomeStatisticsInfoBo bo, PageQuery pageQuery) {
        return userIncomeStatisticsInfoService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<SohuUserIncomeStatisticsInfoVo> queryIncomeList(SohuUserIncomeStatisticsInfoBo bo) {
        return userIncomeStatisticsInfoService.queryList(bo);
    }

    @Override
    public SohuUserIncomeStatisticsInfoVo queryIncomeById(Long id) {
        return userIncomeStatisticsInfoService.queryIncomeById(id);
    }

    @Override
    public List<SohuUserIncomeStatisticsInfoVo> incomeNewest() {
        return userIncomeStatisticsInfoService.incomeNewest();
    }

    /**
     * 按天处理
     *
     * @param startTime
     * @param endTime
     */
    private void handleForDay(Date startTime, Date endTime) {
        String day = DateUtils.formatToDate(startTime);
        List<UserIncomeVO> userIncomeDayList = userIncomeStatisticsService.queryUserIncomeByTime(startTime, endTime);
        if (CollectionUtils.isNotEmpty(userIncomeDayList)) {
            List<GroupedIncomeVO> groupIncomeDayList = sohuReportConvertUtil.groupIncomeData(userIncomeDayList, Boolean.TRUE);
            // 获取该时间段内的所有邀请数据
            List<SohuAgentUserVo> rawAgentUserVoList = remoteAdminService.queryInviteList();
            // 过滤出当前日期范围内的邀请数据
            List<SohuAgentUserVo> agentUserVoListForDay = rawAgentUserVoList.stream()
                    .filter(vo -> {
                        Date inviteCreateTime = vo.getCreateTime();
                        return inviteCreateTime != null && inviteCreateTime.compareTo(startTime) >= 0 && inviteCreateTime.compareTo(endTime) < 0;
                    })
                    .collect(Collectors.toList());

            // 聚合邀请数据，得到每个agentId的邀请总数和绑定数
            Map<Long, List<SohuAgentUserVo>> agentUserMap = agentUserVoListForDay.stream()
                    .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentId));

            Map<Long, List<SohuAgentUserVo>> agentUserSuccessMap = agentUserVoListForDay.stream()
                    .filter(sohuAgentUserVo -> InviteEnum.SUCCESS.getCode().equals(sohuAgentUserVo.getState()))
                    .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentId));

            // 合并处理所有涉及到的用户ID
            Set<Long> allUserIdsToProcess = new HashSet<>();
            groupIncomeDayList.forEach(vo -> allUserIdsToProcess.add(vo.getUserId()));
            allUserIdsToProcess.addAll(agentUserMap.keySet());

            for (Long userId : allUserIdsToProcess) {
                // 获取该用户的收益统计数据 (如果存在)
                List<GroupedIncomeVO> userSpecificIncomeList = groupIncomeDayList.stream()
                        .filter(vo -> vo.getUserId().equals(userId))
                        .collect(Collectors.toList());

                // 获取该用户的邀请人数和绑定人数
                int inviteNum = agentUserMap.getOrDefault(userId, Collections.emptyList()).size();
                int inviteBindNum = agentUserSuccessMap.getOrDefault(userId, Collections.emptyList()).size();

                // 遍历处理每个用户-交易类型组合的收益数据
                if (CollectionUtils.isEmpty(userSpecificIncomeList)) {
                    // 如果只有邀请数据，没有收益数据，也要插入一条记录
                    SohuUserIncomeStatisticsDayBo incomeStatisticsDayBo = new SohuUserIncomeStatisticsDayBo();
                    incomeStatisticsDayBo.setUserId(userId);
                    incomeStatisticsDayBo.setDayDate(day);
                    incomeStatisticsDayBo.setInviteNum((long) inviteNum);
                    incomeStatisticsDayBo.setInviteBindNum((long) inviteBindNum);
                    incomeStatisticsDayBo.setStationId(0L);
                    incomeStatisticsDayBo.setRoleType(RoleCodeEnum.Agent.getCode());
                    incomeStatisticsDayBo.setBusyType(BusyType.Invite.name());
                    incomeStatisticsDayBo.setTotalIncome(BigDecimal.ZERO);
                    incomeStatisticsDayBo.setInviteIncome(BigDecimal.ZERO);
                    incomeStatisticsDayBo.setTradeAmount(BigDecimal.ZERO);
                    incomeStatisticsDayBo.setOrderNum(0L);

                    SohuUserIncomeStatisticsDayVo incomeStatisticsDayVo = userIncomeStatisticsDayService.queryByParam(
                            userId, incomeStatisticsDayBo.getRoleType(), incomeStatisticsDayBo.getStationId(), incomeStatisticsDayBo.getBusyType(), day);
                    if (Objects.isNull(incomeStatisticsDayVo)) {
                        userIncomeStatisticsDayService.insertByBo(incomeStatisticsDayBo);
                        log.debug("新增日统计记录 (仅邀请数据) for user: {} on {}. InviteNum: {}, InviteBindNum: {}", userId, day, inviteNum, inviteBindNum);
                    } else {
                        incomeStatisticsDayBo.setId(incomeStatisticsDayVo.getId());
                        userIncomeStatisticsDayService.updateByBo(incomeStatisticsDayBo);
                        log.debug("更新日统计记录 (仅邀请数据) for user: {} on {}. InviteNum: {}, InviteBindNum: {}", userId, day, inviteNum, inviteBindNum);
                    }

                } else {
                    // 处理按天的数据进天收益表
                    for (GroupedIncomeVO groupedIncomeVO : groupIncomeDayList) {
                        SohuUserIncomeStatisticsDayBo incomeStatisticsDayBo = new SohuUserIncomeStatisticsDayBo();
                        incomeStatisticsDayBo.setUserId(groupedIncomeVO.getUserId());
                        incomeStatisticsDayBo.setRoleType(groupedIncomeVO.getIndependentObject());
                        incomeStatisticsDayBo.setStationId(groupedIncomeVO.getSiteId());
                        incomeStatisticsDayBo.setBusyType(groupedIncomeVO.getTradeType());
                        incomeStatisticsDayBo.setTotalIncome(groupedIncomeVO.getEntryAmount());
                        incomeStatisticsDayBo.setDayDate(day);
                        // 填充邀请人数和绑定人数
                        incomeStatisticsDayBo.setInviteNum((long) inviteNum);
                        incomeStatisticsDayBo.setInviteBindNum((long) inviteBindNum);
                        // 拉新收益
                        incomeStatisticsDayBo.setInviteIncome(userService.getUserInviteIncome(groupedIncomeVO.getUserId(), groupedIncomeVO.getSiteType(), groupedIncomeVO.getSiteId(), groupedIncomeVO.getIndependentObject(), startTime, endTime));
                        // 交易金额  TODO
                        incomeStatisticsDayBo.setTradeAmount(BigDecimal.ZERO);
                        // 订单量  TODO
                        incomeStatisticsDayBo.setOrderNum(0L);
                        SohuUserIncomeStatisticsDayVo incomeStatisticsDayVo = userIncomeStatisticsDayService.queryByParam(groupedIncomeVO.getUserId(), groupedIncomeVO.getIndependentObject(), groupedIncomeVO.getSiteId(), groupedIncomeVO.getTradeType(), day);
                        if (Objects.isNull(incomeStatisticsDayVo)) {
                            // 新增
                            userIncomeStatisticsDayService.insertByBo(incomeStatisticsDayBo);
                        } else {
                            // 编辑
                            incomeStatisticsDayBo.setId(incomeStatisticsDayVo.getId());
                            userIncomeStatisticsDayService.updateByBo(incomeStatisticsDayBo);
                        }
                    }
                }
            }
        }
    }

    /**
     * 按周处理
     *
     * @param startOfWeek
     * @param endOfWeek
     */
    private void handleForWeek(Date startOfWeek, Date endOfWeek) {
        String week = DateUtils.formatToYearWeek(startOfWeek);
        List<UserIncomeVO> userIncomeWeekList = userIncomeStatisticsService.queryUserIncomeByTime(startOfWeek, endOfWeek);
        if (CollectionUtils.isNotEmpty(userIncomeWeekList)) {
            List<GroupedIncomeVO> groupIncomeWeekList = sohuReportConvertUtil.groupIncomeData(userIncomeWeekList, Boolean.TRUE);

            List<SohuAgentUserVo> rawAgentUserVoList = remoteAdminService.queryInviteList();
            List<SohuAgentUserVo> agentUserVoListForWeek = rawAgentUserVoList.stream()
                    .filter(vo -> {
                        Date inviteCreateTime = vo.getCreateTime();
                        return inviteCreateTime != null && inviteCreateTime.compareTo(startOfWeek) >= 0 && inviteCreateTime.compareTo(endOfWeek) < 0;
                    })
                    .collect(Collectors.toList());

            Map<Long, List<SohuAgentUserVo>> agentUserMap = agentUserVoListForWeek.stream()
                    .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentId));
            Map<Long, List<SohuAgentUserVo>> agentUserSuccessMap = agentUserVoListForWeek.stream()
                    .filter(sohuAgentUserVo -> InviteEnum.SUCCESS.getCode().equals(sohuAgentUserVo.getState()))
                    .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentId));

            Set<Long> allUserIdsToProcess = new HashSet<>();
            groupIncomeWeekList.forEach(vo -> allUserIdsToProcess.add(vo.getUserId()));
            allUserIdsToProcess.addAll(agentUserMap.keySet());

            for (Long userId : allUserIdsToProcess) {
                List<GroupedIncomeVO> userSpecificIncomeList = groupIncomeWeekList.stream()
                        .filter(vo -> vo.getUserId().equals(userId))
                        .collect(Collectors.toList());

                int inviteNum = agentUserMap.getOrDefault(userId, Collections.emptyList()).size();
                int inviteBindNum = agentUserSuccessMap.getOrDefault(userId, Collections.emptyList()).size();

                if (CollectionUtils.isEmpty(userSpecificIncomeList)) {
                    SohuUserIncomeStatisticsWeekBo incomeStatisticsWeekBo = new SohuUserIncomeStatisticsWeekBo();
                    incomeStatisticsWeekBo.setUserId(userId);
                    incomeStatisticsWeekBo.setWeekDate(week);
                    incomeStatisticsWeekBo.setInviteNum((long) inviteNum);
                    incomeStatisticsWeekBo.setInviteBindNum((long) inviteBindNum);
                    // 填充其他默认值
                    incomeStatisticsWeekBo.setStationId(0L);
                    incomeStatisticsWeekBo.setRoleType(RoleCodeEnum.Agent.getCode());
                    incomeStatisticsWeekBo.setBusyType(BusyType.Invite.name());
                    incomeStatisticsWeekBo.setTotalIncome(BigDecimal.ZERO);
                    incomeStatisticsWeekBo.setInviteIncome(BigDecimal.ZERO);
                    incomeStatisticsWeekBo.setTradeAmount(BigDecimal.ZERO);
                    incomeStatisticsWeekBo.setOrderNum(0L);

                    SohuUserIncomeStatisticsWeekVo incomeStatisticsWeekVo = userIncomeStatisticsWeekService.queryByParam(
                            userId, incomeStatisticsWeekBo.getRoleType(), incomeStatisticsWeekBo.getStationId(), incomeStatisticsWeekBo.getBusyType(), week);
                    if (Objects.isNull(incomeStatisticsWeekVo)) {
                        userIncomeStatisticsWeekService.insertByBo(incomeStatisticsWeekBo);
                        log.debug("新增周统计记录 (仅邀请数据) for user: {} on week {}. InviteNum: {}, InviteBindNum: {}", userId, week, inviteNum, inviteBindNum);
                    } else {
                        incomeStatisticsWeekBo.setId(incomeStatisticsWeekVo.getId());
                        userIncomeStatisticsWeekService.updateByBo(incomeStatisticsWeekBo);
                        log.debug("更新周统计记录 (仅邀请数据) for user: {} on week {}. InviteNum: {}, InviteBindNum: {}", userId, week, inviteNum, inviteBindNum);
                    }
                } else {
                    // 处理按周的数据进周数据表
                    for (GroupedIncomeVO groupedIncomeVO : groupIncomeWeekList) {
                        SohuUserIncomeStatisticsWeekBo incomeStatisticsWeekBo = new SohuUserIncomeStatisticsWeekBo();
                        incomeStatisticsWeekBo.setUserId(groupedIncomeVO.getUserId());
                        incomeStatisticsWeekBo.setRoleType(groupedIncomeVO.getIndependentObject());
                        incomeStatisticsWeekBo.setStationId(groupedIncomeVO.getSiteId());
                        incomeStatisticsWeekBo.setBusyType(groupedIncomeVO.getTradeType());
                        incomeStatisticsWeekBo.setTotalIncome(groupedIncomeVO.getEntryAmount());
                        incomeStatisticsWeekBo.setWeekDate(week);
                        // 填充邀请人数和绑定人数
                        incomeStatisticsWeekBo.setInviteNum((long) inviteNum);
                        incomeStatisticsWeekBo.setInviteBindNum((long) inviteBindNum);
                        // 拉新收益
                        incomeStatisticsWeekBo.setInviteIncome(userService.getUserInviteIncome(groupedIncomeVO.getUserId(), groupedIncomeVO.getSiteType(), groupedIncomeVO.getSiteId(), groupedIncomeVO.getIndependentObject(), startOfWeek, endOfWeek));
                        // 交易金额  TODO
                        incomeStatisticsWeekBo.setTradeAmount(BigDecimal.ZERO);
                        // 订单量  TODO
                        incomeStatisticsWeekBo.setOrderNum(0L);
                        SohuUserIncomeStatisticsWeekVo incomeStatisticsWeekVo = userIncomeStatisticsWeekService.queryByParam(groupedIncomeVO.getUserId(), groupedIncomeVO.getIndependentObject(), groupedIncomeVO.getSiteId(), groupedIncomeVO.getTradeType(), week);
                        if (Objects.isNull(incomeStatisticsWeekVo)) {
                            // 新增
                            userIncomeStatisticsWeekService.insertByBo(incomeStatisticsWeekBo);
                        } else {
                            // 编辑
                            incomeStatisticsWeekBo.setId(incomeStatisticsWeekVo.getId());
                            userIncomeStatisticsWeekService.updateByBo(incomeStatisticsWeekBo);
                        }
                    }
                }
            }
        }
    }

    /**
     * 按月处理
     *
     * @param startOfMonth
     * @param endOfMonth
     */
    private void handleForMonth(Date startOfMonth, Date endOfMonth) {
        String month = DateUtils.formatToYearMonth(startOfMonth);
        List<UserIncomeVO> userIncomeMonthList = userIncomeStatisticsService.queryUserIncomeByTime(startOfMonth, endOfMonth);
        if (CollectionUtils.isNotEmpty(userIncomeMonthList)) {
            List<GroupedIncomeVO> groupIncomeWeekList = sohuReportConvertUtil.groupIncomeData(userIncomeMonthList, Boolean.TRUE);
            List<GroupedIncomeVO> groupIncomeMonthList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(userIncomeMonthList)) {
                groupIncomeMonthList = sohuReportConvertUtil.groupIncomeData(userIncomeMonthList, Boolean.TRUE);
            }

            // 重新获取原始邀请数据，计算邀请人数和绑定人数
            List<SohuAgentUserVo> rawAgentUserVoList = remoteAdminService.queryInviteList();
            List<SohuAgentUserVo> agentUserVoListForMonth = rawAgentUserVoList.stream()
                    .filter(vo -> {
                        Date inviteCreateTime = vo.getCreateTime();
                        return inviteCreateTime != null && inviteCreateTime.compareTo(startOfMonth) >= 0 && inviteCreateTime.compareTo(endOfMonth) < 0;
                    })
                    .collect(Collectors.toList());

            Map<Long, List<SohuAgentUserVo>> agentUserMap = agentUserVoListForMonth.stream()
                    .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentId));
            Map<Long, List<SohuAgentUserVo>> agentUserSuccessMap = agentUserVoListForMonth.stream()
                    .filter(sohuAgentUserVo -> InviteEnum.SUCCESS.getCode().equals(sohuAgentUserVo.getState()))
                    .collect(Collectors.groupingBy(SohuAgentUserVo::getAgentId));

            Set<Long> allUserIdsToProcess = new HashSet<>();
            groupIncomeMonthList.forEach(vo -> allUserIdsToProcess.add(vo.getUserId()));
            allUserIdsToProcess.addAll(agentUserMap.keySet());

            for (Long userId : allUserIdsToProcess) {
                List<GroupedIncomeVO> userSpecificIncomeList = groupIncomeMonthList.stream()
                        .filter(vo -> vo.getUserId().equals(userId))
                        .collect(Collectors.toList());

                int inviteNum = agentUserMap.getOrDefault(userId, Collections.emptyList()).size();
                int inviteBindNum = agentUserSuccessMap.getOrDefault(userId, Collections.emptyList()).size();

                if (CollectionUtils.isEmpty(userSpecificIncomeList)) {
                    SohuUserIncomeStatisticsMonthBo incomeStatisticsMonthBo = new SohuUserIncomeStatisticsMonthBo();
                    incomeStatisticsMonthBo.setUserId(userId);
                    incomeStatisticsMonthBo.setMonthDate(month);
                    incomeStatisticsMonthBo.setInviteNum((long) inviteNum);
                    incomeStatisticsMonthBo.setInviteBindNum((long) inviteBindNum);
                    // 填充其他默认值
                    incomeStatisticsMonthBo.setStationId(0L);
                    incomeStatisticsMonthBo.setRoleType(RoleCodeEnum.Agent.getCode());
                    incomeStatisticsMonthBo.setBusyType(BusyType.Invite.name());
                    incomeStatisticsMonthBo.setTotalIncome(BigDecimal.ZERO);
                    incomeStatisticsMonthBo.setInviteIncome(BigDecimal.ZERO);
                    incomeStatisticsMonthBo.setTradeAmount(BigDecimal.ZERO);
                    incomeStatisticsMonthBo.setOrderNum(0L);

                    SohuUserIncomeStatisticsMonthVo incomeStatisticsMonthVo = userIncomeStatisticsMonthService.queryByParam(
                            userId, incomeStatisticsMonthBo.getRoleType(), incomeStatisticsMonthBo.getStationId(), incomeStatisticsMonthBo.getBusyType(), month);
                    if (Objects.isNull(incomeStatisticsMonthVo)) {
                        userIncomeStatisticsMonthService.insertByBo(incomeStatisticsMonthBo);
                        log.debug("新增月统计记录 (仅邀请数据) for user: {} on month {}. InviteNum: {}, InviteBindNum: {}", userId, month, inviteNum, inviteBindNum);
                    } else {
                        incomeStatisticsMonthBo.setId(incomeStatisticsMonthVo.getId());
                        userIncomeStatisticsMonthService.updateByBo(incomeStatisticsMonthBo);
                        log.debug("更新月统计记录 (仅邀请数据) for user: {} on month {}. InviteNum: {}, InviteBindNum: {}", userId, month, inviteNum, inviteBindNum);
                    }
                } else {
                    // 处理按月的数据进月数据表
                    for (GroupedIncomeVO groupedIncomeVO : groupIncomeWeekList) {
                        SohuUserIncomeStatisticsMonthBo incomeStatisticsMonthBo = new SohuUserIncomeStatisticsMonthBo();
                        incomeStatisticsMonthBo.setUserId(groupedIncomeVO.getUserId());
                        incomeStatisticsMonthBo.setRoleType(groupedIncomeVO.getIndependentObject());
                        incomeStatisticsMonthBo.setStationId(groupedIncomeVO.getSiteId());
                        incomeStatisticsMonthBo.setBusyType(groupedIncomeVO.getTradeType());
                        incomeStatisticsMonthBo.setTotalIncome(groupedIncomeVO.getEntryAmount());
                        incomeStatisticsMonthBo.setMonthDate(month);
                        // 拉新收益
                        incomeStatisticsMonthBo.setInviteIncome(userService.getUserInviteIncome(groupedIncomeVO.getUserId(), groupedIncomeVO.getSiteType(), groupedIncomeVO.getSiteId(), groupedIncomeVO.getIndependentObject(), startOfMonth, endOfMonth));
                        // 交易金额  TODO
                        incomeStatisticsMonthBo.setTradeAmount(BigDecimal.ZERO);
                        // 订单量  TODO
                        incomeStatisticsMonthBo.setOrderNum(0L);
                        SohuUserIncomeStatisticsMonthVo incomeStatisticsMonthVo = userIncomeStatisticsMonthService.queryByParam(groupedIncomeVO.getUserId(), groupedIncomeVO.getIndependentObject(), groupedIncomeVO.getSiteId(), groupedIncomeVO.getTradeType(), month);
                        if (Objects.isNull(incomeStatisticsMonthVo)) {
                            // 新增
                            userIncomeStatisticsMonthService.insertByBo(incomeStatisticsMonthBo);
                        } else {
                            // 编辑
                            incomeStatisticsMonthBo.setId(incomeStatisticsMonthVo.getId());
                            userIncomeStatisticsMonthService.updateByBo(incomeStatisticsMonthBo);
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<SohuUserIncomeStatisticsInfoVo> queryUserIncomeByTime(Long userId, Date startTime, Date endTime) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return List.of();
        }

        return userIncomeStatisticsInfoService.queryUserIncomeByTime(userId, startTime, endTime);
    }
}