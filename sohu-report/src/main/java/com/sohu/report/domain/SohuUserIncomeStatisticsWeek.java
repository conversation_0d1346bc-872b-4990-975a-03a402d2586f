package com.sohu.report.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

/**
 * 用户收益周统计对象 sohu_user_income_statistics_week
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_user_income_statistics_week")
public class SohuUserIncomeStatisticsWeek extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 站长ID
     */
    private Long userId;
    /**
     * 角色类型
     */
    private String roleType;
    /**
     * 行业ID或城市站点ID
     */
    private Long stationId;
    /**
     * 业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧
     */
    private String busyType;
    /**
     * 拉新用户角色  暂定
     */
    private String inviteRole;
    /**
     * 总收益
     */
    private BigDecimal totalIncome;
    /**
     * 拉新收益
     */
    private BigDecimal inviteIncome;
    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;
    /**
     * 订单量
     */
    private Long orderNum;

    /**
     * 周数
     */
    private String weekDate;
    /**
     * 邀请人数
     */
    private Long inviteNum;
    /**
     * 邀请绑定人数
     */
    private Long inviteBindNum;

}
