package com.sohu.report.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户留存统计对象 sohu_user_retention_stat
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sohu_user_retention_stat")
public class SohuUserRetentionStat extends SohuEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 服务商用户id/站长id
     */
    private Long userId;
    /**
     * 邀请日期（用户首次邀请日期，队列基准日）
     */
    private Date inviteDate;
    /**
     * 留存天数（例如：1, 2, 7, 14, 30）
     */
    private Long retentionDay;
    /**
     * 在该留存天数仍活跃的客户数量
     */
    private Long retainedCount;

}
