<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.report.mapper.SohuUserIncomeStatisticsMapper">

    <resultMap type="com.sohu.report.domain.SohuUserIncomeStatistics" id="SohuUserIncomeStatisticsResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="roleType" column="role_type"/>
        <result property="stationId" column="station_id"/>
        <result property="businessType" column="business_type"/>
        <result property="receiveIncome" column="receive_income"/>
        <result property="waitIncome" column="wait_income"/>
        <result property="waitWithdrawal" column="wait_withdrawal"/>
        <result property="alreadyWithdrawal" column="already_withdrawal"/>
        <result property="todayIncome" column="today_income"/>
        <result property="yesterdayIncome" column="yesterday_income"/>
        <result property="yesterdayTradeAmount" column="yesterday_trade_amount"/>
        <result property="yesterdayOrderNum" column="yesterday_order_num"/>
        <result property="dayDate" column="day_date"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectTopProducts" resultType="com.sohu.report.api.vo.SohuShopGoodsStatVo">
        SELECT
        soi.product_id AS goodsId,
        soi.product_name AS goodsName,
        SUM(soi.pay_num) AS goodsSales
        FROM
        sohu_shop_order_info soi
        INNER JOIN
        sohu_shop_order so ON soi.mer_order_no = so.order_no
        <if test="productCategoryIds != null and productCategoryIds.size() > 0">
            INNER JOIN
            sohu_product p ON soi.product_id = p.id
        </if>
        WHERE
        so.status NOT IN ('UNPAID', 'CANCEL')
        <if test="startDate != null and endDate != null">
            AND soi.create_time BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="stationType!= null and stationType!=''">
            AND soi.site_type = #{stationType}
        </if>
        <if test="stationId!= null and stationId!=''">
            AND soi.site_id = #{stationId}
        </if>
        <if test="productCategoryIds != null and productCategoryIds.size() > 0">
            AND p.category_id IN
            <foreach item="categoryId" collection="productCategoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        GROUP BY
        soi.product_id, soi.product_name
        ORDER BY
        goodsSales DESC
        LIMIT 10
    </select>


    <select id="queryUserIncomeByTime" resultType="com.sohu.report.vo.UserIncomeVO">
        SELECT
            user_id,
            site_id,
            site_type,
            independent_object,
            independent_status,
            trade_type,
            SUM(independent_price) AS totalAmount
        FROM
            sohu_independent_order
        WHERE
            1 = 1
        <if test="startTime != null and endTime != null">
            AND
            update_time BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY
            user_id,
            site_id,
            site_type,
            independent_object,
            independent_status,
            trade_type
        ORDER BY
            user_id,
            site_id,
            site_type,
            independent_object,
            independent_status,
            trade_type;
    </select>

    <select id="queryIncomeOverview" resultType="com.sohu.report.vo.UserIncomeVO">
        SELECT
            user_id,
            site_id,
            site_type,
            independent_object,
            independent_status,
            trade_type,
            SUM(independent_price) AS totalAmount
        FROM
            sohu_independent_order
        WHERE
            site_id = #{stationId}
          AND
            site_type = #{stationType}
          AND
            user_id = #{userId}
          AND
            update_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY
            user_id,
            site_id,
            site_type,
            independent_object,
            independent_status,
            trade_type
        ORDER BY
            user_id,
            site_id,
            site_type,
            independent_object,
            independent_status,
            trade_type;
    </select>


</mapper>
