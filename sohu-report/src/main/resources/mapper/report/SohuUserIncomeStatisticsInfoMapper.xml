<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.report.mapper.SohuUserIncomeStatisticsInfoMapper">

    <resultMap type="com.sohu.report.domain.SohuUserIncomeStatisticsInfo" id="SohuUserIncomeStatisticsInfoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="roleType" column="role_type"/>
        <result property="stationId" column="station_id"/>
        <result property="busyType" column="busy_type"/>
        <result property="busyInfoName" column="busy_info_name"/>
        <result property="incomeType" column="income_type"/>
        <result property="income" column="income"/>
        <result property="consumerUserId" column="consumer_user_id"/>
        <result property="consumerChannelId" column="consumer_channel_id"/>
        <result property="consumerChannelName" column="consumer_channel_name"/>
        <result property="orderAmount" column="order_amount"/>
        <result property="orderNo" column="order_no"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="orderTime" column="order_time"/>
        <result property="incomeTime" column="income_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="getUserTop" resultType="com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo">
        SELECT
            consumer_user_id,
            SUM(income) AS income
        FROM
            `sohu_user_income_statistics_info`
        WHERE
            user_id = #{userId}
        AND
            station_id = #{stationId}
        AND
            station_type = #{stationType}
        AND
            income_time BETWEEN #{startDate} AND #{endDate}

        GROUP BY  consumer_user_id
        ORDER BY income DESC
        LIMIT 10
    </select>

    <select id="getBusyTop" resultType="com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo">
        SELECT
            busy_type,
            busy_info_name,
            SUM(income) AS income
        FROM
            `sohu_user_income_statistics_info`
        WHERE
            user_id = #{userId}
          AND
            station_id = #{stationId}
          AND
            station_type = #{stationType}
          AND
            income_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY  busy_type
        ORDER BY income DESC
        LIMIT 10
    </select>

    <select id="getChannelTop" resultType="com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo">
        SELECT
            consumer_channel_id,
            consumer_channel_name,
            SUM(income) AS income
        FROM
            `sohu_user_income_statistics_info`
        WHERE
            user_id = #{userId}
          AND
            station_id = #{stationId}
          AND
            station_type = #{stationType}
          AND
            income_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY  busy_type
        ORDER BY income DESC
        LIMIT 10
    </select>

</mapper>
