<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sohu.report.mapper.SohuUserRetentionStatMapper">

    <resultMap type="com.sohu.report.domain.SohuUserRetentionStat" id="SohuUserRetentionStatResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="inviteDate" column="invite_date"/>
        <result property="retentionDay" column="retention_day"/>
        <result property="retainedCount" column="retained_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
