server:
  port: 9233

# Spring
spring:
  application:
    # 应用名称
    name: sohu-stream-rocketmq
  profiles:
    # 环境配置
    active: @profiles.active@
  cloud:
    stream:
      function:
        # 重点配置 与 binding 名与消费者对应
        definition: delay;normal;


--- # rocketmq 配置
spring:
  cloud:
    stream:
      rocketmq:
        binder:
          # rocketmq 地址
          name-server: rmqnamesrv:9876
        bindings:
          normal-out-0:
            producer:
              # 必须得写
              group: default
          delay-out-0:
            producer:
              group: default
      bindings:
        normal-out-0:
          content-type: application/json
          destination: stream-normal-topic
          group: sohu-normal-group
          binder: rocketmq
        normal-in-0:
          content-type: application/json
          destination: stream-normal-topic
          group: sohu-normal-group
          binder: rocketmq
        delay-out-0:
          content-type: application/json
          destination: stream-delay-topic
          group: sohu-delay-group
          binder: rocketmq
        delay-in-0:
          content-type: application/json
          destination: stream-delay-topic
          group: sohu-delay-group
          binder: rocketmq


--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: @nacos.server@
      discovery:
        # 注册组
        group: @nacos.discovery.group@
        namespace: ${spring.profiles.active}
        username: @nacos.username@
        password: @nacos.password@
      config:
        # 配置组
        group: @nacos.config.group@
        namespace: ${spring.profiles.active}
        username: @nacos.username@
        password: @nacos.password@
  config:
    import:
      - optional:nacos:sohu-common.yml
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml
  datasource:
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.system-master.url}
          username: ${datasource.system-master.username}
          password: ${datasource.system-master.password}
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
      seata: false
      # 严格模式 匹配不到数据源则报错
      strict: true
