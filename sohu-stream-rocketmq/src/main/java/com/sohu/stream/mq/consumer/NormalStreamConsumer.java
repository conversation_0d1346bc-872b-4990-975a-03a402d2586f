package com.sohu.stream.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.sohu.common.core.utils.DateUtils;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.stream.service.IMqMsgLogService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.function.Consumer;

@Slf4j
@Component
public class NormalStreamConsumer {

    @DubboReference
    private RemoteAccountService remoteAccountService;
    @Resource
    private IMqMsgLogService mqMsgLogService;

    @Bean
    Consumer<MqMessaging> normal() {
        return obj -> {
            String msgId = obj.getMsgId();
            String msgKey = obj.getMsgKey();
            String msgText = obj.getMsgText();

            log.info("{},普通消息接收成功：{}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()), JSONUtil.toJsonStr(obj));

            if (mqMsgLogService.hasConsumeMqMsg(msgId)) {
                log.warn("该消息已被消费！");
                return;
            }
            MqKeyEnum mqKey = MqKeyEnum.fromKey(msgKey);
            if (mqKey == null) {
                log.error("消息类型不存在：{}", msgKey);
                return;
            }
            switch (mqKey) {
                case ACCOUNT_BANK_ADD:
                    remoteAccountService.YiMaBankAdd(msgText);
                    break;
                case TWO:
                    break;
            }
            mqMsgLogService.updateConsumeTime(msgId);
        };
    }

}
