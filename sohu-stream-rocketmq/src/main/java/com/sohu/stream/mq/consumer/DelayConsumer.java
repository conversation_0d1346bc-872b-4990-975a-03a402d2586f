package com.sohu.stream.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.busyorder.api.RemoteBusyOrderService;
import com.sohu.busyorder.api.RemoteBusyTaskNoticeService;
import com.sohu.busyorder.api.RemoteBusyTaskSiteService;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.*;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.constant.ProductConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.middle.api.bo.SohuTradeRecordBo;
import com.sohu.middle.api.bo.airec.SohuAirecBehaviorBo;
import com.sohu.middle.api.service.RemoteMiddleBillRecordService;
import com.sohu.middle.api.service.RemoteMiddleMerTradeRecordService;
import com.sohu.middle.api.service.RemoteMiddleSiteService;
import com.sohu.middle.api.service.RemoteMiddleTradeRecordService;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecAnalyseService;
import com.sohu.middle.api.vo.SohuOrderIndependentPriceVo;
import com.sohu.middle.api.vo.YiMaPayConfig;
import com.sohu.open.api.RemoteOpenReceiveLogHandlerService;
import com.sohu.pay.api.*;
import com.sohu.pay.api.bo.DelayConfirmqueryBo;
import com.sohu.pay.api.model.SohuAccountBankModel;
import com.sohu.pay.api.model.SohuBusyTaskPayModel;
import com.sohu.pay.api.model.SohuIndependentOrderModel;
import com.sohu.shopgoods.api.RemoteProductAttrValueService;
import com.sohu.shopgoods.api.RemoteProductService;
import com.sohu.shopgoods.api.model.SohuProductAttrValueModel;
import com.sohu.shopgoods.api.model.SohuProductWindowMcnModel;
import com.sohu.shopgoods.api.model.SohuProductWindowModel;
import com.sohu.shoporder.api.*;
import com.sohu.shoporder.api.domain.SohuOperateReqBo;
import com.sohu.shoporder.api.model.SohuShopMasterOrderModel;
import com.sohu.shoporder.api.model.SohuShopOrderModel;
import com.sohu.shoporder.api.model.SohuShopRefundOrderModel;
import com.sohu.shoporder.api.vo.SohuShopOrderInfoVo;
import com.sohu.stream.domain.SohuIndependentIdBo;
import com.sohu.stream.service.IMqMsgLogService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteDictService;
import com.sohu.system.api.domain.SysDictData;
import com.wangcaio2o.ipossa.sdk.model.ExtendParams;
import com.wangcaio2o.ipossa.sdk.model.SplitInfo;
import com.wangcaio2o.ipossa.sdk.model.SplitList;
import com.wangcaio2o.ipossa.sdk.request.delayconfirm.DelayConfirm;
import com.wangcaio2o.ipossa.sdk.request.delayconfirm.DelayConfirmRequest;
import com.wangcaio2o.ipossa.sdk.response.delayconfirm.DelayConfirmResponse;
import com.wangcaio2o.ipossa.sdk.test.Client;
import io.seata.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sohu.common.core.enums.SohuTradeRecordEnum.Type.Good;

@Slf4j
@Component
public class DelayConsumer {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private IMqMsgLogService mqMsgLogService;

    @DubboReference
    private RemoteShopRefundOrderService remoteRefundOrderService;
    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;
    @DubboReference
    private RemoteShopOrderStatusService remoteShopOrderStatusService;
    @DubboReference
    private RemoteShopOrderInfoService remoteShopOrderInfoService;
    @DubboReference
    private RemoteMasterOrderService remoteMasterOrderService;
    @DubboReference
    private RemoteShopRefundOrderStatusService remoteRefundOrderStatusService;
    @DubboReference
    private RemoteProductService productService;
    @DubboReference
    private RemoteProductAttrValueService productAttrValueService;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteIndependentTemplateService remoteTemplateService;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemotePayService remotePayService;
    @DubboReference
    private RemoteIndependentOrderService remoteIndependentOrderService;
    @DubboReference
    private RemoteBusyOrderService remoteBusyOrderService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteMiddleAirecAnalyseService remoteMiddleAirecAnalyseService;
    @DubboReference
    private RemoteOpenReceiveLogHandlerService remoteOpenReceiveLogHandlerService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemoteMiddleTradeRecordService remoteMiddleTradeRecordService;
    @DubboReference
    private RemoteMiddleMerTradeRecordService remoteMiddleMerTradeRecordService;
    @DubboReference
    private RemoteBusyTaskNoticeService remoteBusyTaskNoticeService;
    @DubboReference
    private RemoteMiddleBillRecordService remoteMiddleBillRecordService;
    @DubboReference
    private RemoteBusyTaskSiteService remoteBusyTaskSiteService;
    @DubboReference
    private RemotePaySettlementService remotePaySettlementService;
    @DubboReference
    private RemoteMerchantBondPayService remoteMerchantBondPayService;
    @DubboReference
    private RemoteMerchantBondRefundService remoteMerchantBondRefundService;


    @Transactional(rollbackFor = Exception.class)
    @Bean
    Consumer<MqMessaging> delay() {
        return obj -> {
            String msgId = obj.getMsgId();
            String msgKey = obj.getMsgKey();
            String msgText = obj.getMsgText();
            if (!StrUtil.equalsAnyIgnoreCase(MqKeyEnum.BUSY_ORDER_REFUND.getKey(), msgKey)) {
                if (mqMsgLogService.hasConsumeMqMsg(msgId)) {
                    log.warn("该消息已被消费！");
                    return;
                }
            }
            log.info("{},延时消息接收成功：{}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()), JSONUtil.toJsonStr(obj));

            MqKeyEnum delayKey = MqKeyEnum.fromKey(msgKey);
            if (delayKey == null) {
                log.error("消息类型不存在：{}", msgKey);
                return;
            }
            switch (delayKey) {
                case TASK_MERCHANT_COUNT:
                    Objects.requireNonNull(msgText, "参数为空");
                    //SohuOperateBo sohuOperateBo = JSONUtil.toBean(msgText, SohuOperateBo.class);
                    //updateBatchMerchants(sohuOperateBo, Boolean.FALSE);
                    SohuOperateReqBo sohuOperateBo = JSONUtil.toBean(msgText, SohuOperateReqBo.class);
                    this.remoteShopOrderService.mqUpdateBatchMerchants(sohuOperateBo, Boolean.FALSE);
                    break;
                case TWO:
                    log.info("two+++++++++ time:{}", System.currentTimeMillis());
                    break;
                case ACCOUNT_BANK_ADD:
                    remoteAccountService.YiMaBankAdd(msgText);
                    break;
                case YI_MA_REFUND_PAY:
                    refundChange(obj);
                    break;
                case CANCEL_SHOP_ORDER:
                    SohuShopMasterOrderModel sohuShopMasterOrderModel = JSONUtil.toBean(msgText, SohuShopMasterOrderModel.class);
                    Objects.requireNonNull(sohuShopMasterOrderModel, "参数为空");
                    cancel(sohuShopMasterOrderModel);
                    break;
                case TASK_DELAY_ORDER_INDEPENDENT:
                    SohuShopOrderModel shopOrderModel = JSONUtil.toBean(msgText, SohuShopOrderModel.class);
                    Objects.requireNonNull(shopOrderModel, "参数为空");
//                    delayConfirm(shopOrderModel);
                    remoteShopOrderService.mqDelayConfirmV2(shopOrderModel);
                    break;
                case BUSY_ORDER_REFUND:
                    refundBusyOrder(msgText);
                    break;
                case BUSY_TASK_SHELF:
                    SohuBusyTaskSiteModel busyTaskSiteModel = JSONUtil.toBean(msgText, SohuBusyTaskSiteModel.class);
                    Objects.requireNonNull(busyTaskSiteModel, MessageUtils.message("WRONG_PARAMS"));
                    busyTaskShelf(busyTaskSiteModel);
                    break;
                case BUSY_TASK_RECEIVE:
                    SohuBusyTaskReceiveModel taskReceiveModel = JSONUtil.toBean(msgText, SohuBusyTaskReceiveModel.class);
                    Objects.requireNonNull(taskReceiveModel, MessageUtils.message("WRONG_PARAMS"));
                    busyTaskReceive(taskReceiveModel);
                    break;
                case BUSY_TASK_PAY_PARTY:
                    SohuBusyOrderPayModel busyOrderPayModel = JSONUtil.toBean(msgText, SohuBusyOrderPayModel.class);
                    Objects.requireNonNull(busyOrderPayModel, MessageUtils.message("WRONG_PARAMS"));
                    busyOrderPay(busyOrderPayModel);
                    break;
                case AIREC_BEHAVIOR_ANALYSE:
                    SohuAirecBehaviorBo behaviorBo = JSONUtil.toBean(msgText, SohuAirecBehaviorBo.class);
                    Objects.requireNonNull(behaviorBo, MessageUtils.message("WRONG_PARAMS"));
                    this.remoteMiddleAirecAnalyseService.behaviorAnalyse(behaviorBo);
                    break;
                case OPEN_RECEIVE_LOG:
                    //处理批量接收外部消息日志记录
                    Long id = Long.valueOf(msgText);
                    remoteOpenReceiveLogHandlerService.consumeLog(id);
                    break;
                case CANCEL_NOVEL_ORDER:
                    SohuTradeRecordBo tradeRecordBo = JSONUtil.toBean(msgText, SohuTradeRecordBo.class);
                    Objects.requireNonNull(tradeRecordBo, "参数为空");
                    remoteMiddleTradeRecordService.cancelNovelOrder(tradeRecordBo);
                    break;
                case YI_MA_WITHDRAWAL:
                    Objects.requireNonNull(msgText, "参数为空");
                    remoteMiddleBillRecordService.sendYmWithdrawal(msgText);
                    break;
                case DELAY_CONFIRMQUERY:
                    DelayConfirmqueryBo delayConfirmqueryBo = JSONUtil.toBean(msgText, DelayConfirmqueryBo.class);
                    remotePaySettlementService.delayConfirmquery(delayConfirmqueryBo);
                    break;
                case DELAY_CONFIRM_PLATFORM:
                    remotePaySettlementService.delayConfirmPlatform(msgText);
                    break;
                case DELAY_CONFIRM_REFUND:
                    remotePaySettlementService.delayConfirmRefund(msgText);
                    break;
                case DELAY_TASK_UPDATE:
                    remotePaySettlementService.delayTaskUpdate(msgText);
                    break;
                case CANCEL_BOND_ORDER:
                    remoteMerchantBondPayService.cancelBondPay(msgText);
                    break;
                case ACCOUNT_UPGRADE:
                    remoteMerchantService.merchantUpgradeHandler(Long.valueOf(msgText));
                    break;
                case REFUND_RESULT_QUERY:
                    remoteMerchantBondRefundService.refundResultQuery(msgText);
                    break;
                case INCOME_ACCOUNT_PROCESS:
                    DelayConfirmqueryBo bo = JSONUtil.toBean(msgText, DelayConfirmqueryBo.class);
                    remotePaySettlementService.waitIncomeProcess(bo.getBusyType(), bo.getBusyCode());
                    break;
                case INCOME_INFO:
                    // 待补充业务信息 TODO
                    break;
                default:
                    log.error("消息类型不存在：{}", msgKey);
                    break;
            }
            mqMsgLogService.updateConsumeTime(msgId);
        };
    }

    /**
     * 结算商单
     *
     * @param busyOrderPayModel
     */
    private void busyOrderPay(SohuBusyOrderPayModel busyOrderPayModel) {
        BigDecimal zeroPrice = new BigDecimal("0.00");
        // 当前接单
        SohuBusyTaskReceiveModel taskReceiveModel = remoteBusyOrderService.queryBusyTaskRece(busyOrderPayModel.getBusyOrderReceiveId());
        // 是否是佣金结算
        if (null != busyOrderPayModel.getIsIndependentAmount() && busyOrderPayModel.getIsIndependentAmount()) {
            log.info("存在佣金结算");
            taskReceiveModel.setIsIndependent(Boolean.TRUE);
            SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(busyOrderPayModel.getBusyOrder());
            taskSiteModel.setIsIndependent(Boolean.TRUE);
            Boolean execute = transactionTemplate.execute(e -> {
                // 子任务
                remoteBusyOrderService.updateByTaskSite(taskSiteModel);
                // 接单
                remoteBusyOrderService.updateByTaskReceive(taskReceiveModel);
                return Boolean.TRUE;
            });
            if (Boolean.FALSE.equals(execute)) {
                throw new ServiceException("任务佣金相关更新失败");
            }
            return;
        }
        // 是否售后单-结算酬金
        if (null != busyOrderPayModel.getIsAfterSalesAmount() && busyOrderPayModel.getIsAfterSalesAmount()) {
            log.info("售后单-结算酬金");
            taskReceiveModel.setIsIndependent(Boolean.TRUE);
            SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(busyOrderPayModel.getBusyOrder());
            taskSiteModel.setIsIndependent(Boolean.TRUE);
            Boolean execute = transactionTemplate.execute(e -> {
                // 子任务
                remoteBusyOrderService.updateByTaskSite(taskSiteModel);
                // 接单
                remoteBusyOrderService.updateByTaskReceive(taskReceiveModel);
                // 售后
                remoteBusyOrderService.updateBusyOrderAfterSalesSuccess(busyOrderPayModel.getOrderNo());

                //退款保证金
                try {
                    remoteBusyOrderService.refundBusyOrderPromise(taskReceiveModel.getTaskNumber());
                } catch (Exception ex) {
                    log.error("售后单-结算酬金，退款保证金失败, e = ", ex);
                }
                return Boolean.TRUE;
            });
            if (Boolean.FALSE.equals(execute)) {
                throw new ServiceException("售后单佣金相关更新失败");
            }
            return;
        }
        // 先判断是否是阶段性付款任务
        List<SohuBusyTaskDeliveryModel> deliveryModelList = remoteBusyOrderService.listBusyTaskReceive(busyOrderPayModel.getBusyOrder());
        // 一次性结清所有金额
        if (null != busyOrderPayModel.getAllAmount() && busyOrderPayModel.getAllAmount()) {
            log.info("一次性结清所有金额");
            Boolean execute = transactionTemplate.execute(e -> {
                SohuBusyTaskDeliveryModel taskDeliveryModel = null;
                Optional<SohuBusyTaskDeliveryModel> modelOptional = deliveryModelList.stream()
                        .filter(model -> Objects.equals(model.getId(), busyOrderPayModel.getBusyTaskDeliveryId()))
                        .findFirst();
                if (modelOptional.isPresent()) {
                    taskDeliveryModel = modelOptional.get();
                    taskDeliveryModel.setState(SohuBusyTaskState.OverSettle.name());
                    taskDeliveryModel.setPayState(PayStatus.Paid.name());
                    taskDeliveryModel.setAmount(busyOrderPayModel.getPayAmount());
                }
                SohuBusyTaskDeliveryModel finalTaskDeliveryModel = taskDeliveryModel;
                // 筛选出所有不是当前支付阶段的数据
                List<SohuBusyTaskDeliveryModel> filteredList = deliveryModelList.stream()
                        .filter(model -> !Objects.equals(model.getId(), busyOrderPayModel.getBusyTaskDeliveryId())
                                && (null == model.getAmount() || model.getAmount().equals(zeroPrice)))
                        .collect(Collectors.toList());
                for (SohuBusyTaskDeliveryModel deliveryModel : filteredList) {
                    deliveryModel.setState(SohuBusyTaskState.OverSettle.name());
                    deliveryModel.setPayState(PayStatus.Paid.name());
                }
                // 当前接单如果都结束了
                taskReceiveModel.setState(SohuBusyTaskState.OverSettle.name());
                SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(busyOrderPayModel.getBusyOrder());
                SohuBusyTaskModel sohuBusyTaskModel = remoteBusyOrderService.queryByTaskNo(taskSiteModel.getMasterTaskNumber());
                taskSiteModel.setState(SohuBusyTaskState.OverSettle.name());
                sohuBusyTaskModel.setState(SohuBusyTaskState.OverSettle.name());
                // 子任务
                remoteBusyOrderService.updateByTaskSite(taskSiteModel);
                // 主任务
                remoteBusyOrderService.updateByTask(sohuBusyTaskModel);
                // 退保证金
                remotePayService.refund(BeanCopyUtils.copy(busyOrderPayModel, SohuBusyTaskPayModel.class));
                // 接单
                remoteBusyOrderService.updateByTaskReceive(taskReceiveModel);
                // 阶段
                remoteBusyOrderService.updateDelivery(finalTaskDeliveryModel);
                log.info("remotePayService.refund-开始退保证金:{}", JSONUtil.toJsonStr(busyOrderPayModel));
                // 退保证金
                remotePayService.refund(BeanCopyUtils.copy(busyOrderPayModel, SohuBusyTaskPayModel.class));
                log.info("remotePayService.refund-已退保证金:{}", JSONUtil.toJsonStr(busyOrderPayModel));
                remoteBusyTaskNoticeService.sendTaskSiteNotice(taskReceiveModel.getTaskNumber(), TaskNoticeEnum.TASK_END,
                        taskReceiveModel.getUserId(), null, null, Boolean.FALSE);
                // todo ? 执行记录
                return Boolean.TRUE;
            });
            if (Boolean.FALSE.equals(execute)) {
                throw new ServiceException("一次性结清任务相关更新失败");
            }
            return;
        }
        // 阶段性
        if (deliveryModelList.size() > 1) {
            log.info("阶段性支付金额");
            SohuBusyTaskDeliveryModel taskDeliveryModel = null;
            Optional<SohuBusyTaskDeliveryModel> modelOptional = deliveryModelList.stream()
                    .filter(model -> Objects.equals(model.getId(), busyOrderPayModel.getBusyTaskDeliveryId()))
                    .findFirst();
            if (modelOptional.isPresent()) {
                taskDeliveryModel = modelOptional.get();
                taskDeliveryModel.setState(SohuBusyTaskState.Pass.name());
                taskDeliveryModel.setPayState(PayStatus.Paid.name());
            }
            SohuBusyTaskDeliveryModel finalTaskDeliveryModel = taskDeliveryModel;
            finalTaskDeliveryModel.setState(SohuBusyTaskState.OverSettle.name());
            finalTaskDeliveryModel.setPayState(PayStatus.Paid.name());
            finalTaskDeliveryModel.setAmount(busyOrderPayModel.getPayAmount());
            // 筛选出所有不是当前支付阶段的数据
            List<SohuBusyTaskDeliveryModel> filteredList = deliveryModelList.stream()
                    .filter(model -> !Objects.equals(model.getId(), busyOrderPayModel.getBusyTaskDeliveryId()))
                    .collect(Collectors.toList());
            // 判断是否所有的阶段都支付了
            boolean isOver = filteredList.stream().allMatch(delivery -> PayStatus.Paid.name().equals(delivery.getPayState())
                    && SohuBusyTaskState.OverSettle.name().equals(delivery.getState()));
            Boolean execute = transactionTemplate.execute(e -> {
                if (isOver) {
                    // 当前接单如果都结束了
                    taskReceiveModel.setState(SohuBusyTaskState.OverSettle.name());
                    SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(busyOrderPayModel.getBusyOrder());
                    SohuBusyTaskModel sohuBusyTaskModel = remoteBusyOrderService.queryByTaskNo(taskSiteModel.getMasterTaskNumber());
                    taskSiteModel.setState(SohuBusyTaskState.OverSettle.name());
                    sohuBusyTaskModel.setState(SohuBusyTaskState.OverSettle.name());
                    // 子任务
                    remoteBusyOrderService.updateByTaskSite(taskSiteModel);
                    // 主任务
                    remoteBusyOrderService.updateByTask(sohuBusyTaskModel);
                    // 退保证金
                    remotePayService.refund(BeanCopyUtils.copy(busyOrderPayModel, SohuBusyTaskPayModel.class));
                    remoteBusyTaskNoticeService.sendTaskSiteNotice(taskReceiveModel.getTaskNumber(), TaskNoticeEnum.TASK_END,
                            taskReceiveModel.getUserId(), null, null, Boolean.FALSE);
                }
                // todo 计算总分销佣金
//                sohuBusyOrderRece.setDistributionAmount(remoteIndependentOrderService.queryAmount(sohuBusyOrderPay.getOrderNo(),
//                        BusyType.BusyOrder.name(), SohuIndependentObject.distribution.getKey(), sohuBusyOrderRece.getSharePerson()));
                // 接单
                remoteBusyOrderService.updateByTaskReceive(taskReceiveModel);
                // 阶段
                remoteBusyOrderService.updateDelivery(finalTaskDeliveryModel);
                // todo ? 执行记录
                return Boolean.TRUE;
            });
            if (Boolean.FALSE.equals(execute)) {
                throw new ServiceException("阶段性任务相关更新失败");
            }
            return;
        }
        SohuBusyTaskDeliveryModel deliveryModel = deliveryModelList.get(0);
        deliveryModel.setState(SohuBusyTaskState.Pass.name());
        deliveryModel.setPayState(PayStatus.Paid.name());
        deliveryModel.setAmount(busyOrderPayModel.getPayAmount());
        Boolean execute = transactionTemplate.execute(e -> {
            taskReceiveModel.setState(SohuBusyTaskState.OverSettle.name());
            SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(busyOrderPayModel.getBusyOrder());
            SohuBusyTaskModel sohuBusyTaskModel = remoteBusyOrderService.queryByTaskNo(taskSiteModel.getMasterTaskNumber());
            taskSiteModel.setState(SohuBusyTaskState.OverSettle.name());
            sohuBusyTaskModel.setState(SohuBusyTaskState.OverSettle.name());
            // 子任务
            remoteBusyOrderService.updateByTaskSite(taskSiteModel);
            // 主任务
            remoteBusyOrderService.updateByTask(sohuBusyTaskModel);
            // 接单
            remoteBusyOrderService.updateByTaskReceive(taskReceiveModel);
            // 阶段
            remoteBusyOrderService.updateDelivery(deliveryModel);
            // todo 执行记录
            // 退保证金
            remotePayService.refund(BeanCopyUtils.copy(busyOrderPayModel, SohuBusyTaskPayModel.class));
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            throw new ServiceException("非阶段性任务相关更新失败");
        }
    }

    /**
     * 确认接单人后续操作
     *
     * @param taskReceiveModel
     */
    @Transactional(rollbackFor = Exception.class)
    public void busyTaskReceive(SohuBusyTaskReceiveModel taskReceiveModel) {
        // 接单
        taskReceiveModel.setState(SohuBusyTaskState.Execute.name());
        taskReceiveModel.setBackup(Boolean.TRUE);
        taskReceiveModel.setPassTime(new Date());
        // 当前子单
        SohuBusyTaskSiteModel taskSiteModel = remoteBusyOrderService.querySiteByTaskNo(taskReceiveModel.getTaskNumber());
        taskSiteModel.setState(SohuBusyTaskState.Execute.name());
        // 其他子单
        List<SohuBusyTaskSiteModel> busyTaskSiteModels = remoteBusyOrderService.listByMasterTaskNo(taskSiteModel.getMasterTaskNumber());
        List<SohuBusyTaskSiteModel> filteredList = busyTaskSiteModels.stream()
                .filter(model -> !taskReceiveModel.getTaskNumber().equals(model.getTaskNumber()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filteredList)) {
            for (SohuBusyTaskSiteModel taskSite : filteredList) {
                taskSite.setState(SohuBusyTaskState.Over.name());
                taskSite.setShelfState(SohuBusyTaskState.OffShelf.name());
            }
        }
        // 查询所有的其他未接单的集合
        List<SohuBusyTaskReceiveModel> busyTaskReceiveModels = remoteBusyOrderService.listBusyTaskReceiveNotTask(taskSiteModel.getMasterTaskNumber(), taskReceiveModel.getId());
        // 主单
        SohuBusyTaskModel sohuBusyTaskModel = remoteBusyOrderService.queryByTaskNo(taskSiteModel.getMasterTaskNumber());
        sohuBusyTaskModel.setState(SohuBusyTaskState.Execute.name());
        Boolean execute = transactionTemplate.execute(e -> {
            // 修改接单状态
            this.remoteBusyOrderService.updateByTaskReceive(taskReceiveModel);
            if (CollectionUtils.isNotEmpty(busyTaskReceiveModels)) {
                for (SohuBusyTaskReceiveModel busyTaskReceiveModel : busyTaskReceiveModels) {
                    busyTaskReceiveModel.setBackup(Boolean.FALSE);
                    busyTaskReceiveModel.setState(SohuBusyTaskState.Refuse.name());
                    busyTaskReceiveModel.setRefuseMsg("任务方已选择其他接单人");
                    // 发送接单方申请驳回消息
                    remoteBusyTaskNoticeService.sendTaskSiteNotice(busyTaskReceiveModel.getTaskNumber(), TaskNoticeEnum.TASK_APPLY_REFUSE,
                            busyTaskReceiveModel.getUserId(), busyTaskReceiveModel.getRefuseMsg(), null, Boolean.FALSE);
                }
                this.remoteBusyOrderService.updateBatchReceive(busyTaskReceiveModels);
            }
            // 修改接单子任务状态
            this.remoteBusyOrderService.updateByTaskSite(taskSiteModel);
            // 修改其他子任务状态
            this.remoteBusyOrderService.updateBatchSiteById(filteredList);
            // 修改主任务状态
            this.remoteBusyOrderService.updateByTask(sohuBusyTaskModel);
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            throw new ServiceException("缴纳保证金后续操作失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void busyTaskShelf(SohuBusyTaskSiteModel busyTaskSiteModel) {
        List<SohuBusyTaskSiteModel> busyTaskSiteModels = remoteBusyOrderService.listByMasterTaskNo(busyTaskSiteModel.getMasterTaskNumber());
        // 主任务下架
        boolean isOffShelf = busyTaskSiteModels.stream().allMatch(busyTaskSite -> "CompelOff"
                .equals(busyTaskSite.getShelfState()) || "OffShelf"
                .equals(busyTaskSite.getShelfState()));
        if (isOffShelf) {
            SohuBusyTaskModel sohuBusyTaskModel = remoteBusyOrderService.queryByTaskNo(busyTaskSiteModel.getMasterTaskNumber());
            sohuBusyTaskModel.setState("CompelOff");
            sohuBusyTaskModel.setShelfState("OffShelf");
            sohuBusyTaskModel.setAuditTime(busyTaskSiteModel.getAuditTime());
            sohuBusyTaskModel.setAuditUser(busyTaskSiteModel.getAuditUser());
            remoteBusyOrderService.updateByTask(sohuBusyTaskModel);
        }
        // 主任务上架
        boolean isOnShelf = busyTaskSiteModels.stream().allMatch(busyTaskSite -> "OnOff"
                .equals(busyTaskSite.getShelfState()));
        if (isOnShelf) {
            SohuBusyTaskModel sohuBusyTaskModel = remoteBusyOrderService.queryByTaskNo(busyTaskSiteModel.getMasterTaskNumber());
            sohuBusyTaskModel.setState("OnShelf");
            sohuBusyTaskModel.setShelfState("OnShelf");
            sohuBusyTaskModel.setAuditTime(busyTaskSiteModel.getAuditTime());
            sohuBusyTaskModel.setAuditUser(busyTaskSiteModel.getAuditUser());
            remoteBusyOrderService.updateByTask(sohuBusyTaskModel);
        }
    }

//    /**
//     * 主订单号
//     *
//     * @param operateBo
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void updateBatchMerchants(SohuOperateBo operateBo, Boolean isRefund) {
//        // 查询所有子订单
//        List<SohuShopOrderModel> shopOrderModels = remoteShopOrderService.getListByMasterNo(operateBo.getOrderNo());
//        // 修改店铺集合对象
//        List<SohuMerchantModel> updateMerchantModels = Lists.newArrayList();
//        // 不是退货就不修改店铺销售数量
//        if (!isRefund) {
//            // 获取所有商铺id
//            List<Long> merIds = shopOrderModels.stream().distinct().map(SohuShopOrderModel::getMerId).collect(Collectors.toList());
//            // 获取所有店铺信息
//            Map<Long, SohuMerchantModel> merchantModels = remoteMerchantService.getMerIdMapByIdList(merIds);
//            for (SohuShopOrderModel shopOrderModel : shopOrderModels) {
//                SohuMerchantModel sohuMerchantModel = merchantModels.get(shopOrderModel.getMerId());
//                sohuMerchantModel.setSaleNums(sohuMerchantModel.getSaleNums() + shopOrderModel.getTotalNum());
//                updateMerchantModels.add(sohuMerchantModel);
//            }
//        }
//        // mcn相关操作
//        // 获取所有子订单号
//        List<String> orderNos = shopOrderModels.stream().distinct().map(SohuShopOrderModel::getOrderNo).collect(Collectors.toList());
//        List<SohuShopOrderInfoModel> orderInfoModels = remoteShopOrderInfoService.getListByOrderNos(orderNos);
//        // 取出所有的userId和productId
//        List<Long> windowUserIds = new ArrayList<>();
//        List<Long> windowProductIds = new ArrayList<>();
//        List<Long> windowMcnUserIds = new ArrayList<>();
//        List<Long> windowMcnProductIds = new ArrayList<>();
//        extractedIds(orderInfoModels, windowUserIds, windowProductIds, windowMcnUserIds, windowMcnProductIds);
//        // 个人商品橱窗
//        List<SohuProductWindowModel> productWindowModels = remoteProductService.selectByUserIdsAndProIds(windowUserIds, windowProductIds);
//        if (CollUtil.isNotEmpty(productWindowModels)) {
//            productWindowModels.forEach(productWindowModel -> {
//                orderInfoModels.stream()
//                        .filter(orderInfoModel -> Objects.equals(productWindowModel.getProductId(), orderInfoModel.getProductId()))
//                        .filter(orderInfoModel -> {
//                            // 是否分销人=商品橱窗人-不带mcn机构
//                            boolean isIndependent = Objects.equals(orderInfoModel.getIndependentUserId(), productWindowModel.getUserId());
//                            // 是否是mcn机构
//                            boolean isMcn = Objects.equals(orderInfoModel.getMcnId(), productWindowModel.getMcnId());
//                            // 是否分销人=商品橱窗人-带mcn机构
//                            boolean isOriginator = Objects.equals(orderInfoModel.getOriginatorUserId(), productWindowModel.getUserId());
//                            // 返回不通选项
//                            return (isMcn && isOriginator) || isIndependent;
//                        })
//                        .forEach(orderInfoModel ->
//                                productWindowModel.setSaleCount(productWindowModel.getSaleCount() + (operateBo.getOperate() ?
//                                        orderInfoModel.getPayNum() : -orderInfoModel.getPayNum())));
//            });
//        }
//
//        // mcn商品橱窗
//        List<SohuProductWindowMcnModel> productWindowMcnModels = remoteProductService.selectMcnByUserIdsAndProIds(windowMcnUserIds, windowMcnProductIds);
//        if (CollUtil.isNotEmpty(productWindowMcnModels)) {
//            productWindowMcnModels.forEach(productWindowMcnModel -> {
//                orderInfoModels.stream()
//                        .filter(orderInfoModel ->
//                                Objects.equals(productWindowMcnModel.getProductId(), orderInfoModel.getProductId())
//                                        && Objects.equals(orderInfoModel.getMcnId(), productWindowMcnModel.getUserId())
//                        )
//                        .forEach(orderInfoModel ->
//                                productWindowMcnModel.setSaleCount(productWindowMcnModel.getSaleCount() + (operateBo.getOperate() ?
//                                        orderInfoModel.getPayNum() : -orderInfoModel.getPayNum())));
//            });
//        }
//
//        // todo 批量修改mcn商品橱窗
//        Boolean execute = transactionTemplate.execute(e -> {
//            if (!isRefund) {
//                // 商铺数量修改
//                if (CollUtil.isNotEmpty(updateMerchantModels)) {
//                    remoteMerchantService.updateBatch(updateMerchantModels);
//                }
//            }
//            // 个人商铺橱窗修改
//            if (CollUtil.isNotEmpty(productWindowModels)) {
//                remoteProductService.updateBatchWindow(productWindowModels);
//            }
//            // mcn商铺橱窗修改
//            if (CollUtil.isNotEmpty(productWindowMcnModels)) {
//                remoteProductService.updateBatchWindowMcn(productWindowMcnModels);
//            }
//            return Boolean.TRUE;
//        });
//        if (Boolean.FALSE.equals(execute)) {
//            throw new ServiceException("商品数量相关更新失败");
//        }
//    }

    /**
     * 组装ids
     *
     * @param orderInfoModels
     * @param windowUserIds
     * @param windowProductIds
     * @param windowMcnUserIds
     * @param windowMcnProductIds
     */
    private static void extractedIds(List<SohuShopOrderInfoVo> orderInfoModels, List<Long> windowUserIds, List<Long> windowProductIds, List<Long> windowMcnUserIds, List<Long> windowMcnProductIds) {
        for (SohuShopOrderInfoVo info : orderInfoModels) {
            if (null != info.getMcnId()) {
                windowMcnUserIds.add(info.getMcnId());
                windowMcnProductIds.add(info.getProductId());
                windowUserIds.add(info.getOriginatorUserId());
            } else {
                windowUserIds.add(info.getIndependentUserId());
            }
            windowProductIds.add(info.getProductId());
        }
    }

    /**
     * 退款后续操作
     *
     * @param refundObj
     */
    private void refundChange(MqMessaging refundObj) {
        // 查询退款单，修改退款单。并写入相关退款参数
        SohuShopRefundOrderModel refundOrder = remoteRefundOrderService.getByRefundOrderNo(refundObj.getMsgText());
        // 修改订单退款状态。 注：暂时不考虑拆单多笔订单的情况
        SohuShopOrderModel storeOrder = remoteShopOrderService.getByOrderNo(refundOrder.getShopOrderNo());
        storeOrder.setRefundStatus(OrderConstants.ORDER_REFUND_STATUS_REFUND);
        // todo 考虑拆单情况-添加第三方退款单号
        // refundOrder.setRefundId(notifyResponse.getRefundId());
        refundOrder.setRefundStatus(OrderConstants.MERCHANT_REFUND_ORDER_STATUS_REFUND);
        refundOrder.setRefundTime(new Date());

        // 数量统计修改计算
        List<SohuShopOrderInfoVo> orderInfoModels = remoteShopOrderInfoService.getListByOrderNo(refundOrder.getShopOrderNo());
        // 取出所有的userId和productId
        List<Long> windowUserIds = new ArrayList<>();
        List<Long> windowProductIds = new ArrayList<>();
        List<Long> windowMcnUserIds = new ArrayList<>();
        List<Long> windowMcnProductIds = new ArrayList<>();
        extractedIds(orderInfoModels, windowUserIds, windowProductIds, windowMcnUserIds, windowMcnProductIds);
        // 个人商品橱窗
        List<SohuProductWindowModel> productWindowModels = remoteProductService.selectByUserIdsAndProIds(windowUserIds, windowProductIds);
        if (CollUtil.isNotEmpty(productWindowModels)) {
            productWindowModels.forEach(productWindowModel -> {
                orderInfoModels.stream()
                        .filter(orderInfoModel -> Objects.equals(productWindowModel.getProductId(), orderInfoModel.getProductId()))
                        .filter(orderInfoModel -> {
                            // 是否分销人=商品橱窗人-不带mcn机构
                            boolean isIndependent = Objects.equals(orderInfoModel.getIndependentUserId(), productWindowModel.getUserId());
                            // 是否是mcn机构
                            boolean isMcn = Objects.equals(orderInfoModel.getMcnId(), productWindowModel.getMcnId());
                            // 是否分销人=商品橱窗人-带mcn机构
                            boolean isOriginator = Objects.equals(orderInfoModel.getOriginatorUserId(), productWindowModel.getUserId());
                            // 返回不通选项
                            return (isMcn && isOriginator) || isIndependent;
                        })
                        .forEach(orderInfoModel ->
                                productWindowModel.setSaleCount(productWindowModel.getRefundCount() + orderInfoModel.getPayNum()));
            });
        }

        // mcn商品橱窗
        List<SohuProductWindowMcnModel> productWindowMcnModels = remoteProductService.selectMcnByUserIdsAndProIds(windowMcnUserIds, windowMcnProductIds);
        if (CollUtil.isNotEmpty(productWindowMcnModels)) {
            productWindowMcnModels.forEach(productWindowMcnModel -> {
                orderInfoModels.stream()
                        .filter(orderInfoModel ->
                                Objects.equals(productWindowMcnModel.getProductId(), orderInfoModel.getProductId())
                                        && Objects.equals(orderInfoModel.getMcnId(), productWindowMcnModel.getUserId())
                        )
                        .forEach(orderInfoModel ->
                                productWindowMcnModel.setSaleCount(productWindowMcnModel.getRefundCount() + orderInfoModel.getPayNum()));
            });
        }

        Boolean execute = transactionTemplate.execute(e -> {
            remoteRefundOrderService.updateById(refundOrder);
            remoteShopOrderService.updateById(storeOrder);
            // 分账订单修改状态
            remoteIndependentOrderService.updateIndependentStatus(refundOrder.getShopOrderNo(), BusyType.Goods.name(), 4);
            // 修改用户流水明细表
            remoteMiddleTradeRecordService.updateIndependentStatus(refundOrder.getMasterOrderNo(), null, IndependentStatusEnum.REFUNDED.getCode());
            //更新商户订单流水
            remoteMiddleMerTradeRecordService.updateIndependentStatus(refundOrder.getShopOrderNo(), null, IndependentStatusEnum.REFUNDED.getCode());
            // 新增日志
            remoteShopOrderStatusService.saveRefund(storeOrder.getOrderNo(), refundOrder.getRefundPrice(), "退款成功");
            remoteRefundOrderStatusService.createLog(refundOrder.getRefundOrderNo(),
                    OrderConstants.REFUND_ORDER_LOG_TYPE_REFUND, OrderConstants.ORDER_LOG_MESSAGE_REFUND_PRICE.replace("{amount}", refundOrder.getRefundPrice().toString()));

            if (CollUtil.isNotEmpty(productWindowModels)) {
                // 个人商铺橱窗修改
                remoteProductService.updateBatchWindow(productWindowModels);
            }
            if (CollUtil.isNotEmpty(productWindowMcnModels)) {
                // mcn商铺橱窗修改
                remoteProductService.updateBatchWindowMcn(productWindowMcnModels);
            }

            // 订单退款异步处理
            this.payRefundOrder(refundOrder);
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            remoteShopOrderStatusService.saveRefund(storeOrder.getOrderNo(), storeOrder.getPayPrice(), "失败");
            throw new ServiceException("订单更新失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void payRefundOrder(SohuShopRefundOrderModel refundOrder) {
        int i = 0;
        /*
         * 1、回滚库存
         * 2、退优惠券
         * 3、生成帐单记录
         */
        SohuShopOrderModel shopOrder = remoteShopOrderService.getByOrderNo(refundOrder.getShopOrderNo());
        log.info("StoreOrderTaskServiceImpl.refundApply | request:{},{}", JSONUtil.toJsonStr(shopOrder), JSONUtil.toJsonStr(refundOrder));

        Boolean result = transactionTemplate.execute(e -> {
            // 回滚库存
            this.rollbackStock(refundOrder.getShopOrderNo());
            // todo 退优惠券
//            if (storeOrder.getCouponId() > 0) {
//                StoreCouponUser couponUser = couponUserService.getById(storeOrder.getCouponId());
//                couponUser.setStatus(CouponConstants.STORE_COUPON_USER_STATUS_USABLE);
//                couponUserService.updateById(couponUser);
//            }
//            // 帐单记录
//            billService.save(bill);
//            merchantBill.setPid(bill.getId());
//            merchantBillService.save(merchantBill);
            return Boolean.TRUE;
        });
        // 重试5次
        if (Boolean.FALSE.equals(result) && i < 5) {
            i = i++;
            this.payRefundOrder(refundOrder);
        }
    }

    /**
     * 回滚库存
     *
     * @param orderNoStr 订单号字符串（英文逗号分隔）
     */
    private Boolean rollbackStock(String orderNoStr) {
        try {
            // 查找出商品详情
            List<SohuShopOrderInfoVo> orderInfoList = remoteShopOrderInfoService.getListByOrderNos(Arrays.stream(orderNoStr.split(",")).map(String::valueOf).collect(Collectors.toList()));
            if (null == orderInfoList || orderInfoList.size() < 1) {
                return true;
            }
//            List<Long> idList = orderInfoList.stream().distinct().map(SohuShopOrderInfoModel::getProductId).collect(Collectors.toList());
            // 正常商品回滚销量库存
            for (SohuShopOrderInfoVo orderInfoVo : orderInfoList) {
                productService.operationStock(orderInfoVo.getProductId(), orderInfoVo.getPayNum(), "add");
                SohuProductAttrValueModel attrValue = productAttrValueService.queryById(orderInfoVo.getProductAttrValueId());
                productAttrValueService.operationStock(attrValue.getId(), orderInfoVo.getPayNum(), "add", ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS, null);
            }
            log.warn("库存操作成功+++++");
            // 缓存
//            idList.forEach(id -> {
//                redisAsyncService.syncIndexProductRedisValue(RedisFile.FOLDER_SHOP + "homeshop-list:", storeProductService.getById(id).getSiteId());
//            });
        } catch (Exception e) {
            throw new ServiceException("回滚库存失败，error = " + e.getMessage());
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancel(SohuShopMasterOrderModel masterOrderModel) {
        SohuShopMasterOrderModel orderModel = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(masterOrderModel.getOrderNo());
        if (Boolean.FALSE.equals(orderModel.getPaid())) {
            transactionTemplate.execute(e -> {
                remoteMasterOrderService.cancel(masterOrderModel.getOrderNo());
                remoteShopOrderService.cancelByMasterNo(masterOrderModel.getOrderNo(), true);
                commonCancel(masterOrderModel);
                return Boolean.TRUE;
            });
        }
    }

    /**
     * 订单取消公共操作
     *
     * @param masterOrder 主订单
     */
    private void commonCancel(SohuShopMasterOrderModel masterOrder) {
        //写订单日志
        List<SohuShopOrderModel> orderList = remoteShopOrderService.getListByMasterNo(masterOrder.getOrderNo());
        orderList.forEach(order -> {
            remoteShopOrderStatusService.createLog(order.getOrderNo(), OrderConstants.ORDER_LOG_CANCEL, OrderConstants.ORDER_LOG_MESSAGE_CANCEL);
        });
        // todo 退优惠券
//        if (masterOrder.getCouponId() > 0) {
//            StoreCouponUser couponUser = couponUserService.getById(masterOrder.getCouponId());
//            couponUser.setStatus(CouponConstants.STORE_COUPON_USER_STATUS_USABLE);
//            couponUserService.updateById(couponUser);
//        }
        // 回滚库存
        String orderNoStr = orderList.stream().map(SohuShopOrderModel::getOrderNo).collect(Collectors.joining(","));
        rollbackStock(orderNoStr);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean delayConfirm(SohuShopOrderModel shopOrder) {
        /*
         * 先查询主订单
         * 在查询子订单集合
         * 在查询子订单的订单详情
         */
        // 获取主订单
        SohuShopMasterOrderModel masterOrder = remoteMasterOrderService.queryMasterOrderByMasterOrderNo(shopOrder.getMasterOrderNo());
        if (Objects.isNull(masterOrder)) {
            throw new RuntimeException("不存在当前主订单");
        }
        // 获取子订单
        List<SohuShopOrderModel> shopOrderList = remoteShopOrderService.getListByMasterNo(shopOrder.getMasterOrderNo());
        // 计算主订单的总价
        SohuOrderIndependentPriceVo priceVo = extractedMasterOrderPrice(shopOrderList, masterOrder.getOrderNo());
        log.warn("SohuOrderIndependentPriceVo 计算主订单的总价：{}", priceVo);
        // 主单总分销金额
        BigDecimal adminPrice = Objects.requireNonNullElse(CalUtils.sub(priceVo.getAdminPrice(), masterOrder.getChargePrice()), BigDecimal.ZERO);
        masterOrder.setAdminPrice(adminPrice);
        masterOrder.setCountryPrice(Objects.requireNonNullElse(priceVo.getCountryPrice(), BigDecimal.ZERO));
        // 城市站长出平台服务费
        masterOrder.setCityPrice(Objects.requireNonNullElse(priceVo.getCityPrice(), BigDecimal.ZERO));
        masterOrder.setDistributorPrice(masterOrder.getIndependentUserId() != null ? priceVo.getDistributorPrice() : null);
        masterOrder.setInvitePrice(Objects.requireNonNullElse(priceVo.getInvitePrice(), BigDecimal.ZERO));
        // 延时分账请求流水号
        String delayOrderNo = NumberUtil.getOrderNo(OrderConstants.YI_MA_INDEPENDENT_NO);
        shopOrder.setDelayTradeNo(delayOrderNo);
        log.warn("delayOrderNo 延时分账交易流水号：{}", delayOrderNo);
        SohuIndependentIdBo independentIdBo = new SohuIndependentIdBo();
        // 根据城市站点id获取用户id
        Long cityId = remoteMiddleSiteService.queryById(masterOrder.getSiteId()).getStationmasterId();
        independentIdBo.setCityId(cityId);
        // 根据城市站点id获取国家站点用户id
        Long countryId = remoteMiddleSiteService.selectSiteByPid(masterOrder.getSiteId()).getStationmasterId();
        independentIdBo.setCountryId(countryId);
        // todo 获取代理人用户id
//        Long agencyId = siteService.selectSiteByPid(masterOrder.getSiteId()).getStationmasterId();
        Long agencyId = remoteMiddleSiteService.selectSiteByPid(masterOrder.getSiteId()).getStationmasterId();
        independentIdBo.setAgencyId(agencyId);
        // 获取当前的商户id
//        List<Long> merIds = shopOrderList.stream().map(SohuShopOrderModel::getMerId).collect(Collectors.toList());
        Long merId = shopOrder.getMerId();
        List<Long> merIds = Lists.newArrayList();
        merIds.add(merId);
        // 获取所有商户的userId
        Map<Long, SohuMerchantModel> merIdMapByIdList = remoteMerchantService.getMerIdMapByIdList(merIds);
        // 获取到这个店铺信息
        SohuMerchantModel sohuMerchantModel = merIdMapByIdList.get(shopOrder.getMerId());
        List<SohuIndependentOrderModel> independentOrderBoLists = Lists.newArrayList();
        BigDecimal zeroPrice = new BigDecimal("0.00");
        //  增加分账订单记录
        if (shopOrder.getAdminPrice() != null && !shopOrder.getAdminPrice().equals(zeroPrice)) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(1);
            independentOrderBo.setTradeNo(delayOrderNo);
            independentIdBo.setAdminId(2L);
            independentOrderBo.setUserId(2L);
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.platform.getKey());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBo.setIndependentPrice(shopOrder.getAdminPrice());
            independentOrderBoLists.add(independentOrderBo);
        }
        if (shopOrder.getCountryPrice() != null && !shopOrder.getCountryPrice().equals(zeroPrice)) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setUserId(countryId);
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(1);
            independentOrderBo.setTradeNo(delayOrderNo);
            independentOrderBo.setIndependentObject(SohuIndependentObject.country.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getCountryPrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        }
        if (shopOrder.getCityPrice() != null && !shopOrder.getCityPrice().equals(zeroPrice)) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setUserId(cityId);
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(1);
            independentOrderBo.setTradeNo(delayOrderNo);
            independentOrderBo.setIndependentObject(SohuIndependentObject.city.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getCityPrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        }
        if (null != shopOrder.getIndependentUserId() && shopOrder.getDistributorPrice() != null && !shopOrder.getDistributorPrice().equals(zeroPrice)) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(1);
            independentOrderBo.setTradeNo(delayOrderNo);
            independentIdBo.setDistributorId(shopOrder.getIndependentUserId());
            independentOrderBo.setUserId(shopOrder.getIndependentUserId());
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.distribution.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getDistributorPrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        }
        if (null != shopOrder.getInviteUserId() && shopOrder.getInvitePrice() != null && !shopOrder.getInvitePrice().equals(zeroPrice)) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(1);
            independentOrderBo.setTradeNo(delayOrderNo);
            independentIdBo.setInviteId(shopOrder.getInviteUserId());
            independentOrderBo.setUserId(shopOrder.getInviteUserId());
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.invite.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getInvitePrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        }
        // 代理
        if (null != agencyId && shopOrder.getAgencyAdminPrice() != null && !shopOrder.getAgencyAdminPrice().equals(zeroPrice) && !shopOrder.getAgencyAdminPrice().toPlainString().equals("0")) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(1);
            independentOrderBo.setTradeNo(delayOrderNo);
            independentIdBo.setAgencyId(agencyId);
            independentOrderBo.setUserId(agencyId);
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.agency.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getAgencyAdminPrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        }
        // 分销人的拉新人
        if (null != shopOrder.getIndependentInviteUserId() && shopOrder.getDistributorInvitePrice() != null && !shopOrder.getDistributorInvitePrice().equals(zeroPrice) && !shopOrder.getDistributorInvitePrice().toPlainString().equals("0")) {
            SohuIndependentOrderModel independentOrderBo = new SohuIndependentOrderModel();
            independentOrderBo.setOrderNo(shopOrder.getOrderNo());
            independentOrderBo.setTradeType(BusyType.Goods.name());
            independentOrderBo.setIndependentStatus(1);
            independentOrderBo.setTradeNo(delayOrderNo);
            independentIdBo.setDistributorInviteId(shopOrder.getIndependentInviteUserId());
            independentOrderBo.setUserId(shopOrder.getIndependentInviteUserId());
            independentOrderBo.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderBo.setIndependentObject(SohuIndependentObject.distributionInvite.getKey());
            independentOrderBo.setIndependentPrice(shopOrder.getDistributorInvitePrice());
            independentOrderBo.setMerId(shopOrder.getMerId());
            independentOrderBoLists.add(independentOrderBo);
        }
        // 提取所有对象的 id 成为 List<Long>
        List<Long> idList = merIdMapByIdList.values().stream().map(SohuMerchantModel::getUserId).collect(Collectors.toList());
        // 查询所有分账人的翼码帐户信息
        List<Long> allUserIds = getAllIds(independentIdBo);
        allUserIds.addAll(idList);
        List<SohuAccountBankModel> accountBankVos = remoteAccountService.queryListByUserId(allUserIds);
        // 获取翼码支付配置
        YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
        // 延时分账请求参数对象
        DelayConfirmRequest delayConfirmRequest = new DelayConfirmRequest();
        // 固定参数
        delayConfirmRequest.setPosId(yiMaPayConfig.getPosId());
        delayConfirmRequest.setIsspid(yiMaPayConfig.getIssPid());
        delayConfirmRequest.setSystemId(yiMaPayConfig.getSystemId());
        delayConfirmRequest.setStoreId(yiMaPayConfig.getStoreId());
        // 延时确认请求流水号
        delayConfirmRequest.setPosSeq(delayOrderNo);
        // 支付请求流水号及分账扩展参数
        DelayConfirm confirm = new DelayConfirm();
        confirm.setOrgPosSeq(masterOrder.getOrderNo());
        // 分账信息相关扩展参数
        ExtendParams params = new ExtendParams();
        // 分账信息
        SplitInfo splitInfo = new SplitInfo();
        // 分账账号信息参数
        List<SplitList> splitLists = Lists.newArrayList();
        // 计算国家、城市站长、分销、拉新的钱
        accountBankVos.forEach(bank -> {
            // 分销人
            if (null != independentIdBo.getDistributorId() && bank.getUserId().equals(independentIdBo.getDistributorId())) {
                SplitList splitList = new SplitList();
                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getDistributorPrice()).toString());
                splitList.setMerchantId(bank.getMerchantId());
                splitLists.add(splitList);
            }
            // 拉新人
            if (null != independentIdBo.getInviteId() && bank.getUserId().equals(independentIdBo.getInviteId())) {
                SplitList splitList = new SplitList();
                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getInvitePrice()).toString());
                splitList.setMerchantId(bank.getMerchantId());
                splitLists.add(splitList);
            }
            // 国家站长
            if (null != independentIdBo.getCountryId() && bank.getUserId().equals(independentIdBo.getCountryId())) {
                SplitList splitList = new SplitList();
                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getCountryPrice()).toString());
                splitList.setMerchantId(bank.getMerchantId());
                splitLists.add(splitList);
            }
            // 城市站长
            if (null != independentIdBo.getCityId() && bank.getUserId().equals(independentIdBo.getCityId())) {
                SplitList splitList = new SplitList();
                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getCityPrice()).toString());
                splitList.setMerchantId(bank.getMerchantId());
                splitLists.add(splitList);
            }
            // 代理
            if (null != independentIdBo.getAgencyId() && bank.getUserId().equals(independentIdBo.getAgencyId())) {
                SplitList splitList = new SplitList();
                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getAgencyAdminPrice()).toString());
                splitList.setMerchantId(bank.getMerchantId());
                splitLists.add(splitList);
            }
            // 分销人的拉新人
            if (null != independentIdBo.getDistributorInviteId() && bank.getUserId().equals(independentIdBo.getDistributorInviteId())) {
                SplitList splitList = new SplitList();
                splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopOrder.getDistributorInvitePrice()).toString());
                splitList.setMerchantId(bank.getMerchantId());
                splitLists.add(splitList);
            }
        });
        // 计算商户的留存金额
        SplitList splitList = new SplitList();
        // 直接通过 Stream 找到符合条件的对象并获取 店铺的userId去获取店铺在翼码的分账id
        Optional<String> merIdOptional = accountBankVos.stream()
                .filter(accountBankVo -> sohuMerchantModel.getUserId().equals(accountBankVo.getUserId()))
                .map(SohuAccountBankModel::getMerchantId).findFirst();
        // 处理非空的情况
        merIdOptional.ifPresent(merchantId -> {
            // 如果存在值直接返回不存在传空
            splitList.setMerchantId(merIdOptional.orElse(""));
            // 每笔子单商户应该获得的钱
            BigDecimal shopPrice = CalUtils.sub(shopOrder.getPayPrice(), shopOrder.getDistributorPrice(), shopOrder.getInvitePrice(),
                    shopOrder.getAdminPrice(), shopOrder.getCountryPrice(), shopOrder.getCityPrice(), shopOrder.getChargePrice(),
                    shopOrder.getAgencyAdminPrice(), shopOrder.getDistributorInvitePrice());
            splitList.setDivAmt(BigDecimalUtils.yuanToFen(shopPrice).toString());
            splitLists.add(splitList);
            // 商家分账信息
            SohuIndependentOrderModel independentOrderShop = new SohuIndependentOrderModel();
            independentOrderShop.setOrderNo(shopOrder.getOrderNo());
            independentOrderShop.setTradeType(BusyType.Goods.name());
            independentOrderShop.setIndependentStatus(1);
            independentOrderShop.setTradeNo(delayOrderNo);
            independentOrderShop.setUserId(sohuMerchantModel.getUserId());
            independentOrderShop.setSiteId(sohuMerchantModel.getCitySiteId());
            independentOrderShop.setIndependentObject(SohuIndependentObject.shop.getKey());
            independentOrderShop.setIndependentPrice(shopPrice);
            independentOrderShop.setMerId(shopOrder.getMerId());
            log.warn("商家分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderShop));
            independentOrderBoLists.add(independentOrderShop);
        });
        log.warn("分账信息集合independentOrderBoLists：{}", JSONObject.toJSONString(independentOrderBoLists));
        // 去重从新计算之后的传给第三方的分账信息
        Map<String, BigDecimal> result = splitLists.stream()
                .collect(Collectors.groupingBy(SplitList::getMerchantId,
                        Collectors.mapping(s -> new BigDecimal(s.getDivAmt()), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        List<SplitList> mergedList = Lists.newArrayList();
        result.forEach((key, value) -> {
            SplitList newSplitList = new SplitList();
            newSplitList.setMerchantId(key);
            newSplitList.setDivAmt(value.toString());
            mergedList.add(newSplitList);
        });
        // 设置分账参数
        splitInfo.setSplitList(mergedList);
        // 平台总留存金额
        splitInfo.setKeepAmt(BigDecimalUtils.yuanToFen(shopOrder.getAdminPrice()).toString());
        // 分账信息
        params.setSplitInfo(splitInfo);
        // 添加分账扩展参数
        confirm.setExtendParams(params);
        // 延时分账请求添加分账信息
        delayConfirmRequest.setDelayConfirmRequest(confirm);
        log.warn("yi-ma延时分账请求：{}", JSONObject.toJSONString(delayConfirmRequest));
        DelayConfirmResponse response = Client.getClient().execute(delayConfirmRequest);
        log.warn("response 延时分账交易返回参数：{}", JSONUtil.toJsonStr(response));
        // 判断yi-ma延时分账状态
        List<String> resultList = Lists.newArrayList();
        resultList.add("9998");
        resultList.add("0000");
        if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
            log.error("yi-ma延时分账异常");
            throw new RuntimeException(response.getResult().getComment());
        }
        List<SohuTradeRecordBo> tradeRecords = new ArrayList<>();
        for (SohuIndependentOrderModel orderBo : independentOrderBoLists) {
            SohuTradeRecordBo tradeRecordBo = SohuTradeRecordBo.builder().
                    userId(orderBo.getUserId()).
                    type(Good.getCode()).
                    consumeType(Good.getCode()).
                    consumeCode(shopOrder.getMasterOrderNo()).
                    amount(orderBo.getIndependentPrice()).
                    amountType(SohuTradeRecordEnum.AmountType.InCome.getCode()).
                    payType(PayTypeEnum.PAY_TYPE_YI_MA.getStatus()).
                    operateChannel(masterOrder.getPayChannel()).
                    payNumber(masterOrder.getOutTradeNo()).
                    payStatus(PayStatus.Paid.name()).
                    msg("商品分账收入").
                    independent(true).
                    independentObject(orderBo.getIndependentObject()).
                    independentStatus(orderBo.getIndependentStatus()).
                    accountType(SohuTradeRecordEnum.AccountType.Amount.name()).
                    unq(SohuTradeRecordBo.genUnq()).
                    build();
            tradeRecords.add(tradeRecordBo);
        }
        return transactionTemplate.execute(e -> {
            log.warn("masterOrder 开始修改主订单：{}", JSONUtil.toJsonStr(masterOrder));
            remoteMasterOrderService.updateById(masterOrder);
            remoteShopOrderService.updateById(shopOrder);
            log.warn("masterOrder 修改主订单完成：{}", JSONUtil.toJsonStr(masterOrder));
            remoteIndependentOrderService.batchInsert(independentOrderBoLists);
            // todo 延时队列5s后查询分账是否成功-分账成功修改分账订单记录表状态
            return Boolean.TRUE;
        });
    }

    /**
     * 根据对象获取id集合
     *
     * @param bo
     */
    public static List<Long> getAllIds(SohuIndependentIdBo bo) {
        return Stream.of(bo.getAdminId(), bo.getCountryId(), bo.getCityId(), bo.getDistributorId(), bo.getInviteId(),
                bo.getDistributorInviteId(), bo.getAgencyId()).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 计算主订单的总价
     *
     * @param orderModelList
     * @param masterOrderNo
     */
    private static SohuOrderIndependentPriceVo extractedMasterOrderPrice
    (List<SohuShopOrderModel> orderModelList, String masterOrderNo) {
        SohuOrderIndependentPriceVo priceVo = new SohuOrderIndependentPriceVo();
        // 定义字段名和对应提取器的映射
        Map<String, Function<SohuShopOrderModel, BigDecimal>> priceMappings = Map.of(
                "adminPrice", SohuShopOrderModel::getAdminPrice,
                "countryPrice", SohuShopOrderModel::getCountryPrice,
                "cityPrice", SohuShopOrderModel::getCityPrice,
                "invitePrice", SohuShopOrderModel::getInvitePrice,
                "distributorPrice", SohuShopOrderModel::getDistributorPrice,
                "distributorInvitePrice", SohuShopOrderModel::getDistributorInvitePrice,
                "agencyPrice", SohuShopOrderModel::getAgencyAdminPrice
        );
        // 循环处理每个字段
        for (Map.Entry<String, Function<SohuShopOrderModel, BigDecimal>> entry : priceMappings.entrySet()) {
            String fieldName = entry.getKey();
            Function<SohuShopOrderModel, BigDecimal> valueExtractor = entry.getValue();
            // 获取满足条件的值
            List<BigDecimal> matchingValues = orderModelList.stream()
                    .filter(item -> masterOrderNo.equals(item.getMasterOrderNo()))
                    .map(valueExtractor)
                    .filter(Objects::nonNull)  // 过滤掉为null的值
                    .collect(Collectors.toList());
            // 判断是否需要计算和
            if (!matchingValues.isEmpty()) {
                // 计算和
                BigDecimal sum = matchingValues.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                // 设置和到priceVo对象的相应字段
                switch (fieldName) {
                    case "adminPrice":
                        // 平台
                        priceVo.setAdminPrice(sum);
                        break;
                    case "countryPrice":
                        // 国家站长
                        priceVo.setCountryPrice(sum);
                        break;
                    case "cityPrice":
                        // 城市站长
                        priceVo.setCityPrice(sum);
                        break;
                    case "invitePrice":
                        // 拉新人
                        priceVo.setInvitePrice(sum);
                        break;
                    case "distributorPrice":
                        // 分销人
                        priceVo.setDistributorPrice(sum);
                        break;
                    case "distributorInvitePrice":
                        priceVo.setDistributorInvitePrice(sum);
                        break;
                    case "agencyPrice":
                        priceVo.setAgencyPrice(sum);
                        break;
                    // 可以添加其他字段的处理
                    default:
                        throw new IllegalArgumentException("Unknown field: " + fieldName);
                }
            }
        }
        return priceVo.setShopOrderNo(masterOrderNo);
    }

    /**
     * 商单预付款退款
     *
     * @param msgText
     */
    public Boolean refundBusyOrder(String msgText) {
        log.info("延时消息队列执行商单预付款退款逻辑：{}", msgText);
        SohuBusyOrderPayModel sohuBusyOrderPayModel = JSONUtil.toBean(msgText, SohuBusyOrderPayModel.class);
        this.remoteBusyOrderService.mqRefundBusyOrder(sohuBusyOrderPayModel);
//        String busyOrder = sohuBusyOrderPayModel.getBusyOrder();
//        // todo
////        SohuBusyOrderModel sohuBusyOrderModel = remoteBusyOrderService.queryBusyOrder(busyOrder);
//        SohuBusyOrderModel sohuBusyOrderModel = remoteBusyOrderService.queryBusyOrder(1L);
//        if (sohuBusyOrderModel != null && StrUtil.equalsAnyIgnoreCase(sohuBusyOrderModel.getBusyOrderStatus(), BusyOrderStatus.Finished.name())) {
//            return Boolean.TRUE;
//        }
//
//        // 商单接单人数上限
//        Integer applyMax = sohuBusyOrderModel.getApplyMax();
//
//        // todo
////        Long count = remoteBusyOrderService.receCount(busyOrder);
//        Long count = remoteBusyOrderService.receCount(1L);
//
//        if (count != null && applyMax != null && count.intValue() == applyMax.intValue()) {
//
//            if (sohuBusyOrderModel.getPrepayAmount() != null && CalUtils.isZero(sohuBusyOrderModel.getPrepayAmount())
//                    && StrUtil.equalsAnyIgnoreCase(sohuBusyOrderModel.getPrepayState(), PayStatus.Paid.name())) {
//                remoteBusyOrderService.updateBusyOrderAndRece(sohuBusyOrderModel.getId(), true);
//                return Boolean.TRUE;
//            }
//            // todo
////            SohuBusyOrderPayModel busyOrderPayModel = remoteBusyOrderService.queryBusyOrderPay(busyOrder,
////                    PayObject.BusyOrder.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
//            SohuBusyOrderPayModel busyOrderPayModel = remoteBusyOrderService.queryBusyOrderPay("1L",
//                    PayObject.BusyOrder.name(), PayTypeEnum.PAY_TYPE_YI_MA.getStatus(), PayStatus.Paid.name());
//
//            // 查询翼码支付配置
//            YiMaPayConfig yiMaPayConfig = getYiMaPayConfig();
//            // 组装微信小程序退款请求参数
//            BarcodeReverseRequest request = new BarcodeReverseRequest();
//            request.setPosId(yiMaPayConfig.getPosId());
//            request.setIsspid(yiMaPayConfig.getIssPid());
//            request.setSystemId(yiMaPayConfig.getSystemId());
//            request.setStoreId(yiMaPayConfig.getStoreId());
//            // 退款参数封装
//            BarcodeReverse reverse = new BarcodeReverse();
//            reverse.setPayType(yiMaPayConfig.getPayType());
//
//            request.setPosSeq("R" + System.nanoTime());
//            // 支付请求流水号
//            reverse.setOrgPosSeq(busyOrderPayModel.getOrderNo());
//            reverse.setTxAmt(BigDecimalUtils.yuanToFen(busyOrderPayModel.getPayAmount()));
//            request.setBarcodeReverseRequest(reverse);
//            log.info("商单预付款退款请求:{}", JSONUtil.toJsonStr(request));
//
//            BarcodeReverseResponse response = Client.getClient().execute(request);
//            List<String> resultList = com.google.common.collect.Lists.newArrayList("9998", "0000");
//            if (ObjectUtils.isNull(response) || !resultList.contains(response.getResult().getId())) {
//                log.error("商单预付款退款异常：{}", JSONUtil.toJsonStr(response));
//                throw new RuntimeException(response.getResult().getComment());
//            }
//            remoteBusyOrderService.savePrepaymentRefundBillRecord(sohuBusyOrderPayModel);
//            // 更新商单状态完成，且正在合作中的接单改为终止
//            remoteBusyOrderService.updateBusyOrderAndRece(sohuBusyOrderModel.getId(), true);
//            return Boolean.TRUE;
//        }
        return Boolean.TRUE;
    }

    /**
     * 获取翼码支付配置
     *
     * @return
     */
    protected YiMaPayConfig getYiMaPayConfig() {
        SysDictData dictData = remoteDictService.getDictData(DictEnum.payConfig.getKey(), DictEnum.YMPayConfig.getKey());
        Objects.requireNonNull(dictData, "翼码支付配置为空");
        String dictValue = dictData.getDictValue();
        YiMaPayConfig bean = JSONUtil.toBean(dictValue, YiMaPayConfig.class);
//        bean.setNotifyUrl("https://api-pre.sohuglobal.com/pay/pay/callback/yima");
        return bean;
    }

}
