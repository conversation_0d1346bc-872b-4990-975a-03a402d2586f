package com.sohu.job.service;

import com.sohu.system.api.RemoteSysNoticeService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 系统公告定时任务
 *
 * <AUTHOR>
 * @date 2025/06/09
 */
@Slf4j
@Service
public class SysNoticeJobService {

    @DubboReference
    private RemoteSysNoticeService remoteSysNoticeService;

    @GlobalTransactional(rollbackFor = Exception.class)
    @XxlJob(value = "sysNoticeJobService", init = "init", destroy = "destroy")
    public void imFileOverTimeHandler() throws Exception {
        // 发送收益账单公告
        remoteSysNoticeService.sendUserIncomeStatisticsNotice();
    }

    public void init() {
        log.info("SysNoticeService定时任务启动");
        XxlJobHelper.log("系统公告处理处理启动");
    }

    public void destroy() {
        log.info("SysNoticeService定时任务结束");
        XxlJobHelper.log("系统公告处理处理结束");
    }
}
