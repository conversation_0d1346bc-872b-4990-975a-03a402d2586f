package com.sohu.job.service;

import cn.hutool.core.date.DateUtil;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.report.api.vo.SohuAgentRetentionVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户留存定时任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RetentionDataScheduler {

    @DubboReference
    private RemoteAdminService remoteAdminService;

    /**
     * 每天执行一次
     *
     * @throws Exception
     */
    @XxlJob(value = "incomeStatisticsJobHandler", init = "init", destroy = "destroy")
    public void incomeStatisticsJobHandler() throws Exception {
        // 获取昨天的日期字符串
        String yesterdayStr = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");

        // 获取所有需要计算留存的代理商/站长ID (根据实际业务逻辑获取)
        List<Long> allAgentIds = agentUserService.getAllAgentIds(); // 假设有这个方法

        for (Long agentId : allAgentIds) {
            // 模拟调用 getRetentionDetail 内部的计算逻辑，或者直接调用 getRetentionDetail
            // 但为了避免多次查询远程日志服务，更高效的做法是提取 calculateRetentionData 部分
            // 这里为了演示，我们假设直接调用 getRetentionDetail，但要注意性能开销
            List<SohuAgentRetentionVo> calculatedRetentionData =
                    sohuAgentService.getRetentionDetailForStorage(agentId, yesterdayStr, yesterdayStr); // 假设getRetentionDetailForStorage是优化过的只计算不初始化全日期范围的

            sohuAgentService.saveUserRetentionStatsToDatabase(agentId, calculatedRetentionData);
        }
        incomeStatisticsService.handleStatistics();
    }

    /**
     * 每小时执行一次
     *
     * @throws Exception
     */
    @XxlJob(value = "incomeStatisticsDayJobHandler", init = "init", destroy = "destroy")
    public void incomeStatisticsDayJobHandler() throws Exception {
        incomeStatisticsService.handleDayStatistics();
//        incomeStatisticsService.handleDayInviteStatistics();
    }


    public void init() {
        log.info("IncomeStatisticsJobService定时任务启动");
        XxlJobHelper.log("IncomeStatisticsJobService定时任务启动");
    }

    public void destroy() {
        log.info("IncomeStatisticsJobService定时任务结束");
        XxlJobHelper.log("IncomeStatisticsJobService定时任务结束");
    }
}