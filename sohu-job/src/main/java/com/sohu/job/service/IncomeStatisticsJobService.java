package com.sohu.job.service;

import com.sohu.report.api.RemoteIncomeStatisticsService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;


/**
 * 收益统计定时任务
 *
 * @Author: leibo
 * @Date: 2025/05/29 15:03
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomeStatisticsJobService {

    @DubboReference
    private RemoteIncomeStatisticsService incomeStatisticsService;

    /**
     * 每天执行一次
     *
     * @throws Exception
     */
    @XxlJob(value = "incomeStatisticsJobHandler", init = "init", destroy = "destroy")
    public void incomeStatisticsJobHandler() throws Exception {
        incomeStatisticsService.handleStatistics();
    }

    /**
     * 每小时执行一次
     *
     * @throws Exception
     */
    @XxlJob(value = "incomeStatisticsDayJobHandler", init = "init", destroy = "destroy")
    public void incomeStatisticsDayJobHandler() throws Exception {
        incomeStatisticsService.handleDayStatistics();
//        incomeStatisticsService.handleDayInviteStatistics();
    }


    public void init() {
        log.info("IncomeStatisticsJobService定时任务启动");
        XxlJobHelper.log("IncomeStatisticsJobService定时任务启动");
    }

    public void destroy() {
        log.info("IncomeStatisticsJobService定时任务结束");
        XxlJobHelper.log("IncomeStatisticsJobService定时任务结束");
    }


}
