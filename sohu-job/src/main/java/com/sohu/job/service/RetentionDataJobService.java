package com.sohu.job.service;

import cn.hutool.core.date.DateUtil;
import com.sohu.admin.api.RemoteAdminService;
import com.sohu.report.api.vo.SohuAgentRetentionVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户留存定时任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RetentionDataJobService {

    @DubboReference
    private RemoteAdminService remoteAdminService;

    /**
     * 每天执行一次
     *
     * @throws Exception
     */
    @XxlJob(value = "RetentionDataJobHandler", init = "init", destroy = "destroy")
    public void retentionDataJobHandler() throws Exception {
        remoteAdminService.saveUserRetentionStats();
    }

    public void init() {
        log.info("RetentionDataJobService定时任务启动");
        XxlJobHelper.log("RetentionDataJobService定时任务启动");
    }

    public void destroy() {
        log.info("RetentionDataJobService定时任务结束");
        XxlJobHelper.log("RetentionDataJobService定时任务结束");
    }
}
