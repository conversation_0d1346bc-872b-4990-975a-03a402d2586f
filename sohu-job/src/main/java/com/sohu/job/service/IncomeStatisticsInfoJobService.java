package com.sohu.job.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sohu.pay.api.RemoteIndependentOrderService;
import com.sohu.pay.api.model.SohuIndependentOrderModel;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收益明细统计任务
 *
 * @Author: leibo
 * @Date: 2025/6/9 11:26
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomeStatisticsInfoJobService {

    @DubboReference
    private RemoteIndependentOrderService independentOrderService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

    /**
     * 每周执行一次
     *
     * @throws Exception
     */
    @XxlJob(value = "incomeStatisticsInfoJobHandler", init = "init", destroy = "destroy")
    public void incomeStatisticsInfoJobHandler() throws Exception {
        Long id = 0L;
        while (true) {
            List<SohuIndependentOrderModel> orderModelList = independentOrderService.queryListForId(id);
            if (CollectionUtils.isEmpty(orderModelList)) {
                return;
            }
            for (SohuIndependentOrderModel orderModel : orderModelList) {
                MqMessaging mqCancelMessaging = new MqMessaging(orderModel.getOrderNo(), MqKeyEnum.INCOME_INFO.getKey());
                remoteStreamMqService.sendDelayMsg(mqCancelMessaging, 2L);
            }
            id = orderModelList.get(orderModelList.size() - 1).getId();
        }
    }

    public void init() {
        log.info("IncomeStatisticsInfoJobService定时任务启动");
        XxlJobHelper.log("IncomeStatisticsInfoJobService定时任务启动");
    }

    public void destroy() {
        log.info("IncomeStatisticsInfoJobService定时任务结束");
        XxlJobHelper.log("IncomeStatisticsInfoJobService定时任务结束");
    }
}
