package com.sohu.shopgoods.api.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 购物车详情响应对象
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SohuCartInfoModel implements Serializable {

    private static final long serialVersionUID = 3558884699193209193L;

    /**
     * 购物车表ID
     */
    private Long id;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品属性
     */
    private String productAttrUnique;

    /**
     * 商品数量
     */
    private Integer cartNum;

    /**
     * 商品图片
     */
    private String image;

    /**
     * 商品名称
     */
    private String storeName;

    /**
     * 商品规格id
     */
    private Long attrId;

    /**
     * 商品属性索引值 (attr_value|attr_value[|....])
     */
    private String suk;

    /**
     * sku价格
     */
    private BigDecimal price;

    /**
     * 商品是否有效
     */
    private Boolean attrStatus;

    /**
     * sku库存
     */
    private Integer stock;

    /**
     * 状态（0：未上架，1：上架）
     */
    private Boolean isShow;

    /**
     * 站点类型 1 城市站 2 行业站
     */
    private Integer siteType;

    /**
     * 站点Id
     */
    private Long siteId;

}
