package com.sohu.report.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 代理商看板收益统计
 *
 * @author: kz
 * @date: 2025-6-8 11:26:46
 */
@Data
public class SohuAgentIncomeStatisticsVo implements Serializable {

    @Schema(name = "todayTotalIncome", description = "今日收益", example = "888")
    private BigDecimal todayTotalIncome;

    @Schema(name = "yesterdayTotalIncome", description = "昨日收益", example = "777")
    private BigDecimal yesterdayTotalIncome;

    @Schema(name = "todayInviteNum", description = "今日邀请数", example = "666")
    private long todayInviteNum;

    @Schema(name = "yesterdayInviteNum", description = "昨日邀请数", example = "555")
    private long yesterdayInviteNum;

    @Schema(name = "todayInviteBindNum", description = "今日邀请绑定数", example = "444")
    private long todayInviteBindNum;

    @Schema(name = "yesterdayInviteBindNum", description = "昨日邀请绑定数", example = "333")
    private long yesterdayInviteBindNum;

    @Schema(name = "totalIncome", description = "总收益", example = "2222")
    private BigDecimal totalIncome;

    @Schema(name = "lastMonthIncome", description = "上月收益", example = "1111")
    private BigDecimal lastMonthIncome;

    @Schema(name = "walletBalance", description = "可提现余额", example = "999")
    private BigDecimal walletBalance;

}
