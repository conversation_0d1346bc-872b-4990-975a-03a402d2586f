package com.sohu.report.api;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.report.api.bo.SohuUserIncomeStatisticsInfoBo;
import com.sohu.report.api.vo.SohuUserIncomeStatisticsInfoVo;

import java.util.Date;
import java.util.List;

/**
 * 数据报表统计
 *
 * @Author: leibo
 * @Date: 2025/5/30 09:01
 **/
public interface RemoteIncomeStatisticsService {

    /**
     * 处理收益统计
     */
    void handleStatistics();

    /**
     * 处理按天收益统计
     */
    void handleDayStatistics();

    /**
     * 查询用户收益列表-分页
     *
     * @param bo
     * @return
     */
    TableDataInfo<SohuUserIncomeStatisticsInfoVo> queryIncomePageList(SohuUserIncomeStatisticsInfoBo bo, PageQuery pageQuery);

    /**
     * 查询用户收益列表
     *
     * @param bo
     * @return
     */
    List<SohuUserIncomeStatisticsInfoVo> queryIncomeList(SohuUserIncomeStatisticsInfoBo bo);

    /**
     * 获取用户收益详情
     *
     * @param id
     * @return
     */
    SohuUserIncomeStatisticsInfoVo queryIncomeById(Long id);

    /**
     * 代理服务商最近到账收益
     *
     * @return {@link List}
     */
    List<SohuUserIncomeStatisticsInfoVo> incomeNewest();

    /**
     * 同步处理用户到账收益
     *
     * @param orderNo
     */
    void handleIncomeStatistics(String orderNo);
    /**
     * 获取时间区间内的用户收益明细
     */
    List<SohuUserIncomeStatisticsInfoVo> queryUserIncomeByTime(Long userId, Date startTime, Date endTime);

}
