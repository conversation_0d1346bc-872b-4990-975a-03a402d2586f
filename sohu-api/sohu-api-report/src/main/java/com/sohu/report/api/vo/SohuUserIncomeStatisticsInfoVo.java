package com.sohu.report.api.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用户收益明细视图对象
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserIncomeStatisticsInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 站长ID
     */
    @ExcelProperty(value = "站长ID")
    private Long userId;

    /**
     * 角色类型
     */
    @ExcelProperty(value = "角色类型")
    private String roleType;

    /**
     * 行业ID或城市站点ID
     */
    @ExcelProperty(value = "行业ID或城市站点ID")
    private Long stationId;

    /**
     * 业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧
     */
    @ExcelProperty(value = "业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧")
    private String busyType;

    /**
     * 业务明细名称
     */
    @ExcelProperty(value = "业务明细名称")
    private String busyInfoName;

    /**
     * 收益类型  1.拉新  2.分红
     */
    @ExcelProperty(value = "收益类型  1.拉新  2.分红")
    private String incomeType;

    /**
     * 到账收益
     */
    @ExcelProperty(value = "到账收益")
    private BigDecimal income;

    /**
     * 消费者id
     */
    @ExcelProperty(value = "消费者id")
    private Long consumerUserId;

    /**
     * 消费者对应的渠道id
     */
    @ExcelProperty(value = "消费者对应的渠道id")
    private Long consumerChannelId;

    /**
     * 消费者对应的渠道名称
     */
    @ExcelProperty(value = "消费者对应的渠道名称")
    private String consumerChannelName;

    /**
     * 订单消费金额
     */
    @ExcelProperty(value = "订单消费金额")
    private String orderAmount;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 第三方交易单号
     */
    @ExcelProperty(value = "第三方交易单号")
    private String tradeNo;

    /**
     * 下单时间
     */
    @ExcelProperty(value = "下单时间")
    private Date orderTime;

    /**
     * 收益到账时间
     */
    @ExcelProperty(value = "收益到账时间")
    private Date incomeTime;


}
