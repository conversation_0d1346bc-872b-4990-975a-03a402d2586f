package com.sohu.report.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户收益概览
 *
 * @Author: leibo
 * @Date: 2025/6/4 12:03
 **/
@Data
public class SohuUserIncomeOverviewVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总收益 = 累计到账收益 + 待入账收益
     */
    private BigDecimal totalIncome;
    /**
     * 累计到账收益 = 已分账收益
     */
    private BigDecimal receiveIncome;
    /**
     * 待入账收益 = 待分账收益
     */
    private BigDecimal waitIncome;
    /**
     * 分红收益
     */
    private BigDecimal dividendIncome;
    /**
     * 拉新收益
     */
    private BigDecimal inviteUserIncome;
    /**
     * 业务收益数组
     */
    private List<BusyIncomeVo> busyIncomeList;

    /**
     * 业务收益对象
     */
    @Data
    public static class BusyIncomeVo {
        /**
         * 业务类型
         */
        private String busyType;
        /**
         * 收益金额
         */
        private BigDecimal incomeAmount;
    }
}
