package com.sohu.report.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户收益top对象
 *
 * @Author: leibo
 * @Date: 2025/6/6 09:20
 **/
@Data
public class SohuUserIncomeTopVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户top
     */
    private List<IncomeUserTopVo> incomeUserTopList;

    /**
     * 业务top
     */
    private List<IncomeBusyTopVo> incomeBusyTopList;

    /**
     * 渠道top
     */
    private List<IncomeChannelTopVo> incomeChannelTopList;

    /**
     * 用户top对象
     */
    @Data
    public static class IncomeUserTopVo {
        /**
         * 用户id
         */
        private Long userId;
        /**
         * 用户名称
         */
        private String userName;
        /**
         * 到账金额
         */
        private BigDecimal amount;
    }

    /**
     * 业务top对象
     */
    @Data
    public static class IncomeBusyTopVo {
        /**
         * 业务类型
         */
        private String busyType;
        /**
         * 业务名称
         */
        private String busyName;
        /**
         * 到账金额
         */
        private BigDecimal amount;
    }

    /**
     * 渠道top对象
     */
    @Data
    public static class IncomeChannelTopVo {
        /**
         * 渠道id
         */
        private Long channelId;
        /**
         * 渠道名称
         */
        private String channelName;
        /**
         * 到账金额
         */
        private BigDecimal amount;
    }

}



