package com.sohu.report.api.bo;

import cn.hutool.core.date.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础时间参数
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class SohuBaseDateBo implements Serializable {

    @Schema(name = "startDate", description = "开始时间", example = "2025-05-31 00:00:00")
    private String startDate;

    @Schema(name = "endDate", description = "结束时间", example = "2025-05-31 23:59:59")
    private String endDate;

    /**
     * 初始化时间
     */
    public void initDateIfNull(){
        if (endDate == null) {
            // 设置 endDate 为当天的 23:59:59
            endDate = DateUtil.endOfDay(DateUtil.date()).toString("yyyyMMdd");
        }
        if (startDate == null) {
            // 获取 endDate 前7天的 00:00:00
            startDate = DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.parse(endDate), -7)).toString("yyyyMMdd");
        }
    }

}
