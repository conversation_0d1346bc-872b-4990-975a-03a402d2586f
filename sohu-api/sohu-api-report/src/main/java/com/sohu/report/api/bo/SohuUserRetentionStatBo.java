package com.sohu.report.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 用户留存统计业务对象
 *
 * <AUTHOR>
 * @date 2025-06-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuUserRetentionStatBo extends SohuEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 服务商用户id/站长id
     */
    @NotNull(message = "服务商用户id/站长id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 邀请日期（用户首次邀请日期，队列基准日）
     */
    @NotNull(message = "邀请日期（用户首次邀请日期，队列基准日）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date inviteDate;

    /**
     * 留存天数（例如：1, 2, 7, 14, 30）
     */
    @NotNull(message = "留存天数（例如：1, 2, 7, 14, 30）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long retentionDay;

    /**
     * 在该留存天数仍活跃的客户数量
     */
    @NotNull(message = "在该留存天数仍活跃的客户数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long retainedCount;

}
