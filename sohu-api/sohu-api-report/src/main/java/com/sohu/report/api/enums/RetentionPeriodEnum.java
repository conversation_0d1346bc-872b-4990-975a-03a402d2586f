package com.sohu.report.api.enums;

import com.sohu.report.api.vo.SohuAgentRetentionVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 客户留存表格枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RetentionPeriodEnum {
    DAY_1_AFTER(1, "1天后", SohuAgentRetentionVo::setDay1After, SohuAgentRetentionVo::getDay1After),
    DAY_2_AFTER(2, "2天后", SohuAgentRetentionVo::setDay2After, SohuAgentRetentionVo::getDay2After),
    DAY_3_AFTER(3, "3天后", SohuAgentRetentionVo::setDay3After, SohuAgentRetentionVo::getDay3After),
    DAY_4_AFTER(4, "4天后", SohuAgentRetentionVo::setDay4After, SohuAgentRetentionVo::getDay4After),
    DAY_5_AFTER(5, "5天后", SohuAgentRetentionVo::setDay5After, SohuAgentRetentionVo::getDay5After),
    DAY_6_AFTER(6, "6天后", SohuAgentRetentionVo::setDay6After, SohuAgentRetentionVo::getDay6After),
    DAY_7_AFTER(7, "7天后", SohuAgentRetentionVo::setDay7After, SohuAgentRetentionVo::getDay7After),
    DAY_14_AFTER(14, "14天后", SohuAgentRetentionVo::setDay14After, SohuAgentRetentionVo::getDay14After),
    DAY_30_AFTER(30, "30天后", SohuAgentRetentionVo::setDay30After, SohuAgentRetentionVo::getDay30After);

    private final int days;
    private final String description;
    private final BiConsumer<SohuAgentRetentionVo, Long> setter;
    private final Function<SohuAgentRetentionVo, Long> getter;

    /**
     * 将留存数量设置到对应的 RetentionVo 字段上
     * @param retentionVo 留存数据VO
     * @param count 留存数量
     */
    public void setRetentionValue(SohuAgentRetentionVo retentionVo, Long count) {
        this.setter.accept(retentionVo, count);
    }

    /**
     * 从 RetentionVo 中获取对应留存周期的数量
     * @param retentionVo 留存数据VO
     * @return 留存数量
     */
    public Long getRetentionValue(SohuAgentRetentionVo retentionVo) {
        return this.getter.apply(retentionVo);
    }
}