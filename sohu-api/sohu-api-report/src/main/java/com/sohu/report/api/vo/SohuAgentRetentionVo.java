package com.sohu.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 客户留存数据视图对象
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
public class SohuAgentRetentionVo implements Serializable {
    @Schema(name = "date", description = "日期", example = "2025-05-31")
    private String date;

    @Schema(name = "invitedClientCount", description = "邀请客户数", example = "100")
    private Long invitedClientCount;

    @Schema(name = "day1After", description = "1天后留存", example = "100")
    private Long day1After;

    @Schema(name = "day2After", description = "2天后留存", example = "90")
    private Long day2After;

    @Schema(name = "day3After", description = "3天后留存", example = "80")
    private Long day3After;

    @Schema(name = "day4After", description = "4天后留存", example = "70")
    private Long day4After;

    @Schema(name = "day5After", description = "5天后留存", example = "60")
    private Long day5After;

    @Schema(name = "day6After", description = "6天后留存", example = "50")
    private Long day6After;

    @Schema(name = "day7After", description = "7天后留存", example = "40")
    private Long day7After;

    @Schema(name = "day14After", description = "14天后留存", example = "100")
    private Long day14After;

    @Schema(name = "day30After", description = "30天后留存", example = "80")
    private Long day30After;

}