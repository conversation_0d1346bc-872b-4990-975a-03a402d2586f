package com.sohu.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/26 14:11
 */
@Data
@Builder
public class SohuLineInviteVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "x", description = "x轴", example = "20250524")
    private String x;

    @Schema(name = "inviteY", description = "y轴-邀请", example = "100人")
    private String inviteY;

    @Schema(name = "bandY", description = "y轴-绑定", example = "50人")
    private String bandY;

}
