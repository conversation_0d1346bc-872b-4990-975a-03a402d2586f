package com.sohu.report.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 用户收益周统计视图对象
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserIncomeStatisticsWeekVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 站长ID
     */
    @ExcelProperty(value = "站长ID")
    private Long userId;

    /**
     * 角色类型
     */
    @ExcelProperty(value = "角色类型")
    private String roleType;

    /**
     * 行业ID或城市站点ID
     */
    @ExcelProperty(value = "行业ID或城市站点ID")
    private Long stationId;

    /**
     * 业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧
     */
    @ExcelProperty(value = "业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧")
    private String busyType;

    /**
     * 拉新用户角色  暂定
     */
    @ExcelProperty(value = "拉新用户角色  暂定")
    private String inviteRole;

    /**
     * 总收益
     */
    @ExcelProperty(value = "总收益")
    private BigDecimal totalIncome;

    /**
     * 拉新收益
     */
    @ExcelProperty(value = "拉新收益")
    private BigDecimal inviteIncome;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    private BigDecimal tradeAmount;

    /**
     * 订单量
     */
    @ExcelProperty(value = "订单量")
    private Long orderNum;

    /**
     * 周数
     */
    @ExcelProperty(value = "周数")
    private String weekDate;

    /**
     * 邀请绑定人数
     */
    private Long inviteBindNum;

    /**
     * 邀请人数
     */
    private Long inviteNum;

}
