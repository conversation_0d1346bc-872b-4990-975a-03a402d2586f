package com.sohu.report.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 站点报表数据入参
 *
 * <AUTHOR>
 */
@Data
public class SohuStationReportBo extends SohuBaseDateBo {

    @Schema(name = "siteId", description = "站点ID", example = "1", requiredMode = Schema.RequiredMode.AUTO)
    private Long siteId;

    @Schema(name = "siteCate", description = "站点类型（true-站点，false-行业）", example = "true", requiredMode = Schema.RequiredMode.AUTO)
    private Boolean siteCate;

    @Schema(name = "roleType", description = "角色类型", example = "cityStationAgent", requiredMode = Schema.RequiredMode.AUTO)
    private String roleType;

}
