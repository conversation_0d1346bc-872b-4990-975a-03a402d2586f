package com.sohu.report.api.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 用户收益统计视图对象
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserIncomeStatisticsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 站长ID
     */
    @ExcelProperty(value = "站长ID")
    private Long userId;

    /**
     * 角色类型
     */
    @ExcelProperty(value = "角色类型")
    private String roleType;

    /**
     * 行业ID或城市站点ID
     */
    @ExcelProperty(value = "行业ID或城市站点ID")
    private Long stationId;

    /**
     * 累计到账收益
     */
    @ExcelProperty(value = "累计到账收益")
    private BigDecimal receiveIncome = BigDecimal.ZERO;

    /**
     * 待入账收益
     */
    @ExcelProperty(value = "待入账收益")
    private BigDecimal waitIncome = BigDecimal.ZERO;

    /**
     * 待提现收益
     */
    @ExcelProperty(value = "待提现收益")
    private BigDecimal waitWithdrawal = BigDecimal.ZERO;

    /**
     * 已提现收益
     */
    @ExcelProperty(value = "已提现收益")
    private BigDecimal alreadyWithdrawal = BigDecimal.ZERO;

    /**
     * 今日收益
     */
    @ExcelProperty(value = "今日收益")
    private BigDecimal todayIncome = BigDecimal.ZERO;

    /**
     * 昨日收益
     */
    @ExcelProperty(value = "昨日收益")
    private BigDecimal yesterdayIncome = BigDecimal.ZERO;

    /**
     * 昨日交易金额
     */
    @ExcelProperty(value = "昨日交易金额")
    private BigDecimal yesterdayTradeAmount = BigDecimal.ZERO;

    /**
     * 昨日订单量
     */
    @ExcelProperty(value = "昨日订单量")
    private Long yesterdayOrderNum = 0L;

}
