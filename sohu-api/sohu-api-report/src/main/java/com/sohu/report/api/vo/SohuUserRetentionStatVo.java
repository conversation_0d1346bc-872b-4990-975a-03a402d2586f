package com.sohu.report.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 用户留存统计视图对象
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ExcelIgnoreUnannotated
public class SohuUserRetentionStatVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 服务商用户id/站长id
     */
    @ExcelProperty(value = "服务商用户id/站长id")
    private Long userId;

    /**
     * 邀请日期（用户首次邀请日期，队列基准日）
     */
    @ExcelProperty(value = "邀请日期", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "用户首次邀请日期，队列基准日")
    private Date inviteDate;

    /**
     * 留存天数（例如：1, 2, 7, 14, 30）
     */
    @ExcelProperty(value = "留存天数", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "例如：1,,2=,,7=,,1=4,,3=0")
    private Integer retentionDay;

    /**
     * 在该留存天数仍活跃的客户数量
     */
    @ExcelProperty(value = "在该留存天数仍活跃的客户数量")
    private Long retainedCount;

    /**
     * 邀请客户数
     */
    @ExcelProperty(value = "邀请客户数")
    private Long inviteCount;

}
