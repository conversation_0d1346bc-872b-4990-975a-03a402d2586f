package com.sohu.report.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 用户收益月统计业务对象
 *
 * <AUTHOR>
 * @date 2025-05-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuUserIncomeStatisticsMonthBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 站长ID
     */
    @NotNull(message = "站长ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 角色类型
     */
    @NotBlank(message = "角色类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String roleType;

    /**
     * 行业ID或城市站点ID
     */
    @NotNull(message = "行业ID或城市站点ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long stationId;

    /**
     * 业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧
     */
    @NotBlank(message = "业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧不能为空", groups = {AddGroup.class, EditGroup.class})
    private String busyType;

    /**
     * 拉新用户角色  暂定
     */
    @NotBlank(message = "拉新用户角色  暂定不能为空", groups = {AddGroup.class, EditGroup.class})
    private String inviteRole;

    /**
     * 总收益
     */
    @NotNull(message = "总收益不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal totalIncome;

    /**
     * 拉新收益
     */
    @NotNull(message = "拉新收益不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal inviteIncome;

    /**
     * 交易金额
     */
    @NotNull(message = "交易金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal tradeAmount;

    /**
     * 订单量
     */
    @NotNull(message = "订单量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderNum;

    /**
     * 月份
     */
    @NotNull(message = "月份不能为空", groups = {AddGroup.class, EditGroup.class})
    private String monthDate;

    @Schema(name = "startDate", description = "开始时间", example = "2025-05-31 00:00:00")
    private String startDate;

    @Schema(name = "endDate", description = "结束时间", example = "2025-05-31 23:59:59")
    private String endDate;

//    public SohuUserIncomeStatisticsMonthBo() {
//    }
}
