package com.sohu.report.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.math.BigDecimal;
import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 用户收益明细业务对象
 *
 * <AUTHOR>
 * @date 2025-06-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuUserIncomeStatisticsInfoBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 站长ID
     */
    @NotNull(message = "站长ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 角色类型
     */
    @NotBlank(message = "角色类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String roleType;

    /**
     * 行业ID或城市站点ID
     */
    @NotNull(message = "行业ID或城市站点ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long stationId;
    /**
     * 类型
     */
    private Integer stationType;
    /**
     * 业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧
     */
    @NotBlank(message = "业务类型 BusyTask 愿望 Goods 商品 Novel 小说 ShortPlay 短剧不能为空", groups = { AddGroup.class, EditGroup.class })
    private String busyType;

    /**
     * 业务明细名称
     */
    @NotBlank(message = "业务明细名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String busyInfoName;

    /**
     * 收益类型  1.拉新  2.分红
     */
    @NotBlank(message = "收益类型  1.拉新  2.分红不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer incomeType;

    /**
     * 到账收益
     */
    @NotNull(message = "到账收益不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal income;

    /**
     * 消费者id
     */
    @NotNull(message = "消费者id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long consumerUserId;

    /**
     * 消费者对应的渠道id
     */
    @NotNull(message = "消费者对应的渠道id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long consumerChannelId;

    /**
     * 消费者对应的渠道名称
     */
    @NotBlank(message = "消费者对应的渠道名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String consumerChannelName;

    /**
     * 订单消费金额
     */
    @NotBlank(message = "订单消费金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal orderAmount;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 第三方交易单号
     */
    @NotBlank(message = "第三方交易单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tradeNo;

    /**
     * 下单时间
     */
    @NotNull(message = "下单时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date orderTime;

    /**
     * 收益到账时间
     */
    @NotNull(message = "收益到账时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date incomeTime;

    private String delFlag;

    private Long independentOrderId;

}
