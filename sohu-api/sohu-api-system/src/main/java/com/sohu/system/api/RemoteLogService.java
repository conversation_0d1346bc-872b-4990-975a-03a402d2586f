package com.sohu.system.api;

import com.sohu.system.api.domain.SysLogininfor;
import com.sohu.system.api.domain.SysOperLog;

import java.util.Collection;
import java.util.Date;

/**
 * 日志服务
 *
 * <AUTHOR> Li
 */
public interface RemoteLogService {

    /**
     * 保存系统日志
     *
     * @param sysOperLog 日志实体
     * @return 结果
     */
    Boolean saveLog(SysOperLog sysOperLog);

    /**
     * 保存访问记录
     *
     * @param sysLogininfor 访问实体
     * @return 结果
     */
    Boolean saveLogininfor(SysLogininfor sysLogininfor);

    /**
     * 根据用户id集合以及时间获取在线用户数
     */
    Long getOnlineUserNumByUserIdsAndTime(Collection<Long> userIds, Date startTime, Date endTime);
}
