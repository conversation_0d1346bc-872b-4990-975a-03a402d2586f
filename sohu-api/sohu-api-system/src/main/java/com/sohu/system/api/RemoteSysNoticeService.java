package com.sohu.system.api;

import java.util.Date;

public interface RemoteSysNoticeService {

    /**
     * 发送公告
     *
     * @param receUserId 接收人ID
     * @param noticeRole 接收人角色 {@link com.sohu.common.core.enums.RoleCodeEnum}
     * @param noticeDate 大于公告创建的时间
     * @return {@link Boolean}
     */
    Boolean sendNotice(Long receUserId, String noticeRole, Date noticeDate);

    /**
     * 发送系统账单公告
     */
    void sendUserIncomeStatisticsNotice();

}
