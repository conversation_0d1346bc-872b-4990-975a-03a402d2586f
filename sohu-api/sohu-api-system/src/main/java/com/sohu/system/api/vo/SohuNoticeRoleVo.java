package com.sohu.system.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class SohuNoticeRoleVo implements Serializable {

    @Schema(name = "code", description = "角色编码", example = "mcn")
    private String code;

    @Schema(name = "name", description = "角色名字", example = "MCN机构功能")
    private String name;

    @Schema(name = "subName", description = "角色副名称(简称)", example = "MCN机构")
    private String subName;

}
