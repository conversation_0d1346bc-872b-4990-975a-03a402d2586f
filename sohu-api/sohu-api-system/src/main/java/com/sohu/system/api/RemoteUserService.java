package com.sohu.system.api;

import com.sohu.common.core.exception.user.UserException;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.system.api.bo.SohuUserAppConfigSortBo;
import com.sohu.system.api.bo.SysUserQueryBo;
import com.sohu.system.api.domain.SysUser;
import com.sohu.system.api.domain.SysUserInfo;
import com.sohu.system.api.domain.UserVo;
import com.sohu.system.api.model.XcxLoginUser;
import com.sohu.system.api.vo.SohuUserAppConfigVo;
import com.sohu.system.api.vo.SohuUserOrderInfoVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户服务
 *
 * <AUTHOR> Li
 */
public interface RemoteUserService {

    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @return 结果
     */
    LoginUser getUserInfo(String username) throws UserException;

    /**
     * 通过手机号查询用户信息，找不到会报错
     *
     * @param phonenumber 手机号
     * @return 结果
     */
    LoginUser getUserInfoByPhonenumber(String phonenumber) throws UserException;

    /**
     * 通过手机号查询用户信息，找不到不会报错
     *
     * @param phonenumber 手机号
     * @return 结果
     */
    LoginUser getUserInfoByPhone(String phonenumber);

    /**
     * 通过邮箱查询用户信息-会报错
     *
     * @param email 邮箱
     * @return 结果
     */
    LoginUser getUserInfoByEmail(String email) throws UserException;

    /**
     * 通过邮箱查询用户信息-不会报错
     *
     * @param email 邮箱
     * @return 结果
     */
    LoginUser getUserInfoByMail(String email);

    /**
     * 通过openid查询用户信息
     *
     * @param openid openid
     * @return 结果
     */
    XcxLoginUser getUserInfoByOpenid(String openid) throws UserException;

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    Long registerUserInfo(SysUser sysUser);

    /**
     * 通过userId查询用户账户
     *
     * @param userId 用户id
     * @return 结果
     */
    String selectUserNameById(Long userId);

    /**
     * 通过userId查询用户
     *
     * @param userId 用户id
     * @return {@link LoginUser}
     */
    LoginUser selectById(Long userId);

    /**
     * 通过userId查询用户(已删除的也查)
     *
     * @param userId 用户id
     * @return {@link LoginUser}
     */
    LoginUser selectAllUserById(Long userId);

    /**
     * 通过用户账号查询用户
     *
     * @param userName 用户账号
     * @return {@link LoginUser}
     */
    LoginUser selectByUserName(String userName);

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return {@link LoginUser}
     */
    LoginUser selectByPhonenumber(String phonenumber);

    /**
     * 返回map形式,会返回权限字段
     *
     * @param userIds
     * @return
     */
    Map<Long, LoginUser> queryMap(Collection<Long> userIds);

    /**
     * 更新用户信息
     *
     * @param sysUser
     */
    void update(SysUser sysUser);

    /**
     * 删除用户信息
     */
    void deleteById(Long userId);

    /**
     * 更新邀请码
     *
     * @param userId     用户ID
     * @param inviteCode 邀请码
     */
    void updateInviteCode(Long userId, String inviteCode);

    /**
     * 用户列表
     *
     * @param notInUserIds 需要排除的用户ID
     * @param keyword      搜索关键词
     * @param pageNum      页码
     * @param pageSize     数量
     * @return {@link List}
     */
    TableDataInfo<UserVo> page(Collection<Long> notInUserIds, String keyword, Boolean queryFriend, Integer pageNum, Integer pageSize);

    /**
     * 建议迁移重构，已准备的位置com.sohu.system.service.ISysUserBizService
     *
     * @param user
     * @return
     */
    LoginUser buildLoginUser(SysUser user);

    /**
     * 建议迁移重构，已准备的位置com.sohu.system.service.ISysUserBizService
     *
     * @param user
     * @return
     */
    LoginUser buildUser(SysUser user);

    List<LoginUser> selectList(Collection<Long> userIds);

    List<LoginUser> idsByName(String name);

    /**
     * 查询用户，不会查询权限等数据
     *
     * @param userId
     * @return
     */
    LoginUser queryById(Long userId);

    /**
     * 返回map形式,不会返回权限字段
     *
     * @param name
     * @return {@link Map}
     */
    Map<Long, LoginUser> selectMapByName(String name);

    /**
     * 返回map形式,不会返回权限字段
     *
     * @param userIds 用户id集合
     * @return {@link Map}}
     */
    Map<Long, LoginUser> selectMap(Collection<Long> userIds);

    /**
     * 返回map形式,不会返回权限字段
     *
     * @param userIds 用户id集合
     * @return {@link Map}} 用户ID-用户名称（优先获取昵称）
     */
    Map<Long, String> selectNameMap(Collection<Long> userIds);

    /**
     * 修改密码
     */
    Integer updateUserPwd(Long userId, String password);

    /**
     * 修改支付密码
     */
    Integer updatePayPwd(Long userId, String password);

    /**
     * 新用户判断
     */
    Boolean isNewUser(Long userId);

    /**
     * 查询用户邀请码
     */
    Long selectByCode(String inviteCode);

    /**
     * 通过uid查询用户
     *
     * @param uid
     * @return {@link LoginUser}}
     */
    LoginUser selectByUid(Long uid);

    void updateUser(SysUser sysUser);

    /**
     * 根据用户id强制登出
     *
     * @param userId
     */
    void logoutByUserId(Long userId);

    /**
     * 根据用户id刷新登录缓存
     *
     * @param userId
     */
    void flushLoginCacheByUserId(Long userId);

    /**
     * 绑定MCN机构成员
     */
    @Deprecated
    void bindMcnUser(Long id);

    /**
     * 绑定MCN机构成员
     */
    void bindMcnUser(Long id, Long userId);

    /**
     * 机构名称唯一校验
     */
    Boolean checkUserNameUnique(SysUser user);

    /**
     * 手机号唯一校验
     */
    Boolean checkPhoneUnique(SysUser user);

    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param status 帐号状态（0正常 1停用）
     */
    void updateUserStatus(Long userId, String status);

    /**
     * 精确搜索用户
     *
     * @param keyword 手机号或者userName
     * @return {@link UserVo}
     */
    UserVo search(String keyword);

    /**
     * 根据站点获取用户
     *
     * @param siteIds 站点id
     * @return List<LoginUser>
     */
    List<Long> getUserBySiteId(List<Long> siteIds);

    /**
     * 记录拉新信息
     *
     * @param inviteType     邀请类型
     * @param publishMediaId 发布之前的唯一标识
     */
    @Deprecated
    void saveInviteInfo(String inviteType, String publishMediaId);

    /**
     * 记录拉新信息
     *
     * @param inviteType     邀请类型
     * @param publishMediaId 发布之前的唯一标识
     */
    void saveInviteInfo(String inviteType, String publishMediaId, Long userId);

    /**
     * 实名通过绑定拉新关系
     *
     * @param userId
     */
    void saveInviteInfo(Long userId);

    /**
     * 更新用户邮箱
     *
     * @param phoneNumber 用户手机号
     * @param email       邮箱
     * @return
     */
    void updateEmail(String phoneNumber, String email);

    /**
     * 根据用户名查询用户id
     *
     * @param nickname 用户名
     * @return {@link List}
     */
    List<Long> queryByUserName(String nickname);

    /**
     * 根据手机号模糊查询返回用户id
     *
     * @param phoneNumber
     * @return
     */
    List<Long> queryByLikePhonenumber(String phoneNumber);

    /**
     * 初始化智能推荐用户
     */
    Boolean initAirecUsers();

    /**
     * 修改支付密码发送消息通知
     */
    void sendMsgOfUpdatePayPwd(Long userId);

    /**
     * 更新用户拓展信息
     *
     * @return
     */
    void updateUserInfo(SysUserInfo sysUserInfo);

    /**
     * 获取用户拓展信息
     */
    SysUserInfo getUserInfo(Long userId);

    /**
     * 查询用户平台角色
     *
     * @param userId 用户ID
     * @return {@link List}
     */
    List<String> queryPlatformRole(Long userId);

    /**
     * 获取时间区间内创建的人数
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Long getUserNumByCreateTime(Date startTime, Date endTime);

    /**
     * 获取平台用户总数
     *
     * @return
     */
    Long getUserTotalNum();

    /**
     * 获取用户常用工具配置
     *
     * @param userId
     * @return
     */
    List<SohuUserAppConfigVo> getAppConfig(Long userId, Integer type, String platform, String version, String channel);

    /**
     * 用户配置排序
     *
     * @param list
     * @return
     */
    Boolean configSort(List<SohuUserAppConfigSortBo> list, Long userId, String platform);

    /**
     * 注册IM用户
     *
     * @param phone
     * @param serverCode
     * @return
     */
    Boolean registerImUser(String phone, String serverCode);

    /**
     * 获取租户注册人数
     *
     * @param tenantIds
     * @return
     */
    Map<Long, Integer> getTenantRegisterNum(List<Long> tenantIds);

    /**
     * 查询用户id集合
     *
     * @param bo
     * @return
     */
    List<Long> queryUserIdByBo(SysUserQueryBo bo);

    /**
     * 绑定邀请人信息
     *
     * @param userId
     * @param inviteUserId
     */
    void updateInviteUserId(Long userId, Long inviteUserId);

    /**
     * 查询角色用户收入
     *
     * @param userId            用户Id
     * @param siteType          站点类型
     * @param siteId            站点Id
     * @param userRole          用户查询角色 {@link com.sohu.common.core.enums.UserRoleEnum}
     * @param independentStatus 分账状态
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return
     */
    BigDecimal getUserIncome(Long userId, Integer siteType, Long siteId, String userRole, Integer independentStatus, Date startTime, Date endTime);

    /**
     * 查询角色用户提现总额
     *
     * @param userId    用户Id
     * @param siteType  站点类型
     * @param siteId    站点Id
     * @param userRole  用户查询角色 {@link com.sohu.common.core.enums.UserRoleEnum}
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    BigDecimal getUserWithdrawal(Long userId, Integer siteType, Long siteId, String userRole, Date startTime, Date endTime);


    /**
     * 获取用户拉新收益
     *
     * @param userId            用户Id
     * @param siteType          站点类型
     * @param siteId            站点Id
     * @param independentObject 分账对象 {@link  com.sohu.common.core.enums.SohuIndependentObject}
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return
     */
    BigDecimal getUserInviteIncome(Long userId, Integer siteType, Long siteId, String independentObject, Date startTime, Date endTime);


    /**
     * 获取用户订单量与交易金额
     *
     * @param userId            用户Id
     * @param siteType          站点类型
     * @param siteId            站点Id
     * @param independentObject 分账对象 {@link  com.sohu.common.core.enums.SohuIndependentObject}
     * @param tradeType         订单类型
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return
     */
    SohuUserOrderInfoVo getUserOrderInfo(Long userId, Integer siteType, Long siteId, List<String> independentObject, String tradeType, Date startTime, Date endTime);
}
