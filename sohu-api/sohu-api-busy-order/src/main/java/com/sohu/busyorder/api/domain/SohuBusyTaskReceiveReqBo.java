package com.sohu.busyorder.api.domain;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商单接单业务对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuBusyTaskReceiveReqBo extends SohuEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 子任务编号
     */
    @NotNull(message = "子任务编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String taskNumber;

    /**
     * 主任务编号
     */
    private String masterTaskNumber;

    /**
     * 子任务状态：OnShelf：上架，OffShelf：下架，CompelOff：强制下架
     */
    private String shelfState;

    /**
     * 是否支付佣金
     */
    private Boolean isIndependent;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布的国家站点
     */
    private Long countrySiteId;

    /**
     * 接单人ID
     */
    private Long userId;

    /**
     * 任务方用户id
     */
    private Long taskUserId;

    /**
     * 站点类型 1 城市站 2 行业站
     */
    private Integer siteType;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 申请理由
     */
    private String applyMsg;

    /**
     * 申请附件
     */
    private String applyAnnex;

    /**
     * 保证金;接单方希望任务方交的保证金
     */
    private BigDecimal amount;

    /**
     * 分销人ID
     */
    private Long sharePerson;

    /**
     * 分销人金额
     */
    private BigDecimal distributionAmount;

    /**
     * 拆单思路ID
     */
    private Long templateId;

    /**
     * 状态;WaitApprove-审核中，Pass-通过，Refuse-审核拒绝，Execute-执行中且发单方已支付保证金，WaitSettle-待结算，OverSettle-已结算即完成，Error-商单异常
     */
    private String state;

    /**
     * 拒绝理由
     */
    private String refuseMsg;

    /**
     * 选择状态：0=淘汰 1=同意
     */
    private Boolean backup;

    /**
     * 任务类型
     */
    private Long type;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 支付渠道：pc、mobile
     */
    private String payChannel;

    /**
     * MCN机构ID(用户ID)
     */
    private Long mcnId;

}
