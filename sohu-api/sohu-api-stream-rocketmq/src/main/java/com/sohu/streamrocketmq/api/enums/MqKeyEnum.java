package com.sohu.streamrocketmq.api.enums;

import lombok.Getter;

/**
 * 延时消息key
 *
 * @Author: leibo
 * @Date: 2025/6/7 09:30
 **/
@Getter
public enum MqKeyEnum {

    TASK_MERCHANT_COUNT("task_merchant_count", "短剧商户数量"),
    TWO("two", "测试two"),
    ACCOUNT_BANK_ADD("account_bank_add", "银行账户添加"),
    YI_MA_REFUND_PAY("yi_ma_refund_pay", "易码退款支付"),
    CANCEL_SHOP_ORDER("cancel_shop_order", "取消店铺订单"),
    TASK_DELAY_ORDER_INDEPENDENT("task_delay_order_independent", "延迟订单独立任务"),
    BUSY_ORDER_REFUND("busy_order_refund", "商单退款退款"),
    BUSY_TASK_SHELF("busy_task_shelf", "商单上架"),
    BUSY_TASK_RECEIVE("busy_task_receive", "商单任务接收"),
    BUSY_TASK_PAY_PARTY("busy_task_pay_party", "商单任务支付"),
    AIREC_BEHAVIOR_ANALYSE("airec_behavior_analyse", "AI行为分析"),
    OPEN_RECEIVE_LOG("open_receive_log", "开放日志"),
    CANCEL_NOVEL_ORDER("cancel_novel_order", "取消小说订单"),
    YI_MA_WITHDRAWAL("yi_ma_withdrawal", "易码提现"),
    DELAY_CONFIRMQUERY("delay_confirmquery", "延迟确认查询"),
    DELAY_CONFIRM_PLATFORM("delay_confirm_platform", "延迟确认平台"),
    DELAY_CONFIRM_REFUND("delay_confirm_refund", "延迟确认退款"),
    DELAY_TASK_UPDATE("delay_task_update", "延迟任务更新"),
    CANCEL_BOND_ORDER("cancel_bond_order", "取消保证金订单"),
    ACCOUNT_UPGRADE("account_upgrade", "账户升级"),
    REFUND_RESULT_QUERY("refund_result_query", "退款结果查询"),
    INCOME_ACCOUNT_PROCESS("income_account_process", "收入账户处理"),
    INCOME_INFO("income_info", "分账明细处理");

    private String key;
    private String message;

    MqKeyEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static MqKeyEnum fromKey(String key) {
        for (MqKeyEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value;
            }
        }
        return null;
    }
}
