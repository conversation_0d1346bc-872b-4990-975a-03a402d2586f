package com.sohu.admin.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 代理首页统计
 */
@Data
public class SohuAgentStatVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 机构名称
     */
    private String institutionName;

    /**
     * 公司名称
     */
    private String merchantName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 代理商类型：SALE-销售类型代理商，FLOW-流量类型代理商，SERVER-服务类型代理商，RESOURCE资源类型代理商
     */
    private String agentType;

    /**
     * 累计收益
     */
    private BigDecimal earnStat;

    /**
     * 昨日收益
     */
    private BigDecimal yesterdayIncome;

    /**
     * 邀请成功人数
     */
    private Long successStat;

    /**
     * 等待加入人数
     */
    private Long waitJoinStat;

    /**
     * 可提现余额
     */
    private BigDecimal walletBalance = BigDecimal.ZERO;

    /**
     * 待提现余额
     */
    private BigDecimal independentBalance = BigDecimal.ZERO;

    /**
     * 活跃人数
     */
    private Long activeUserCount;
}
