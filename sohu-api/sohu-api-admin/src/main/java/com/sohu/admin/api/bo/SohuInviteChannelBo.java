package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.sohu.common.core.web.domain.BaseEntity;

/**
 * 平台拉新渠道业务对象
 *
 * <AUTHOR>
 * @date 2025-06-05
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuInviteChannelBo extends BaseEntity {

    @Schema(name = "id",  description = "ID", example = "1")
    private Long id;

    /**
     * 渠道名称
     */
    @NotBlank(message = "渠道名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(name = "name",  description = "渠道名称", example = "渠道名称")
    private String name;

    @Schema(name = "status",  description = "状态（ENABLE：启用，DISABLE：禁用）", example = "ENABLE")
    private String status;


}
