package com.sohu.admin.api.vo;

import com.sohu.common.core.web.domain.BaseEntity;
import com.sohu.common.core.web.domain.SohuEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 平台拉新渠道视图对象
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
public class SohuInviteChannelVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(name = "id", description = "ID", example = "1")
    private Long id;

    @Schema(name = "name", description = "渠道名称", example = "渠道名称")
    private String name;

    @Schema(name = "status", description = "状态（ENABLE：启用，DISABLE：禁用）", example = "ENABLE")
    private String status;


}
