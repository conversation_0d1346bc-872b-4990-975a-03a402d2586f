package com.sohu.admin.api.bo;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 代理用户记录业务对象
 *
 * <AUTHOR>
 * @date 2024-01-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuAgentUserBo extends SohuEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 用户昵称
     */
    @NotBlank(message = "用户昵称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String merchantName;

    /**
     * 用户手机号
     */
    @NotBlank(message = "用户手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumber;

    /**
     * 用户邮箱
     */
    @NotBlank(message = "用户邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentEmail;

    /**
     * 账号类型（personal=个人  business=企业认证）
     */
    @NotBlank(message = "账号类型（personal=个人  business=企业认证）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountType;

    /**
     * 入驻角色权限字符串【mcn:MCN机构,agent:代理商，professor：拆单方】
     */
    @NotBlank(message = "入驻角色权限字符串【mcn:MCN机构,agent:代理商，professor：拆单方】不能为空", groups = { AddGroup.class, EditGroup.class })
    private String roleKey;

    /**
     * 状态（WAIT_JOIN=待认证，SUCCESS=邀请成功，REFUSE=邀请失败，TIMEOUT=超时未加入）
     */
    @NotBlank(message = "状态（WAIT_JOIN=待认证，SUCCESS=邀请成功，REFUSE=邀请失败，TIMEOUT=超时未加入）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String state;

    /**
     * 开始时间,2023-10-19
     */
    private String startTime;

    /**
     * 结束时间,2023-10-19
     */
    private String endTime;

    /**
     * 绑定开始时间
     */
    private String bindStartTime;
    /**
     * 绑定结束时间
     */
    private String bindEndTime;
    /**
     * 站点Id
     */
    private Long siteId;

    /**
     * 代理角色 cityStationAgent 站长  agent 企业主  provider
     */
    private List<String> agentRoles;
    /**
     * 邀请渠道
     */
    private Long agentChannelId;

}
