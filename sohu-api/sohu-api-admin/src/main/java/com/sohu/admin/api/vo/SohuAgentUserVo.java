package com.sohu.admin.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.annotation.Sensitive;
import com.sohu.common.core.enums.SensitiveStrategy;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 代理用户记录视图对象
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Data
@ExcelIgnoreUnannotated
public class SohuAgentUserVo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ExcelProperty(value = "id")
    @Schema(name = "id", description = "主键ID", example = "1")
    private Long id;

    @Schema(name = "agentId", description = "服务商账号ID", example = "552")
    private Long agentId;

    @Schema(name = "userId", description = "注册用户id", example = "100")
    @ExcelProperty(value = "用户id")
    private Long userId;

    @Schema(name = "merchantName", description = "用户昵称", example = "*********")
    @ExcelProperty(value = "用户昵称")
    private String merchantName;

    @Schema(name = "phoneNumber", description = "用户手机号", example = "***********")
    @ExcelProperty(value = "用户手机号")
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String phoneNumber;

    @Schema(name = "agentEmail", description = "用户邮箱", example = "<EMAIL>")
    @ExcelProperty(value = "用户邮箱")
    private String agentEmail;

    @Schema(name = "accountType", description = "账号类型（personal=个人  business=企业认证）", example = "personal")
    @ExcelProperty(value = "账号类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "p=ersonal=个人,b=usiness=企业认证")
    private String accountType;

    /**
     * 入驻角色权限字符串【MCN:MCN机构,Agent:代理商，Professor：拆单方，Server：服务方，Article：创作者，Conversion：转化方】
     * {@link com.sohu.common.core.enums.RoleCodeEnum}
     */
    @Schema(name = "roleKey", description = "入驻角色权限字符串【MCN:MCN机构,Agent:代理商，Professor：拆单方，Server：服务方，Article：创作者，Conversion：转化方】", example = "article")
    @ExcelProperty(value = "入驻角色权限字符串【mcn:MCN机构,agent:代理商，professor：拆单方】")
    private String roleKey;

    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "W=AIT_JOIN=待认证，SUCCESS=邀请成功，REFUSE=邀请失败，TIMEOUT=超时未加入 EXPIRED 已失效")
    @Schema(name = "state", description = "状态（WAIT_JOIN=待认证，SUCCESS=邀请成功，REFUSE=邀请失败，TIMEOUT=超时未加入 EXPIRED 已失效）", example = "WAIT_JOIN")
    private String state;

    @ExcelProperty(value = "可提现余额")
    @Schema(name = "walletBalance", description = "可提现余额", example = "100.00")
    private BigDecimal walletBalance = BigDecimal.ZERO;

    @ExcelProperty(value = "待提现余额")
    @Schema(name = "independentBalance", description = "待提现余额", example = "100.00")
    private BigDecimal independentBalance = BigDecimal.ZERO;

    @ExcelProperty(value = "银行卡号")
    @Schema(name = "cardNo", description = "银行卡号", example = "****************")
    private String cardNo;

    @Schema(name = "cardNoEncrypt", description = "加密银行卡号", example = "622*********0000")
    @Sensitive(strategy = SensitiveStrategy.BANK_CARD)
    @ExcelProperty(value = "银行卡号")
    private String cardNoEncrypt;

    @Schema(name = "rejectReason", description = "邀请失败原因", example ="该客户已被其它代理邀请关联,不能再被邀请关联")
    private String rejectReason;

    @ExcelProperty(value = "邀请时间")
    @Schema(name = "createTime", description = "邀请时间", example = "2024-01-08 10:10:10")
    private Date createTime;

    @ExcelProperty(value = "完成时间")
    @Schema(name = "finishTime", description = "完成时间", example = "2024-01-08 10:10:10")
    private Date finishTime;

    @ExcelProperty(value = "注册时间")
    @Schema(name = "regTime", description = "注册时间", example = "2024-01-08 10:10:10")
    private Date regTime;

    @Schema(name = "agentChannelId", description = "渠道id", example = "1")
    private Long agentChannelId;

    @Schema(name = "channelName", description = "渠道名称", example = "小红书")
    private String agentChannelName;

    @Schema(name = "nickName", description = "昵称", example = "小红")
    private String nickName;
}
