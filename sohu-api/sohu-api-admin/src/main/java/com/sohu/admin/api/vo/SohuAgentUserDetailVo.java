package com.sohu.admin.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:45
 */
@Data
public class SohuAgentUserDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "baseInfo", description = "用户基本信息", example = "{}")
    private SohuAgentUserVo baseInfo;

    @Schema(name = "consumerInfo", description = "用户消费信息", example = "{}")
    private ConsumerInfo consumerInfo;
    @Schema(name = "independentInfo", description = "用户分账信息", example = "{}")
    private List<IndependentInfo> independentInfo;

    @Data
    public static class ConsumerInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        @Schema(name = "firstConsumeTime", description = "首次消费时间", example = "2025-06-05 15:45:00")
        private Date firstConsumerTime;
        @Schema(name = "firstConsumerAmount", description = "首单消费金额", example = "100.00")
        private BigDecimal firstConsumerAmount;
        @Schema(name = "firstConsumerType", description = "首单消费类型", example = "商单")
        private String firstConsumerType;
        @Schema(name = "bindAfterConsumerDays", description = "注册后至消费天数", example = "5")
        private Long bindAfterConsumerDays;
        @Schema(name = "totalConsumerAmount", description = "累计消费金额", example = "100.00")
        private BigDecimal totalConsumerAmount;
        @Schema(name = "totalConsumerCount", description = "累计消费次数", example = "1")
        private Integer totalConsumerCount;
    }
    @Data
    public static class IndependentInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        @Schema(name = "orderNo", description = "订单号", example = "2025060515450000000000000001")
        private String orderNo;
        @Schema(name = "tradeNo", description = "流水号", example = "2025060515450000000000000001")
        private String tradeNo;
        @Schema(name = "tradeType", description = "分账类型", example = "商单")
        private String tradeType;
        @Schema(name = "independentPrice", description = "分账金额", example = "100.00")
        private BigDecimal independentPrice;
        @Schema(name = "createTime", description = "分账时间", example = "2025-06-05 15:45:00")
        private Date createTime;
    }
}
