package com.sohu.pay.api.bo;

import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/6 11:19
 */
@Data
public class AccountDistributionBo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "shareUserId", description = "分享人Id", example = "1")
    private Long shareUserId;

    @Schema(name = "shareAmount", description = "分销总额", example = "1.00")
    private BigDecimal shareAmount;

    @Schema(name = "chargeFee", description = "手续费", example = "1.00")
    private BigDecimal chargeFee;

    @Schema(name = "templateModel", description = "模板信息")
    private SohuIndependentTemplateModel templateModel;
}
