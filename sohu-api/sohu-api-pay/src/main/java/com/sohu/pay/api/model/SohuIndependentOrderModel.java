package com.sohu.pay.api.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.sohu.common.core.web.domain.SohuEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 第三方分账单
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@Data
public class SohuIndependentOrderModel extends SohuEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 系统-用户id
     */
    private Long userId;
    /**
     * 站点类型 1 城市站 2 行业站
     */
    private Integer siteType;
    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 订单号-子单
     */
    private String orderNo;
    /**
     * 第三方交易单号-唯一
     */
    private String tradeNo;
    /**
     * 分账方对象：平台、分销人、拉新人、国家站长、城市站长
     */
    private String independentObject;
    /**
     * 分账金额-0.00
     */
    private BigDecimal independentPrice;
    /**
     * 分账总金额-0.00
     */
    private BigDecimal independentTotalPrice;
    /**
     * 分账状态：0 未分账  1 已分账  2 分账处理中  3 分账异常  4 已退款
     */
    private Integer independentStatus;
    /**
     * 交易类型（商品，商单）
     */
    private String tradeType;

    /**
     * 商户id
     */
    private Long merId;

    /**
     * 第三方服务费-0.00
     */
    private BigDecimal chargePrice;
    /**
     * 任务金额-0.00
     */
    @Schema(name = "taskFullAmount", description = "任务金额-0.00", example = "0.00")
    private BigDecimal taskFullAmount;

    /**
     * 任务名称
     */
    @Schema(name = "taskTitle", description = "任务名称", example = "任务名称")
    private String taskTitle;
    @Schema(name = "consumerUserId", description = "消费者用户id", example = "1")
    private Long consumerUserId;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

}
