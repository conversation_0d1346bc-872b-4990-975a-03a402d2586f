package com.sohu.pay.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/6 14:36
 */
@Data
public class DelayConfirmqueryBo implements Serializable {

    private static final long serialVersionUID = 1L;

    public DelayConfirmqueryBo(String posSeq, String taskNumber, Boolean suscess, String busyType) {
        this.posSeq = posSeq;
        this.taskNumber = taskNumber;
        this.suscess = suscess;
        this.busyType = busyType;
    }

    public DelayConfirmqueryBo(String busyType,  Long busyCode){
        this.busyType = busyType;
        this.busyCode = busyCode;
    }

    @Schema(title = "posSeq", description = "流水号", example = "FZ88191781889189")
    private String posSeq;

    @Schema(title = "taskNumber", description = "任务号", example = "FZ88191781889189")
    private String taskNumber;

    @Schema(title = "suscess", description = "是否已分账成功", example = "true")
    private Boolean suscess;

    @Schema(title = "busyType", description = "业务类型", example = "FZ88191781889189")
    private String busyType;

    @Schema(title = "busyCode", description = "业务编码", example = "1")
    private Long busyCode;
}
