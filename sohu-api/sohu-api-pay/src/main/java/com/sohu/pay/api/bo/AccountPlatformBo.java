package com.sohu.pay.api.bo;

import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/5 9:23
 */
@Data
public class AccountPlatformBo implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(name = "busyType", description = "业务类型", example = "BusyTask")
    private String busyType;
    @Schema(name = "busyCode", description = "业务编号", example = "123456")
    private Long busyCode;
    @Schema(name = "busyName", description = "业务名称", example = "任务")
    private String busyName;
    @Schema(name = "userId", description = "用户ID", example = "1")
    private Long userId;
    @Schema(name = "siteType", description = "站点类型", example = "1")
    private Integer siteType;
    @Schema(name = "entranceSiteId", description = "入口站点ID", example = "1")
    private Long entranceSiteId;
    @Schema(name = "citySiteId", description = "城市站点ID", example = "1")
    private Long citySiteId;
    @Schema(name = "industryType", description = "行业类型", example = "1")
    private Long industryType;
    @Schema(name = "payPrice", description = "支付金额", example = "1.00")
    private BigDecimal payPrice;
    @Schema(name = "chargeFee", description = "手续费", example = "1.00")
    private BigDecimal chargeFee;
    @Schema(name = "templateModel", description = "模板信息")
    private SohuIndependentTemplateModel templateModel;
    @Schema(name = "orderTime", description = "订单时间", example = "2025-06-05 09:23:00")
    private Date orderTime;
}
