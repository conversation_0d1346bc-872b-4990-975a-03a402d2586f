package com.sohu.pay.api;

import com.sohu.pay.api.model.SohuIndependentOrderModel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 第三方分账单
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
public interface RemoteIndependentOrderService {

    /**
     * 添加账单
     *
     * @param model
     */
    Boolean insert(SohuIndependentOrderModel model);

    /**
     * 批量添加
     */
    Boolean batchInsert(List<SohuIndependentOrderModel> model);

    /**
     * 分账状态：0 未分账  1 已分账  2 分账处理中  3 分账异常  4 已退款
     */
    void updateIndependentStatus(String orderNo, String tradeType, int independentStatus);

    /**
     * 查询分账金额
     *
     * @param orderNo
     * @param tradeType
     * @param userId
     */
    BigDecimal queryAmount(String orderNo, String tradeType, String independentObject, Long userId);

    /**
     * 查询分账记录
     *
     * @param orderNo
     * @param tradeType
     * @return
     */
    List<SohuIndependentOrderModel> queryByOrderNo(String orderNo, String tradeType);

    /**
     * 查询分账记录
     *
     * @param orderNo
     * @param tradeType
     * @return
     */
    List<SohuIndependentOrderModel> queryByOrderNo(String orderNo, String tradeType, Integer independentStatus);

    /**
     * 根据用户id获取分账处理中的金额
     */
    BigDecimal selectIndependentingByUserId(Long userId, Integer siteType, Long siteId, List<String> selectRoles, String independentObject, Integer independentStatus, Date startTime, Date endTime);

    /**
     * 根据用户id获取邀请好友的金额
     */
    BigDecimal selectIndependentInviteByUserId(Long userId);

    /**
     * 分页查询用户的分账记录
     *
     * @param userId            用户ID
     * @param independentObject 分账方对象：平台、分销人、拉新人、国家站长、城市站长 {@link com.sohu.common.core.enums.SohuIndependentObject}
     * @param startTime         开始时间,2023-10-19
     * @param endTime           结束时间,2023-10-19
     * @param pageNum           页码
     * @param pageSize          每页数量
     * @return {@link List}
     */
    Map<String, Object> queryByUserId(Long userId, String independentObject, String startTime, String endTime, Integer pageNum, Integer pageSize);


    /**
     * 查询邀请好友分账金额
     */
    List<SohuIndependentOrderModel> inviteAmount(Long userId);

    /**
     * 分组查询用户分账金额
     *
     * @param userId
     * @param siteId
     * @param selectRoles
     * @return
     */
    List<SohuIndependentOrderModel> groupIndependentByUserId(Long userId, Long siteId, List<String> selectRoles);

    /**
     * 根据用户id查询邀请用户的分账记录
     *
     * @param consumerUserId    消费者ID
     * @param inviteUserId      邀请人ID
     * @param independentObject 邀请人角色
     * @param independentStatus 分账状态
     * @return
     */
    List<SohuIndependentOrderModel> queryByInviteUser(Long consumerUserId, Long inviteUserId, String independentObject, List<Integer> independentStatus);
}
