package com.sohu.pay.api;

import com.sohu.pay.api.bo.DelayConfirmqueryBo;
import com.sohu.pay.api.model.SohuBusyTaskPayModel;
import com.sohu.pay.api.model.SohuBusyTaskPayNewModel;

import java.math.BigDecimal;

/**
 * 结算服务接口
 *
 * <AUTHOR>
 * @date 2025/1/14 15:51
 */
public interface RemotePaySettlementService {

    /**
     * 商单主任务完结
     *
     * @param taskNumber 主任务单号
     * @return
     */
    Boolean overSettle(String taskNumber);

    /**
     * 撤销交易
     *
     * @param taskNumber 主任务单号
     * @return
     */
    Boolean barcodeCancelPay(String taskNumber);

    /**
     * 查询延时分账是否到账
     *
     * @param delayConfirmqueryBo
     * @return
     */
    Boolean delayConfirmquery(DelayConfirmqueryBo delayConfirmqueryBo);

    /**
     * 平台分账
     *
     * @param taskNumber
     * @return
     */
    Boolean delayConfirmPlatform(String taskNumber);

    /**
     * 延时退款
     *
     * @param taskNumber
     * @return
     */
    Boolean delayConfirmRefund(String taskNumber);


    /**
     * 延时修改商单状态
     * @param payNumber
     * @return
     */
    Boolean delayTaskUpdate(String payNumber);

    /**
     * 处理结算逻辑
     *
     * @param receiveId
     * @return
     */
    Boolean handleSettle(Long receiveId);

    /**
     * 是否处于分账中
     *
     * @param userId
     * @param childTaskNumber
     * @return
     */
    Boolean onDistributing(Long userId, String childTaskNumber);

    /**
     * 处理批量结算
     *
     * @param taskNumber 主任务编号
     * @return
     */
    Boolean handleBatchSettle(String taskNumber);

    /**
     * 管理员审核结算商单
     *
     * @param taskNumber 主任务编号
     * @param reason     审核原因
     * @return
     */
    Boolean applySettle(String taskNumber, String reason,String busyType);

    /**
     * 取消商单
     *
     * @param taskNumber   主任务编号
     * @param paySceneType {@link com.sohu.pay.api.enums.PaySceceTypeEnum}
     * @return
     */
    Boolean cancelTaskSettle(String taskNumber, Integer paySceneType);


    /**
     * 业务退款
     *
     * @param payVo
     * @param refundAmount
     * @param refundReason
     * @return
     */
    Boolean operRefund(SohuBusyTaskPayNewModel payVo, BigDecimal refundAmount, String refundReason);

    /**
     * 通用商单待入账处理
     * @param busyType
     * @param busyCode
     * @return
     */
    Boolean waitIncomeProcess(String busyType, Long busyCode);
}
