package com.sohu.pay.api;

import com.sohu.pay.api.model.SohuIndependentTemplateModel;

import java.util.List;
import java.util.Map;

/**
 * 分账模板服务
 *
 * <AUTHOR>
 * @date 2023-10-16
 */
public interface RemoteIndependentTemplateService {

    /**
     * 根据siteId和模板类型查询模板配置
     *
     * @param siteId
     * @param type
     * @return SohuIndependentTemplateModel
     */
    SohuIndependentTemplateModel queryByIdAndType(Long siteId, Integer type);

    /**
     * 根据siteId和模板类型查询模板配置
     *
     * @param siteId
     * @param type
     * @return
     */
    SohuIndependentTemplateModel queryTemplateInfo(Integer siteType, Long siteId, Integer type);

    /**
     * 根据一批站点和模版登记查询模版配置
     *
     * @param addSiteIds
     * @param i
     */
    Map<Long, SohuIndependentTemplateModel> listByIdAndType(List<Long> addSiteIds, int i);

    /**
     * 根据行业站点id查询模版配置
     *
     * @param industryId
     * @return
     */
    Boolean queryByIndustryId(Long industryId);

}
