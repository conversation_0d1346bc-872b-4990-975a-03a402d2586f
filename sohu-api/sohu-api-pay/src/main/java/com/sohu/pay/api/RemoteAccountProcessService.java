package com.sohu.pay.api;

import com.sohu.pay.api.bo.AccountPlatformBo;
import com.sohu.shoporder.api.bo.SohuIndependentTempBo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 15:55
 */
public interface RemoteAccountProcessService {

    /**
     * 处理分账-待入账
     * @param busyType
     * @param busyCode
     * @return
     */
    Boolean waitIncomeProcess(String busyType,Long busyCode);

    /**
     * 获取平台分账对象
     * @param bo
     * @return
     */
    List<SohuIndependentTempBo> accountPlatformObjects(AccountPlatformBo bo);
}
