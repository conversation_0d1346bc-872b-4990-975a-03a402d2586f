package com.sohu.pay.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分账模版视图对象
 *
 * <AUTHOR>
 * @date 2023-10-11
 */
@Data
@ExcelIgnoreUnannotated
public class SohuIndependentTemplateVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 分账模版类型：1、商品   2、商单
     */
    @ExcelProperty(value = "分账模版类型：1、商品   2、商单   3、短剧分账(平台采购)   4、短剧分账(版权方上传)")
    private Integer templateType;

    /**
     * 分销人比例-0.00
     */
    @ExcelProperty(value = "分销人比例-0.00")
    private BigDecimal distributorRatio;

    /**
     * 分销人的拉新人比例-0.00
     */
    @ExcelProperty(value = "分销人的拉新人比例-0.00")
    private BigDecimal distributorInviteRatio;

    /**
     * 消费者的拉新人比例-0.00
     */
    @ExcelProperty(value = "消费者的拉新人比例-0.00")
    private BigDecimal consumerInviteRatio;

    /**
     * 代理人比例-0.00
     */
    @ExcelProperty(value = "代理人比例-0.00")
    private BigDecimal agencyRatio;

    /**
     * 平台系数-0.00
     */
    @ExcelProperty(value = "平台系数-0.00")
    private BigDecimal platformRatio;

    /**
     * 城市站长比例-0.00
     */
    @ExcelProperty(value = "城市站长比例-0.00")
    private BigDecimal cityRatio;

    /**
     * 入口站长比例-0.00
     */
    private BigDecimal entranceRatio;

    /**
     * 行业站长比例-0.00
     */
    private BigDecimal industryRatio;

    /**
     * 站长拉新比例-0.00
     */
    private BigDecimal inviteCityRatio;

    /**
     * 国家站长比例-0.00
     */
    @ExcelProperty(value = "国家站长比例-0.00")
    private BigDecimal countryRatio;

    /**
     * 平台比例-0.00
     */
    @ExcelProperty(value = "平台比例-0.00")
    private BigDecimal adminRatio;

    /**
     * 站点id
     */
    @ExcelProperty(value = "站点id")
    private Long siteId;

    /**
     * 站点类型
     */
    private Integer siteType;

    /**
     * 站点名称
     */
    @ExcelProperty(value = "站点名称")
    private String siteName;

    /**
     * 是否有效：1:有效  0:无效
     */
    @ExcelProperty(value = "是否有效：1:有效  0:无效")
    private Boolean isValid;

    /**
     * 是否是通用模板
     */
    private Boolean templateCommon;

    /**
     * 手续费承担方 0 分账方 1 平台
     */
    private Integer chargeUndertake;

}
