package com.sohu.middle.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class SohuBindStationCheckBo implements Serializable {

    @Schema(name = "stationmasterPhone", description = "站长手机号")
    private String stationmasterPhone;

    @Schema(name = "checkAccountExist", description = "是否校验认证信息已存在")
    private boolean checkAccountExist = true;

    @Schema(name = "checkHasAgentRole", description = "是否校验有服务商角色")
    private boolean checkHasAgentRole = true;

    @Schema(name = "checkPersonAccount", description = "是否校验有个人认证")
    private boolean checkPersonAccount = true;

    @Schema(name = "checkHasStationRole", description = "是否校验已经绑过站长")
    private boolean checkHasStationRole = true;

}
