package com.sohu.middle.api.service;

import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.middle.api.bo.SohuSiteBo;
import com.sohu.middle.api.bo.SohuSiteV2QueryBo;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.common.SohuSiteOfEnableVo;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 站点
 *
 * <AUTHOR>
 */
public interface RemoteMiddleSiteService {

    /**
     * 查询站点
     */
    SohuSiteVo queryById(Long id);

    /**
     * 查询站点列表
     */
    TableDataInfo<SohuSiteVo> queryPageList(SohuSiteBo bo, PageQuery pageQuery);

    /**
     * 查询站点列表
     */
    List<SohuSiteVo> queryList(SohuSiteBo bo);

    /**
     * 修改站点
     */
    Boolean insertByBo(SohuSiteBo bo);

    /**
     * 修改站点
     */
    Boolean updateByBo(SohuSiteBo bo);

    /**
     * 校验并批量删除站点信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询站点结构树
     */
    List<SohuSiteVo> getSiteTree(SohuSiteBo bo);


    /**
     * 查询站点结构树
     */
    List<SohuSiteVo> getSiteClearTree(SohuSiteBo bo);

    /**
     * 获取已启用的站点结构树
     */
    List<SohuSiteOfEnableVo> getSiteTreeOfEnable();

    /**
     * 获取已启用的站点集合
     */
    List<SohuSiteOfEnableVo> getSiteListOfEnable();

    /**
     * 获取腾讯地图逆地址解析结果
     *
     * @param location
     * @return
     */
    Object getLocationDetail(String location);

    /**
     * 开通站点
     *
     * @param bo
     * @return
     */
    Boolean openSite(SohuSiteBo bo);

    /**
     * 上线站点
     *
     * @param siteId 站点ID
     * @return
     */
    Boolean online(Long siteId);


    /**
     * 下线站点
     *
     * @param siteId 站点ID
     * @return
     */
    Boolean offline(Long siteId);

    /**
     * 根据站点集合查询set集合
     *
     * @param siteIds
     * @return
     */
    Map<Long, SohuSiteVo> queryMap(Set<Long> siteIds);

    /**
     * 根据行业站点集合查询set集合
     *
     * @param siteIds
     * @return
     */
    Map<Long, SohuSiteVo> queryIndustryMap(Set<Long> siteIds);

    /**
     * 根据站点id查询列表
     *
     * @param ids
     * @return
     */
    List<SohuSiteVo> selectSiteList(Collection<Long> ids);

    /**
     * 根据城市站点查询国家站数据
     *
     * @param id
     * @return
     */
    SohuSiteVo selectSiteByPid(Long id);

    /**
     * 根据城市站点查询国家站点
     *
     * @param cityId 城市站点id
     * @return {@link Integer}
     */
    Long selectCountryIdByCityId(Long cityId);

    SohuSiteVo selectSiteByUserId(Long userId);

    /**
     * 查询所有子站点
     *
     * @param pid
     * @return
     */
    List<Long> queryChildSiteIds(Long pid);

    /**
     * 站点英文名称
     *
     * @param categoryEnName 站点英文名称
     * @return {@link Long} 站点ID
     */
    Long getSiteIdByEnName(String categoryEnName);

    /**
     * 根据用户id查询站点信息
     *
     * @param stationmasterId 站长用户ID
     * @return {@link SohuSiteVo}
     */
    SohuSiteVo queryByStationmasterId(Long stationmasterId);

    /**
     * 根据用户id查询站点信息
     *
     * @param stationmasterId     站长用户ID
     * @param stationmasterStatus 站长状态:0=正常 1=禁用
     * @return {@link SohuSiteVo}
     */
    List<SohuSiteVo> listByStationmasterId(Long stationmasterId, Integer stationmasterStatus);

    /**
     * 根据IP获取站点信息
     *
     * @param ip ip
     * @return {@link SohuSiteVo}
     */
    SohuSiteVo getSiteByIp(String ip);

    /**
     * 根据站点名称获取站点信息
     *
     * @param siteName
     * @return
     */
    SohuSiteVo getBySiteNameOfEnable(String siteName);

    /**
     * 新增站点且完善站长认证信息
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean addV2(SohuSiteBo bo);

    /**
     * 查询站点列表-v2
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SohuSiteVo> queryPageListV2(SohuSiteV2QueryBo bo, PageQuery pageQuery);

    /**
     * 获取站点详细信息
     *
     * @param id 站点ID
     * @return {@link SohuSiteVo}
     */
    SohuSiteVo getInfoV2(@NotNull(message = "主键不能为空") Long id);

    /**
     * 修改站点
     *
     * @param bo
     * @return {@link Boolean}
     */
    Boolean updateV2(SohuSiteBo bo);

    /**
     * 通过行业ID查询站点信息
     *
     * @param platformIndustryId 平台行业ID
     * @return {@link SohuSiteVo}
     */
    SohuSiteVo queryByPlatformIndustry(Long platformIndustryId);

    /**
     * 绑定站长校验站长是否符合条件
     *
     * @param stationmasterPhone 站长手机号
     * @return
     */
    Boolean bindStationCheck(String stationmasterPhone);

}
