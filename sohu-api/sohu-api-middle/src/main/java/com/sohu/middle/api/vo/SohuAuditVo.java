package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.common.excel.annotation.ExcelDictFormat;
import com.sohu.common.excel.convert.ExcelDictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 业务审核视图对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@ExcelIgnoreUnannotated
public class SohuAuditVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 审核业务类型:[Article:图文,Video视频,Project:项目,Question:问题,Answer:回答,Card:资源名片,BusyModel:生意模式,BusyOrder:商单]
     * {@link com.sohu.common.core.enums.BusyType}
     */
    @ExcelProperty(value = "审核业务类型:[Article:图文,Video视频,Project:项目,Question:问题,Answer:回答,Card:资源名片,BusyModel:生意模式,BusyOrder:商单]")
    private String busyType;

    /**
     * 业务id
     */
    @ExcelProperty(value = "业务id")
    private Long busyCode;

    /**
     * 业务标题（回答的标题指的是：对应问题的标题）
     */
    @ExcelProperty(value = "业务标题", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "回=答的标题指的是：对应问题的标题")
    private String busyTitle;

    /**
     * 业务封面图-ossId
     */
    @ExcelProperty(value = "业务封面图-ossId")
    private String busyCoverImg;

    /**
     * 业务封面图-ossUrl
     */
    @ExcelProperty(value = "业务封面图-ossUrl")
    private String busyCoverUrl;

    /**
     * 业务归属人
     */
    @ExcelProperty(value = "业务归属人")
    private Long busyBelonger;

    /**
     * 业务归属人名称
     */
    @ExcelProperty(value = "业务归属人")
    @Schema(description = "归属人名称", example = "张三")
    private String busyBelongerName;

    /**
     * 站点类型 1 城市站 2 行业站
     */
    private Integer siteType;

    /**
     * 站点ID
     */
    @ExcelProperty(value = "站点ID")
    private Long siteId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 城市站点ID
     */
    @ExcelProperty(value = "城市站点ID")
    private Long citySiteId;

    /**
     * 城市站点名称
     */
    @ExcelProperty(value = "城市站点名称")
    private String citySiteName;

    /**
     * 国家站点ID
     */
    @ExcelProperty(value = "国家站点ID")
    private Long countrySiteId;

    /**
     * 城市站审核状态
     */
    @ExcelProperty(value = "城市站审核状态")
    private String cityAuditState;

    /**
     * 国家站审核状态
     */
    @ExcelProperty(value = "国家站审核状态")
    private String countryAuditState;

    /**
     * 管理员审核状态
     */
    @ExcelProperty(value = "管理员审核状态")
    private String sysAuditState;

    /**
     * 驳回理由
     */
    @ExcelProperty(value = "驳回理由")
    private String rejectReason;

    /**
     * 发布时间
     */
    @ExcelProperty(value = "发布时间")
    private Date publishTime;

    /**
     * 关联项
     */
    private RelationRespVo relation;

    /**
     *入驻行业认证信息
     */
    private SohuEntryAuthRespVo entryAuth;

    /**
     * 分类ID
     */
    @Schema(name = "categoryId", description = "分类ID", example = "116")
    private Long categoryId;

    /**
     * 分类ID
     */
    @Schema(name = "categoryName", description = "分类名称", example = "水果")
    private String categoryName;

    /**
     * 提交次数
     */
    private Integer submitNum;

    /**
     * 提交场景
     */
    private String submitScene;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 状态
     */
    private String contentState;

    /**
     * 内容审核状态（WaitApprove：审核中,Pass：已通过，Refuse：未通过），查询使用
     * {@link com.sohu.common.core.enums.CommonState}
     */
    private String auditState;

    /**
     * 内容
     */
    private String content;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 是否申诉
     */
    private Boolean appealStatus;

    /**
     * 申述原因
     */
    private String appealReason;

    /**
     * 作品类型（普通视频-general，狐少少课堂-lesson）
     * {@link com.sohu.common.core.enums.VideoEnum}
     */
    private String type;

    /**
     * 排序
     */
    private Long sortIndex;

    /**
     * 初始学习人数
     */
    @Schema(description = "初始学习人数")
    private Integer learnNum;

    /**
     * 审核人员名称
     */
    private String auditorName;
    /**
     * 申请理由
     */
    private String applyMsg;
    /**
     * 申请附件
     */
    private String applyAnnex;
    /**
     * 备注
     */
    private String remark;
}
