package com.sohu.middle.api.bo.diy;

import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 装修模版业务对象
 *
 * <AUTHOR>
 * @date 2025-04-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuDiyTemplateAuditQueryBo extends BaseEntity {

    @Schema(name = "stationmasterPhone", description = "站长手机号", example = "13543211234")
    private String stationmasterPhone;

    @Schema(name = "siteCate", description = "站点类型（1-站点，0-行业）", example = "1")
    private Boolean siteCate;

    @Schema(name = "platformIndustryId", description = "关联的平台行业，传平台行业ID", example = "1")
    private Long platformIndustryId;

    @Schema(name = "cityId", description = "站长城市ID", example = "11")
    private Long cityId;

    @Schema(name = "auditState", description = "审核状态，Edit=草稿 WaitApprove=审核中 Refuse=审核拒绝 Pass=审核通过", example = "WaitApprove")
    private String auditState;

    @Schema(name = "shelfState", description = "上下架状态，OnShelf=上架 OffShelf=下架 CompelOff=强制下架（上架只能有一个）", example = "OnShelf")
    private String shelfState;

}
