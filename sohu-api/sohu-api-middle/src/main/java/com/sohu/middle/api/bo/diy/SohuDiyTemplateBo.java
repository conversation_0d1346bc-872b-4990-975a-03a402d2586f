package com.sohu.middle.api.bo.diy;

import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 装修模版业务对象
 *
 * <AUTHOR>
 * @date 2025-04-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SohuDiyTemplateBo extends BaseEntity {

    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    @Schema(name = "id", description = "装修主键ID,新增不传，编辑必须传！", example = "1", requiredMode = Schema.RequiredMode.AUTO)
    private Long id;

    @Schema(name = "userId", description = "用户id，模版所属人员id", example = "1")
    private Long userId;

    @NotBlank(message = "模版名称不能为空", groups = {AddGroup.class})
    @Schema(name = "name", description = "装修模版名称", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @NotNull(message = "场景类型不能为空", groups = {AddGroup.class})
    @Schema(name = "sceneType", description = "场景类型  1.许愿狐场景  2.商城场景 3.站点装修 4.行业装修",
            example = "3", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long sceneType;

    @Schema(name = "sceneTypeList", description = "场景类型  1.许愿狐场景  2.商城场景 3.站点装修 4.行业装修",
            example = "[3,4]", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> sceneTypeList;

    @Schema(name = "sceneCode", description = "场景对应的类型值，站点id或行业id", example = "11", requiredMode = Schema.RequiredMode.AUTO)
    private Long sceneCode;

    @Schema(name = "effectConfig", description = "生效的配置json", requiredMode = Schema.RequiredMode.REQUIRED)
    private String effectConfig;

    @Schema(name = "editConfig", description = "草稿的配置json", requiredMode = Schema.RequiredMode.AUTO)
    private String editConfig;

    @Schema(name = "imageUrl", description = "图片地址", requiredMode = Schema.RequiredMode.AUTO)
    private String imageUrl;

    @Schema(name = "lastPublishTime", description = "上次发布的时间", requiredMode = Schema.RequiredMode.AUTO)
    @Hidden
    private Date lastPublishTime;

    @Schema(name = "status", description = "状态 0.禁用 1.正常", requiredMode = Schema.RequiredMode.AUTO)
    @Hidden
    private Long status;

    @Schema(name = "auditState", description = "审核状态，Edit=草稿 WaitApprove=审核中 Refuse=审核拒绝 Pass=审核通过", example = "WaitApprove")
    private String auditState;

    @Schema(name = "rejectReason", description = "拒绝理由", example = "跳转链接违规")
    private String rejectReason;

    @Schema(name = "shelfState", description = " 上下架状态，OnShelf=上架 OffShelf=下架 CompelOff=强制下架（上架只能有一个）", example = "OnShelf")
    private String shelfState;

    @Schema(name = "shelfReason", description = "下架理由", example = "跳转链接违规")
    private String shelfReason;

    @Schema(name = "auditUser", description = "审核人ID", example = "1")
    @Hidden
    private Long auditUser;

}
