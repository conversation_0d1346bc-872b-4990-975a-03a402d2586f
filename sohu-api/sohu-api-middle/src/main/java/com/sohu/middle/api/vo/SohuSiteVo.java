package com.sohu.middle.api.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sohu.middle.api.model.SohuAccountModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 站点视图对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@ExcelIgnoreUnannotated
public class SohuSiteVo implements Serializable {

    @ExcelProperty(value = "站点id")
    @Schema(name = "id", description = "站点id", example = "2")
    private Long id;

    @ExcelProperty(value = "上级站点id")
    @Schema(name = "pid", description = "上级站点id", example = "1")
    private Long pid;

    @ExcelProperty(value = "站点中文名")
    @Schema(name = "name", description = "站点中文名", example = "武汉")
    private String name;

    @Schema(name = "enName", description = "站点英文名", example = "wuhan")
    @ExcelProperty(value = "站点英文名")
    private String enName;

    @ExcelProperty(value = "站点类型")
    @Schema(name = "type", description = "站点类型:[Continent:洲,Country:国家,City:城市]", example = "City")
    private String type;

    @ExcelProperty(value = "站点状态")
    @Schema(name = "state", description = "站点状态:Y-开启 N-关闭", example = "Y")
    private String state;

    @Schema(name = "stationmasterId", description = "站长用户Id", example = "100")
    @ExcelProperty(value = "站长用户Id")
    private Long stationmasterId;

    @Schema(name = "stationmasterName", description = "站长用户名称", example = "不吃香菜")
    @ExcelProperty(value = "站长名称")
    private String stationmasterName;

    @Schema(name = "stationmasterAvatar", description = "站长头像", example = "https://sohugloba.oss-cn-beijing.aliyuncs.com/2024/12/06/ab79cda9f6b34761935f9247089fd273_640x640.jpeg")
    @ExcelProperty(value = "站长头像")
    private String stationmasterAvatar;

    @ExcelProperty(value = "站长手机号")
    @Schema(name = "stationmasterPhone", description = "站长手机号", example = "13543211234")
    private String stationmasterPhone;

    @Schema(name = "ext", description = "扩展字段")
    private String ext;

    @Schema(name = "siteCate", description = "站点类型（1-站点，0-行业）", example = "1")
    private Boolean siteCate;

    @Schema(name = "longitude", description = "经度", example = "114.312085452741930000")
    private BigDecimal longitude;

    @Schema(name = "latitude", description = "纬度", example = "30.596994151613963000")
    private BigDecimal latitude;

    @Schema(name = "hot", description = "站点热度量", example = "9999")
    private Long hot;

    @Schema(name = "parentName", description = "父级站点名", example = "中国")
    private String parentName;

    @Schema(name = "parentEnglishName", description = "父级站点英文名", example = "China")
    private String parentEnglishName;

    @Schema(name = "isDefault", description = "是否为默认站点，Y:默认 N:非默认", example = "Y")
    private String isDefault;

    @Schema(name = "countryCode", description = "国家编码ISO3166-1alpha-3编码", example = "CHN")
    private String countryCode;

    @Schema(name = "platformIndustryId", description = "关联的平台行业ID", example = "1,2")
    private Long platformIndustryId;

    @Schema(name = "platformIndustryIds", description = "关联的平台行业ID,英文逗号间隔保存", example = "1,2")
    private String platformIndustryIds;

    @Schema(name = "platformIndustryName", description = "关联的平台行业名称", example = "五养健康")
    private String platformIndustryName;

    @Schema(name = "platformIndustryNames", description = "关联的平台行业名称，英文逗号间隔保存", example = "五养健康,娱乐")
    private String platformIndustryNames;

    @Schema(name = "stationmasterBeginTime", description = "当选站长开始时间", example = "2025-5-28 12:10:41")
    private Date stationmasterBeginTime;

    @Schema(name = "stationmasterBeginTime", description = "当选站长结束时间", example = "2026-5-28 12:10:41")
    private Date stationmasterEndTime;

    @Schema(name = "stationmasterStatus", description = "站长状态:0=正常 1=禁用", example = "0", defaultValue = "0")
    private Integer stationmasterStatus;

    @Schema(name = "operator", description = "操作者ID", example = "1")
    private Long operator;

    @Schema(name = "operatorName", description = "操作者名称", example = "超级管理员")
    private String operatorName;

    @Schema(name = "account", description = "认证信息")
    private SohuAccountModel account;

    @Schema(name = "cityCode", description = "城市编码", example = "420100")
    private String cityCode;

    @Schema(name = "updateTime", description = "更新时间", example = "2025-5-27 12:10:58")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(name = "finalSiteName", description = "最终的名称")
    private String finalSiteName;
}
