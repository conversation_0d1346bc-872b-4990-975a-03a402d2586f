package com.sohu.middle.api.bo;

import cn.hutool.core.util.StrUtil;
import com.sohu.busyorder.api.bo.SohuBusyOrderBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskVo;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.validate.AddGroup;
import com.sohu.common.core.validate.EditGroup;
import com.sohu.common.core.web.domain.SohuBaseBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.List;

/**
 * 业务审核业务对象
 *
 * <AUTHOR>
 * @date 2023-06-21
 */

@Data
//@EqualsAndHashCode(callSuper = true)
public class SohuAuditBo extends SohuBaseBo {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 审核业务类型:[Article:图文,Video视频,Project:项目,Question:问题,Answer:回答,Card:资源名片,BusyModel:生意模式,BusyOrder:商单]
     * {@link com.sohu.common.core.enums.BusyType}
     */
    @NotBlank(message = "审核业务类型:[Article:图文,Video视频,Project:项目,Question:问题,Answer:回答," +
            "Card:资源名片,BusyModel:生意模式,BusyOrder:商单]不能为空", groups = {AddGroup.class, EditGroup.class})
    private String busyType;

    /**
     * 审核业务类型:[Article:图文,Video视频,Project:项目,Question:问题,Answer:回答,Card:资源名片,BusyModel:生意模式,BusyOrder:商单]
     * {@link com.sohu.common.core.enums.BusyType}
     */
    @Schema(name = "busyTypes", description = "审核业务类型集合", example = "Article,Video")
    private List<String> busyTypes;

    /**
     * 业务id
     */
    @NotNull(message = "业务id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long busyCode;

    /**
     * 业务标题（回答的标题指的是：对应问题的标题）
     */
    @NotBlank(message = "业务标题（回答的标题指的是：对应问题的标题）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String busyTitle;

    /**
     * 业务封面图
     */
    @NotBlank(message = "业务封面图不能为空", groups = {AddGroup.class, EditGroup.class})
    private String busyCoverImg;

    /**
     * 业务归属人
     */
    @NotNull(message = "业务归属人不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long busyBelonger;

    /**
     * 城市站点ID
     */
    @NotNull(message = "城市站点ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long citySiteId;

    /**
     * 国家站点ID
     */
    @NotNull(message = "国家站点ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long countrySiteId;

    /**
     * 城市站审核状态
     */
    @NotBlank(message = "城市站审核状态不能为空", groups = {AddGroup.class})
    private String cityAuditState;
    /**
     * 国家站审核状态
     */
    private String countryAuditState;
    /**
     * 管理员审核状态
     */
    private String sysAuditState;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 状态，查询使用
     */
    private String state;

    /**
     * 审核状态（WaitApprove：审核中,Pass：已通过，Refuse：未通过），查询使用
     * {@link com.sohu.common.core.enums.CommonState}
     */
    private String auditState;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 驳回理由
     */
    private String rejectReason;

    /**
     * 商单专用-预付款金额
     */
    private BigDecimal prepayAmount;

    /**
     * 商单专用-预付款币种
     */
    private Long prepayCurrency;

    /**
     * 商单专用-预付时间
     */
    private String prepayTime;

    /**
     * 站点ID集合
     */
    @Schema(name = "siteIds", description = "站点ID集合", example = "1,2")
    private List<Long> siteIds;

    /**
     * 作品状态
     * {@link com.sohu.common.core.enums.CommonState}
     */
    @Schema(name = "contentState", description = "作品状态", example = "WaitApprove-发布中,OnShelf-已发布,OffShelf-已下架(自主),CompelOff-已下架(强制),Delete-已删除(自主),ForceDelete-已删除(强制),Refuse-发布失败")
    private String contentState;

    /**
     * 作品分类集合
     * {@link com.sohu.common.core.enums.CommonState}
     */
    @Schema(name = "contentState", description = "作品状态", example = "WaitApprove-发布中,OnShelf-已发布,OffShelf-已下架(自主),CompelOff-已下架(强制),Delete-已删除(自主),ForceDelete-已删除(强制),Refuse-发布失败")
    private String content;

    /**
     * 分类ID集合
     */
    @Schema(name = "categoryIds", description = "分类ID集合", example = "116,111")
    private List<Long> categoryIds;

    /**
     * 分类ID
     */
    @Schema(name = "categoryId", description = "分类ID", example = "116")
    private Long categoryId;

    /**
     * 说明备注-记录使用
     */
    private String recordRemark;

    /**
     * AI审核-机审
     */
    private Boolean isAiAudit;

    /**
     * 发布用户
     */
    private String publishUser;

    /**
     * 是否申诉
     */
    private Boolean appealStatus;
    /**
     * 申请理由
     */
    private String applyMsg;
    /**
     * 申请附件
     */
    private String applyAnnex;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 站点类型 1 城市站 2 行业站
     */
    private Integer siteType;

    /**
     * 站点Id
     */
    private Long siteId;

    public SohuAuditBo() {
    }

    /**
     * 图文
     *
     * @param itemBo
     */
    public SohuAuditBo(SohuArticleBo itemBo) {
        if (Objects.nonNull(itemBo) && !CalUtils.isNullOrZero(itemBo.getId())) {
            this.busyType = BusyType.Article.name();
            this.busyCode = itemBo.getId();
            this.busyTitle = itemBo.getTitle();
            this.busyCoverImg = itemBo.getCoverImage();
            this.busyBelonger = itemBo.getUserId();
            this.citySiteId = itemBo.getSiteId();
            this.publishTime = new Date();
            this.categoryId = itemBo.getCategoryId();
            this.recordRemark = itemBo.getRejectReason();
            handleState(this, itemBo.getState());
        }
    }

    /**
     * 视频
     *
     * @param itemBo
     */
    public SohuAuditBo(SohuVideoBo itemBo) {
        if (Objects.nonNull(itemBo) && !CalUtils.isNullOrZero(itemBo.getId())) {
            this.busyType = BusyType.Video.name();
            this.busyCode = itemBo.getId();
            this.busyTitle = itemBo.getTitle();
            this.busyCoverImg = itemBo.getCoverImage();
            this.busyBelonger = itemBo.getUserId();
            this.citySiteId = itemBo.getSiteId();
            this.publishTime = new Date();
            this.categoryId = itemBo.getCategoryId();
            this.recordRemark = itemBo.getRejectReason();
            handleState(this, itemBo.getState());
        }
    }

    /**
     * 问题
     *
     * @param itemBo
     */
    public SohuAuditBo(SohuQuestionBo itemBo) {
        if (Objects.nonNull(itemBo) && !CalUtils.isNullOrZero(itemBo.getId())) {
            this.busyType = BusyType.Question.name();
            this.busyCode = itemBo.getId();
            this.busyTitle = itemBo.getTitle();
            this.busyCoverImg = itemBo.getCoverImage();
            this.busyBelonger = itemBo.getUserId();
            this.citySiteId = itemBo.getSiteId();
            this.publishTime = new Date();
            this.categoryId = itemBo.getCategoryId();
            this.recordRemark = itemBo.getRejectReason();
            handleState(this, itemBo.getState());
        }
    }

    /**
     * 诗文
     *
     * @param itemBo
     */
    public SohuAuditBo(SohuLiteratureBo itemBo) {
        if (Objects.nonNull(itemBo) && !CalUtils.isNullOrZero(itemBo.getId())) {
            this.busyType = itemBo.getType();
            this.busyCode = itemBo.getId();
            this.busyTitle = itemBo.getTitle();
            this.busyCoverImg = itemBo.getCoverImage();
            this.busyBelonger = itemBo.getUserId();
            this.citySiteId = itemBo.getSiteId();
            this.publishTime = new Date();
            this.categoryId = itemBo.getCategoryId();
            this.recordRemark = itemBo.getRejectReason();
            handleState(this, itemBo.getState());
        }
    }

    /**
     * 原商单
     *
     * @param itemBo
     * @param busyType
     */
    public SohuAuditBo(SohuBusyOrderBo itemBo, String busyType) {
        if (Objects.nonNull(itemBo) && !CalUtils.isNullOrZero(itemBo.getId())) {
            this.busyType = busyType;
            this.busyCode = itemBo.getId();
            this.busyTitle = itemBo.getOrderTitle();
            this.busyCoverImg = itemBo.getCoverImg();
            this.busyBelonger = itemBo.getUserId();
            this.citySiteId = itemBo.getSiteId();
            this.publishTime = new Date();
            handleState(this, itemBo.getBusyOrderStatus());
        }
    }

    /**
     * 新商单结算申请
     *
     * @param receiveVo
     * @param busyType
     */
    public SohuAuditBo(SohuBusyTaskReceiveVo receiveVo, String busyType) {
        if (Objects.nonNull(receiveVo) && !CalUtils.isNullOrZero(receiveVo.getId())) {
            this.busyType = busyType;
            this.busyCode = receiveVo.getId();
            this.busyTitle = "用户申请结算";
            this.busyBelonger = receiveVo.getUserId();
            this.citySiteId = receiveVo.getSiteId();
            this.publishTime = new Date();
            this.cityAuditState = CommonState.WaitApprove.getCode();
            this.countryAuditState = CommonState.WaitApprove.getCode();
            this.sysAuditState = CommonState.WaitApprove.getCode();
        }
    }

    /**
     * 新商单接单申请
     *
     * @param busyTaskId
     * @param busyType
     * @param userId
     * @param siteId
     * @param applyMsg
     * @param applyAnnex
     */
    public SohuAuditBo(Long busyTaskId, String busyType,
                       Long userId, Long siteId,
                       String applyMsg, String applyAnnex) {
            this.busyType = busyType;
            this.busyCode = busyTaskId;
            this.busyTitle = "用户申请接单";
            this.busyBelonger = userId;
            this.citySiteId = siteId;
            this.publishTime = new Date();
            this.cityAuditState = CommonState.WaitApprove.getCode();
            this.countryAuditState = CommonState.WaitApprove.getCode();
            this.sysAuditState = CommonState.WaitApprove.getCode();
            this.applyMsg = applyMsg;
            this.applyAnnex = applyAnnex;
    }

    /**
     * 新商单接单申请
     *
     * @param busyTaskId
     * @param busyType
     * @param userId
     * @param siteId
     * @param applyMsg
     * @param applyAnnex
     */
    public SohuAuditBo(Long busyTaskId, String busyType,
                       Long userId, Long siteId,
                       String applyMsg, String applyAnnex,Integer siteType,Long entranceSiteId) {
        this.busyType = busyType;
        this.busyCode = busyTaskId;
        this.busyTitle = "用户申请接单";
        this.busyBelonger = userId;
        this.citySiteId = siteId;
        this.publishTime = new Date();
        this.cityAuditState = CommonState.WaitApprove.getCode();
        this.countryAuditState = CommonState.WaitApprove.getCode();
        this.sysAuditState = CommonState.WaitApprove.getCode();
        this.applyMsg = applyMsg;
        this.applyAnnex = applyAnnex;
        this.siteType = siteType;
        this.siteId = entranceSiteId;
    }

    public void handleState(SohuAuditBo sohuAuditBo, String state) {
        if (StrUtil.isBlankIfStr(state)) {
            // 设置为待审核
            state = CommonState.WaitApprove.getCode();
        }
        sohuAuditBo.setState(state);
        if (StrUtil.equalsAnyIgnoreCase(state, CommonState.OnShelf.getCode(), CommonState.Pass.getCode())) {
            // 如果为审核通过，则城市站，国家站，系统的审核状态都为已通过
            sohuAuditBo.setCityAuditState(CommonState.OnShelf.getCode());
            sohuAuditBo.setCountryAuditState(CommonState.OnShelf.getCode());
            sohuAuditBo.setSysAuditState(CommonState.OnShelf.getCode());
        }
    }
}
