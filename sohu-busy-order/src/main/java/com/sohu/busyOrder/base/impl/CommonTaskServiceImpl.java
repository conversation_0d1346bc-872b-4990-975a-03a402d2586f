package com.sohu.busyOrder.base.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.busyOrder.appevent.event.BusyTaskReceiveEvent;
import com.sohu.busyOrder.base.CommonTaskService;
import com.sohu.busyOrder.domain.*;
import com.sohu.busyOrder.mapper.SohuBusyTaskLabelMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskSourceMaterialMapper;
import com.sohu.busyOrder.service.*;
import com.sohu.busyorder.api.bo.SohuBusyTaskBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveApplyProcessBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.utils.ip.AddressUtils;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.SohuImGroupFormHandleBo;
import com.sohu.im.api.service.RemoteImGroupFromGeneralService;
import com.sohu.middle.api.bo.SohuAuditBo;
import com.sohu.middle.api.bo.SohuIndependentMaterialBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.bo.SohuUserLabelRelationBo;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.enums.McnUserStateEnum;
import com.sohu.middle.api.service.RemoteMiddleAuditService;
import com.sohu.middle.api.service.RemoteMiddleCategoryService;
import com.sohu.middle.api.service.RemoteMiddleCommonLabelService;
import com.sohu.middle.api.vo.SohuAuditVo;
import com.sohu.middle.api.vo.SohuCategoryVo;
import com.sohu.middle.api.vo.SohuIndependentMaterialVo;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.RemotePaySettlementService;
import com.sohu.pay.api.bo.DelayConfirmqueryBo;
import com.sohu.pay.api.enums.PaySceceTypeEnum;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import io.seata.common.util.CollectionUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 通用商单实现类
 *
 * @Author: zlf
 * @Date: 2025/3/5 17:00
 **/
@Service
@Slf4j
public class CommonTaskServiceImpl extends BaseTaskServiceImpl implements CommonTaskService {

    @Autowired
    private SohuBusyTaskLabelMapper sohuBusyTaskLabelMapper;
    @Autowired
    private SohuBusyTaskSourceMaterialMapper sohuBusyTaskSourceMaterialMapper;
    @Autowired
    private ISohuBusyTaskSourceMaterialService sourceMaterialService;
    @Autowired
    private ISohuBusyTaskLabelService labelService;
    @Autowired
    private ISohuBusyTaskUserService sohuBusyTaskUserService;
    @Autowired
    private ISohuBusyTaskPayService iSohuBusyTaskPayService;
    @Autowired
    private ISohuBusyTaskNoticeService sohuBusyTaskNoticeService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;
    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;
    @DubboReference
    private RemoteMiddleAuditService sohuAuditService;
    @DubboReference
    private RemotePaySettlementService remotePaySettlementService;
    @DubboReference
    private RemoteIndependentTemplateService remoteIndependentTemplateService;
    @DubboReference
    private RemoteImGroupFromGeneralService remoteImGroupFromGeneralService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addTask(SohuBusyTaskBo bo, Long userId) {
        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(),
                RoleCodeEnum.OPERATION_ADMIN.getCode(),
                RoleCodeEnum.Article.getCode())) {
            throw new RuntimeException("非法操作，您无权操作");
        }
        // 通用商单单位默认为天
        bo.setTimeUnit(Constants.ONE);
        SohuBusyTask busyTask = super.createTask(bo, userId);
        busyTask.setDeliveryType(3);
        boolean sendNotice = false;
        if (BooleanUtil.isTrue(bo.getIsDraft())) {
            busyTask.setState(SohuBusyTaskState.Edit.name());
        } else if (StrUtil.equalsAnyIgnoreCase(bo.getKickbackType(), KickbackType.none.getCode())) {
            // 未设置佣金,无需审核
            busyTask.setKickbackValue(BigDecimal.ZERO);
            busyTask.setState(SohuBusyTaskState.WaitReceive.name());
            sendNotice = true;
        } else {
            // 设置佣金,需要支付佣金
            busyTask.setState(SohuBusyTaskState.WaitIndependentPay.name());
        }
        bo.setFullAmount(busyTask.getFullAmount());
        HttpServletRequest request = ServletUtils.getRequest();
        final String ip = ServletUtils.getClientIP(request);
        busyTask.setIpAddress(ip);
        // 新建任务
        sohuBusyTaskMapper.insert(busyTask);
        // 处理站点信息
        this.hanleTaskSite(bo, userId, busyTask);
        // 保存标签信息
        labelService.save(bo.getLabelList(), busyTask.getTaskNumber());
        // 保存素材信息
        sourceMaterialService.save(bo.getSourceMaterialList(), busyTask.getId());
        if (sendNotice) {
            // 发送发单方任务审核通过通知
            sohuBusyTaskNoticeService.sendTaskNotice(busyTask.getId(), TaskNoticeEnum.TASK_PUBLISH_SUCCESS, busyTask.getUserId(), null, null, Boolean.TRUE);
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        //异步新增埋点数据
        CompletableFuture.runAsync(() -> {
            SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
            eventAttribute.setContentNo(busyTask.getTaskNumber());
            eventAttribute.setContentName(busyTask.getTitle());
            eventAttribute.setAmount(busyTask.getFullAmount());
            eventAttribute.setContentType("通用");
            syncTaskUserBehavior(loginUser.getUserId(), loginUser.getNickname(),eventAttribute);
        }, asyncConfig.getAsyncExecutor());
        return busyTask.getId();
    }

    @Override
    public void afterReceive(boolean flag,
                             SohuBusyTaskReceive add,
                             SohuBusyTaskSite sohuBusyTaskSite,
                             SohuBusyTaskVo sohuBusyTaskVo, Long userId, Boolean isSendNotice) {
        log.error("接单入参flag:{},SohuBusyTaskReceive:{},SohuBusyTaskSite:{},SohuBusyTaskVo:{}", flag, add, sohuBusyTaskSite, sohuBusyTaskVo);
        if (flag) {
            // 异步更新站点表商单状态
            sohuBusyTaskSite.setIsReceive(Boolean.TRUE);
            sohuBusyTaskSite.setState(SohuBusyTaskState.WaitFullAmountPay.name());
            sohuBusyTaskReceiveMapper.updateById(add);
            sohuBusyTaskSiteMapper.updateById(sohuBusyTaskSite);
            SohuBusyTask sohuBusyTask = new SohuBusyTask();
            sohuBusyTask.setId(sohuBusyTaskVo.getId());
            sohuBusyTask.setState(SohuBusyTaskState.WaitFullAmountPay.name());
            sohuBusyTaskMapper.updateById(sohuBusyTask);
            // 发送发单方任务支付商单金额通知
            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_BEEN_APPLY_COMMON,
                    sohuBusyTaskSite.getUserId(), null, getEncryptName(add.getUserId()), Boolean.FALSE, null, null);
            // 异步绑定用户标签与行业标签
            CompletableFuture.runAsync(() -> this.bindUserLabelRelation(sohuBusyTaskVo, userId), asyncConfig.getAsyncExecutor());
            // 单人接单同步素材库
            remoteMiddleIndependentMaterialService.deleteByCodeAndType(sohuBusyTaskVo.getTaskNumber(), BusyTaskTypeEnum.COMMON_TASK.getCode());
            // 补充事件统计
//            super.addEventReport(BusyTaskReportEnum.JQSD.getType(), sohuBusyTaskSite.getTaskNumber(), sohuBusyTaskVo.getTitle(), LoginHelper.getUserId());
        }
    }

    /**
     * 绑定用户标签与行业标签
     *
     * @param vo     主任务vo
     * @param userId 接单用户id
     */
    private void bindUserLabelRelation(SohuBusyTaskVo vo, Long userId) {
        ArrayList<SohuUserLabelRelationBo> userLabelList = new ArrayList<>();
        // 添加行业标签
        userLabelList.add(createUserLabelRelation(userId, vo.getPid(), LabelEnum.INDUSTRY));
        // 添加普通标签
        List<SohuBusyTaskLabel> taskLabelList = sohuBusyTaskLabelMapper.selectList(Wrappers.<SohuBusyTaskLabel>lambdaQuery()
                .eq(SohuBusyTaskLabel::getTaskNumber, vo.getTaskNumber()));
        if (CollectionUtils.isNotEmpty(taskLabelList)) {
            for (SohuBusyTaskLabel taskLabel : taskLabelList) {
                userLabelList.add(createUserLabelRelation(userId, taskLabel.getLabelId(), LabelEnum.COMMON));
            }
        }
        // 批量插入用户标签关联
        remoteMiddleCommonLabelService.insertBatch(userLabelList);
    }

    /**
     * 创建用户标签关联对象
     *
     * @param userId    用户id
     * @param labelId   标签id
     * @param labelType 标签类型
     * @return 用户标签关联对象
     */
    private SohuUserLabelRelationBo createUserLabelRelation(Long userId, Long labelId, LabelEnum labelType) {
        SohuUserLabelRelationBo userLabel = new SohuUserLabelRelationBo();
        userLabel.setUserId(userId);
        userLabel.setLabelId(labelId);
        userLabel.setLabelType(labelType.getCode());
        return userLabel;
    }

    @Override
    public void afterBuild(SohuBusyTaskReceive receive) {
        receive.setApplyMsg("通用型商单接单");
        receive.setAmount(BigDecimal.ZERO);
        receive.setState(SohuBusyTaskState.WaitFullAmountPay.name());
        receive.setPassTime(new Date());
        receive.setBackup(Boolean.FALSE);
    }

    @Override
    public Long updateTask(SohuBusyTask busyTask, SohuBusyTaskBo bo) {
        SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoById(bo.getId());
        if (Objects.isNull(sohuBusyTaskVo)) {
            throw new ServiceException("该商单不存在,请确认后再试");
        }
        if (!sohuBusyTaskVo.getState().equals(SohuBusyTaskState.Edit.name())) {
            throw new ServiceException("通用商单非草稿状态下不允许编辑");
        }
        if (BooleanUtil.isFalse(bo.getIsDraft())) {
            // 未设置佣金,无需审核
            if (StrUtil.equalsAnyIgnoreCase(bo.getKickbackType(), KickbackType.none.getCode())) {
                busyTask.setKickbackValue(BigDecimal.ZERO);
                busyTask.setState(SohuBusyTaskState.WaitReceive.name());
            } else {
                // 设置佣金,需要支付佣金
                busyTask.setState(SohuBusyTaskState.WaitIndependentPay.name());
            }
        } else {
            busyTask.setState(SohuBusyTaskState.Edit.name());
        }
        sohuBusyTaskMapper.updateById(busyTask);
        // 处理站点信息
        this.hanleTaskSite(bo, sohuBusyTaskVo.getUserId(), busyTask);
        labelService.save(bo.getLabelList(), sohuBusyTaskVo.getTaskNumber());
        sourceMaterialService.save(bo.getSourceMaterialList(), bo.getId());
        return bo.getId();
    }

    @Override
    public void afterUpdateTask(SohuBusyTask busyTask, SohuBusyTaskBo bo) {
    }

    @Override
    public void afterQueryTask(SohuBusyTaskVo vo) {
        // 任务是否收藏
        vo.setCollectObj(Boolean.FALSE);
        Long userId = LoginHelper.getUserId();
        if (userId != null && userId > 0L) {
            SohuBusyTaskUserVo sohuBusyTaskUserVo = sohuBusyTaskUserService.getByUserIdAndTaskNumber(userId, vo.getTaskNumber());
            if (Objects.nonNull(sohuBusyTaskUserVo)) {
                vo.setCollectObj(sohuBusyTaskUserVo.getIsCollect());
                sohuBusyTaskUserService.updateTime(sohuBusyTaskUserVo.getId(), null);
            } else {
                sohuBusyTaskUserService.addReadRecord(userId, vo.getTaskNumber());
            }
        }
        List<SohuBusyTaskLabel> taskLabelList = sohuBusyTaskLabelMapper.selectList(Wrappers.<SohuBusyTaskLabel>lambdaQuery()
                .eq(SohuBusyTaskLabel::getTaskNumber, vo.getTaskNumber()));
        if (CollectionUtils.isNotEmpty(taskLabelList)) {
            Map<Long, String> labelMap = remoteMiddleCommonLabelService.queryLabelNamesByIds(
                    taskLabelList.stream().map(SohuBusyTaskLabel::getLabelId).collect(Collectors.toList()));
            if (ObjectUtils.isNotNull(labelMap)) {
                List<SohuBusyTaskVo.Label> labelList = new ArrayList<>();
                for (Map.Entry<Long, String> entry : labelMap.entrySet()) {
                    SohuBusyTaskVo.Label label = new SohuBusyTaskVo.Label();
                    label.setLabelName(entry.getValue());
                    label.setLabelId(entry.getKey());
                    labelList.add(label);
                }
                vo.setLabelList(labelList);
            }
        }
        List<SohuBusyTaskSourceMaterial> materialList = sohuBusyTaskSourceMaterialMapper.selectList(Wrappers.<SohuBusyTaskSourceMaterial>lambdaQuery()
                .eq(SohuBusyTaskSourceMaterial::getBusyTaskId, vo.getId()));
        if (CollectionUtils.isNotEmpty(materialList)) {
            vo.setSourceMaterialList(materialList.stream().map(SohuBusyTaskSourceMaterial::getSourceMaterialUrl).collect(Collectors.toList()));
        }
        List<SohuBusyTaskSite> sohuBusyTaskSiteList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, vo.getTaskNumber()));
        // 补充子任务编号
        if (CollectionUtils.isNotEmpty(sohuBusyTaskSiteList)) {
            SohuBusyTaskSite sohuBusyTaskSite = sohuBusyTaskSiteList.get(0);
            vo.setChildTaskNumber(sohuBusyTaskSiteList.get(0).getTaskNumber());
        }
        // 接单人信息
        List<SohuBusyTaskSiteVo> sohuBusyTaskSiteVoList = sohuBusyTaskSiteMapper.selectTaskList(vo.getTaskNumber());
        SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = null;
        if (CollectionUtils.isNotEmpty(sohuBusyTaskSiteVoList)) {
            SohuBusyTaskSiteVo sohuBusyTaskSiteVo = sohuBusyTaskSiteVoList.get(0);
            sohuBusyTaskReceiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSiteVo.getTaskNumber())
                    .in(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitFullAmountPay.name(), SohuBusyTaskState.WaitApproveSettle.name(), SohuBusyTaskState.Execute.name())
                    .last("limit 1"));
            if (Objects.nonNull(sohuBusyTaskReceiveVo)) {
                vo.setReceiveUserId(sohuBusyTaskReceiveVo.getUserId());
                vo.setReceiveId(sohuBusyTaskReceiveVo.getId());
                LoginUser loginUser = remoteUserService.queryById(sohuBusyTaskReceiveVo.getUserId());
                vo.setReceiveAvatar(loginUser.getAvatar());
                vo.setReceiveNickName(loginUser.getNickname());
            }
        }
        // 服务费
        SohuIndependentTemplateModel independentTemplateModel = remoteIndependentTemplateService.queryTemplateInfo(null,null, Constants.TWO);
        if (Objects.nonNull(independentTemplateModel)) {
            BigDecimal add = vo.getFullAmount();
            BigDecimal platformRatio = independentTemplateModel.getPlatformRatio();
            vo.setPlatformFee((add.multiply(platformRatio)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        }
        SohuBusyTaskPayVo payVo = iSohuBusyTaskPayService.queryByPayScene(vo.getTaskNumber(), PayStatus.Paid.name(), PaySceceTypeEnum.BUSY_ORDER_PAY.getCode());
        if (Objects.nonNull(payVo)) {
            vo.setPayAmount(payVo.getPayAmount());
            vo.setPlatformFee(CalUtils.add(payVo.getPlatformAmount(), payVo.getChargeAmount()));
        }
        // 判断接单是否需要审核,如果需要审核则查询申请接单记录
        if (vo.getIsApproveReceive()) {
            Long receiveNum = sohuAuditService.countByBusyCodeAndBusyType(vo.getId(), BusyType.ReceiveBusyTask.getType(), null);
            vo.setIsAuditRecord(receiveNum > 0);
            Long waitApproveNum = sohuAuditService.countByBusyCodeAndBusyType(vo.getId(), BusyType.ReceiveBusyTask.getType(), SohuBusyTaskState.WaitApprove.name());
            vo.setWaitApproveNum(waitApproveNum.intValue());
        }
        // 剩余支付时间
        if (StrUtil.equalsAnyIgnoreCase(vo.getState(), SohuBusyTaskState.WaitFullAmountPay.name())) {
            SohuAuditVo sohuAuditVo = sohuAuditService.selectByObj(vo.getId(), BusyType.ReceiveBusyTask.name());
            if (Objects.nonNull(sohuAuditVo) && StrUtil.equalsAnyIgnoreCase(sohuAuditVo.getSysAuditState(), CommonState.OnShelf.getCode())) {
                vo.setRemainPayTime(CalUtils.getRemainTime(sohuAuditVo.getPublishTime(), 24));
            }else{
                vo.setRemainPayTime(CalUtils.getRemainTime(sohuBusyTaskReceiveVo.getPassTime(), 24));
            }
        }
        if (StrUtil.equalsAnyIgnoreCase(vo.getState(), SohuBusyTaskState.WaitPromisePay.name())) {
            vo.setRemainPayTime(CalUtils.getRemainTime(vo.getUpdateTime(), 4));
        }
        if (StrUtil.equalsAnyIgnoreCase(vo.getState(), SohuBusyTaskState.WaitPromisePay.name())) {
            vo.setRemainPayTime(CalUtils.getRemainTime(vo.getUpdateTime(), 4));
        }

    }

    @Override
    public String getTypeName() {
        return BusyTaskTypeEnum.COMMON_TASK.getMsg();
    }

    @Override
    public String getTypeCode() {
        return BusyTaskTypeEnum.COMMON_TASK.getCode();
    }

    @Override
    public void afterShelfTask(SohuBusyTask busyTask) {
        if (!SohuBusyTaskState.CompelOff.name().equals(busyTask.getState())) {
            throw new ServiceException("该类型商单不支持二次上架");
        }
        // 下架逻辑同步素材库
        List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteService.listByMasterTaskNo(busyTask.getTaskNumber());

        if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
            // 1. 同步删除素材库 (优化点：只在任务站点列表非空时才删除)
            remoteMiddleIndependentMaterialService.deleteByCodeAndType(busyTask.getTaskNumber(), BusyTaskTypeEnum.COMMON_TASK.getCode());

            // 2. 更新接单状态为已取消
            SohuBusyTaskSite sohuBusyTaskSite = busyTaskSiteList.get(0);

            // 3. 优化接单状态更新
            if (Objects.nonNull(sohuBusyTaskSite)) {
                sohuBusyTaskReceiveMapper.update(
                        null,
                        Wrappers.<SohuBusyTaskReceive>lambdaUpdate()
                                .set(SohuBusyTaskReceive::getState, SohuBusyTaskState.Cancel.name())
                                .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSite.getTaskNumber())
                );
            }
        }
        // 发单方商单预算退款
        remotePaySettlementService.cancelTaskSettle(busyTask.getTaskNumber(), PaySceceTypeEnum.BUSY_SHARE_PAY.getCode());
    }

    @Override
    public Boolean beforeReceiveTask(SohuBusyTaskReceiveBo bo, SohuBusyTaskVo sohuBusyTaskVo, SohuBusyTaskSite sohuBusyTaskSite, Long userId) {
        this.checkReceive(sohuBusyTaskVo, sohuBusyTaskSite, null);
        // 判断是否需要审核接单,需要审核接单的话则存入审核记录，返回false,不需要审核则直接返回TRUE
        Boolean isReceive = Boolean.TRUE;
        if (sohuBusyTaskVo.getIsApproveReceive()) {
            if (StringUtils.isEmpty(bo.getApplyMsg())) {
                throw new ServiceException("申请接单理由不能为空");
            }
            // 查询最近的一条审核记录
            SohuAuditVo sohuAuditVo = sohuAuditService.selectNearByObj(sohuBusyTaskVo.getId(), BusyType.ReceiveBusyTask.getType(), userId);
            if (Objects.nonNull(sohuAuditVo) && sohuAuditVo.getSysAuditState().equals(SohuBusyTaskState.WaitApprove.name())) {
                throw new ServiceException("已申请过该商单,无需重新申请,请等待发单方处理");
            }
            // 基于用户查询是否存在待审核的记录
            SohuBusyTaskReceive sohuBusyTaskReceive = sohuBusyTaskReceiveMapper.selectOne(new LambdaQueryWrapper<SohuBusyTaskReceive>()
                    .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSite.getTaskNumber())
                    .eq(SohuBusyTaskReceive::getUserId, userId)
                    .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitApprove.name())
                    .orderByDesc(SohuBusyTaskReceive::getPassTime)
                    .last("limit 1"));
            if (Objects.nonNull(sohuBusyTaskReceive)) {
                throw new ServiceException("已申请过该商单,无需重新申请,请等待发单方处理");
            }
            String lockKey = String.format("sohu:lock:apply_receive_master_task_number:%s:%s", sohuBusyTaskVo.getTaskNumber(), userId);
            if (RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(1))) {
                // 补充待审核记录
                sohuAuditService.createAudited(new SohuAuditBo(sohuBusyTaskVo.getId(), BusyType.ReceiveBusyTask.getType(),
                        userId, sohuBusyTaskSite.getSiteId(), bo.getApplyMsg(), bo.getApplyAnnex()));
                // 补充接单记录
                SohuBusyTaskReceive hasReceiveRecord = sohuBusyTaskReceiveMapper.selectOne(new LambdaUpdateWrapper<SohuBusyTaskReceive>()
                        .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSite.getTaskNumber())
                        .eq(SohuBusyTaskReceive::getUserId, userId));
                if (Objects.nonNull(hasReceiveRecord)) {
                    hasReceiveRecord.setState(SohuBusyTaskState.WaitApprove.name());
                    hasReceiveRecord.setApplyMsg(bo.getApplyMsg());
                    hasReceiveRecord.setApplyAnnex(bo.getApplyAnnex());
                    hasReceiveRecord.setUpdateTime(new Date());
                    sohuBusyTaskReceiveMapper.updateById(hasReceiveRecord);
                } else {
                    SohuBusyTaskReceive add = this.buildReceive(bo, sohuBusyTaskVo, sohuBusyTaskSite, userId);
                    sohuBusyTaskReceiveMapper.insert(add);
                }
                // 发送发单方任务接单通知
                sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_APPLY,
                        sohuBusyTaskSite.getUserId(), null, getEncryptName(userId), Boolean.TRUE, null, null);
                isReceive = Boolean.FALSE;
            } else {
                throw new ServiceException("申请接单太频繁,请刷新后再试");
            }
        }
        return isReceive;
    }

    /**
     * 默认构建接单对象
     *
     * @param bo
     * @param sohuBusyTaskVo
     * @param sohuBusyTaskSite
     * @param userId
     * @return
     */
    @Override
    public SohuBusyTaskReceive buildReceive(SohuBusyTaskReceiveBo bo,
                                            SohuBusyTaskVo sohuBusyTaskVo,
                                            SohuBusyTaskSite sohuBusyTaskSite,
                                            Long userId) {
        SohuBusyTaskReceive add = BeanUtil.toBean(bo, SohuBusyTaskReceive.class);
        validEntityBeforeSave(add);
        add.setUserId(userId);
        add.setState(SohuBusyTaskState.WaitApprove.name());
        add.setBackup(Boolean.TRUE);
        List<String> states = new ArrayList<>();
        states.add(McnUserStateEnum.NORMAL.getCode());
        states.add(McnUserStateEnum.DISABLED.getCode());
        SohuMcnUserVo sohuMcnUserVo = middleMcnUserService.findMcnIdByLoginId(states);
        if (Objects.nonNull(sohuMcnUserVo)) {
            add.setMcnId(sohuMcnUserVo.getMcnUserId());
        }
        if (!sohuBusyTaskVo.getIsApproveReceive()) {
            this.afterBuild(add);
        }
        return add;
    }

    /**
     * 获取加密名称
     *
     * @param userId 加密用户id
     * @return String 加密后的名称
     */
    private String getEncryptName(Long userId) {
        LoginUser receiverTaskUser = remoteUserService.queryById(userId);
        String name = "";
        if (Objects.nonNull(receiverTaskUser)) {
            name = StringUtils.sensitive(1, receiverTaskUser.getNickname(), 2, false);
        }
        return name;
    }

    /**
     * 接单校验
     *
     * @param sohuBusyTaskVo
     * @param sohuBusyTaskSite
     */
    private void checkReceive(SohuBusyTaskVo sohuBusyTaskVo,
                              SohuBusyTaskSite sohuBusyTaskSite, Long userId) {
        if (sohuBusyTaskVo.getState().equals(SohuBusyTaskState.WaitPay.name())) {
            throw new ServiceException(MessageUtils.message("商单未支付成功,无法进行接单操作"));
        }
        // 校验当前状态
        super.checkOnlineTask(sohuBusyTaskSite, userId);
    }

    @Override
    public void afterQueryExtendValue(SohuBusyTaskSiteVo vo, Long receiveId, Long receiveUserId, Long userId) {
        // 获取素材id
        SohuIndependentMaterialVo sohuIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByCodeAndType(vo.getMasterTaskNumber(), BusyTaskTypeEnum.COMMON_TASK.getCode());
        if (Objects.nonNull(sohuIndependentMaterialVo)) {
            vo.setMaterialId(sohuIndependentMaterialVo.getId());
        }
        if (userId != null && userId > 0L) {
            // 任务是否收藏
            vo.setCollectObj(Boolean.FALSE);
            SohuBusyTaskUserVo sohuBusyTaskUserVo = sohuBusyTaskUserService.getByUserIdAndTaskNumber(userId, vo.getMasterTaskNumber());
            if (Objects.nonNull(sohuBusyTaskUserVo)) {
                vo.setCollectObj(sohuBusyTaskUserVo.getIsCollect());
                sohuBusyTaskUserService.updateTime(sohuBusyTaskUserVo.getId(), null);
            } else {
                sohuBusyTaskUserService.addReadRecord(userId, vo.getMasterTaskNumber());
            }
            // 需要审核,查询审核记录
            if (vo.getIsApproveReceive()) {
                SohuAuditVo sohuAuditVo = sohuAuditService.selectNearByObj(vo.getBusyTaskId(), BusyType.ReceiveBusyTask.name(), userId);
                if (Objects.nonNull(sohuAuditVo)) {
                    vo.setApplyAnnex(sohuAuditVo.getApplyAnnex());
                    vo.setApplyMsg(sohuAuditVo.getApplyMsg());
                }
            }
        }
        if (Objects.nonNull(vo.getIpAddress())) {
            vo.setIpInfo(AddressUtils.getRealAddressByIP(vo.getIpAddress()));
        }
        // 服务费
        SohuIndependentTemplateModel independentTemplateModel = remoteIndependentTemplateService.queryTemplateInfo(null,null, Constants.TWO);
        if (Objects.nonNull(independentTemplateModel)) {
            BigDecimal add = vo.getFullAmount().add(Objects.nonNull(vo.getKickbackValue()) ? vo.getKickbackValue() : BigDecimal.ZERO);
            BigDecimal platformRatio = independentTemplateModel.getPlatformRatio();
            vo.setPlatformFee((add.multiply(platformRatio)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        }
        SohuBusyTaskPayVo payVo = iSohuBusyTaskPayService.queryByPayScene(vo.getMasterTaskNumber(), PayStatus.Paid.name(), PaySceceTypeEnum.BUSY_ORDER_PAY.getCode());
        if (Objects.nonNull(payVo)) {
            vo.setPayAmount(payVo.getPayAmount());
            vo.setPlatformFee(CalUtils.add(payVo.getPlatformAmount(), payVo.getChargeAmount()));
        }
        // 是否需要替换状态 驳回原因
        SohuBusyTaskReceiveVo receiveVo = null;
        if (Objects.nonNull(receiveId)) {
            receiveVo = sohuBusyTaskReceiveMapper.selectVoById(receiveId);
            vo.setState(receiveVo.getState());
        } else if (Objects.nonNull(receiveUserId)) {
            // 是否需要替换状态
            receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getTaskNumber, vo.getTaskNumber())
                    .eq(SohuBusyTaskReceive::getUserId, receiveUserId).orderByDesc(SohuBusyTaskReceive::getCreateTime)
                    .last("limit 1"));
            if (Objects.nonNull(receiveVo)) {
                vo.setState(receiveVo.getState());
            }
        }
        if (userId != null && userId > 0L) {
            SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getTaskNumber, vo.getTaskNumber())
                    .eq(SohuBusyTaskReceive::getUserId, userId).orderByDesc(SohuBusyTaskReceive::getCreateTime).last("limit 1"));
            if (Objects.nonNull(sohuBusyTaskReceiveVo)) {
                SohuAuditVo sohuAuditVo = null;
                if (vo.getState().equals(SohuBusyTaskState.WaitReceive.name()) || vo.getState().equals(SohuBusyTaskState.Refuse.name())) {
                    sohuAuditVo = sohuAuditService.selectNearByObj(vo.getBusyTaskId(), BusyType.ReceiveBusyTask.name(), userId);
                } else if (vo.getState().equals(SohuBusyTaskState.Execute.name())) {
                    sohuAuditVo = sohuAuditService.selectNearByObj(sohuBusyTaskReceiveVo.getId(), BusyType.SettleBusyTask.name(), userId);
                }
                if (Objects.nonNull(sohuAuditVo)) {
                    vo.setRefuseMsg(sohuAuditVo.getRejectReason());
                }
            }
        }
        // 获取支付商单预算以及时间
        SohuBusyTaskPayVo sohuBusyTaskPayVo = iSohuBusyTaskPayService.queryByPayScene(vo.getMasterTaskNumber(), PayStatus.Paid.name(), PaySceceTypeEnum.BUSY_ORDER_PAY.getCode());
        if (Objects.nonNull(sohuBusyTaskPayVo)) {
            vo.setPayAmount(sohuBusyTaskPayVo.getPayAmount());
            vo.setPayTime(sohuBusyTaskPayVo.getPayTime());
        }
        // 接单方返回剩余支付时间,四小时倒计时
        SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getTaskNumber, vo.getTaskNumber())
                .eq(SohuBusyTaskReceive::getUserId, userId)
                .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitPromisePay.name()));
        if (Objects.nonNull(sohuBusyTaskReceiveVo)) {
            vo.setRemainPayTime(CalUtils.getRemainTime(sohuBusyTaskReceiveVo.getPassTime(), Constants.FOUR));
        }
    }

    /**
     * 处理站点信息
     *
     * @param bo
     * @param userId
     * @param task
     */
    private void hanleTaskSite(SohuBusyTaskBo bo, Long userId, SohuBusyTask task) {
        if (BooleanUtil.isFalse(bo.getIsDraft())) {
            List<SohuBusyTaskSite> taskSites = new ArrayList<>();
            for (Long siteId : bo.getAddSiteIds()) {
                SohuBusyTaskSite taskSite = new SohuBusyTaskSite();
                taskSite.setMasterTaskNumber(task.getTaskNumber());
                taskSite.setUserId(userId);
                taskSite.setDistributionAmount(bo.getKickbackValue());
                taskSite.setTaskNumber(NumberUtil.getOrderNo(OrderConstants.CHILD_TASK_PREFIX));
                taskSite.setSiteId(siteId);
                taskSite.setCountrySiteId(task.getCountrySiteId());
                taskSite.setAuditUser(userId);
                taskSite.setAuditTime(new Date());
                taskSite.setShelfState(SohuBusyTaskState.OnShelf.name());
                this.extractedTaskSite(task, taskSite);
                if (BooleanUtil.isTrue(bo.getIsDraft())) {
                    taskSite.setState(SohuBusyTaskState.Edit.name());
                } else if (StrUtil.equalsAnyIgnoreCase(bo.getKickbackType(), KickbackType.none.getCode())) {
                    // 未设置佣金,无需审核
                    taskSite.setKickbackValue(BigDecimal.ZERO);
                    taskSite.setState(SohuBusyTaskState.WaitReceive.name());
                } else {
                    // 设置佣金,需要支付佣金
                    taskSite.setState(SohuBusyTaskState.WaitIndependentPay.name());
                }
                taskSites.add(taskSite);
            }
            if (CollectionUtils.isNotEmpty(taskSites)) {
                // 保存站点关联
                sohuBusyTaskSiteMapper.insertBatch(taskSites);
            }
            // 同步分销素材库
            this.syncIndependentMaterial(task);
            // 撤销交易
            remotePaySettlementService.barcodeCancelPay(task.getTaskNumber());
        }
    }

    /**
     * 同步分销素材库
     *
     * @param task 任务主体
     */
    public void syncIndependentMaterial(SohuBusyTask task) {
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(task.getType());
        if (Objects.nonNull(sohuCategoryVo)) {
            // 流量任务,开启分销,同步素材库
            if (StrUtil.equalsAnyIgnoreCase(sohuCategoryVo.getConstMark(), BusyTaskTypeEnum.COMMON_TASK.getCode()) && !task.getKickbackType().equals(KickbackType.none.getCode())) {
                remoteMiddleIndependentMaterialService.insertByBo(buildFlowTaskBo(task.getTaskNumber()));
            }
        }
    }

    /**
     * 通用性型表单商单单独构建
     *
     * @param masterTaskNumber 主任务编号
     * @return SohuIndependentMaterialBo
     */
    public SohuIndependentMaterialBo buildFlowTaskBo(String masterTaskNumber) {
        SohuBusyTaskVo vo = sohuBusyTaskMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, masterTaskNumber));
        SohuIndependentMaterialBo independentMaterialBo = new SohuIndependentMaterialBo();
        independentMaterialBo.setMaterialName(vo.getTitle());
        independentMaterialBo.setMaterialType(BusyTaskTypeEnum.COMMON_TASK.getCode());
        independentMaterialBo.setPrice(vo.getFullAmount());
        independentMaterialBo.setMaterialUserId(vo.getUserId());
        independentMaterialBo.setSiteId(Long.parseLong(vo.getSiteIds()));
        independentMaterialBo.setMaterialCode(vo.getTaskNumber());
        independentMaterialBo.setCategoryId(vo.getType());
        independentMaterialBo.setStatus(CommonState.OnShelf.getCode());
        // 计算分销金额
        if (!StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.none.getCode())) {
            if (StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.price.getCode())) {
                // 一口价
                independentMaterialBo.setIndependentPrice(vo.getKickbackValue());
            } else {
                BigDecimal taskDivide = BigDecimalUtils.divide(vo.getKickbackValue(), CalUtils.PERCENTAGE);
                // 百分比
                independentMaterialBo.setIndependentPrice(vo.getFullAmount().multiply(taskDivide).setScale(2, RoundingMode.HALF_UP));
            }
        } else {
            vo.setDistributionAmount(BigDecimal.ZERO);
        }
        return independentMaterialBo;
    }

    /**
     * 查询申请记录
     * 判断审核通过还是驳回
     * 审核通过:
     * 1、基于主单进行上锁
     * 2、从主单中取一个待接单的子单编号
     * 3、统一校验
     * 4、调用正常的接单逻辑进行接单操作
     * 5、更改审核状态为通过并发送申请通过通知
     * 6、判断是否还存在待接单的子单编号（存在,则将同一个人的申请记录给全部驳回掉, 不存在,则将其他审核中的申请记录驳回,驳回原因默认接单人数已达上限并发送驳回通知）
     * <p>
     * 驳回:更改记录为驳回并记录驳回原因并发送接单申请驳回系统通知
     *
     * @param processBo
     * @param busyTask
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean receiveApplyProcess(SohuBusyTaskReceiveApplyProcessBo processBo, SohuBusyTask busyTask) {
        // 查询申请记录
        SohuAuditVo sohuAudit = sohuAuditService.queryById(processBo.getId());
        if (Objects.isNull(sohuAudit)) {
            throw new ServiceException("已操作该记录");
        }
        if (!sohuAudit.getSysAuditState().equals(CommonState.WaitApprove.getCode())) {
            throw new ServiceException("该记录已进行审核,无需进行二次审核");
        }
        // 从主单中取一个待接单的子单编号
        List<SohuBusyTaskSite> sohuBusyTaskSiteList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                .eq(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive.name()));
        // 从主单中取一个待接单的子单编号
        if (CollectionUtils.isEmpty(sohuBusyTaskSiteList)) {
            throw new ServiceException("接单人数已达上限");
        }
        SohuBusyTaskSite sohuBusyTaskSite = sohuBusyTaskSiteList.get(0);
        // 统一校验
        SohuBusyTaskReceive taskReceive = sohuBusyTaskReceiveMapper.selectOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSite.getTaskNumber())
                .eq(SohuBusyTaskReceive::getUserId, sohuAudit.getBusyBelonger())
                .in(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitFullAmountPay.name(), SohuBusyTaskState.WaitApprove.name()).last("limit 1"));
        if (Objects.isNull(taskReceive)) {
            throw new ServiceException("数据异常，请检查后重试");
        }
        if (processBo.getAuditState().equals(CommonState.OnShelf.getCode())) {
            // 审核通过上架
            String lockKey = String.format("sohu:lock:receive_master_task_number:%s", busyTask.getTaskNumber());
            if (RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(1))) {
                SohuBusyTaskVo sohuBusyTaskVo = BeanUtil.toBean(busyTask, SohuBusyTaskVo.class);
                taskReceive.setBackup(Boolean.TRUE);
                taskReceive.setState(SohuBusyTaskState.WaitFullAmountPay.name());
                taskReceive.setPassTime(new Date());
                sohuBusyTaskReceiveMapper.updateById(taskReceive);
                this.afterReceive(Boolean.TRUE, taskReceive, sohuBusyTaskSite, sohuBusyTaskVo, sohuAudit.getUserId(), Boolean.TRUE);
                // 更改审核状态为通过并发送申请通过通知
                this.handleAuditRecord(sohuAudit, CommonState.OnShelf.getCode(), "", sohuBusyTaskSite);
                // 驳回剩余接单状态
                List<SohuBusyTaskReceive> receiveList = sohuBusyTaskReceiveMapper.selectList(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                        .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSite.getTaskNumber())
                        .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitApprove.name()));
                if (CollectionUtils.isNotEmpty(receiveList)) {
                    for (SohuBusyTaskReceive receive : receiveList) {
                        receive.setBackup(Boolean.FALSE);
                        receive.setState(SohuBusyTaskState.Refuse.name());
                        receive.setRefuseMsg("接单人数已达上限");
                    }
                    sohuBusyTaskReceiveMapper.updateBatchById(receiveList);
                }
                SohuAuditBo sohuAuditBo = new SohuAuditBo();
                sohuAuditBo.setSysAuditState(CommonState.WaitApprove.getCode());
                sohuAuditBo.setBusyType(BusyType.ReceiveBusyTask.getType());
                sohuAuditBo.setBusyCode(busyTask.getId());
                List<SohuAuditVo> sohuAuditList = sohuAuditService.queryList(sohuAuditBo);
                if (CollectionUtils.isNotEmpty(sohuAuditList)) {
                    for (SohuAuditVo sohuAuditVo : sohuAuditList) {
                        //  获取对应的 SohuBusyTaskReceive 记录
                        SohuBusyTaskReceive receive = sohuBusyTaskReceiveMapper.selectOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                                .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSite.getTaskNumber())
                                .eq(SohuBusyTaskReceive::getUserId, sohuAuditVo.getBusyBelonger())
                                .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitApprove.name()));
                        if (receive != null) {
                            receive.setBackup(Boolean.FALSE);
                            receive.setState(SohuBusyTaskState.Refuse.name());
                            sohuBusyTaskReceiveMapper.updateById(receive);
                        }
                        // 审核拒绝更改记录为驳回并记录驳回原因
                        this.handleAuditRecord(sohuAuditVo, CommonState.Refuse.getCode(), "接单人数已达上限", sohuBusyTaskSite);
                    }
                }
            } else {
                throw new ServiceException("上个审核流程未处理结束,请稍后再试");
            }
        } else {
            taskReceive.setBackup(Boolean.FALSE);
            taskReceive.setState(SohuBusyTaskState.Refuse.name());
            sohuBusyTaskReceiveMapper.updateById(taskReceive);
            // 审核拒绝更改记录为驳回并记录驳回原因
            this.handleAuditRecord(sohuAudit, CommonState.Refuse.getCode(), StringUtils.isEmpty(processBo.getRejectReason()) ? "接单人数已达上限" : processBo.getRejectReason(), sohuBusyTaskSite);
        }
        return Boolean.TRUE;
    }

    /**
     * 处理审核记录
     * 1、更改记录审核状态
     * 2、发送系统消息
     *
     * @param sohuAudit
     * @param auditState
     * @param rejectReason
     * @param sohuBusyTaskSite
     */
    private void handleAuditRecord(SohuAuditVo sohuAudit, String auditState, String rejectReason, SohuBusyTaskSite sohuBusyTaskSite) {
        SohuAuditBo sohuAuditBo = BeanUtil.toBean(sohuAudit, SohuAuditBo.class);
        sohuAuditBo.setState(auditState);
        sohuAuditBo.setRejectReason(rejectReason);
        sohuAuditService.audit(sohuAuditBo);
        if (auditState.equals(CommonState.OnShelf.getCode())) {
            // 审核通过消息
            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_APPLY_SUCCESS_COMMON,
                    sohuAudit.getBusyBelonger(), null, null, Boolean.TRUE, null, null);
        } else {
            // 同步待支付订单状态
            remotePaySettlementService.barcodeCancelPay(sohuBusyTaskSite.getTaskNumber());
            // 审核拒绝消息
            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_APPLY_REFUSE,
                    sohuAudit.getBusyBelonger(), rejectReason, null, Boolean.TRUE, null, null);
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean cancel(SohuBusyTaskVo busyTaskVo, SohuBusyTaskSiteVo busyTaskSiteVo, SohuBusyTaskReceiveVo busyTaskReceiveVo) {
        // 子单回到待接单状态
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        site.setId(busyTaskSiteVo.getId());
        site.setState(SohuBusyTaskState.WaitReceive.name());
        sohuBusyTaskSiteMapper.updateById(site);
        // 接单记录改为已取消
        SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
        receive.setId(busyTaskReceiveVo.getId());
        receive.setState(SohuBusyTaskState.Cancel.name());
        sohuBusyTaskReceiveMapper.updateById(receive);
        // 查询主单下子单是否都是待接单,是则更改主单为待接单状态
        List<String> stateList = new ArrayList<>();
        stateList.add(SohuBusyTaskState.OverSettle.name());
        stateList.add(SohuBusyTaskState.Execute.name());
        stateList.add(SohuBusyTaskState.WaitApproveSettle.name());
        stateList.add(SohuBusyTaskState.WaitSettle.name());
        Long num = sohuBusyTaskSiteMapper.selectCount(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTaskVo.getTaskNumber())
                .in(SohuBusyTaskSite::getState, stateList));
        if (num == 0) {
            sohuBusyTaskMapper.updateTaskState(busyTaskSiteVo.getMasterTaskNumber(), SohuBusyTaskState.WaitReceive.name());
        }
        // 单人取消接单同步素材库
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(busyTaskVo.getType());
        if (Objects.nonNull(sohuCategoryVo)) {
            // 通用商单,开启分销,同步素材库
            if (StrUtil.equalsAnyIgnoreCase(sohuCategoryVo.getConstMark(), BusyTaskTypeEnum.COMMON_TASK.getCode()) && !busyTaskVo.getKickbackType().equals(KickbackType.none.getCode())) {
                SohuIndependentMaterialVo sohuIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByFlowTask(busyTaskVo.getTaskNumber(), BusyTaskTypeEnum.COMMON_TASK.getCode());
                if (Objects.nonNull(sohuIndependentMaterialVo)) {
                    remoteMiddleIndependentMaterialService.updateByBo(new SohuIndependentMaterialBo()
                            .setId(sohuIndependentMaterialVo.getId())
                            .setStatus(CommonState.OnShelf.getCode())
                    );
                }
            }
        }
        // 发单方商单预算退款
        remotePaySettlementService.cancelTaskSettle(busyTaskSiteVo.getMasterTaskNumber(), PaySceceTypeEnum.BUSY_ORDER_PAY.getCode());
        //给发单方发送取消商单通知
        sohuBusyTaskNoticeService.sendTaskNotice(busyTaskVo.getId(), TaskNoticeEnum.TASK_RECEIVE_CANCEL,
                busyTaskVo.getUserId(), null, getEncryptName(busyTaskReceiveVo.getUserId()), Boolean.TRUE);
        // 解散群聊
        SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().mainTaskNumber(busyTaskSiteVo.getMasterTaskNumber()).build();
        remoteImGroupFromGeneralService.handleOverGroup(formHandleBo);
        // 清空审核记录
        SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoOne(Wrappers.lambdaQuery(SohuBusyTask.class).eq(SohuBusyTask::getTaskNumber, busyTaskSiteVo.getMasterTaskNumber()));
        if (Objects.nonNull(sohuBusyTaskVo)) {
            sohuAuditService.deleteByObj(sohuBusyTaskVo.getId(), BusyType.ReceiveBusyTask.name());
            // 只允许一人接单
            taskExecuteMapper.delete(Wrappers.lambdaQuery(SohuBusyTaskExecute.class)
                    .eq(SohuBusyTaskExecute::getTaskNumber, busyTaskSiteVo.getTaskNumber()));
        }
        // 删除交付记录
        List<SohuBusyTaskReceiveVo> sohuBusyTaskReceiveVoList = sohuBusyTaskReceiveMapper.selectVoList(Wrappers.lambdaQuery(SohuBusyTaskReceive.class)
                .eq(SohuBusyTaskReceive::getTaskNumber, busyTaskSiteVo.getTaskNumber())
                .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.Cancel.name()));
        if (CollUtil.isNotEmpty(sohuBusyTaskReceiveVoList)) {
            for (SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo : sohuBusyTaskReceiveVoList) {
                sohuAuditService.deleteByObj(sohuBusyTaskReceiveVo.getId(), BusyType.SettleBusyTask.name());
            }
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applySettle(SohuBusyTaskVo busyTaskVo, SohuBusyTaskSiteVo busyTaskSiteVo, SohuBusyTaskReceiveVo busyTaskReceiveVo, SohuBusyTaskDeliveryCommonVo vo) {
        // 修改主单为待结算状态
        sohuBusyTaskMapper.updateTaskState(busyTaskSiteVo.getMasterTaskNumber(), SohuBusyTaskState.WaitApproveSettle.name());
        // 子单改为待结算状态（也叫结算审核中状态）
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        site.setId(busyTaskSiteVo.getId());
        site.setState(SohuBusyTaskState.WaitApproveSettle.name());
        sohuBusyTaskSiteMapper.updateById(site);
        // 接单记录改为待结算状态（也叫结算审核中状态）
        SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
        receive.setId(busyTaskReceiveVo.getId());
        receive.setState(SohuBusyTaskState.WaitApproveSettle.name());
        sohuBusyTaskReceiveMapper.updateById(receive);
        busyTaskReceiveVo.setSiteId(busyTaskSiteVo.getSiteId());
        // 补充待审核记录
        sohuAuditService.createAudited(new SohuAuditBo(busyTaskReceiveVo, BusyType.SettleBusyTask.name()));
        // 补充商单交付记录
        SohuBusyTaskExecute execute = new SohuBusyTaskExecute();
        execute.setTaskNumber(busyTaskSiteVo.getTaskNumber());
        execute.setBusyTaskReceiveId(busyTaskReceiveVo.getUserId());
        execute.setBusyTaskDeliveryId(0L);
        execute.setExecuteType("Pull");
        execute.setAnnex(Objects.nonNull(vo.getAnnex()) ? vo.getAnnex() : null);
        execute.setContent(Objects.nonNull(vo.getContent()) ? vo.getContent() : null);
        taskExecuteMapper.insert(execute);
        // 发送任务交付通知
        sohuBusyTaskNoticeService.sendTaskSiteNotice(busyTaskSiteVo.getTaskNumber(), TaskNoticeEnum.TASK_UPDATE,
                busyTaskSiteVo.getUserId(), null, getEncryptName(busyTaskReceiveVo.getUserId()), Boolean.TRUE, null, null);
        return Boolean.TRUE;
    }

    @Override
    public Boolean auditReceiveTask(SohuBusyTaskReceive taskReceive, SohuBusyTaskVo sohuBusyTaskVo, SohuBusyTaskSite sohuBusyTaskSite) {
        if (StrUtil.equalsAnyIgnoreCase(taskReceive.getState(), SohuBusyTaskState.Refuse.name())) {
            // 审核拒绝,更改接单状态为进行中
            SohuBusyTask task = new SohuBusyTask();
            task.setId(sohuBusyTaskVo.getId());
            task.setState(SohuBusyTaskState.Execute.name());
            sohuBusyTaskMapper.updateById(task);
            SohuBusyTaskSite site = new SohuBusyTaskSite();
            site.setId(sohuBusyTaskSite.getId());
            site.setState(SohuBusyTaskState.Execute.name());
            sohuBusyTaskSiteMapper.updateById(site);
            // 接单记录改为进行中状态
            SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
            receive.setId(taskReceive.getId());
            receive.setState(SohuBusyTaskState.Execute.name());
            sohuBusyTaskReceiveMapper.updateById(receive);
            this.auditRecord(taskReceive.getId(), taskReceive.getRefuseMsg(), SohuBusyTaskState.Refuse.name(), taskReceive.getUserId());
            // 发送任务结算申请未通过通知
            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_SETTLE_APPLY_FAIL,
                    taskReceive.getUserId(), taskReceive.getRefuseMsg(), null, Boolean.TRUE, null, null);
        }
        return Boolean.TRUE;
    }

    /**
     * 审核记录
     *
     * @param receiveId
     * @param rejectReason
     * @param state
     */
    private void auditRecord(Long receiveId, String rejectReason, String state, Long userId) {
        SohuAuditVo sohuAuditVo = sohuAuditService.selectNearByObj(receiveId, BusyType.SettleBusyTask.name(), userId);
        SohuAuditBo auditBo = new SohuAuditBo();
        auditBo.setId(sohuAuditVo.getId());
        auditBo.setRejectReason(rejectReason);
        auditBo.setBusyType(BusyType.SettleBusyTask.name());
        auditBo.setBusyCode(receiveId);
        auditBo.setState(state);
        sohuAuditService.process(auditBo);
    }

    /**
     * 一键结算商单
     *
     * @param busyTask
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean settleBusyTask(SohuBusyTask busyTask) {
        // 修改主单状态已完结
        sohuBusyTaskMapper.updateTaskState(busyTask.getTaskNumber(), SohuBusyTaskState.OverSettle.name());

        // 修改子单状态已完结
        List<String> stateList = new ArrayList<>();
        stateList.add(SohuBusyTaskState.OverSettle.name());
        stateList.add(SohuBusyTaskState.Cancel.name());
        stateList.add(SohuBusyTaskState.WaitReceive.name());
        List<SohuBusyTaskSite> siteList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                .notIn(SohuBusyTaskSite::getState, stateList));
        if (CollectionUtils.isNotEmpty(siteList)) {
            // 通用商单主子单一对一
            String taskNumber = siteList.get(0).getTaskNumber();
            SohuBusyTaskSiteVo sohuBusyTaskSiteVo = sohuBusyTaskSiteService.queryByTaskNumber(taskNumber);
            if (Objects.isNull(sohuBusyTaskSiteVo)) {
                return Boolean.FALSE;
            }
            SohuBusyTaskSite site = new SohuBusyTaskSite();
            site.setId(sohuBusyTaskSiteVo.getId());
            site.setState(SohuBusyTaskState.OverSettle.name());
            sohuBusyTaskSiteMapper.updateById(site);

            // 修改接单状态已完结
            List<String> receiveStateList = new ArrayList<>();
            receiveStateList.add(SohuBusyTaskState.WaitSettle.name());
            receiveStateList.add(SohuBusyTaskState.WaitApproveSettle.name());
            receiveStateList.add(SohuBusyTaskState.Execute.name());
            SohuBusyTaskReceiveVo receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getTaskNumber, taskNumber)
                    .in(SohuBusyTaskReceive::getState, receiveStateList).last("limit 1"));
            if (Objects.nonNull(receiveVo)) {
                SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
                receive.setId(receiveVo.getId());
                receive.setState(SohuBusyTaskState.OverSettle.name());
                sohuBusyTaskReceiveMapper.updateById(receive);
            }

            // 解散群聊
            SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().mainTaskNumber(sohuBusyTaskSiteVo.getMasterTaskNumber()).build();
            remoteImGroupFromGeneralService.handleOverGroup(formHandleBo);

            // 发送系统通知
            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSiteVo.getTaskNumber(), TaskNoticeEnum.TASK_END,
                    receiveVo.getUserId(), null, null, Boolean.TRUE, null, null);

            // 补充事件统计
//            SohuEventReportBo reportBo = new SohuEventReportBo();
//            reportBo.setEventType(BusyTaskReportEnum.WCSD.getType());
//            reportBo.setBusyCode(taskNumber);
//            reportBo.setBusyName(sohuBusyTaskSiteVo.getTitle());
//            reportBo.setUserId(receiveVo.getUserId());
//            remoteMiddleEventReportService.getEventId(reportBo);

        }
        return Boolean.TRUE;
    }

    /**
     * 接单后逻辑处理
     *
     * @param flag
     * @param add
     * @param sohuBusyTaskSite
     * @param sohuBusyTaskVo
     * @return
     */
    @Override
    public Boolean afterReceiveTask(boolean flag,
                                    SohuBusyTaskReceive add,
                                    SohuBusyTaskSite sohuBusyTaskSite,
                                    SohuBusyTaskVo sohuBusyTaskVo, Long userId,
                                    Boolean isSendNotice) {
        if (flag) {
            try {
                if (!sohuBusyTaskSite.getIsReceive()) {
                    sohuBusyTaskSite.setIsReceive(Boolean.TRUE);
                    sohuBusyTaskSiteMapper.updateById(sohuBusyTaskSite);
                }
            } catch (Exception e) {
                log.error("通知发送异常,避免影响后续流程及逻辑");
            }
        }
        this.afterReceive(flag, add, sohuBusyTaskSite, sohuBusyTaskVo, userId, isSendNotice);
        BusyTaskReceiveEvent receiveEvent = new BusyTaskReceiveEvent(this, add);
        applicationEventPublisher.publishEvent(receiveEvent);
        return flag;
    }

    @Override
    public void queryChildReceive(SohuBusyTaskSiteVo vo, Long userId) {
        // 是否存在接单待审核(锁单)
        List<SohuBusyTaskReceive> sohuBusyTaskReceiveList = sohuBusyTaskReceiveMapper.selectList(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getTaskNumber, vo.getTaskNumber())
                .in(SohuBusyTaskReceive::getState
                        , SohuBusyTaskState.WaitFullAmountPay.name()
                        , SohuBusyTaskState.Execute.name()
                        , SohuBusyTaskState.WaitPromisePay.name()
                        , SohuBusyTaskState.WaitApproveSettle.name()
                ));
        if (CollectionUtils.isNotEmpty(sohuBusyTaskReceiveList)) {
            for (SohuBusyTaskReceive receive : sohuBusyTaskReceiveList) {
                if (Objects.nonNull(userId) && !receive.getUserId().equals(userId)) {
                    throw new ServiceException("当前愿望已被他人接取,可前往愿望集市查看更多愿望");
                }
            }
        }
    }

}
