package com.sohu.busyOrder.base.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.busyOrder.base.FlowService;
import com.sohu.busyOrder.domain.*;
import com.sohu.busyOrder.mapper.SohuBusyTaskLabelMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskSourceMaterialMapper;
import com.sohu.busyOrder.service.ISohuBusyTaskLabelService;
import com.sohu.busyOrder.service.ISohuBusyTaskSourceMaterialService;
import com.sohu.busyOrder.service.ISohuBusyTaskUserService;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.busyorder.api.RemoteBusyTaskService;
import com.sohu.busyorder.api.bo.SohuBusyTaskBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskDeliveryBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveApplyProcessBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.utils.ip.AddressUtils;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.SohuImGroupFormCreateBo;
import com.sohu.im.api.bo.SohuImGroupFormHandleBo;
import com.sohu.im.api.bo.SohuImGroupUserBo;
import com.sohu.im.api.enums.ImGroupType;
import com.sohu.im.api.service.RemoteImGroupFormService;
import com.sohu.im.api.service.RemoteImGroupOrderUserService;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.im.api.vo.SohuImGroupUserVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.middle.api.bo.SohuAuditBo;
import com.sohu.middle.api.bo.SohuIndependentMaterialBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.bo.SohuUserLabelRelationBo;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.enums.report.BusyTaskReportEnum;
import com.sohu.middle.api.service.RemoteMiddleAuditService;
import com.sohu.middle.api.service.RemoteMiddleCategoryService;
import com.sohu.middle.api.service.RemoteMiddleCommonLabelService;
import com.sohu.middle.api.vo.SohuAuditVo;
import com.sohu.middle.api.vo.SohuCategoryVo;
import com.sohu.middle.api.vo.SohuIndependentMaterialVo;
import com.sohu.pay.api.RemotePayOrderService;
import com.sohu.pay.api.RemotePaySettlementService;
import com.sohu.resource.api.RemoteSmsService;
import io.seata.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 流量商单
 *
 * @Author: leibo
 * @Date: 2025/1/13 15:03
 **/
@Slf4j
@Service
public class FlowServiveImpl extends BaseTaskServiceImpl implements FlowService {

    @Autowired
    private SohuBusyTaskLabelMapper sohuBusyTaskLabelMapper;
    @Autowired
    private SohuBusyTaskSourceMaterialMapper sohuBusyTaskSourceMaterialMapper;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;
    @DubboReference
    private RemotePayOrderService remotePayOrderService;
    @DubboReference
    private RemoteImGroupFormService remoteImGroupFormService;
    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;
    @DubboReference
    private RemoteImGroupOrderUserService remoteImGroupOrderUserService;
    @DubboReference
    private RemoteMiddleAuditService sohuAuditService;
    @DubboReference
    private RemoteBusyTaskService remoteBusyTaskService;
    @DubboReference
    private RemoteBusyTaskReceiveService remoteBusyTaskReceiveService;
    @DubboReference
    private RemoteImService remoteImService;
    @DubboReference
    private RemotePaySettlementService remotePaySettlementService;
    @Autowired
    private ISohuBusyTaskSourceMaterialService sourceMaterialService;
    @Autowired
    private ISohuBusyTaskLabelService labelService;
    @Autowired
    private ISohuBusyTaskUserService sohuBusyTaskUserService;
    @DubboReference
    private RemoteSmsService remoteSmsService;

    @Override
    public Long updateTask(SohuBusyTask busyTask, SohuBusyTaskBo bo) {
        SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoById(bo.getId());
        if (Objects.isNull(sohuBusyTaskVo)) {
            throw new ServiceException("该愿望不存在,请确认后再试");
        }
        if (!sohuBusyTaskVo.getState().equals(SohuBusyTaskState.Edit.name())) {
            throw new ServiceException("愿望非草稿状态下不允许编辑");
        }
        if (BooleanUtil.isFalse(bo.getIsDraft())) {
            busyTask.setState(SohuBusyTaskState.WaitPay.name());
        } else {
            busyTask.setState(SohuBusyTaskState.Edit.name());
        }
        busyTask.setFullAmount(bo.getSingleAmount().multiply(new BigDecimal(bo.getDeliveryStandard())));
        boolean flag = sohuBusyTaskMapper.updateById(busyTask) > 0;
        // 处理站点信息
        this.hanleTaskSite(bo, sohuBusyTaskVo.getUserId(), busyTask);
        labelService.save(bo.getLabelList(), sohuBusyTaskVo.getTaskNumber());
        sourceMaterialService.save(bo.getSourceMaterialList(), bo.getId());
        return bo.getId();
    }

    @Override
    public void afterUpdateTask(SohuBusyTask busyTask, SohuBusyTaskBo bo) {
    }

    @Override
    public void afterQueryTask(SohuBusyTaskVo vo) {
        List<SohuBusyTaskLabel> taskLabelList = sohuBusyTaskLabelMapper.selectList(Wrappers.<SohuBusyTaskLabel>lambdaQuery()
                .eq(SohuBusyTaskLabel::getTaskNumber, vo.getTaskNumber()));
        if (CollectionUtils.isNotEmpty(taskLabelList)) {
            Map<Long, String> labelMap = remoteMiddleCommonLabelService.queryLabelNamesByIds(
                    taskLabelList.stream().map(SohuBusyTaskLabel::getLabelId).collect(Collectors.toList()));
            if (ObjectUtils.isNotNull(labelMap)) {
                List<SohuBusyTaskVo.Label> labelList = new ArrayList<>();
                for (Map.Entry<Long, String> entry : labelMap.entrySet()) {
                    SohuBusyTaskVo.Label label = new SohuBusyTaskVo.Label();
                    label.setLabelName(entry.getValue());
                    label.setLabelId(entry.getKey());
                    labelList.add(label);
                }
                vo.setLabelList(labelList);
            }
        }
        List<SohuBusyTaskSourceMaterial> materialList = sohuBusyTaskSourceMaterialMapper.selectList(Wrappers.<SohuBusyTaskSourceMaterial>lambdaQuery()
                .eq(SohuBusyTaskSourceMaterial::getBusyTaskId, vo.getId()));
        if (CollectionUtils.isNotEmpty(materialList)) {
            vo.setSourceMaterialList(materialList.stream().map(SohuBusyTaskSourceMaterial::getSourceMaterialUrl).collect(Collectors.toList()));
        }
        // 剩余招募人数
        Integer passNum = remoteImGroupOrderUserService.getPassPersonUserByMasterTaskNumber(vo.getTaskNumber());
        vo.setWaitPassPersonNum(vo.getDeliveryStandard() - passNum);
        // 1.2新增优化项
        vo.setPassPersonNum(Long.valueOf(passNum));
        vo.setCompletedAmount(vo.getSingleAmount().multiply(new BigDecimal(passNum)));
        SohuImGroupVo sohuImGroupVo = remoteImService.queryByTaskNumber(ImGroupType.groupForm.name(), vo.getTaskNumber());
        if (Objects.nonNull(sohuImGroupVo)) {
            vo.setGroupId(sohuImGroupVo.getId());
        }
        // 判断接单是否需要审核,如果需要审核则查询申请接单记录
        if (vo.getIsApproveReceive()) {
            Long receiveNum = sohuAuditService.countByBusyCodeAndBusyType(vo.getId(), BusyType.ReceiveBusyTask.getType(), null);
            vo.setIsAuditRecord(receiveNum > 0);
            Long waitApproveNum = sohuAuditService.countByBusyCodeAndBusyType(vo.getId(), BusyType.ReceiveBusyTask.getType(), SohuBusyTaskState.WaitApprove.name());
            vo.setWaitApproveNum(waitApproveNum.intValue());
        }
        // 根据主任务编号获取子任务列表
        List<SohuBusyTaskSiteModel> busyTaskSiteList = remoteBusyTaskService.getChildList(vo.getTaskNumber());
        if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
            // 根据子任务编号获取接单详情
            List<SohuBusyTaskReceiveVo> sohuBusyTaskReceiveVoList = remoteBusyTaskReceiveService.queryReceiveListByTaskNumber(busyTaskSiteList.stream().map(SohuBusyTaskSiteModel::getTaskNumber).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(sohuBusyTaskReceiveVoList)) {
                ArrayList<SohuBusyTaskChildVo> sohuBusyTaskChildVos = new ArrayList<>();
                for (SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo : sohuBusyTaskReceiveVoList) {
                    SohuBusyTaskChildVo sohuBusyTaskChildVo = new SohuBusyTaskChildVo();
                    sohuBusyTaskChildVo.setId(sohuBusyTaskReceiveVo.getId());
                    sohuBusyTaskChildVo.setChildGroupId(Optional.ofNullable(sohuBusyTaskReceiveVo.getRelationId()).orElse(0L));
                    sohuBusyTaskChildVo.setReceiveTime(sohuBusyTaskReceiveVo.getCreateTime());
                    sohuBusyTaskChildVo.setChildTaskNumber(sohuBusyTaskReceiveVo.getTaskNumber());
                    sohuBusyTaskChildVo.setReceiveStatus(sohuBusyTaskReceiveVo.getState());
                    sohuBusyTaskChildVo.setReceiveUserName(sohuBusyTaskReceiveVo.getNickName());
                    // 根据子任务编号获取已达标数量
                    Integer passPersonNum = remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(sohuBusyTaskReceiveVo.getTaskNumber());
                    sohuBusyTaskChildVo.setPassPersonNum(passPersonNum.longValue());
                    // 计算已完成金额 = 单价 * 已达标数量
                    sohuBusyTaskChildVo.setCompletedAmount(vo.getSingleAmount().multiply(new BigDecimal(passPersonNum)));
                    sohuBusyTaskChildVos.add(sohuBusyTaskChildVo);
                }
                // 根据时间倒叙排序
                sohuBusyTaskChildVos.sort(Comparator.comparing(SohuBusyTaskChildVo::getReceiveTime).reversed());
                vo.setChildVoList(sohuBusyTaskChildVos);
            }
        }
    }

    @Override
    public void afterShelfTask(SohuBusyTask busyTask) {
        if (!SohuBusyTaskState.CompelOff.name().equals(busyTask.getState())) {
            throw new ServiceException("该类型愿望不支持二次上架");
        }
        // 下架逻辑同步素材库
        List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteService.listByMasterTaskNo(busyTask.getTaskNumber());
        if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
            remoteMiddleIndependentMaterialService.deleteByCodeAndType(busyTask.getTaskNumber(), BusyTaskTypeEnum.FLOW_TASK.getCode());
        }
        remotePayOrderService.refundPayOrder(busyTask.getTaskNumber(), SohuTradeRecordEnum.Type.BusyTaskFlow.getCode());
    }

    @Override
    public Boolean beforeReceiveTask(SohuBusyTaskReceiveBo bo, SohuBusyTaskVo sohuBusyTaskVo,
                                     SohuBusyTaskSite sohuBusyTaskSite, Long userId) {
        this.checkReceive(sohuBusyTaskVo, sohuBusyTaskSite, null);
        // 判断是否需要审核接单,需要审核接单的话则存入审核记录，返回false,不需要审核则直接返回TRUE
        Boolean isReceive = Boolean.TRUE;
        if (sohuBusyTaskVo.getIsApproveReceive()) {
            if (StringUtils.isEmpty(bo.getApplyMsg())) {
                throw new ServiceException("申请接单理由不能为空");
            }
            // 查询最近的一条审核记录
            SohuAuditVo sohuAuditVo = sohuAuditService.selectNearByObj(sohuBusyTaskVo.getId(), BusyType.ReceiveBusyTask.getType(), userId);
            if (Objects.nonNull(sohuAuditVo) && sohuAuditVo.getSysAuditState().equals(SohuBusyTaskState.WaitApprove.name())) {
                throw new ServiceException("已申请过该愿望,无需重新申请,请等待许愿人处理");
            }
            String lockKey = String.format("sohu:lock:apply_receive_master_task_number:%s:%s", sohuBusyTaskVo.getTaskNumber(), userId);
            if (RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(1))) {
                // 补充待审核记录
                sohuAuditService.createAudited(new SohuAuditBo(sohuBusyTaskVo.getId(), BusyType.ReceiveBusyTask.getType(),
                        userId, sohuBusyTaskSite.getSiteId(), bo.getApplyMsg(), bo.getApplyAnnex(),bo.getSiteType(),bo.getSiteId()));
                // 发送发单方任务接单通知
                sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_APPLY,
                        sohuBusyTaskSite.getUserId(), null, null, Boolean.TRUE, null, null);
                isReceive = Boolean.FALSE;
            } else {
                throw new ServiceException("申请接单太频繁,请刷新后再试");
            }
        }
        return isReceive;
    }

    @Override
    public String getTypeName() {
        return BusyTaskTypeEnum.FLOW_TASK.getMsg();
    }

    @Override
    public String getTypeCode() {
        return BusyTaskTypeEnum.FLOW_TASK.getCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addTask(SohuBusyTaskBo bo, Long userId) {
        if (!LoginHelper.anyMatchRole(RoleCodeEnum.ADMIN.getCode(),
                RoleCodeEnum.OPERATION_ADMIN.getCode(),
                RoleCodeEnum.PROVIDER.getCode())) {
            throw new RuntimeException("非法操作，您无权操作");
        }
        bo.setTimeUnit(2);
        SohuBusyTask task = this.createTask(bo, userId);
        task.setFullAmount(bo.getSingleAmount().multiply(new BigDecimal(bo.getDeliveryStandard())));
        task.setDeliveryType(3);
        if (BooleanUtil.isFalse(bo.getIsDraft())) {
            task.setState(SohuBusyTaskState.WaitPay.name());
        } else {
            task.setState(SohuBusyTaskState.Edit.name());
        }
        bo.setFullAmount(task.getFullAmount());
        HttpServletRequest request = ServletUtils.getRequest();
        final String ip = ServletUtils.getClientIP(request);
        task.setIpAddress(ip);
        // 线上不存在拆单逻辑
        boolean flag = sohuBusyTaskMapper.insert(task) > 0;
        // 处理站点信息
        this.hanleTaskSite(bo, userId, task);
        labelService.save(bo.getLabelList(), task.getTaskNumber());
        sourceMaterialService.save(bo.getSourceMaterialList(), task.getId());
        LoginUser loginUser = LoginHelper.getLoginUser();
        //异步新增埋点数据
        CompletableFuture.runAsync(() -> {
            SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute = new SohuUserBehaviorRecordPointBo.EventAttribute();
            eventAttribute.setContentNo(task.getTaskNumber());
            eventAttribute.setContentName(task.getTitle());
            eventAttribute.setAmount(task.getFullAmount());
            eventAttribute.setContentType("流量");
            syncTaskUserBehavior(loginUser.getUserId(), loginUser.getNickname(),eventAttribute);
        }, asyncConfig.getAsyncExecutor());
        return task.getId();
    }

    @Override
    public void sendTaskApply(SohuBusyTaskSite sohuBusyTaskSite) {

    }

    @Override
    public void afterReceive(boolean flag,
                             SohuBusyTaskReceive add,
                             SohuBusyTaskSite sohuBusyTaskSite,
                             SohuBusyTaskVo sohuBusyTaskVo, Long userId, Boolean isSendNotice) {
        log.error("接单入参flag:{},SohuBusyTaskReceive:{},SohuBusyTaskSite:{},SohuBusyTaskVo:{}", flag, add, sohuBusyTaskSite, sohuBusyTaskVo);
        if (flag) {
            // 异步更新站点表商单状态
            sohuBusyTaskSite.setIsReceive(Boolean.TRUE);
            sohuBusyTaskSite.setState(SohuBusyTaskState.Execute.name());
            // 创建群并返回群id
            log.error("调用创建群聊");
            SohuImGroupFormCreateBo createBo = new SohuImGroupFormCreateBo();
            createBo.setMasterTaskNumber(sohuBusyTaskSite.getMasterTaskNumber());
            createBo.setTaskNumber(sohuBusyTaskSite.getTaskNumber());
            createBo.setReceiveUserId(userId);
            Long groupId = remoteImGroupFormService.createGroupForm(createBo);
            if (Objects.isNull(groupId)) {
                throw new ServiceException("接单失败,请稍后再试");
            }
            // 回填群id
            add.setRelationId(groupId);
            sohuBusyTaskReceiveMapper.updateById(add);
            sohuBusyTaskSiteMapper.updateById(sohuBusyTaskSite);
            SohuBusyTask sohuBusyTask = new SohuBusyTask();
            sohuBusyTask.setId(sohuBusyTaskVo.getId());
            sohuBusyTask.setState(SohuBusyTaskState.Execute.name());
            sohuBusyTaskMapper.updateById(sohuBusyTask);
            // 发送发单方任务接单通知
            LoginUser receiverTaskUser = remoteUserService.queryById(userId);
            String name = "";
            if (Objects.nonNull(receiverTaskUser)) {
                name = StringUtils.sensitive(1, receiverTaskUser.getNickname(), 2, false);
            }
//            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_BEEN_APPLY,
//                    sohuBusyTaskSite.getUserId(), null, name, Boolean.FALSE, null, null);
            // 发送接单方任务接单通知
            if (isSendNotice) {
                sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.APPLY_SUCCESS,
                        userId, null, name, BooleanUtils.isTrue(sohuBusyTaskVo.getIsApproveReceive()), null, null);
            }
            // 异步绑定用户标签与行业标签
            CompletableFuture.runAsync(() -> this.bindUserLabelRelation(sohuBusyTaskVo, userId), asyncConfig.getAsyncExecutor());
            // 单人接单同步素材库
            if (sohuBusyTaskVo.getReceiveNum() == Constants.ONE) {
                remoteMiddleIndependentMaterialService.deleteByCodeAndType(sohuBusyTaskVo.getTaskNumber(), BusyTaskTypeEnum.FLOW_TASK.getCode());
            }
            // 补充事件统计
            super.addEventReport(BusyTaskReportEnum.JQSD.getType(), sohuBusyTaskSite.getTaskNumber(), sohuBusyTaskVo.getTitle(), LoginHelper.getUserId());
        }
    }

    @Override
    public void afterBuild(SohuBusyTaskReceive receive) {
        receive.setApplyMsg("流量型商单接单");
        receive.setAmount(BigDecimal.ZERO);
        receive.setState(SohuBusyTaskState.Execute.name());
        receive.setPassTime(new Date());
        receive.setBackup(Boolean.TRUE);
    }


    @Override
    public Boolean deliverTask(SohuBusyTaskDeliveryBo bo, SohuBusyTaskSiteVo busyTaskSiteVo, SohuBusyTaskVo sohuBusyTaskVo) {
        throw new ServiceException("当前类型愿望不允许手动交付");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(SohuBusyTaskVo busyTaskVo, SohuBusyTaskSiteVo busyTaskSiteVo, SohuBusyTaskReceiveVo busyTaskReceiveVo) {
        // 判断已达标任务是否大于0,大于0则不允许进行商单取消
        Integer passNum = remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(busyTaskSiteVo.getTaskNumber());
        if (passNum > 0) {
            throw new ServiceException("当前愿望已存在达标数量不允许取消");
        }
        // 子单回到待接单状态
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        site.setId(busyTaskSiteVo.getId());
        site.setState(SohuBusyTaskState.WaitReceive.name());
        sohuBusyTaskSiteMapper.updateById(site);
        // 接单记录改为已取消
        SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
        receive.setId(busyTaskReceiveVo.getId());
        receive.setState(SohuBusyTaskState.Cancel.name());
        sohuBusyTaskReceiveMapper.updateById(receive);
        // 需要将群解散
        SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().childTaskNumber(busyTaskSiteVo.getTaskNumber()).build();
        remoteImGroupFormService.handleOverChildGroup(formHandleBo);
        // 查询主单下子单是否都是待接单,是则更改主单为待接单状态
        List<String> stateList = new ArrayList<>();
        stateList.add(SohuBusyTaskState.OverSettle.name());
        stateList.add(SohuBusyTaskState.Execute.name());
        stateList.add(SohuBusyTaskState.WaitApproveSettle.name());
        stateList.add(SohuBusyTaskState.WaitSettle.name());
        Long num = sohuBusyTaskSiteMapper.selectCount(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTaskVo.getTaskNumber())
                .in(SohuBusyTaskSite::getState, stateList));
        if (num == 0) {
            sohuBusyTaskMapper.updateTaskState(busyTaskSiteVo.getMasterTaskNumber(), SohuBusyTaskState.WaitReceive.name());
        }
        // 单人取消接单同步素材库
        if (busyTaskVo.getReceiveNum() == Constants.ONE) {
            SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(busyTaskVo.getType());
            if (Objects.nonNull(sohuCategoryVo)) {
                // 流量任务,开启分销,同步素材库
                if (StrUtil.equalsAnyIgnoreCase(sohuCategoryVo.getConstMark(), BusyTaskTypeEnum.FLOW_TASK.getCode()) && !busyTaskVo.getKickbackType().equals(KickbackType.none.getCode())) {
                    SohuIndependentMaterialVo sohuIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByFlowTask(busyTaskVo.getTaskNumber(), BusyTaskTypeEnum.FLOW_TASK.getCode());
                    if (Objects.nonNull(sohuIndependentMaterialVo)) {
                        remoteMiddleIndependentMaterialService.updateByBo(new SohuIndependentMaterialBo()
                                .setId(sohuIndependentMaterialVo.getId())
                                .setStatus(CommonState.OnShelf.getCode())
                        );
                    }
                }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applySettle(SohuBusyTaskVo busyTaskVo, SohuBusyTaskSiteVo busyTaskSiteVo, SohuBusyTaskReceiveVo busyTaskReceiveVo, SohuBusyTaskDeliveryCommonVo vo) {
        // 判断已达标任务是否大于0,不大于0则不允许进行商单结算,提示请取消任务
        Integer passNum = remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(busyTaskSiteVo.getTaskNumber());
        if (passNum == 0) {
            throw new ServiceException("当前愿望无已达标人数,不允许结算");
        }
        // 子单改为待结算状态（也叫结算审核中状态）
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        site.setId(busyTaskSiteVo.getId());
        site.setState(SohuBusyTaskState.WaitApproveSettle.name());
        sohuBusyTaskSiteMapper.updateById(site);
        // 接单记录改为待结算状态（也叫结算审核中状态）
        SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
        receive.setId(busyTaskReceiveVo.getId());
        receive.setState(SohuBusyTaskState.WaitApproveSettle.name());
        sohuBusyTaskReceiveMapper.updateById(receive);
        busyTaskReceiveVo.setSiteId(busyTaskSiteVo.getSiteId());
        // 补充待审核记录
        sohuAuditService.createAudited(new SohuAuditBo(busyTaskReceiveVo, BusyType.SettleBusyTask.name()));
        // 发送任务通知
        LoginUser receiverTaskUser = remoteUserService.queryById(busyTaskReceiveVo.getUserId());
        String name = "";
        if (Objects.nonNull(receiverTaskUser)) {
            name = StringUtils.sensitive(1, receiverTaskUser.getNickname(), 2, false);
        }
        sohuBusyTaskNoticeService.sendTaskSiteNotice(busyTaskSiteVo.getTaskNumber(), TaskNoticeEnum.TASK_UPDATE,
                busyTaskSiteVo.getUserId(), null, name, Boolean.TRUE, null, null);
        // 发送短信通知
        LoginUser loginUser = remoteUserService.queryById(busyTaskSiteVo.getUserId());
        if (Objects.nonNull(loginUser) && Objects.nonNull(loginUser.getPhoneNumber())) {
            this.sendMsg(name, loginUser.getPhoneNumber());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean auditReceiveTask(SohuBusyTaskReceive taskReceive, SohuBusyTaskVo sohuBusyTaskVo, SohuBusyTaskSite sohuBusyTaskSite) {
        if (StrUtil.equalsAnyIgnoreCase(taskReceive.getState(), SohuBusyTaskState.Refuse.name())) {
            // 审核拒绝,更改接单状态为进行中
            SohuBusyTaskSite site = new SohuBusyTaskSite();
            site.setId(sohuBusyTaskSite.getId());
            site.setState(SohuBusyTaskState.Execute.name());
            sohuBusyTaskSiteMapper.updateById(site);
            // 接单记录改为进行中状态
            SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
            receive.setId(taskReceive.getId());
            receive.setState(SohuBusyTaskState.Execute.name());
            sohuBusyTaskReceiveMapper.updateById(receive);
            this.auditRecord(taskReceive.getId(), taskReceive.getRefuseMsg(), SohuBusyTaskState.Refuse.name(), taskReceive.getUserId());
            // 发送任务通知
            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_SETTLE_APPLY_FAIL,
                    taskReceive.getUserId(), taskReceive.getRefuseMsg(), null, Boolean.FALSE, null, null);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFlowBusyTask() {
        List<SohuBusyTaskReceiveVo> receiveList = sohuBusyTaskReceiveMapper.selectVoList(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.Execute.name()));
        if (CollectionUtils.isNotEmpty(receiveList)) {
            for (SohuBusyTaskReceiveVo receiveVo : receiveList) {
                if (Objects.isNull(receiveVo.getRelationId())) {
                    continue;
                }
                SohuBusyTaskSiteVo taskSiteVo = sohuBusyTaskSiteService.queryByTaskNumber(receiveVo.getTaskNumber());
                // 查询群id是否有值，有值，处理查询群已达标的数据
                List<SohuImGroupUserVo> groupUserList = remoteImService.groupUsers(receiveVo.getRelationId());
                if (CollectionUtils.isEmpty(groupUserList)) {
                    continue;
                }
                groupUserList.sort(Comparator.comparing(SohuImGroupUserVo::getCreateTime));
                // 查询群满足的用户数据
                List<Long> userIds = remoteImGroupOrderUserService.listPassUserIdsByGroupId(receiveVo.getRelationId());
                // 过滤接单方
                userIds.add(receiveVo.getUserId());
                // 过滤发单方
                userIds.add(taskSiteVo.getUserId());
                List<Long> passUserIds = new ArrayList<>();
                // 对比差值
                for (SohuImGroupUserVo groupUserVo : groupUserList) {
                    if (Objects.nonNull(groupUserVo.getCreateTime())
                            && this.addHoursToDate(groupUserVo.getCreateTime(), taskSiteVo.getDeliveryDay()).before(new Date())
                            && !userIds.contains(groupUserVo.getUserId())) {
                        passUserIds.add(groupUserVo.getUserId());
                    }
                }
                // 将差值存入达标用户表
                if (CollectionUtils.isNotEmpty(passUserIds)) {
                    for (Long passUserId : passUserIds) {
                        SohuImGroupUserBo sohuImGroupUserBo = SohuImGroupUserBo.builder().
                                userId(passUserId).groupId(receiveVo.getRelationId()).build();
                        remoteImGroupOrderUserService.passGroupOrderUser(sohuImGroupUserBo);
                    }
                }
            }
        }
    }

    @Override
    public void queryChildReceive(SohuBusyTaskSiteVo vo, Long userId) {
        List<SohuBusyTaskReceiveVo> taskReceiveVoList = sohuBusyTaskReceiveMapper.selectReceiveAndInfo(vo.getTaskNumber(), Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(taskReceiveVoList)) {
            for (SohuBusyTaskReceiveVo receiveVo : taskReceiveVoList) {
                if (vo.getState().equals(CommonState.Exceed.getCode())
                        && Objects.nonNull(userId) && !receiveVo.getUserId().equals(userId)) {
                    throw new ServiceException("当前愿望已被他人接取,请刷新页面重新接取其他愿望");
                }
                if (ObjectUtils.isNotNull(receiveVo.getRelationId()) && Objects.nonNull(LoginHelper.getUserId())) {
                    SohuFlowTaskReceiveVO flowTaskReceive = new SohuFlowTaskReceiveVO();
                    // 获取素材地址
                    List<SohuBusyTaskSourceMaterial> materialList = sohuBusyTaskSourceMaterialMapper.selectList(
                            Wrappers.<SohuBusyTaskSourceMaterial>lambdaQuery()
                                    .eq(SohuBusyTaskSourceMaterial::getBusyTaskId, vo.getBusyTaskId()));
                    flowTaskReceive.setSourceMaterialList(materialList.stream().map(SohuBusyTaskSourceMaterial::getSourceMaterialUrl)
                            .collect(Collectors.toList()));
                    // 获取群口令
                    flowTaskReceive.setGroupWord(remoteImService.getGroupWord(receiveVo.getRelationId()));
                    flowTaskReceive.setGroupId(receiveVo.getRelationId());
                    // 获取已达标数量
                    Integer passPersonNum = remoteImGroupOrderUserService.getPassPersonUserByTaskNumber(vo.getTaskNumber());
                    flowTaskReceive.setPassPersonNum(passPersonNum.longValue());
                    // 结算金额
                    flowTaskReceive.setSettleAmount(vo.getSingleAmount().multiply(new BigDecimal(passPersonNum)));
                    // 获取申请审核记录
                    List<SohuAuditVo> auditVoList = sohuAuditService.selectListByObj(receiveVo.getId(), BusyType.SettleBusyTask.name());
                    if (CollectionUtils.isNotEmpty(auditVoList)) {
                        List<SohuFlowTaskReceiveVO.AuditRecord> recordList = new ArrayList<>();
                        for (SohuAuditVo sohuAudit : auditVoList) {
                            if (!CommonState.WaitApprove.getCode().equals(sohuAudit.getSysAuditState())) {
                                SohuFlowTaskReceiveVO.AuditRecord auditRecord = new SohuFlowTaskReceiveVO.AuditRecord();
                                auditRecord.setTime(sohuAudit.getAuditTime());
                                auditRecord.setUserName(sohuAudit.getAuditorName());
                                auditRecord.setAuditState(sohuAudit.getSysAuditState());
                                auditRecord.setRejectReason(sohuAudit.getRejectReason());
                                recordList.add(auditRecord);
                            }
                            SohuFlowTaskReceiveVO.AuditRecord publishRecord = new SohuFlowTaskReceiveVO.AuditRecord();
                            publishRecord.setTime(sohuAudit.getPublishTime());
                            LoginUser loginUser = remoteUserService.queryById(sohuAudit.getUserId());
                            if (Objects.nonNull(loginUser)) {
                                publishRecord.setUserName(loginUser.getNickname());
                            }
                            publishRecord.setAuditState(CommonState.WaitApprove.getCode());
                            recordList.add(publishRecord);
                        }
                        flowTaskReceive.setAuditRecordList(recordList);
                    }
                    vo.setFlowTaskReceive(flowTaskReceive);
                }
            }
            vo.setWaitPassPersonNum(vo.getWaitPassPersonNum() - remoteImGroupOrderUserService.getPassPersonUserByMasterTaskNumber(vo.getMasterTaskNumber()));
        }
    }

    @Override
    public void afterQueryExtendValue(SohuBusyTaskSiteVo vo, Long receiveId, Long receiveUserId, Long userId) {
        // 获取素材id
        SohuIndependentMaterialVo sohuIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByCodeAndType(vo.getMasterTaskNumber(), BusyTaskTypeEnum.FLOW_TASK.getCode());
        if (Objects.nonNull(sohuIndependentMaterialVo)) {
            vo.setMaterialId(sohuIndependentMaterialVo.getId());
        }
        if (userId != null && userId > 0L) {
            // 任务是否收藏
            vo.setCollectObj(Boolean.FALSE);
            SohuBusyTaskUserVo sohuBusyTaskUserVo = sohuBusyTaskUserService.getByUserIdAndTaskNumber(userId, vo.getMasterTaskNumber());
            if (Objects.nonNull(sohuBusyTaskUserVo)) {
                vo.setCollectObj(sohuBusyTaskUserVo.getIsCollect());
                sohuBusyTaskUserService.updateTime(sohuBusyTaskUserVo.getId(), null);
            } else {
                sohuBusyTaskUserService.addReadRecord(userId, vo.getMasterTaskNumber());
            }
            // 需要审核,查询审核记录
            if (vo.getIsApproveReceive()) {
                SohuAuditVo sohuAuditVo = sohuAuditService.selectNearByObj(vo.getBusyTaskId(), BusyType.ReceiveBusyTask.name(), userId);
                if (Objects.nonNull(sohuAuditVo) && sohuAuditVo.getSysAuditState().equals(CommonState.WaitApprove.getCode())) {
                    vo.setApplyAnnex(sohuAuditVo.getApplyAnnex());
                    vo.setApplyMsg(sohuAuditVo.getApplyMsg());
                }
            }
            // 基于CT 查询当前用户接单数据
            SohuBusyTaskReceiveVo receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getTaskNumber, vo.getTaskNumber())
                    .ne(SohuBusyTaskReceive::getState, SohuBusyTaskState.Cancel.name())
                    .orderByDesc(SohuBusyTaskReceive::getCreateTime)
                    .last("limit 1"));
            if (Objects.nonNull(receiveVo)) {
                vo.setReceiveUserId(receiveVo.getUserId());
            }
        }
        if (Objects.nonNull(vo.getIpAddress())) {
            vo.setIpInfo(AddressUtils.getRealAddressByIP(vo.getIpAddress()));
        }
        // 是否需要替换状态
        if (Objects.nonNull(receiveId)) {
            SohuBusyTaskReceiveVo receiveVo = sohuBusyTaskReceiveMapper.selectVoById(receiveId);
            vo.setState(receiveVo.getState());
        } else if (Objects.nonNull(receiveUserId)) {
            // 是否需要替换状态
            SohuBusyTaskReceiveVo receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getTaskNumber, vo.getTaskNumber())
                    .eq(SohuBusyTaskReceive::getUserId, receiveUserId).orderByDesc(SohuBusyTaskReceive::getCreateTime)
                    .last("limit 1"));
            if (Objects.nonNull(receiveVo)) {
                vo.setState(receiveVo.getState());
            }
        }
    }

    /**
     * 查询申请记录
     * 判断审核通过还是驳回
     * 审核通过:
     * 1、基于主单进行上锁
     * 2、从主单中取一个待接单的子单编号
     * 3、统一校验
     * 4、调用正常的接单逻辑进行接单操作
     * 5、更改审核状态为通过并发送申请通过通知
     * 6、判断是否还存在待接单的子单编号（存在,则将同一个人的申请记录给全部驳回掉, 不存在,则将其他审核中的申请记录驳回,驳回原因默认接单人数已达上限并发送驳回通知）
     * <p>
     * 驳回:更改记录为驳回并记录驳回原因并发送接单申请驳回系统通知
     *
     * @param processBo
     * @param busyTask
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean receiveApplyProcess(SohuBusyTaskReceiveApplyProcessBo processBo, SohuBusyTask busyTask) {
        // 查询申请记录
        SohuAuditVo sohuAudit = sohuAuditService.queryById(processBo.getId());
        if (Objects.isNull(sohuAudit)) {
            throw new ServiceException("申请记录不存在");
        }
        if (!sohuAudit.getSysAuditState().equals(CommonState.WaitApprove.getCode())) {
            throw new ServiceException("该记录已进行审核,无需进行二次审核");
        }
        // 从主单中取一个待接单的子单编号
        List<SohuBusyTaskSite> sohuBusyTaskSiteList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                .eq(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive.name()));
        // 从主单中取一个待接单的子单编号
        if (CollectionUtils.isEmpty(sohuBusyTaskSiteList)) {
            throw new ServiceException("接单人数已达上限");
        }
        SohuBusyTaskSite sohuBusyTaskSite = sohuBusyTaskSiteList.get(0);
        if (processBo.getAuditState().equals(CommonState.OnShelf.getCode())) {
            // 审核通过上架
            String lockKey = String.format("sohu:lock:receive_master_task_number:%s", busyTask.getTaskNumber());
            if (RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(1))) {
                SohuBusyTaskVo sohuBusyTaskVo = BeanUtil.toBean(busyTask, SohuBusyTaskVo.class);
                // 统一校验
                this.checkReceive(sohuBusyTaskVo, sohuBusyTaskSite, sohuAudit.getBusyBelonger());
                SohuBusyTaskReceiveBo receiveBo = new SohuBusyTaskReceiveBo();
                receiveBo.setTaskNumber(sohuBusyTaskSite.getTaskNumber());
                receiveBo.setSiteType(sohuAudit.getSiteType());
                receiveBo.setSiteId(sohuAudit.getSiteId());
                // 调用正常的接单逻辑进行接单操作
                super.commonReceiveTask(true, receiveBo, sohuBusyTaskVo, sohuBusyTaskSite, sohuAudit.getBusyBelonger(), Boolean.FALSE);
                // 更改审核状态为通过并发送申请通过通知
                this.handleAuditRecord(sohuAudit, CommonState.OnShelf.getCode(), "", sohuBusyTaskSite);
                // 判断是否还存在待接单的子单编号（存在,则将同一个人的申请记录给全部驳回掉, 不存在,则将其他审核中的申请记录驳回,驳回原因默认接单人数已达上限并发送驳回通知）
                sohuBusyTaskSiteList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                        .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                        .eq(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive.name()));
                if (CollectionUtils.isEmpty(sohuBusyTaskSiteList)) {
                    SohuAuditBo sohuAuditBo = new SohuAuditBo();
                    sohuAuditBo.setSysAuditState(CommonState.WaitApprove.getCode());
                    sohuAuditBo.setBusyType(BusyType.ReceiveBusyTask.getType());
                    sohuAuditBo.setBusyCode(busyTask.getId());
                    List<SohuAuditVo> sohuAuditList = sohuAuditService.queryList(sohuAuditBo);
                    if (CollectionUtils.isNotEmpty(sohuAuditList)) {
                        for (SohuAuditVo sohuAuditVo : sohuAuditList) {
                            // 审核拒绝更改记录为驳回并记录驳回原因
                            this.handleAuditRecord(sohuAuditVo, CommonState.Refuse.getCode(), "接单人数已达上限", sohuBusyTaskSite);
                        }
                    }
                }
            } else {
                throw new ServiceException("上个审核流程未处理结束,请稍后再试");
            }
        } else {
            // 审核拒绝更改记录为驳回并记录驳回原因
            this.handleAuditRecord(sohuAudit, CommonState.Refuse.getCode(), StringUtils.isEmpty(processBo.getRejectReason()) ? "接单人数已达上限" : processBo.getRejectReason(), sohuBusyTaskSite);
        }
        return Boolean.TRUE;
    }

    /**
     * 一键结算商单
     *
     * @param busyTask
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean settleBusyTask(SohuBusyTask busyTask) {
        List<String> stateList = new ArrayList<>();
        stateList.add(SohuBusyTaskState.OverSettle.name());
        stateList.add(SohuBusyTaskState.Cancel.name());
        stateList.add(SohuBusyTaskState.WaitReceive.name());
        List<SohuBusyTaskSite> siteList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                .notIn(SohuBusyTaskSite::getState, stateList));
        List<SohuBusyTaskSite> waitReceiveList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                .eq(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive.name()));
        if (CollectionUtils.isNotEmpty(waitReceiveList)) {
            for (SohuBusyTaskSite waitReceiveTask : waitReceiveList) {
                SohuBusyTaskSite newTaskSite = new SohuBusyTaskSite();
                newTaskSite.setId(waitReceiveTask.getId());
                newTaskSite.setState(SohuBusyTaskState.OverSettle.name());
                sohuBusyTaskSiteMapper.updateById(newTaskSite);
            }
            // 判断如果后面无已接单的订单则直接退钱
            if (CollectionUtils.isEmpty(siteList)) {
                sohuBusyTaskMapper.updateTaskState(busyTask.getTaskNumber(), SohuBusyTaskState.OverSettle.name());
                remotePaySettlementService.overSettle(busyTask.getTaskNumber());
                SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().mainTaskNumber(busyTask.getTaskNumber()).build();
                remoteImGroupFormService.handleOverMainGroup(formHandleBo);
            }
        }
        if (CollectionUtils.isNotEmpty(siteList)) {
            for (SohuBusyTaskSite sohuBusyTaskSite : siteList) {
                sohuBusyTaskSiteService.overBusyTask(sohuBusyTaskSite.getTaskNumber());
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 结算订单(管理员审核)
     *
     * @param busyTask
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applySettleBusyTask(SohuBusyTask busyTask, String rejectReason) {
        rejectReason = StrUtil.isEmptyIfStr(rejectReason) ? "涉嫌违规" : rejectReason;
        List<String> stateList = new ArrayList<>();
        stateList.add(SohuBusyTaskState.OverSettle.name());
        stateList.add(SohuBusyTaskState.Cancel.name());
        stateList.add(SohuBusyTaskState.WaitReceive.name());
        List<SohuBusyTaskSite> siteList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                .notIn(SohuBusyTaskSite::getState, stateList));
        List<SohuBusyTaskSite> waitReceiveList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                .eq(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive.name()));
        if (CollectionUtils.isNotEmpty(waitReceiveList)) {
            for (SohuBusyTaskSite waitReceiveTask : waitReceiveList) {
                SohuBusyTaskSite newTaskSite = new SohuBusyTaskSite();
                newTaskSite.setId(waitReceiveTask.getId());
                newTaskSite.setState(SohuBusyTaskState.OverSettle.name());
                sohuBusyTaskSiteMapper.updateById(newTaskSite);
            }
        }
        // 有存在接单的,直接将子单及接单状态改为完结,并发送系统推送消息
        if (CollectionUtils.isNotEmpty(siteList)) {
            for (SohuBusyTaskSite sohuBusyTaskSite : siteList) {
                List<String> receiveStateList = new ArrayList<>();
                receiveStateList.add(SohuBusyTaskState.WaitSettle.name());
                receiveStateList.add(SohuBusyTaskState.WaitApproveSettle.name());
                receiveStateList.add(SohuBusyTaskState.Execute.name());
                SohuBusyTaskReceiveVo receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                        .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSite.getTaskNumber())
                        .in(SohuBusyTaskReceive::getState, receiveStateList).last("limit 1"));
                if (Objects.nonNull(receiveVo)) {
                    SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
                    receive.setId(receiveVo.getId());
                    receive.setState(SohuBusyTaskState.OverSettle.name());
                    sohuBusyTaskReceiveMapper.updateById(receive);
                }
                SohuBusyTaskSite site = new SohuBusyTaskSite();
                site.setId(sohuBusyTaskSite.getId());
                site.setState(SohuBusyTaskState.OverSettle.name());
                sohuBusyTaskSiteMapper.updateById(site);
                // 发送系统通知
                sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.APPLY_TASK_COMPEL_OFF_RECEIVER,
                        receiveVo.getUserId(), rejectReason, null, Boolean.TRUE, null, null);
                // 解散子群
                SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().childTaskNumber(sohuBusyTaskSite.getTaskNumber())
                        .passPersonJoinGroup(false).build();
                remoteImGroupFormService.handleOverChildGroup(formHandleBo);
            }
        }
        // 查询商单的申请接单记录,全部驳回并推送系统消息
        SohuAuditBo sohuAuditBo = new SohuAuditBo();
        sohuAuditBo.setSysAuditState(CommonState.WaitApprove.getCode());
        sohuAuditBo.setBusyType(BusyType.ReceiveBusyTask.getType());
        sohuAuditBo.setBusyCode(busyTask.getId());
        List<SohuAuditVo> sohuAuditList = sohuAuditService.queryList(sohuAuditBo);
        if (CollectionUtils.isNotEmpty(sohuAuditList)) {
            for (SohuAuditVo sohuAuditVo : sohuAuditList) {
                SohuBusyTaskSite sohuBusyTaskSite = new SohuBusyTaskSite();
                if (CollectionUtils.isNotEmpty(waitReceiveList)) {
                    sohuBusyTaskSite = waitReceiveList.get(0);
                } else {
                    sohuBusyTaskSite = siteList.get(0);
                }
                // 审核拒绝更改记录为驳回并记录驳回原因
                this.handleAuditRecord(sohuAuditVo, CommonState.Refuse.getCode(), rejectReason, sohuBusyTaskSite);
            }
        }
        // 发送发单方系统推送消息
        sohuBusyTaskNoticeService.sendTaskNotice(busyTask.getId(), TaskNoticeEnum.APPLY_TASK_COMPEL_OFF,
                busyTask.getUserId(), rejectReason, null, Boolean.FALSE);
        sohuBusyTaskMapper.updateTaskState(busyTask.getTaskNumber(), SohuBusyTaskState.OverSettle.name());
        SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().mainTaskNumber(busyTask.getTaskNumber()).build();
        remoteImGroupFormService.handleOverMainGroup(formHandleBo);
        return Boolean.TRUE;
    }

    @Override
    public void updatePassNum(SohuBusyTask busyTask) {
        Integer passNum = remoteImGroupOrderUserService.getPassPersonUserByMasterTaskNumber(busyTask.getTaskNumber());
        SohuBusyTask newTask = new SohuBusyTask();
        newTask.setId(busyTask.getId());
        newTask.setPassNum(passNum);
        sohuBusyTaskMapper.updateById(newTask);
        if (busyTask.getReceiveNum().equals(passNum)) {
            List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                    .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber()));
            if (CollectionUtils.isEmpty(busyTaskSiteList)) {
                return;
            }
            SohuAuditBo sohuAuditBo = new SohuAuditBo();
            sohuAuditBo.setSysAuditState(CommonState.WaitApprove.getCode());
            sohuAuditBo.setBusyType(BusyType.ReceiveBusyTask.getType());
            sohuAuditBo.setBusyCode(busyTask.getId());
            List<SohuAuditVo> sohuAuditList = sohuAuditService.queryList(sohuAuditBo);
            if (CollectionUtils.isNotEmpty(sohuAuditList)) {
                for (SohuAuditVo sohuAuditVo : sohuAuditList) {
                    // 审核拒绝更改记录为驳回并记录驳回原因
                    this.handleAuditRecord(sohuAuditVo, CommonState.Refuse.getCode(), "达标人数已达上限", busyTaskSiteList.get(0));
                }
            }
        }
    }

    /**
     * 处理审核记录
     * 1、更改记录审核状态
     * 2、发送系统消息
     *
     * @param sohuAudit
     * @param auditState
     * @param rejectReason
     * @param sohuBusyTaskSite
     */
    private void handleAuditRecord(SohuAuditVo sohuAudit, String auditState, String rejectReason, SohuBusyTaskSite sohuBusyTaskSite) {
        SohuAuditBo sohuAuditBo = BeanUtil.toBean(sohuAudit, SohuAuditBo.class);
        sohuAuditBo.setState(auditState);
        sohuAuditBo.setRejectReason(rejectReason);
        sohuAuditService.audit(sohuAuditBo);
        if (auditState.equals(CommonState.OnShelf.getCode())) {
            // 审核通过消息
            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_APPLY_SUCCESS,
                    sohuAudit.getBusyBelonger(), null, null, Boolean.TRUE, null, null);
        } else {
            // 审核拒绝消息
            sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_APPLY_REFUSE,
                    sohuAudit.getBusyBelonger(), rejectReason, null, Boolean.TRUE, null, null);
        }
    }

    /**
     * 绑定用户标签与行业标签
     *
     * @param vo     主任务vo
     * @param userId 接单用户id
     */
    private void bindUserLabelRelation(SohuBusyTaskVo vo, Long userId) {
        ArrayList<SohuUserLabelRelationBo> userLabelList = new ArrayList<>();
        // 添加行业标签
        userLabelList.add(createUserLabelRelation(userId, vo.getPid(), LabelEnum.INDUSTRY));
        // 添加普通标签
        List<SohuBusyTaskLabel> taskLabelList = sohuBusyTaskLabelMapper.selectList(Wrappers.<SohuBusyTaskLabel>lambdaQuery()
                .eq(SohuBusyTaskLabel::getTaskNumber, vo.getTaskNumber()));
        if (CollectionUtils.isNotEmpty(taskLabelList)) {
            for (SohuBusyTaskLabel taskLabel : taskLabelList) {
                userLabelList.add(createUserLabelRelation(userId, taskLabel.getLabelId(), LabelEnum.COMMON));
            }
        }
        // 批量插入用户标签关联
        remoteMiddleCommonLabelService.insertBatch(userLabelList);
    }

    /**
     * 创建用户标签关联对象
     *
     * @param userId    用户id
     * @param labelId   标签id
     * @param labelType 标签类型
     * @return 用户标签关联对象
     */
    private SohuUserLabelRelationBo createUserLabelRelation(Long userId, Long labelId, LabelEnum labelType) {
        SohuUserLabelRelationBo userLabel = new SohuUserLabelRelationBo();
        userLabel.setUserId(userId);
        userLabel.setLabelId(labelId);
        userLabel.setLabelType(labelType.getCode());
        return userLabel;
    }

    /**
     * 同步分销素材库
     *
     * @param task 任务主体
     */
    public void syncIndependentMaterial(SohuBusyTask task) {
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(task.getType());
        if (Objects.nonNull(sohuCategoryVo)) {
            // 流量任务,开启分销,同步素材库
            if (StrUtil.equalsAnyIgnoreCase(sohuCategoryVo.getConstMark(), BusyTaskTypeEnum.FLOW_TASK.getCode()) && !task.getKickbackType().equals(KickbackType.none.getCode())) {
                remoteMiddleIndependentMaterialService.insertByBo(buildFlowTaskBo(task.getTaskNumber()));
            }
        }
    }

    /**
     * 流量型表单商单单独构建
     *
     * @param masterTaskNumber 主任务编号
     * @return SohuIndependentMaterialBo
     */
    public SohuIndependentMaterialBo buildFlowTaskBo(String masterTaskNumber) {
        SohuBusyTaskVo vo = remoteBusyTaskService.getByTaskNo(masterTaskNumber);

        SohuIndependentMaterialBo independentMaterialBo = new SohuIndependentMaterialBo();
        independentMaterialBo.setMaterialName(vo.getTitle());
        independentMaterialBo.setMaterialType(BusyTaskTypeEnum.FLOW_TASK.getCode());
        independentMaterialBo.setPrice(vo.getFullAmount());
        independentMaterialBo.setMaterialUserId(vo.getUserId());
        independentMaterialBo.setSiteId(Long.parseLong(vo.getSiteIds()));
        independentMaterialBo.setMaterialCode(vo.getTaskNumber());
        independentMaterialBo.setCategoryId(vo.getType());
        independentMaterialBo.setStatus(CommonState.OnShelf.getCode());
        // 计算分销金额
        if (!StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.none.getCode())) {
            if (StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.price.getCode())) {
                // 一口价
                independentMaterialBo.setIndependentPrice(vo.getKickbackValue());
            } else {
                BigDecimal taskDivide = BigDecimalUtils.divide(vo.getKickbackValue(), CalUtils.PERCENTAGE);
                // 百分比
                independentMaterialBo.setIndependentPrice(vo.getFullAmount().multiply(taskDivide).setScale(2, RoundingMode.HALF_UP));
            }
        } else {
            vo.setDistributionAmount(BigDecimal.ZERO);
        }
        return independentMaterialBo;
    }

    /**
     * 审核记录
     *
     * @param receiveId
     * @param rejectReason
     * @param state
     */
    private void auditRecord(Long receiveId, String rejectReason, String state, Long userId) {
        SohuAuditVo sohuAuditVo = sohuAuditService.selectNearByObj(receiveId, BusyType.SettleBusyTask.name(), userId);
        SohuAuditBo auditBo = new SohuAuditBo();
        auditBo.setId(sohuAuditVo.getId());
        auditBo.setRejectReason(rejectReason);
        auditBo.setBusyType(BusyType.SettleBusyTask.name());
        auditBo.setBusyCode(receiveId);
        auditBo.setState(state);
        sohuAuditService.process(auditBo);
    }

    /**
     * 增加时长
     *
     * @param date
     * @param hours
     * @return
     */
    private Date addHoursToDate(Date date, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, hours);
        return calendar.getTime();
    }

    /**
     * 处理站点信息
     *
     * @param bo
     * @param userId
     * @param task
     */
    private void hanleTaskSite(SohuBusyTaskBo bo, Long userId, SohuBusyTask task) {
        if (BooleanUtil.isFalse(bo.getIsDraft())) {
            if ((bo.getKickbackType().equals(KickbackType.price.getCode())
                    || bo.getKickbackType().equals(KickbackType.percentage.getCode())) && bo.getReceiveNum() > 1) {
                throw new ServiceException("多人接单分销功能暂未开放");
            }
            List<SohuBusyTaskSite> taskSites = new ArrayList<>();
            // 佣金
            BigDecimal independentPrice = BigDecimal.ZERO.stripTrailingZeros();
            // 一口价
            if (bo.getKickbackType().equals(KickbackType.price.getCode())) {
                independentPrice = bo.getKickbackValue();
            } else if (bo.getKickbackType().equals(KickbackType.percentage.getCode())) {
                // 任务分销百分比
                BigDecimal taskDivide = BigDecimalUtils.divide(bo.getKickbackValue(), CalUtils.PERCENTAGE);
                // 任务分销总金额 = 任务价格 * 分销百分比
                independentPrice = bo.getFullAmount().multiply(taskDivide).setScale(2, RoundingMode.HALF_UP);
            }
            for (Long siteId : bo.getAddSiteIds()) {
                Integer num = task.getReceiveNum();
                for (int count = 0; count < num; count++) {
                    SohuBusyTaskSite taskSite = new SohuBusyTaskSite();
                    taskSite.setMasterTaskNumber(task.getTaskNumber());
                    taskSite.setUserId(userId);
                    taskSite.setDistributionAmount(independentPrice);
                    taskSite.setTaskNumber(NumberUtil.getOrderNo(OrderConstants.CHILD_TASK_PREFIX));
                    taskSite.setSiteId(siteId);
                    taskSite.setCountrySiteId(task.getCountrySiteId());
                    taskSite.setAuditUser(userId);
                    taskSite.setAuditTime(new Date());
                    taskSite.setShelfState(SohuBusyTaskState.OnShelf.name());
                    this.extractedTaskSite(task, taskSite);
                    taskSite.setState(SohuBusyTaskState.WaitReceive.name());
                    taskSites.add(taskSite);
                }
            }
            if (CollectionUtils.isNotEmpty(taskSites)) {
                // 保存站点关联
                sohuBusyTaskSiteMapper.insertBatch(taskSites);
            }
            // 同步分销素材库
            this.syncIndependentMaterial(task);
            // 撤销交易
            remotePaySettlementService.barcodeCancelPay(task.getTaskNumber());
        }
    }

    /**
     * 接单校验
     *
     * @param sohuBusyTaskVo
     * @param sohuBusyTaskSite
     */
    private void checkReceive(SohuBusyTaskVo sohuBusyTaskVo,
                              SohuBusyTaskSite sohuBusyTaskSite, Long userId) {
        if (sohuBusyTaskVo.getState().equals(SohuBusyTaskState.WaitPay.name())) {
            throw new ServiceException(MessageUtils.message("商单未支付成功,无法进行接单操作"));
        }
        // 校验当前状态
        super.checkOnlineTask(sohuBusyTaskSite, userId);
        // 校验剩余达标数量是否大于0
        Integer passNum = remoteImGroupOrderUserService.getPassPersonUserByMasterTaskNumber(sohuBusyTaskVo.getTaskNumber());
        if (sohuBusyTaskVo.getDeliveryStandard() - passNum <= 0) {
            throw new ServiceException("该商单达标人数已达上限,无法进行接单操作");
        }
        // 校验同一个商单是否已接过一个子单
        List<SohuBusyTaskSite> siteList = sohuBusyTaskSiteService.listByMasterTaskNo(sohuBusyTaskVo.getTaskNumber());
        if (CollectionUtils.isNotEmpty(siteList)) {
            Long num = sohuBusyTaskReceiveMapper.selectCount(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getUserId, LoginHelper.getUserId())
                    .in(SohuBusyTaskReceive::getTaskNumber, siteList.stream().map(SohuBusyTaskSite::getTaskNumber).collect(Collectors.toList()))
                    .ne(SohuBusyTaskReceive::getState, SohuBusyTaskState.Cancel.name()));
            if (num > 0) {
                throw new ServiceException("已接取过该商单,无法进行二次接单");
            }
        }
    }

    /**
     * 发送短信
     *
     * @param name
     * @param phone
     */
    private void sendMsg(String name, String phone) {
        LinkedHashMap<String, String> param = new LinkedHashMap<>();
        param.put("name", name);
        remoteSmsService.send(phone, "SMS_481790169", param);
    }

}

