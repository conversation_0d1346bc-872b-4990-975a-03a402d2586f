package com.sohu.busyOrder.base.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sohu.busyOrder.appevent.event.BusyTaskAuditEvent;
import com.sohu.busyOrder.appevent.event.BusyTaskReceiveEvent;
import com.sohu.busyOrder.base.BaseTaskService;
import com.sohu.busyOrder.domain.*;
import com.sohu.busyOrder.mapper.*;
import com.sohu.busyOrder.service.ISohuBusyTaskAfterSalesExplainService;
import com.sohu.busyOrder.service.ISohuBusyTaskNoticeService;
import com.sohu.busyOrder.service.ISohuBusyTaskTemplateNumberService;
import com.sohu.busyOrder.utils.BusyTaskUtil;
import com.sohu.busyorder.api.bo.SohuBusyTaskBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskDeliveryBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveApplyProcessBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.OrderConstants;
import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.IncomeBusyOrderBo;
import com.sohu.middle.api.bo.SohuIndependentMaterialBo;
import com.sohu.middle.api.bo.SohuUserBehaviorRecordPointBo;
import com.sohu.middle.api.enums.behavior.BehaviorBusinessTypeEnum;
import com.sohu.middle.api.enums.behavior.OperaTypeEnum;
import com.sohu.middle.api.enums.report.BusyTaskReportEnum;
import com.sohu.middle.api.enums.McnUserStateEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.mcn.RemoteMiddleMcnUserService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pay.api.vo.SohuAccountVo;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteUserService;
import io.seata.common.util.CollectionUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商单公共service实现
 *
 * @Author: leibo
 * @Date: 2025/1/6 14:31
 **/
@RequiredArgsConstructor
@Service
@Slf4j
public abstract class BaseTaskServiceImpl extends CommonService implements BaseTaskService {

    @Autowired
    protected SohuBusyTaskDeliveryMapper sohuBusyTaskDeliveryMapper;
    @Autowired
    protected SohuBusyTaskReceiveMapper sohuBusyTaskReceiveMapper;
    @Autowired
    protected SohuBusyTaskExecuteMapper taskExecuteMapper;
    @Autowired
    protected ISohuBusyTaskTemplateNumberService sohuBusyTaskTemplateNumberService;
    @Autowired
    private SohuBusyTaskAfterSalesMapper sohuBusyTaskAfterSalesMapper;
    @DubboReference
    protected RemoteIndustryCategoryService remoteIndustryCategoryService;
    @DubboReference
    protected RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    protected RemoteMiddleIndependentMaterialService remoteMiddleIndependentMaterialService;
    @DubboReference
    protected RemoteMiddleUserCollectService remoteMiddleUserCollectService;
    @DubboReference
    protected RemoteAccountService remoteAccountService;
    @DubboReference
    protected RemoteMiddleMcnUserService middleMcnUserService;
    @DubboReference
    protected RemoteMiddleUserBalanceService remoteMiddleUserBalanceService;
    @DubboReference
    protected RemoteUserService remoteUserService;
    @Resource
    protected TransactionTemplate transactionTemplate;
    @Autowired
    protected ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    protected AsyncConfig asyncConfig;
    @Autowired
    private ISohuBusyTaskAfterSalesExplainService sohuBusyTaskAfterSalesExplainService;
    @Autowired
    protected ISohuBusyTaskNoticeService sohuBusyTaskNoticeService;
    @DubboReference
    private RemoteMiddleViewRecordService remoteMiddleViewRecordService;
    @DubboReference
    private RemoteIndependentTemplateService remoteIndependentTemplateService;
    @DubboReference
    private RemoteMiddleUserBehaviorRecordService remoteMiddleUserBehaviorRecordService;
    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByBo(SohuBusyTaskBo bo) {
        Long userId = LoginHelper.getUserId();
        if (Objects.isNull(bo.getNum())) {
            bo.setNum(1);
        }
        Long id = 0L;
        for (int count = 0; count < bo.getNum(); count++) {
            id = this.addTask(bo, userId);
        }
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateByBo(SohuBusyTaskBo bo) {
        SohuBusyTask busyTask = BeanUtil.toBean(bo, SohuBusyTask.class);
        if (!Objects.equals(LoginHelper.getUserId(), bo.getUserId())) {
            throw new ServiceException(MessageUtils.message("no.power"));
        }
        if (ObjectUtils.isNotNull(busyTask.getDeliveryDay()) && ObjectUtils.isNotNull(busyTask.getTimeUnit())) {
            // 处理时间
            busyTask.setDeliveryTime(BusyTaskUtil.buildCurrentTime(busyTask.getDeliveryDay(), busyTask.getTimeUnit()));
        }
        // 站点是否为空
        if (CollUtil.isEmpty(bo.getAddSiteIds())) {
            SohuSiteVo siteVo = remoteMiddleSiteService.getSiteByIp(ServletUtils.getClientIP());
            busyTask.setSiteIds(String.valueOf(siteVo.getId()));
            bo.setAddSiteIds(Collections.singletonList(siteVo.getId()));
            if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
                Long pid = siteVo.getPid();
                busyTask.setCountrySiteId(pid);
            }
        } else {
            SohuSiteVo sohuSiteVo = remoteMiddleSiteService.selectSiteByPid(bo.getAddSiteIds().get(0));
            if (Objects.nonNull(sohuSiteVo)) {
                busyTask.setCountrySiteId(sohuSiteVo.getPid());
            }
            busyTask.setSiteIds(String.valueOf(bo.getAddSiteIds().get(0)));
        }
        if (bo.getIsDraft() != null && !bo.getIsDraft()) {
            validEntityBeforeSave(busyTask);
        }
        return this.updateTask(busyTask, bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(SohuBusyTaskBo bo, SohuBusyTask busyTask, Long userId) {
        if (!LoginHelper.hasRole(RoleCodeEnum.ADMIN) && !LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
            return Boolean.FALSE;
        }
        // 审核人、审核时间
        busyTask.setAuditUser(userId);
        // 审核时间
        Date auditTime = new Date();
        busyTask.setAuditTime(auditTime);
        Boolean resultFlag = Boolean.FALSE;
        // 超管审核、国家站审核
        // 待审核、需要拆单、未拆单的情况--审核通过即到拆单广场
        if (busyTask.getState().equals(SohuBusyTaskState.WaitApprove.name())
                && busyTask.getNeedSplit() && !busyTask.getSplitState()
                && bo.getState().equals(SohuBusyTaskState.Pass.name())) {
            busyTask.setState(SohuBusyTaskState.WaitSplit.name());
            return sohuBusyTaskMapper.updateById(busyTask) > 0;
        }
        // 查询当前主任务所有子任务
        List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteService.listByMasterTaskNo(busyTask.getTaskNumber());
        // 待审核、需要拆单、已拆单的情况--审核通过即所有子单都通过（已拆单就生成所有的子单。各城市站长、国家站长、超管都可以审核）
        if (busyTask.getState().equals(SohuBusyTaskState.WaitApprove.name())
                && (!busyTask.getNeedSplit() || busyTask.getSplitState())
                && bo.getState().equals(SohuBusyTaskState.Pass.name())) {
            // 审核通过任务、并且通过所有子单
            busyTask.setState(SohuBusyTaskState.Pass.name());
            busyTaskSiteList.forEach(siteList -> {
                siteList.setAuditUser(userId);
                siteList.setAuditTime(auditTime);
                if (bo.getState().equals(SohuBusyTaskState.Pass.name())) {
                    siteList.setState(SohuBusyTaskState.WaitReceive.name());
                }
                siteList.setShelfState(SohuBusyTaskState.OnShelf.name());
                //保存智能推荐物料数据
                this.saveAirecContentItem(siteList);
            });
            resultFlag = transactionTemplate.execute(e -> {
                this.sohuBusyTaskMapper.updateById(busyTask);
                sohuBusyTaskSiteService.updateBatch(busyTaskSiteList);
                // 发送发单方任务审核通过通知
                sohuBusyTaskNoticeService.sendTaskNotice(busyTask.getId(), TaskNoticeEnum.TASK_PUBLISH_SUCCESS, busyTask.getUserId(), null, null, Boolean.FALSE);
                // 审核通过之后进入素材库
                if (!busyTask.getKickbackType().equals(KickbackType.none.getCode())) {
                    remoteMiddleIndependentMaterialService.insertByBo(buildBo(busyTask.getTaskNumber()));
                }
                return Boolean.TRUE;
            });
        }
        // 如果状态为待审核。传入状态为拒绝
        if (busyTask.getState().equals(SohuBusyTaskState.WaitApprove.name())
                && bo.getState().equals(SohuBusyTaskState.Refuse.name())) {
            busyTask.setState(SohuBusyTaskState.Refuse.name());
            busyTask.setRefuseMsg(bo.getRefuseMsg());
            // 审核拒绝任务、并且拒绝所有子单
            busyTaskSiteList.forEach(siteList -> {
                siteList.setAuditUser(userId);
                siteList.setAuditTime(auditTime);
                siteList.setState(SohuBusyTaskState.Refuse.name());
                siteList.setRefuseMsg(bo.getRefuseMsg());
                siteList.setShelfState(SohuBusyTaskState.OffShelf.name());
            });
            resultFlag = transactionTemplate.execute(e -> {
                sohuBusyTaskMapper.updateById(busyTask);
                sohuBusyTaskSiteService.updateBatch(busyTaskSiteList);
                // 发送发单方任务审核不通过通知
                sohuBusyTaskNoticeService.sendTaskNotice(busyTask.getId(), TaskNoticeEnum.TASK_PUBLISH_FAIL, busyTask.getUserId(), bo.getRefuseMsg(), null, Boolean.FALSE);
                return Boolean.TRUE;
            });
        }
        BusyTaskAuditEvent auditEvent = new BusyTaskAuditEvent(this, busyTask);
        applicationEventPublisher.publishEvent(auditEvent);
        return resultFlag;
    }

    @Override
    public SohuBusyTaskVo queryById(Long id) {
        SohuBusyTaskVo vo = sohuBusyTaskMapper.queryByTaskId(id);
        if (vo.getDeliveryStep() != null && vo.getDeliveryStep()) {
            vo.setDeliveryList(sohuBusyTaskDeliveryMapper.selectVoList(Wrappers.<SohuBusyTaskDelivery>lambdaQuery()
                    .eq(SohuBusyTaskDelivery::getMasterTaskNumber, vo.getTaskNumber()).orderByAsc(SohuBusyTaskDelivery::getSortIndex)));
        }
        if (StringUtils.isNotBlank(vo.getSiteIds())) {
            List<Long> idList = Arrays.stream(vo.getSiteIds().split(StrPool.COMMA)).map(Long::parseLong).collect(Collectors.toList());
            vo.setSiteVoIds(idList);
        }
        if (Objects.nonNull(vo.getUserId())) {
            SohuAccountVo sohuAccountModel = remoteAccountService.queryByUserIdOfPass(vo.getUserId());
            vo.setAccountType(Objects.nonNull(sohuAccountModel) ? sohuAccountModel.getAccountType() : null);
        }
        if (StringUtils.isNotBlank(vo.getRelateTaskNumber())) {
            SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoById(vo.getRelateTaskNumber());
            vo.setRelateTaskNumber(Objects.nonNull(sohuBusyTaskVo) ? sohuBusyTaskVo.getTaskNumber() : null);
        }
        // 是否有行业类型
        if (vo.getIndustryType() != null && vo.getIndustryType() > 0L) {
            // 任务类型名称
            SohuIndustryCategoryVo industryCategoryVo = remoteIndustryCategoryService.queryById(vo.getIndustryType());
            vo.setIndustryName(Objects.nonNull(industryCategoryVo) ? industryCategoryVo.getIndustryName() : null);
        }
        // 是否有愿望分类及供需分类
        if (Objects.nonNull(vo.getCategoryType()) && vo.getCategoryType() > 0L) {
            // 愿望分类名称
            SohuCategoryVo taskCategoryVo = remoteMiddleCategoryService.queryById(vo.getCategoryType());
            vo.setCategoryTypeName(Objects.nonNull(taskCategoryVo) ? taskCategoryVo.getName() : null);
        }
        if (Objects.nonNull(vo.getSupplyType()) && vo.getSupplyType() > 0L) {
            // 供需分类名称
            SohuCategoryVo supplyCategoryVo = remoteMiddleCategoryService.queryById(vo.getSupplyType());
            vo.setSupplyTypeName(Objects.nonNull(supplyCategoryVo) ? supplyCategoryVo.getName() : null);
        }
        // 站点名称
        if (CollUtil.isNotEmpty(vo.getSiteVoIds())) {
            List<SohuSiteVo> sohuSiteVoList = remoteMiddleSiteService.selectSiteList(vo.getSiteVoIds());
            List<String> siteVoNames = sohuSiteVoList.stream().map(SohuSiteVo::getName).collect(Collectors.toList());
            vo.setSiteVoNames(siteVoNames);
        }
        // 需要拆单
        if (vo.getNeedSplit() && vo.getSplitState()) {
            vo.setTemplateVo(sohuBusyTaskTemplateNumberService.queryByMasterTaskNo(vo.getTaskNumber()));
        }
        // 设置主任务佣金
        if (!StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.none.getCode())) {
            if (StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.price.getCode())) {
                // 一口价
                vo.setDistributionAmount(vo.getKickbackValue());
            } else {
                BigDecimal taskDivide = BigDecimalUtils.divide(vo.getKickbackValue(), CalUtils.PERCENTAGE);
                // 百分比
                vo.setDistributionAmount(vo.getFullAmount().multiply(taskDivide).setScale(2, RoundingMode.HALF_UP));
            }
        } else {
            vo.setDistributionAmount(BigDecimal.ZERO);
        }
        // 任务是否收藏
        Map<Long, SohuUserCollectVo> collectVoMap = new HashMap<>();
        if (LoginHelper.getUserId() != null && LoginHelper.getUserId() > 0L) {
            collectVoMap = remoteMiddleUserCollectService.queryMap(LoginHelper.getUserId(), BusyType.BusyTask.name(), Collections.singletonList(vo.getId()));
        }
        vo.setCollectObj(Objects.nonNull(collectVoMap.get(vo.getId())));
        // 是否关联任务
        if (vo.getIndustryType() != null && vo.getIndustryType() > 0L) {
            SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = sohuBusyTaskReceiveMapper.selectVoById(id);
            if (Objects.nonNull(sohuBusyTaskReceiveVo)) {
                SohuBusyTaskSiteVo sohuBusyTaskSiteVo = sohuBusyTaskSiteMapper.queryByTaskNumber(sohuBusyTaskReceiveVo.getTaskNumber(), null);
                BeanUtil.copyProperties(sohuBusyTaskSiteVo, sohuBusyTaskReceiveVo, "state", "refuseMsg");
                vo.setRelationTitle(sohuBusyTaskReceiveVo.getTitle());
            }
        }
        vo.setTypeName(this.getTypeName());
        vo.setConstMark(this.getTypeCode());
        this.afterQueryTask(vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean shelf(SohuBusyTaskBo bo, SohuBusyTask busyTask, Long userId) {
        // 审核时间
        Date auditTime = new Date();
        /*
         * 首先是任务上下架
         * 主任务发布后--子任务审核。有一个未通过的就是待审核任务。全部审核通过就是已通过状态
         * 主任务发布后--子任务有一个被接单了。主任务无法下架。没有子任务接单是可以全部下架的
         */
        // 超管审核，国家站长操作
//        if (LoginHelper.hasRole(RoleCodeEnum.ADMIN) || LoginHelper.hasRole(RoleCodeEnum.CountryStationAgent)) {
        // 上下架
        busyTask.setState(bo.getState());
        busyTask.setAuditTime(auditTime);
        busyTask.setAuditUser(userId);
        // 查询当前主任务所有子任务
        List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteService.listByMasterTaskNo(busyTask.getTaskNumber());
        // 判断全部满足这些状态的子任务。才能下架
        boolean canUpdateSite = isCanUpdateSite(busyTaskSiteList);
        if (!canUpdateSite) {
            throw new RuntimeException("已有站点在进行中，请查看站点详情联系");
        }
        // 可下架子单
        busyTaskSiteList.forEach(taskSite -> {
            if (bo.getState().equals(SohuBusyTaskState.CompelOff.name())) {
                taskSite.setShelfState(SohuBusyTaskState.CompelOff.name());
                taskSite.setState(SohuBusyTaskState.CompelOff.name());
                // 获取素材对象
                SohuIndependentMaterialVo sohuIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByCodeAndType(taskSite.getTaskNumber(), BusyType.BusyTask.getType());
                if (Objects.nonNull(sohuIndependentMaterialVo)) {
                    remoteMiddleIndependentMaterialService.updateByBo(new SohuIndependentMaterialBo()
                            .setId(sohuIndependentMaterialVo.getId())
                            .setStatus(CommonState.OffShelf.getCode())
                    );
                }
            } else {
                taskSite.setShelfState(SohuBusyTaskState.OnShelf.name());
                taskSite.setState(SohuBusyTaskState.Pass.name());
            }
            taskSite.setAuditTime(auditTime);
            taskSite.setAuditUser(userId);
            //智能推荐更新状态
            this.updateAirecContentItemStatusToOffShelf(taskSite);
        });
        return transactionTemplate.execute(e -> {
            this.afterShelfTask(busyTask);
            // 子单上下架
            sohuBusyTaskSiteService.updateBatch(busyTaskSiteList);
            // 主单上下架
            sohuBusyTaskMapper.updateById(busyTask);
            return Boolean.TRUE;
        });
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean receiveTask(SohuBusyTaskReceiveBo bo, SohuBusyTaskVo sohuBusyTaskVo,
                               SohuBusyTaskSite sohuBusyTaskSite, Long userId) {
        Boolean isReceive = this.beforeReceiveTask(bo, sohuBusyTaskVo, sohuBusyTaskSite, userId);
        return this.commonReceiveTask(isReceive, bo, sohuBusyTaskVo, sohuBusyTaskSite, userId, Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deliver(SohuBusyTaskDeliveryBo bo, SohuBusyTaskSiteVo busyTaskSiteVo, SohuBusyTaskVo sohuBusyTaskVo) {
        //待规划key规范
        String lockKey = String.format("sohu:lock:deliver_user_task_number:%s:%s", LoginHelper.getUserId(), bo.getTaskNumber());
        if (RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(1))) {
            return this.deliverTask(bo, busyTaskSiteVo, sohuBusyTaskVo);
        } else {
            throw new ServiceException("请稍后再试");
        }
    }

    @Override
    public Boolean cancel(SohuBusyTaskVo busyTaskVo, SohuBusyTaskSiteVo busyTaskSiteVo, SohuBusyTaskReceiveVo busyTaskReceiveVo) {
        throw new ServiceException("该愿望类型不支持手动取消愿望,请确认后再试");
    }

    @Override
    public Boolean applySettle(SohuBusyTaskVo busyTaskVo, SohuBusyTaskSiteVo busyTaskSiteVo, SohuBusyTaskReceiveVo busyTaskReceiveVo, SohuBusyTaskDeliveryCommonVo vo) {
        throw new ServiceException("该愿望类型不支持申请结算愿望,请确认后再试");
    }

    @Override
    public Boolean auditReceiveTask(SohuBusyTaskReceive taskReceive, SohuBusyTaskVo sohuBusyTaskVo, SohuBusyTaskSite sohuBusyTaskSite) {
        if (StrUtil.equalsAnyIgnoreCase(taskReceive.getState(), SohuBusyTaskState.Pass.name())) {
            // 其它接单状态置为拒绝
            LambdaUpdateWrapper<SohuBusyTaskReceive> luw = new LambdaUpdateWrapper<>();
            luw.set(SohuBusyTaskReceive::getState, SohuBusyTaskState.Refuse.name());
            luw.set(SohuBusyTaskReceive::getRefuseMsg, "当前愿望已被其他人接取");
            luw.eq(SohuBusyTaskReceive::getTaskNumber, taskReceive.getTaskNumber());
            luw.ne(SohuBusyTaskReceive::getId, taskReceive.getId());
//            this.baseMapper.update(null, luw);
            // 设置当前接单的为已通过
            taskReceive.setState(SohuBusyTaskState.Pass.name());
            taskReceive.setRefuseMsg(null);
            taskReceive.setPassTime(new Date());
//            this.baseMapper.updateById(taskReceive);
            return transactionTemplate.execute(e -> {
                // 不是接单状态的其他申请接单数据
                sohuBusyTaskReceiveMapper.update(new SohuBusyTaskReceive(), luw);
                // 当前接单状态通过的数据
                sohuBusyTaskReceiveMapper.updateById(taskReceive);
                return Boolean.TRUE;
            });
        } else if (StrUtil.equalsAnyIgnoreCase(taskReceive.getState(), SohuBusyTaskState.Refuse.name())) {
            taskReceive.setState(SohuBusyTaskState.Refuse.name());
            taskReceive.setRefuseMsg(taskReceive.getRefuseMsg());
            return this.sohuBusyTaskReceiveMapper.updateById(taskReceive) > 0;
        }
        return Boolean.FALSE;
    }

    @Override
    public SohuBusyTaskSiteVo getChildInfo(SohuBusyTaskSiteVo vo, Long receiveId,
                                           Long receiveUserId, Long shareUserId, Long userId) {
        if (vo.getDeliveryStep() != null && vo.getDeliveryStep()) {
            vo.setDeliveryList(sohuBusyTaskDeliveryMapper.selectVoList(Wrappers.<SohuBusyTaskDelivery>lambdaQuery().eq(SohuBusyTaskDelivery::getMasterTaskNumber, vo.getMasterTaskNumber())));
        }
        // 需要拆单
        if (null != vo.getTemplateId()) {
            vo.setTemplateVo(Objects.requireNonNull(sohuBusyTaskTemplateNumberService.queryByMasterTaskNo(vo.getMasterTaskNumber())));
        }
        // 是否有人接单
        if (null != vo.getIsReceive() && vo.getIsReceive()) {
            this.queryChildReceive(vo, userId);
        }
        vo.setReceiveTime(DateUtil.formatTime(vo.getUpdateTime()));
        // 任务详情属性扩展
        extendValueByTaskSiteVo(vo, receiveId, receiveUserId);
        if (userId != null && userId > 0L) {
            //登陆之后 记录浏览数据
            remoteMiddleViewRecordService.savePlayetViewRecord(userId, vo.getId(), BusyType.BusyOrder.name(), vo.getUserId(), Constants.Y, shareUserId, null);
        }
        return vo;
    }

    @Override
    public Boolean receiveApplyProcess(SohuBusyTaskReceiveApplyProcessBo processBo, SohuBusyTask busyTask) {
        SohuBusyTaskReceive taskReceive = sohuBusyTaskReceiveMapper.selectById(processBo.getId());
        if (Objects.isNull(taskReceive)) {
            throw new ServiceException(MessageUtils.message("当前接单记录不存在,请确认后再试"));
        }
        taskReceive.setState(processBo.getAuditState());
        taskReceive.setRefuseMsg(processBo.getRejectReason());
        return this.auditReceiveTask(taskReceive, null, null);
    }

    @Override
    public Boolean settleBusyTask(SohuBusyTask busyTask) {
        throw new ServiceException(MessageUtils.message("当前类型愿望不支持一键结算"));
    }

    @Override
    public Boolean applySettleBusyTask(SohuBusyTask busyTask, String rejectReason) {
        throw new ServiceException(MessageUtils.message("当前类型愿望不支持审核结算"));
    }

    @Override
    public void updatePassNum(SohuBusyTask busyTask) {
        return;
    }

    public void queryChildReceive(SohuBusyTaskSiteVo vo, Long userId) {
    }

    /**
     * 线上子单查询
     */
    public void queryOnlineChildReceive(SohuBusyTaskSiteVo vo, SohuBusyTaskReceiveVo receiveVo) {
        if (!receiveVo.getState().equals(CommonState.Exceed.getCode())) {
            vo.setTaskReceiveVo(receiveVo);
            // 基于接单时间获取对应的过期时间
            vo.setExpiryTime(BusyTaskUtil.addTimeToCurrentTime(vo.getDeliveryDay(), vo.getTimeUnit(), receiveVo.getCreateTime()));
        }
    }

    public String getReceiverTaskNickName(String taskNumber) {
        SohuBusyTaskReceive busyTaskReceive = sohuBusyTaskReceiveMapper.selectOne(SohuBusyTaskReceive::getTaskNumber, taskNumber);
        if (Objects.isNull(busyTaskReceive)) {
            return StrUtil.EMPTY;
        }
        LoginUser receiverTaskUser = remoteUserService.queryById(busyTaskReceive.getUserId());
        if (Objects.nonNull(receiverTaskUser)) {
            return DesensitizedUtil.chineseName(receiverTaskUser.getNickname());
        }
        return StrUtil.EMPTY;
    }

    /**
     * 默认提交愿望
     *
     * @param bo
     * @param busyTaskSiteVo
     * @param sohuBusyTaskVo
     * @return
     */
    public Boolean deliverTask(SohuBusyTaskDeliveryBo bo,
                               SohuBusyTaskSiteVo busyTaskSiteVo,
                               SohuBusyTaskVo sohuBusyTaskVo) {
        SohuBusyTaskDelivery add = BeanUtil.toBean(bo, SohuBusyTaskDelivery.class);
        SohuBusyTaskExecute taskExecute = BeanUtil.toBean(bo.getTaskExecuteBo(), SohuBusyTaskExecute.class);
        if (Objects.nonNull(bo.getId())) {
            if (Objects.nonNull(bo.getTaskExecuteBo().getId()) && bo.getState().equals(SohuBusyTaskState.WaitApprove.name())) {
                String receiverTaskNickName = getReceiverTaskNickName(busyTaskSiteVo.getTaskNumber());
                return transactionTemplate.execute(e -> {
                    sohuBusyTaskDeliveryMapper.updateById(add);
                    // 修改执行记录-附件、描述
                    taskExecuteMapper.updateById(taskExecute);
                    // 发送任务通知
                    sohuBusyTaskNoticeService.sendTaskSiteNotice(busyTaskSiteVo.getTaskNumber(), TaskNoticeEnum.TASK_UPDATE_STEP, busyTaskSiteVo.getUserId(), null, receiverTaskNickName, Boolean.FALSE, null, null);
                    sohuBusyTaskNoticeService.sendTaskSiteNotice(busyTaskSiteVo.getTaskNumber(), TaskNoticeEnum.UPDATE_TASK_PLAN, LoginHelper.getUserId(), null, null, Boolean.FALSE, null, null);
                    // 接单方提交任务进度极光推送
//                    CompletableFuture.runAsync(() -> this.pushUploadProgressTaskNotice(busyTaskSiteVo, Boolean.TRUE, LoginHelper.getUsername()),
//                            asyncConfig.getAsyncExecutor());
                    return Boolean.TRUE;
                });
            }
            if (Objects.nonNull(bo.getTaskExecuteBo().getId()) && bo.getState().equals(SohuBusyTaskState.Refuse.name())) {
                return transactionTemplate.execute(e -> {
                    add.setState(SohuBusyTaskState.WaitApprove.name());
                    sohuBusyTaskDeliveryMapper.updateById(add);
                    // 修改执行记录-附件、描述
                    taskExecuteMapper.updateById(taskExecute);
                    return Boolean.TRUE;
                });
            }
        } else {
            add.setMasterTaskNumber(sohuBusyTaskVo.getTaskNumber());
            boolean flag = sohuBusyTaskDeliveryMapper.insert(add) > 0;
            if (flag) {
                bo.setId(add.getId());
            } else {
                return Boolean.FALSE;
            }
        }
        taskExecute.setTaskNumber(bo.getTaskNumber());
        taskExecute.setBusyTaskDeliveryId(bo.getId());
        taskExecute.setBusyTaskReceiveId(LoginHelper.getUserId());
        // 发单方
        taskExecute.setExecuteType("Pull");
        return taskExecuteMapper.insert(taskExecute) > 0;
    }

    /**
     * 线上商单提交商单
     *
     * @param bo
     * @param busyTaskSiteVo
     * @param sohuBusyTaskVo
     * @return
     */
    public Boolean deliverOnLineTask(SohuBusyTaskDeliveryBo bo,
                                     SohuBusyTaskSiteVo busyTaskSiteVo,
                                     SohuBusyTaskVo sohuBusyTaskVo) {
        this.checkTask(bo.getTaskNumber(), bo.getTime(), LoginHelper.getUserId(), sohuBusyTaskVo);
        SohuBusyTask task = new SohuBusyTask();
        task.setId(sohuBusyTaskVo.getId());
        task.setState(SohuBusyTaskState.OverSettle.name());
        sohuBusyTaskMapper.updateById(task);
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        site.setId(busyTaskSiteVo.getId());
        site.setState(SohuBusyTaskState.OverSettle.name());
        sohuBusyTaskSiteMapper.updateById(site);
        SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
        receive.setId(bo.getId());
        receive.setState(SohuBusyTaskState.OverSettle.name());
        sohuBusyTaskReceiveMapper.updateById(receive);
        IncomeBusyOrderBo incomeBusyOrderBo = new IncomeBusyOrderBo();
        incomeBusyOrderBo.setOrderConstants(OrderConstants.BUSY_TASK_PREFIX);
        incomeBusyOrderBo.setUserId(LoginHelper.getUserId());
        incomeBusyOrderBo.setAmount(sohuBusyTaskVo.getFullAmount());
        incomeBusyOrderBo.setTaskId(busyTaskSiteVo.getId());
        incomeBusyOrderBo.setTaskNumber(busyTaskSiteVo.getTaskNumber());
        incomeBusyOrderBo.setTransactionType(SohuTradeRecordEnum.Type.BusyTaskParty.getCode());
        incomeBusyOrderBo.setMsg("愿望");
        String transactionNo = remoteMiddleUserBalanceService.incomeBusyOrder(incomeBusyOrderBo);
        SohuBusyTaskDelivery delivery = new SohuBusyTaskDelivery();
        delivery.setTaskNumber(busyTaskSiteVo.getTaskNumber());
        delivery.setMasterTaskNumber(busyTaskSiteVo.getMasterTaskNumber());
        delivery.setEndTime(new Date());
        delivery.setContent("愿望完成交付");
        delivery.setPercentage(BigDecimal.ONE);
        delivery.setState(SohuBusyTaskState.OverSettle.name());
        delivery.setPayState(PayStatus.Paid.name());
        delivery.setPayTime(new Date());
        delivery.setTransactionNo(transactionNo);
        sohuBusyTaskDeliveryMapper.insert(delivery);
        // 补充事件统计
        super.addEventReport(BusyTaskReportEnum.WCSD.getType(), bo.getTaskNumber(), sohuBusyTaskVo.getTitle(), LoginHelper.getUserId());
        SohuBusyTaskExecute taskExecute = new SohuBusyTaskExecute();
        taskExecute.setTaskNumber(busyTaskSiteVo.getTaskNumber());
        taskExecute.setBusyTaskReceiveId(bo.getId());
        taskExecute.setBusyTaskDeliveryId(delivery.getId());
        taskExecute.setUserId(LoginHelper.getUserId());
        taskExecute.setContent("愿望完成");
        // 发单方
        taskExecute.setExecuteType("Pull");
        return taskExecuteMapper.insert(taskExecute) > 0;
    }

    /**
     * 默认构建接单对象
     *
     * @param bo
     * @param sohuBusyTaskVo
     * @param sohuBusyTaskSite
     * @param userId
     * @return
     */
    public SohuBusyTaskReceive buildReceive(SohuBusyTaskReceiveBo bo,
                                            SohuBusyTaskVo sohuBusyTaskVo,
                                            SohuBusyTaskSite sohuBusyTaskSite,
                                            Long userId) {
        SohuBusyTaskReceive add = BeanUtil.toBean(bo, SohuBusyTaskReceive.class);
        validEntityBeforeSave(add);
        add.setUserId(userId);
        add.setState(SohuBusyTaskState.WaitApprove.name());
        add.setBackup(Boolean.TRUE);
        List<String> states = new ArrayList<>();
        states.add(McnUserStateEnum.NORMAL.getCode());
        states.add(McnUserStateEnum.DISABLED.getCode());
        SohuMcnUserVo sohuMcnUserVo = middleMcnUserService.findMcnIdByLoginId(states);
        if (Objects.nonNull(sohuMcnUserVo)) {
            add.setMcnId(sohuMcnUserVo.getMcnUserId());
        }
        this.afterBuild(add);
        return add;
    }

    /**
     * 线上商单接单对象
     *
     * @param bo
     * @param sohuBusyTaskVo
     * @param sohuBusyTaskSite
     * @param userId
     * @return
     */
    public SohuBusyTaskReceive buildOnlineReceive(SohuBusyTaskReceiveBo bo,
                                                  SohuBusyTaskVo sohuBusyTaskVo,
                                                  SohuBusyTaskSite sohuBusyTaskSite,
                                                  Long userId) {
        SohuBusyTaskReceive add = new SohuBusyTaskReceive();
        add.setTaskNumber(sohuBusyTaskSite.getTaskNumber());
        add.setUserId(userId);
        add.setApplyMsg("简化接单");
        add.setAmount(BigDecimal.ZERO);
        add.setState(SohuBusyTaskState.Execute.name());
        add.setBackup(Boolean.TRUE);
        // 一期任务商单全由平台发出,无需支付佣金
        add.setIsIndependent(Boolean.FALSE);
        add.setPassTime(DateUtils.getNowDate());
        add.setExpireTime(BusyTaskUtil.addTimeToCurrentTime(sohuBusyTaskVo.getDeliveryDay(), sohuBusyTaskVo.getTimeUnit(), new Date()));
        this.validEntityBeforeSave(add);
        return add;
    }

    /**
     * 通用新增线上商单方法
     *
     * @param bo
     * @param userId
     * @return
     */
    public Long addOnlineBo(SohuBusyTaskBo bo, Long userId) {
        SohuBusyTask busyTask = this.createTask(bo, userId);
        busyTask.setState(SohuBusyTaskState.Pass.name());
        // 线上不存在拆单逻辑
        boolean flag = sohuBusyTaskMapper.insert(busyTask) > 0;
        if (busyTask.getIsDraft() == null || !busyTask.getIsDraft()) {
            List<SohuBusyTaskSite> taskSites = new ArrayList<>();
            for (Long siteId : bo.getAddSiteIds()) {
                SohuBusyTaskSite taskSite = new SohuBusyTaskSite();
                taskSite.setMasterTaskNumber(busyTask.getTaskNumber());
                taskSite.setUserId(userId);
                taskSite.setDistributionAmount(BigDecimal.ZERO);
                taskSite.setTaskNumber(NumberUtil.getOrderNo(OrderConstants.CHILD_TASK_PREFIX));
                taskSite.setSiteId(siteId);
                taskSite.setCountrySiteId(busyTask.getCountrySiteId());
                taskSite.setAuditUser(userId);
                taskSite.setAuditTime(new Date());
                taskSite.setShelfState(SohuBusyTaskState.OnShelf.name());
                this.extractedTaskSite(busyTask, taskSite);
                taskSite.setState(SohuBusyTaskState.WaitReceive.name());
                taskSites.add(taskSite);
            }
            if (CollectionUtils.isNotEmpty(taskSites)) {
                // 保存站点关联
                sohuBusyTaskSiteMapper.insertBatch(taskSites);
            }
        }
        return busyTask.getId();
    }

    /**
     * 通用新增线下商单方法
     *
     * @param bo
     * @param userId
     * @return
     */
    public Long addOfflineBo(SohuBusyTaskBo bo, Long userId) {
        SohuBusyTask busyTask = this.createTask(bo, userId);
        busyTask.setState(SohuBusyTaskState.WaitApprove.name());
        // 判断是否需要拆单--选择拆单后不能指定人接单
        if (bo.getNeedSplit()) {
            // 待拆单
            busyTask.setSplitState(Boolean.FALSE);
            // 是否是阶段性任务
            sohuBusyTaskTemplateNumberService.isDeliveryStep(busyTask, bo.getDeliveryStep(), bo.getDeliveryList());
            // 新建任务
            transactionTemplate.execute(e -> {
                // 主任务保存
                sohuBusyTaskMapper.insert(busyTask);
                // 发单方消息通知
                sohuBusyTaskNoticeService.sendTaskNotice(busyTask.getId(), TaskNoticeEnum.TASK_WAIT_AUDIT, LoginHelper.getUserId(), null, null, Boolean.FALSE);
                // 保存阶段性列表
                if (CollectionUtils.isNotEmpty(bo.getDeliveryList())) {
                    sohuBusyTaskDeliveryMapper.insertBatch(BeanCopyUtils.copyList(bo.getDeliveryList(), SohuBusyTaskDelivery.class));
                }
                return Boolean.TRUE;
            });
            return busyTask.getId();
        } else {
            // 新建任务
            boolean flag = sohuBusyTaskMapper.insert(busyTask) > 0;
            // 发单方消息通知
            sohuBusyTaskNoticeService.sendTaskNotice(busyTask.getId(), TaskNoticeEnum.TASK_WAIT_AUDIT, LoginHelper.getUserId(), null, null, Boolean.FALSE);
            if (flag) {
                List<SohuBusyTaskSite> taskSites = new ArrayList<>();
                if (bo.getIsDraft() == null || !bo.getIsDraft()) {
                    // 发布审核--国家站/超管
                    bo.setId(busyTask.getId());
                    // 佣金
                    BigDecimal independentPrice = BigDecimal.ZERO.stripTrailingZeros();
                    // 一口价
                    if (busyTask.getKickbackType().equals(KickbackType.price.getCode())) {
                        independentPrice = busyTask.getKickbackValue();
                    } else if (busyTask.getKickbackType().equals(KickbackType.percentage.getCode())) {
                        // 任务分销百分比
                        BigDecimal taskDivide = BigDecimalUtils.divide(busyTask.getKickbackValue(), CalUtils.PERCENTAGE);
                        // 任务分销总金额 = 任务价格 * 分销百分比
                        independentPrice = busyTask.getFullAmount().multiply(taskDivide).setScale(2, RoundingMode.HALF_UP);
                        log.warn("任务按照百分比分销金额：{}", independentPrice);
                    }
                    for (Long siteId : bo.getAddSiteIds()) {
                        SohuBusyTaskSite taskSite = new SohuBusyTaskSite();
                        taskSite.setMasterTaskNumber(busyTask.getTaskNumber());
                        taskSite.setUserId(userId);
                        taskSite.setDistributionAmount(independentPrice);
                        taskSite.setTaskNumber(NumberUtil.getOrderNo(OrderConstants.CHILD_TASK_PREFIX));
                        taskSite.setSiteId(siteId);
                        taskSite.setCountrySiteId(busyTask.getCountrySiteId());
                        extractedTaskSite(busyTask, taskSite);
                        taskSites.add(taskSite);
                    }
                    // 是否是阶段性任务
                    sohuBusyTaskTemplateNumberService.isDeliveryStep(busyTask, bo.getDeliveryStep(), bo.getDeliveryList());
                }
                transactionTemplate.execute(e -> {
                    if (CollectionUtils.isNotEmpty(taskSites)) {
                        // 保存站点关联--如果有接单人：审核通过之后关联指定接单人
                        sohuBusyTaskSiteMapper.insertBatch(taskSites);
                    }
                    // 保存阶段性列表
                    if (CollectionUtils.isNotEmpty(bo.getDeliveryList())) {
                        sohuBusyTaskTemplateNumberService.isDeliveryStep(busyTask, bo.getDeliveryStep(), bo.getDeliveryList());
                        sohuBusyTaskDeliveryMapper.insertBatch(BeanCopyUtils.copyList(bo.getDeliveryList(), SohuBusyTaskDelivery.class));
                    }
                    return Boolean.TRUE;
                });
            }
            return busyTask.getId();
        }
    }

    /**
     * 通用编辑方法
     *
     * @param busyTask
     * @param bo
     * @return
     */
    public Long updateTask(SohuBusyTask busyTask, SohuBusyTaskBo bo) {
        int updated = sohuBusyTaskMapper.updateById(busyTask);
        if (updated < 1) {
            return bo.getId();
        }
        this.afterUpdateTask(busyTask, bo);
        return bo.getId();
    }

    /**
     * 编辑之后钩子方法
     *
     * @param busyTask
     * @param bo
     */
    public abstract void afterUpdateTask(SohuBusyTask busyTask, SohuBusyTaskBo bo);

    /**
     * 查询明细之后钩子方法
     *
     * @param vo
     */
    public abstract void afterQueryTask(SohuBusyTaskVo vo);

    /**
     * 商单上下架钩子方法
     */
    public abstract void afterShelfTask(SohuBusyTask busyTask);

    /**
     * 商单接单前置校验钩子方法
     *
     * @param bo
     * @param sohuBusyTaskVo
     * @param sohuBusyTaskSite
     */
    public abstract Boolean beforeReceiveTask(SohuBusyTaskReceiveBo bo, SohuBusyTaskVo sohuBusyTaskVo, SohuBusyTaskSite sohuBusyTaskSite, Long userId);

    /**
     * 返回类型名称
     *
     * @return
     */
    public abstract String getTypeName();

    /**
     * 返回类型编码
     *
     * @return
     */
    public abstract String getTypeCode();

    /**
     * 抽象新增方法钩子(必须实现)
     *
     * @param bo
     * @param userId
     * @return
     */
    public abstract Long addTask(SohuBusyTaskBo bo, Long userId);

    /**
     * 接单之后钩子方法（必须实现）
     */
    public abstract void afterReceive(boolean flag,
                                      SohuBusyTaskReceive add,
                                      SohuBusyTaskSite sohuBusyTaskSite,
                                      SohuBusyTaskVo sohuBusyTaskVo, Long userId,
                                      Boolean isSendNotice);

    /**
     * 接单对象组装后的钩子方法
     *
     * @param receive
     */
    public abstract void afterBuild(SohuBusyTaskReceive receive);

    /**
     * 接单后逻辑处理
     *
     * @param flag
     * @param add
     * @param sohuBusyTaskSite
     * @param sohuBusyTaskVo
     * @return
     */
    public Boolean afterReceiveTask(boolean flag,
                                    SohuBusyTaskReceive add,
                                    SohuBusyTaskSite sohuBusyTaskSite,
                                    SohuBusyTaskVo sohuBusyTaskVo, Long userId,
                                    Boolean isSendNotice) {
        if (flag) {
            try {
                this.sendTaskApply(sohuBusyTaskSite);
                if (!sohuBusyTaskSite.getIsReceive()) {
                    sohuBusyTaskSite.setIsReceive(Boolean.TRUE);
                    sohuBusyTaskSiteMapper.updateById(sohuBusyTaskSite);
                }
            } catch (Exception e) {
                log.error("通知发送异常,避免影响后续流程及逻辑");
            }
        }
        this.afterReceive(flag, add, sohuBusyTaskSite, sohuBusyTaskVo, userId, isSendNotice);
        BusyTaskReceiveEvent receiveEvent = new BusyTaskReceiveEvent(this, add);
        applicationEventPublisher.publishEvent(receiveEvent);
        return flag;
    }

    /**
     * 发送审核消息
     *
     * @param sohuBusyTaskSite
     */
    public void sendTaskApply(SohuBusyTaskSite sohuBusyTaskSite) {
        // 发送发单方任务接单通知
        sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.TASK_APPLY,
                sohuBusyTaskSite.getUserId(), null, null, Boolean.FALSE, null, null);
        //发送接单方申请接单消息
        sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSite.getTaskNumber(), TaskNoticeEnum.SEND_TASK_APPLY,
                LoginHelper.getUserId(), null, null, Boolean.FALSE, null, null);
    }

    /**
     * 接单后逻辑处理（线上商单）
     *
     * @param flag
     * @param sohuBusyTaskSite
     * @param sohuBusyTaskVo
     * @return
     */
    public Boolean afterReceiveOnlineTask(boolean flag,
                                          SohuBusyTaskSite sohuBusyTaskSite,
                                          SohuBusyTaskVo sohuBusyTaskVo) {
        if (flag) {
            // 异步更新站点表商单状态
            sohuBusyTaskSite.setIsReceive(Boolean.TRUE);
            sohuBusyTaskSite.setState(SohuBusyTaskState.Execute.name());
            CompletableFuture.runAsync(() -> sohuBusyTaskSiteMapper.updateById(sohuBusyTaskSite), asyncConfig.getAsyncExecutor());
        }
        SohuBusyTask busyTask = new SohuBusyTask();
        busyTask.setId(sohuBusyTaskVo.getId());
        busyTask.setState(SohuBusyTaskState.Execute.name());
        sohuBusyTaskMapper.updateById(busyTask);
        // 补充事件统计
        super.addEventReport(BusyTaskReportEnum.JQSD.getType(), sohuBusyTaskSite.getTaskNumber(), sohuBusyTaskVo.getTitle(), LoginHelper.getUserId());
        // 延迟队列--更新广告缓存信息
        MsgContent msgContent = new MsgContent(sohuBusyTaskSite.getId(), CommonState.Delete.getCode(), BusyType.BusyOrder.name());
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
        RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
        return flag;
    }

    /**
     * 线下商单通用后续逻辑更改方法
     *
     * @param busyTask
     * @param bo
     * @return
     */
    public Boolean updateOfflineBo(SohuBusyTask busyTask, SohuBusyTaskBo bo) {
        List<SohuBusyTaskSite> taskSites = new ArrayList<>();
        // 发布审核--国家站/超管
        if (CollectionUtils.isNotEmpty(bo.getAddSiteIds())) {
            List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteService.listByMasterTaskNo(bo.getTaskNumber());
            if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
                this.handleChildTask(taskSites, busyTaskSiteList, bo, busyTask);
            } else {
                for (Long siteId : bo.getAddSiteIds()) {
                    SohuBusyTaskSite taskSite = new SohuBusyTaskSite();
                    taskSite.setMasterTaskNumber(busyTask.getTaskNumber());
                    taskSite.setUserId(bo.getUserId());
                    taskSite.setTaskNumber(NumberUtil.getOrderNo(OrderConstants.CHILD_TASK_PREFIX));
                    taskSite.setSiteId(siteId);
                    taskSite.setCountrySiteId(bo.getCountrySiteId());
                    extractedTaskSite(busyTask, taskSite);
                    taskSites.add(taskSite);
                }
            }
            if (null == bo.getIsSaveDraft() || bo.getIsSaveDraft().equals(Boolean.FALSE)) {
                // 如果有城市站点默认不是草稿了
                busyTask.setIsDraft(Boolean.FALSE);
            }
        }
        // 是否是阶段性任务
        sohuBusyTaskTemplateNumberService.isDeliveryStep(busyTask, bo.getDeliveryStep(), bo.getDeliveryList());
        return transactionTemplate.execute(e -> {
            sohuBusyTaskMapper.updateById(busyTask);
            if (CollectionUtils.isNotEmpty(taskSites)) {
                // 保存站点关联--如果有接单人：审核通过之后关联指定接单人
                sohuBusyTaskSiteMapper.insertOrUpdateBatch(taskSites);
            }
            // 保存阶段性列表
            if (CollectionUtils.isNotEmpty(bo.getDeliveryList())) {
                // 是否是阶段性任务
//                    busyTaskTemplateNumberService.isDeliveryStep(busyTask, bo.getDeliveryStep(), bo.getDeliveryList());
                sohuBusyTaskDeliveryMapper.insertOrUpdateBatch(BeanCopyUtils.copyList(bo.getDeliveryList(), SohuBusyTaskDelivery.class));
            }
            return Boolean.TRUE;
        });
    }

    /**
     * 组装taskSite
     *
     * @param busyTask
     * @param taskSite
     */
    public void extractedTaskSite(SohuBusyTask busyTask, SohuBusyTaskSite taskSite) {
        if (busyTask.getReceiveLimit()) {
            taskSite.setReceiveOnlyUserId(busyTask.getReceiveOnlyUserId());
        }
        taskSite.setType(busyTask.getType());
        taskSite.setIndustryType(busyTask.getIndustryType());
        taskSite.setTitle(busyTask.getTitle());
        taskSite.setContent(busyTask.getContent());
        taskSite.setAnnex(busyTask.getAnnex());
        taskSite.setReceiveLimit(busyTask.getReceiveLimit());
        taskSite.setDeliveryDay(busyTask.getDeliveryDay());
        taskSite.setDeliveryMsg(busyTask.getDeliveryMsg());
        taskSite.setSettleType(busyTask.getSettleType());
        taskSite.setDeliveryStep(busyTask.getDeliveryStep());
        taskSite.setKickbackType(busyTask.getKickbackType());
        taskSite.setKickbackValue(busyTask.getKickbackValue());
        taskSite.setFullAmount(busyTask.getFullAmount());
        taskSite.setFullCurrency(busyTask.getFullCurrency());
        taskSite.setState(SohuBusyTaskState.WaitApprove.name());
    }

    /**
     * 修改所有满足条件的子任务
     *
     * @param busyTask
     */
    public Boolean updateChildTask(SohuBusyTask busyTask, SohuBusyTaskBo bo) {
        // 查询当前主任务所有子任务
        List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteService.listByMasterTaskNo(busyTask.getTaskNumber());
        if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
            // 判断可修改的任务
            boolean canUpdateSite = isCanUpdateSite(busyTaskSiteList);
            if (canUpdateSite) {
                List<SohuBusyTaskSite> taskSites = Lists.newArrayList();
                // 判断是否有新增
                if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
                    this.handleChildTask(taskSites, busyTaskSiteList, bo, busyTask);
                }
                taskSites.forEach(taskSite -> {
                    extractedTaskSite(busyTask, taskSite);
                });
                if (busyTask.getState().equals(SohuBusyTaskState.OffShelf.name())) {
                    // 发送任务通知
                    sohuBusyTaskNoticeService.sendTaskNotice(busyTask.getId(), TaskNoticeEnum.TASK_COMPEL_OFF,
                            LoginHelper.getUserId(), null, null, Boolean.FALSE);
                }
                boolean flag = sohuBusyTaskSiteMapper.insertOrUpdateBatch(taskSites);
                if (flag) {
                    // 同步接单表状态为 Cancel
                    List<String> taskNumberList = taskSites.stream()
                            .map(SohuBusyTaskSite::getTaskNumber)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(taskNumberList)) {
                        List<SohuBusyTaskReceive> receiveList = sohuBusyTaskReceiveMapper.selectByTaskNumberList(taskNumberList);
                        if (CollectionUtils.isNotEmpty(receiveList)) {
                            for (SohuBusyTaskReceive receive : receiveList) {
                                receive.setState(SohuBusyTaskState.Cancel.name());
                                receive.setRefuseMsg("由于发布者已编辑此任务相关信息发生变更,系统自动取消,您可在任务广场申请再次接单");
                            }
                            sohuBusyTaskReceiveMapper.insertOrUpdateBatch(receiveList);
                            // 同步素材表
                            remoteMiddleIndependentMaterialService.deleteByCodeAndType(receiveList.get(0).getTaskNumber(), BusyType.BusyTask.getType());
                        } else {
                            // 同步素材表
                            remoteMiddleIndependentMaterialService.deleteByCodeAndType(busyTaskSiteList.get(0).getTaskNumber(), BusyType.BusyTask.getType());
                        }
                    }
                }
                return sohuBusyTaskSiteMapper.insertOrUpdateBatch(taskSites);
            }
            throw new RuntimeException("当前任务有已审核通过的站点不能修改主任务、请先下架已通过的站点再修改");
        }
        // 同步素材库状态
        remoteMiddleIndependentMaterialService.deleteByCodeAndType(busyTaskSiteList.get(0).getTaskNumber(), BusyType.BusyTask.getType());
        return Boolean.TRUE;
    }

    /**
     * 创建商单
     *
     * @param bo
     * @param userId
     */
    public SohuBusyTask createTask(SohuBusyTaskBo bo, Long userId) {
        SohuBusyTask busyTask = BeanUtil.toBean(bo, SohuBusyTask.class);
        // 主任务编号
        busyTask.setTaskNumber(NumberUtil.getOrderNo(OrderConstants.MASTER_TASK_PREFIX));
        busyTask.setUserId(userId);
        // 处理时间
        busyTask.setDeliveryTime(BusyTaskUtil.buildCurrentTime(busyTask.getDeliveryDay(), busyTask.getTimeUnit()));
        SohuIndustryCategoryVo industryCategoryVo = this.remoteIndustryCategoryService.queryRootById(bo.getIndustryType());
        if (Objects.nonNull(industryCategoryVo)) {
            busyTask.setPid(industryCategoryVo.getId());
        }
        // 处理站点信息
        if (CollUtil.isEmpty(bo.getAddSiteIds())) {
            // 默认处理站点设置为11
//            SohuSiteVo siteVo = remoteMiddleSiteService.getSiteByIp(ServletUtils.getClientIP());
//            busyTask.setSiteIds(String.valueOf(siteVo.getId()));
//            bo.setAddSiteIds(Collections.singletonList(siteVo.getId()));
//            if (Objects.equals(siteVo.getType(), SiteType.City.name())) {
//                Long pid = siteVo.getPid();
//                busyTask.setCountrySiteId(pid);
//            }
            busyTask.setSiteIds("11");
            busyTask.setCountrySiteId(5L);
            bo.setAddSiteIds(Collections.singletonList(11L));
        } else {
            Long countryId = remoteMiddleSiteService.selectCountryIdByCityId(bo.getAddSiteIds().get(0));
            if (countryId != null) {
                busyTask.setCountrySiteId(countryId);
            }
            busyTask.setSiteIds(String.valueOf(bo.getAddSiteIds().get(0)));
        }
        return busyTask;
    }

    /**
     * 处理组装子任务信息
     *
     * @param taskSites
     * @param busyTaskSiteList
     * @param bo
     * @param busyTask
     */
    private void handleChildTask(List<SohuBusyTaskSite> taskSites,
                                 List<SohuBusyTaskSite> busyTaskSiteList,
                                 SohuBusyTaskBo bo,
                                 SohuBusyTask busyTask) {
        List<Long> siteIdSite = busyTaskSiteList.stream().map(SohuBusyTaskSite::getSiteId).collect(Collectors.toList());
        // 原本的-传入的
        List<Long> nowIdList = siteIdSite.stream().filter(item -> !bo.getAddSiteIds().contains(item)).collect(Collectors.toList());
        // 需要删除的集合
        List<SohuBusyTaskSite> deleteList = busyTaskSiteList.stream().filter(site -> nowIdList.contains(site.getSiteId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            sohuBusyTaskSiteMapper.deleteBatchIds(deleteList);
        }
        // 传入的没有的
        List<Long> noIdList = bo.getAddSiteIds().stream().filter(item -> !siteIdSite.contains(item)).collect(Collectors.toList());
        // 共有的
        List<Long> yesIdList = bo.getAddSiteIds().stream().filter(siteIdSite::contains).collect(Collectors.toList());
        // 共有集合
        List<SohuBusyTaskSite> filteredList = busyTaskSiteList.stream().filter(site -> yesIdList.contains(site.getSiteId())).collect(Collectors.toList());
        // 分销佣金计算
        BigDecimal independentPrice = new BigDecimal("0.00");
        // 一口价
        if (busyTask.getKickbackType().equals(KickbackType.price.getCode())) {
            independentPrice = busyTask.getKickbackValue();
        } else if (busyTask.getKickbackType().equals(KickbackType.percentage.getCode())) {
            // 任务分销百分比
            BigDecimal taskDivide = BigDecimalUtils.divide(busyTask.getKickbackValue(), CalUtils.PERCENTAGE);
            // 任务分销总金额 = 任务价格 * 分销百分比
            independentPrice = busyTask.getFullAmount().multiply(taskDivide).setScale(2, RoundingMode.HALF_UP);
        }
        // 原有的做update
        for (SohuBusyTaskSite busyTaskSite : filteredList) {
            busyTaskSite.setMasterTaskNumber(busyTask.getTaskNumber());
            busyTaskSite.setUserId(bo.getUserId());
            busyTaskSite.setDistributionAmount(independentPrice);
            busyTaskSite.setCountrySiteId(bo.getCountrySiteId());
            extractedTaskSite(busyTask, busyTaskSite);
            taskSites.add(busyTaskSite);
        }
        for (Long aLong : noIdList) {
            SohuBusyTaskSite taskSite = new SohuBusyTaskSite();
            taskSite.setMasterTaskNumber(busyTask.getTaskNumber());
            taskSite.setUserId(bo.getUserId());
            taskSite.setDistributionAmount(independentPrice);
            taskSite.setTaskNumber(NumberUtil.getOrderNo(OrderConstants.CHILD_TASK_PREFIX));
            taskSite.setSiteId(aLong);
            taskSite.setCountrySiteId(bo.getCountrySiteId());
            extractedTaskSite(busyTask, taskSite);
            taskSites.add(taskSite);
        }
    }

    /**
     * 属性扩展
     *
     * @param vo            SohuBusyTaskSiteVo
     * @param receiveId
     * @param receiveUserId
     */
    private void extendValueByTaskSiteVo(SohuBusyTaskSiteVo vo, Long receiveId, Long receiveUserId) {
        // 设置子任务佣金
        if (StringUtils.isNotBlank(vo.getKickbackType()) && !StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.none.getCode())) {
            if (StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.price.getCode())) {
                // 一口价
                vo.setDistributionAmount(vo.getKickbackValue());
            } else {
                BigDecimal taskDivide = BigDecimalUtils.divide(vo.getKickbackValue(), CalUtils.PERCENTAGE);
                // 百分比
                vo.setDistributionAmount(vo.getFullAmount().multiply(taskDivide).setScale(2, RoundingMode.HALF_UP));
            }
        } else {
            vo.setDistributionAmount(BigDecimal.ZERO);
        }
        // 获取发单方账户状态
        if (ObjectUtils.isNotNull(vo) && null != vo.getUserId()) {
            SohuAccountVo sohuAccountModel = remoteAccountService.queryByUserIdOfPass(vo.getUserId());
            if (Objects.nonNull(sohuAccountModel)) {
                vo.setAccountType(sohuAccountModel.getAccountType());
                // 个人认证加密头像以及昵称
                if (StrUtil.equalsAnyIgnoreCase(sohuAccountModel.getAccountType(), UserAuthEnum.personal.getType())) {
                    vo.setAvatar(Constants.DEFAULT_USER_AVATAR);
                    String originalNickname = vo.getNickName();
                    if (originalNickname.length() > Constants.THRID) {
                        String modifiedNickname = originalNickname.substring(Constants.ZERO, Constants.THRID) + originalNickname.substring(Constants.THRID).replaceAll(".", "*");
                        vo.setNickName(modifiedNickname);
                    }
                }
            }
        }
        // 获取当前登录用户id
        Long userId = LoginHelper.getUserId();
        if (userId != null && userId > 0L) {
            // 接单方开户账号视图
            if (StringUtils.isNotBlank(vo.getRelateTaskNumber())) {
                SohuBusyTaskSiteVo busyTaskSiteVo = sohuBusyTaskSiteService.queryByTaskNumber(vo.getRelateTaskNumber());
                vo.setRelateBusyTaskSiteVo(Objects.nonNull(busyTaskSiteVo) ? busyTaskSiteVo : null);
            }
        }
        // 增加浏览量--初版
        if (ObjectUtils.isNotNull(vo)) {
            LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SohuBusyTask::getTaskNumber, vo.getMasterTaskNumber());
            SohuBusyTask sohuBusyTask = sohuBusyTaskMapper.selectOne(wrapper);
            if (Objects.nonNull(sohuBusyTask)) {
                sohuBusyTaskMapper.updateViewCount(vo.getMasterTaskNumber());
                vo.setViewCount(sohuBusyTask.getViewCount());
            }
        }
        this.afterQueryExtendValue(vo, receiveId, receiveUserId, userId);
    }

    public abstract void afterQueryExtendValue(SohuBusyTaskSiteVo vo, Long receiveId, Long receiveUserId, Long userId);

    /**
     * 线下商单查询附加逻辑处理
     *
     * @param vo
     * @param userId
     */
    public void afterOfflineQueryExtendValue(SohuBusyTaskSiteVo vo, Long userId) {
        // 任务是否收藏
        Map<Long, SohuUserCollectVo> collectVoMap = new HashMap<>();
        if (userId != null && userId > 0L) {
            collectVoMap = remoteMiddleUserCollectService.queryMap(LoginHelper.getUserId(), BusyType.BusyTask.name(), Collections.singletonList(vo.getId()));
        }
        vo.setCollectObj(Objects.nonNull(collectVoMap.get(vo.getId())));
        // 是否关联任务
        SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoOne(
                Wrappers.<SohuBusyTask>lambdaQuery().eq(SohuBusyTask::getTaskNumber, vo.getMasterTaskNumber()));
//        if (sohuBusyTaskVo.getRelationId() != null) {
//            SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = sohuBusyTaskReceiveService.queryById(sohuBusyTaskVo.getRelationId());
//            vo.setRelationTitle(sohuBusyTaskReceiveVo.getTitle());
//            vo.setRelationId(sohuBusyTaskVo.getRelationId());
//        }
        //售后信息
        LambdaQueryWrapper<SohuBusyTaskAfterSales> lmq = Wrappers.lambdaQuery();
        lmq.eq(SohuBusyTaskAfterSales::getTaskNumber, vo.getTaskNumber());
        List<SohuBusyTaskAfterSales> afterSales = sohuBusyTaskAfterSalesMapper.selectList(lmq);
        if (CollUtil.isNotEmpty(afterSales)) {
            SohuBusyTaskAfterSales lastAfterSale = afterSales.get(afterSales.size() - 1);
            SohuBusyTaskAfterSalesVo taskAfterSaleVo = BeanUtil.toBean(lastAfterSale, SohuBusyTaskAfterSalesVo.class);
            vo.setSohuBusyTaskAfterSalesVo(taskAfterSaleVo);
            vo.setAfterSalesId(taskAfterSaleVo.getId());
            if (StringUtils.isNotBlank(taskAfterSaleVo.getReceiveOnlyUserId())) {
                vo.setReceiveOnlyUserId(Long.valueOf(taskAfterSaleVo.getReceiveOnlyUserId()));
            }
            Long explainCount = sohuBusyTaskAfterSalesExplainService.countByAfterSaleId(vo.getAfterSalesId());
            vo.setExplainCount(explainCount);
        }
        // 服务费
        SohuIndependentTemplateModel independentTemplateModel = remoteIndependentTemplateService.queryTemplateInfo(null,null, Constants.TWO);
        if (Objects.nonNull(independentTemplateModel)) {
            BigDecimal add = vo.getFullAmount().add(vo.getDistributionAmount());
            BigDecimal platformRatio = independentTemplateModel.getPlatformRatio();
            vo.setPlatformFee((add.multiply(platformRatio)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 公共接单方法
     *
     * @param isReceive
     * @param bo
     * @param sohuBusyTaskVo
     * @param sohuBusyTaskSite
     * @param userId
     * @return
     */
    public Boolean commonReceiveTask(Boolean isReceive, SohuBusyTaskReceiveBo bo,
                                     SohuBusyTaskVo sohuBusyTaskVo,
                                     SohuBusyTaskSite sohuBusyTaskSite, Long userId,
                                     Boolean isSendNotice) {
        if (isReceive) {
            String lockKey = String.format("sohu:lock:receive_task_number:%s", bo.getTaskNumber());
            if (RedisUtils.setObjectIfAbsent(lockKey, 1, Duration.ofSeconds(1))) {
                SohuBusyTaskReceive add = this.buildReceive(bo, sohuBusyTaskVo, sohuBusyTaskSite, userId);
                boolean flag = sohuBusyTaskReceiveMapper.insert(add) > 0;
                try {
                    return this.afterReceiveTask(flag, add, sohuBusyTaskSite, sohuBusyTaskVo, userId, isSendNotice);
                } catch (Exception e) {
                    throw new RuntimeException("Error in afterReceiveTask, rolling back transaction", e);
                }
            } else {
                throw new ServiceException("活动太火爆了,请稍后再试");
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 同步愿望发布行为
     * @param eventAttribute
     */
    protected void syncTaskUserBehavior(Long userId,String nickName,SohuUserBehaviorRecordPointBo.EventAttribute eventAttribute){
        SohuUserBehaviorRecordPointBo pointBo = new SohuUserBehaviorRecordPointBo();
        pointBo.setUserId(userId);
        pointBo.setUserName(nickName);
        pointBo.setEventSign("sd_new_success");
        pointBo.setEventName("愿望发布成功");
        pointBo.setBusinessType(BehaviorBusinessTypeEnum.BUSY_ORDER.getCode());
        pointBo.setOperaType(OperaTypeEnum.ADD.getOperaType());
        pointBo.setOperaSource(Constants.ZERO);
        pointBo.setRequestId(UUID.randomUUID().toString());
        pointBo.setEventAttribute(eventAttribute);
        remoteMiddleUserBehaviorRecordService.addList(Arrays.asList(pointBo));
    }
}
