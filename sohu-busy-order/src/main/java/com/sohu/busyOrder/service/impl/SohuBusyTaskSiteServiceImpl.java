package com.sohu.busyOrder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.sohu.busyOrder.domain.SohuBusyTask;
import com.sohu.busyOrder.domain.SohuBusyTaskAfterSales;
import com.sohu.busyOrder.domain.SohuBusyTaskReceive;
import com.sohu.busyOrder.domain.SohuBusyTaskSite;
import com.sohu.busyOrder.mapper.SohuBusyTaskAfterSalesMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskReceiveMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskSiteMapper;
import com.sohu.busyOrder.service.ISohuBusyTaskNoticeService;
import com.sohu.busyOrder.service.ISohuBusyTaskSiteService;
import com.sohu.busyOrder.utils.BusyTaskUtil;
import com.sohu.busyorder.api.RemoteBusinessEventReportService;
import com.sohu.busyorder.api.bo.SohuBusinessEventReportBo;
import com.sohu.busyorder.api.bo.SohuBusyTaskSiteBo;
import com.sohu.busyorder.api.bo.SohuMcnBusyTaskSiteBo;
import com.sohu.busyorder.api.enums.BusyTaskTypeEnum;
import com.sohu.busyorder.api.enums.SettleTypeEnums;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.SohuBusinessEventReportVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.ServletUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.SohuImGroupFormGeneralCreateBo;
import com.sohu.im.api.bo.SohuImGroupFormHandleBo;
import com.sohu.im.api.bo.SohuImGroupRelateBo;
import com.sohu.im.api.service.RemoteImGroupFormService;
import com.sohu.im.api.service.RemoteImGroupFromGeneralService;
import com.sohu.im.api.service.RemoteImGroupOrderUserService;
import com.sohu.im.api.service.RemoteImGroupRelateService;
import com.sohu.im.api.vo.SohuImGroupRelateVo;
import com.sohu.middle.api.bo.SohuAuditBo;
import com.sohu.middle.api.bo.SohuEventReportBo;
import com.sohu.middle.api.bo.SohuUserFollowBo;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.enums.McnUserStateEnum;
import com.sohu.middle.api.enums.report.BusyTaskReportEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.mcn.RemoteMiddleMcnUserService;
import com.sohu.middle.api.vo.*;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import com.sohu.pay.api.RemotePaySettlementService;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteUserService;
import com.sohu.third.aliyun.airec.constants.AliyunAirecConstant;
import com.sohu.third.aliyun.airec.constants.AliyunAirecTagsConstant;
import com.sohu.third.aliyun.airec.domain.AliyunAirecJoinFilterRule;
import com.sohu.third.aliyun.airec.domain.AliyunAirecSingleFilterRule;
import com.sohu.third.aliyun.airec.enums.AliyunAirecContentItemTypeEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecQueryCondEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecQueryFieldContentEnum;
import com.sohu.third.aliyun.airec.enums.AliyunAirecQueryJoinEnum;
import com.sohu.third.aliyun.airec.util.AliyunAirecUtil;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商单与站点关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SohuBusyTaskSiteServiceImpl implements ISohuBusyTaskSiteService {

    private final SohuBusyTaskSiteMapper baseMapper;
    private final SohuBusyTaskAfterSalesMapper sohuBusyTaskAfterSalesMapper;
    private final SohuBusyTaskMapper sohuBusyTaskMapper;
    private final SohuBusyTaskReceiveMapper sohuBusyTaskReceiveMapper;
    private final AsyncConfig asyncConfig;
    private final ISohuBusyTaskNoticeService sohuBusyTaskNoticeService;

    @DubboReference
    private RemoteMiddleUserFollowService remoteMiddleUserFollowService;
    @DubboReference
    private RemoteMiddleMcnUserService remoteMiddleMcnUserService;
    @DubboReference
    private RemoteImGroupRelateService remoteImGroupRelateService;
    @DubboReference
    private RemoteIndustryCategoryService sohuIndustryCategoryService;
    @DubboReference
    private RemoteMiddleCategoryService sohuCategoryService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemoteBusinessEventReportService remoteBusinessEventReportService;
    @DubboReference
    private RemoteMiddleEventReportService remoteMiddleEventReportService;
    @DubboReference
    private RemoteImGroupFormService remoteImGroupFormService;
    @DubboReference
    private RemotePaySettlementService remotePaySettlementService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;
    @DubboReference
    private RemoteMiddleAuditService remoteMiddleAuditService;
    @DubboReference
    private RemoteImGroupOrderUserService remoteImGroupOrderUserService;
    @DubboReference
    private RemoteImGroupFromGeneralService remoteImGroupFromGeneralService;

    /**
     * 查询商单与站点关联
     */
    @Override
    public SohuBusyTaskSiteVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public SohuBusyTaskSite getById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    public SohuBusyTaskSite getByNo(String taskNumber) {
        return this.baseMapper.selectOne(SohuBusyTaskSite::getTaskNumber, taskNumber);
    }

    /**
     * 查询商单与站点关联列表
     */
    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageList(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        // 国家站长
        boolean isCountry = loginUser.getRoles().stream().anyMatch(role -> RoleCodeEnum.CountryStationAgent.getCode()
                .equals(role.getRoleKey()));
        // 城市站长
        boolean isCity = loginUser.getRoles().stream().anyMatch(role -> RoleCodeEnum.CityStationAgent.getCode()
                .equals(role.getRoleKey()));
        /*
         * 超级管理员--查询全部任务
         * 国家站长--查询国家下所有任务
         * 个人--查询自己的所有任务
         */
        if (isCity) {
            if (StringUtils.isNotBlank(bo.getState()) && bo.getState().equals(SohuBusyTaskState.Pass.name())) {
                bo.setState(SohuBusyTaskState.WaitReceive.name());
            }
            Objects.requireNonNull(bo.getSiteId(), "城市站点不能为空");
        } else if (isCountry) {
            Objects.requireNonNull(bo.getCountrySiteId(), "国家站点不能为空");
        } else if (Objects.equals(loginUser.getUserId(), bo.getUserId())) {
            bo.setUserId(loginUser.getUserId());
        }
        // 时间转换
        changeDateTimeUtil(bo);
        // 处理时间
        BusyTaskUtil.buildDeliveryTime(bo);
        Page<SohuBusyTaskSiteVo> result = baseMapper.selectTaskSitePage(PageQueryUtils.build(pageQuery), bo);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> userIdSet = result.getRecords().stream().map(SohuBusyTaskSiteVo::getUserId).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = this.remoteUserService.selectMap(userIdSet);
            for (SohuBusyTaskSiteVo vo : result.getRecords()) {
                LoginUser userVo = userMap.get(vo.getUserId());
                if (Objects.nonNull(userVo)) {
                    vo.setNickName(userVo.getNickname());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageOfOnShelf(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        Page<SohuBusyTaskSiteVo> result = baseMapper.queryPageTaskList(PageQueryUtils.build(pageQuery), bo);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            Set<Long> userIdSet = result.getRecords().stream().map(SohuBusyTaskSiteVo::getUserId).collect(Collectors.toSet());
            Map<Long, LoginUser> userMap = this.remoteUserService.selectMap(userIdSet);
            for (SohuBusyTaskSiteVo vo : result.getRecords()) {
                LoginUser userVo = userMap.get(vo.getUserId());
                if (Objects.nonNull(userVo)) {
                    vo.setNickName(userVo.getNickname());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageMyList(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        bo.setUserId(loginUser.getUserId());
        // 时间转换
        changeDateTimeUtil(bo);
        Page<SohuBusyTaskSiteVo> result = baseMapper.selectMyTaskSitePage(PageQueryUtils.build(pageQuery), bo);
        List<SohuBusyTaskSiteVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (SohuBusyTaskSiteVo record : records) {
                LambdaQueryWrapper<SohuBusyTaskAfterSales> query = Wrappers.lambdaQuery();
                query.eq(SohuBusyTaskAfterSales::getTaskNumber, record.getTaskNumber());
                query.eq(SohuBusyTaskAfterSales::getAfterSalesType, SohuBusyTaskaAfterSalesType.Abort);
                List<SohuBusyTaskAfterSales> sohuBusyTaskAfterSalesList = sohuBusyTaskAfterSalesMapper.selectList(query);
                record.setAbortCount(sohuBusyTaskAfterSalesList.size());
                LambdaQueryWrapper<SohuBusyTaskAfterSales> queryReplace = Wrappers.lambdaQuery();
                queryReplace.eq(SohuBusyTaskAfterSales::getTaskNumber, record.getTaskNumber());
                queryReplace.eq(SohuBusyTaskAfterSales::getAfterSalesType, SohuBusyTaskaAfterSalesType.Replace);
                List<SohuBusyTaskAfterSales> sohuBusyTaskAfterSales = sohuBusyTaskAfterSalesMapper.selectList(queryReplace);
                record.setReplaceCount(sohuBusyTaskAfterSales.size());
            }
        }

        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskList(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        if (Objects.isNull(bo.getSiteId())) {
            SohuSiteVo siteVo = remoteMiddleSiteService.getSiteByIp(ServletUtils.getClientIP());
            bo.setSiteId(siteVo.getId());
        }
        if (Objects.nonNull(bo.getRecommend()) && bo.getRecommend() && Objects.nonNull(bo.getSiteId())) {
            // 推荐页就不分站点 只分国家
            SohuSiteVo sohuSiteVo = remoteMiddleSiteService.selectSiteByPid(bo.getSiteId());
            if (Objects.nonNull(sohuSiteVo)) {
                bo.setCountrySiteId(sohuSiteVo.getId());
                bo.setSiteId(null);
            }
        }
        if (Objects.nonNull(bo.getRecommend()) && bo.getReceiveLimit()) {
            bo.setReceiveOnlyUserId(LoginHelper.getUserId());
        }
        this.setMcnQueryParam(bo);
        //当前用户好友以及关注的人发布的商单
        if (Objects.nonNull(bo.getRecommend()) && bo.getIsFocus()) {
            // 获取登陆用户id
            Long userId = LoginHelper.getUserId();
            if (userId == null || userId <= 0L) {
                throw new ServiceException(MessageUtils.message("user.not.login"));
            }
            SohuUserFollowBo followBo = new SohuUserFollowBo();
            followBo.setUserId(userId);
            List<Long> userIdList = Stream.concat(
                            remoteMiddleUserFollowService.queryList(followBo).stream().map(SohuUserFollowVo::getFocusUserId),
                            remoteMiddleUserFollowService.friendIdList(userId).stream().map(SohuUserFollowVo::getUserId))
                    .distinct()
                    .collect(Collectors.toList());

            bo.setFocusUserIdList(userIdList);
        }
        // 判断是否已登录,已登录需要将标签查出来放进去
        if (Objects.nonNull(LoginHelper.getUserId())) {
            List<SohuUserLabelRelationVo> relationList = remoteMiddleCommonLabelService.queryLabelsByUserId(LoginHelper.getUserId());
            if (CollectionUtils.isNotEmpty(relationList)) {
                bo.setLabelList(relationList.stream()
                        // 过滤出labelType为"common"的项
                        .filter(relation -> LabelEnum.COMMON.getCode().equals(relation.getLabelType()))
                        .map(SohuUserLabelRelationVo::getLabelId).collect(Collectors.toList()));
                bo.setIndustryTypeList(relationList.stream()
                        // 过滤出labelType为"common"的项
                        .filter(relation -> LabelEnum.INDUSTRY.getCode().equals(relation.getLabelType()))
                        .map(SohuUserLabelRelationVo::getLabelId).collect(Collectors.toList()));
            }
        }
        // 时间转换
        changeDateTimeUtil(bo);
        // 处理时间
        BusyTaskUtil.buildDeliveryTime(bo);
        Page<SohuBusyTaskSiteVo> result = baseMapper.queryPageTaskList(PageQueryUtils.build(pageQuery), bo);
        if (result == null || CollUtil.isEmpty(result.getRecords())) {
            return TableDataInfoUtils.build();
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> categoryIds = Sets.newHashSet();
        for (SohuBusyTaskSiteVo record : result.getRecords()) {
            userIds.add(record.getUserId());
            categoryIds.add(record.getType());
            SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery()
                    .eq(SohuBusyTask::getTaskNumber, record.getMasterTaskNumber()));
            record.setImageUrl(sohuBusyTaskVo.getImageUrl());
            // 基于master_task_number 统计状态为非待接单的数量
            Long count = baseMapper.selectCount(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                    .ge(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive));
            record.setReceivedNum(count.intValue());
        }
        // 查询处理
        Map<Long, SohuCategoryVo> categoryMap = sohuCategoryService.queryMap(categoryIds);
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
        List<SohuBusyTaskSiteVo> records = result.getRecords();
        for (SohuBusyTaskSiteVo record : result.getRecords()) {
            String typeName = Optional.ofNullable(categoryMap.get(record.getType()))
                    .map(SohuCategoryVo::getName)
                    .orElse(StringUtils.EMPTY);
            record.setTypeName(typeName);
            LoginUser user = userMap.get(record.getUserId());
            if (Objects.isNull(user)) {
                continue;
            }
            record.setUserName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
            record.setUserAvatar(StrUtil.isNotBlank(user.getAvatar()) ? user.getAvatar() : Constants.DEFAULT_USER_AVATAR);
        }

        // IM任务群相关逻辑
        if (bo.getGroupId() != null && bo.getGroupId() > 0L) {
            SohuImGroupRelateBo imGroupRelateBo = new SohuImGroupRelateBo();
            imGroupRelateBo.setGroupId(bo.getGroupId());
            imGroupRelateBo.setBusyType(BusyType.BusyTask.name());
            List<SohuImGroupRelateVo> groupRelates = remoteImGroupRelateService.queryList(imGroupRelateBo);
            if (CollUtil.isNotEmpty(groupRelates)) {
                Map<String, SohuImGroupRelateVo> relateMap = groupRelates.stream().collect(Collectors.toMap(SohuImGroupRelateVo::getBusyCode, g -> g));
                for (SohuBusyTaskSiteVo record : records) {
                    record.setGroupRelate(Objects.nonNull(relateMap.get(record.getTaskNumber())));
                }
            }
        }
        result.setRecords(records);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 设置MCN请求参数
     *
     * @param bo
     */
    private void setMcnQueryParam(SohuBusyTaskSiteBo bo) {
        Long userId = LoginHelper.getUserId();
        if (userId == null || userId <= 0L) {
            return;
        }
        List<String> states = new ArrayList<>();
        states.add(McnUserStateEnum.NORMAL.getCode());
        SohuMcnUserVo mcnUserVo = remoteMiddleMcnUserService.findMcnIdByLoginId(states);
        if (Objects.nonNull(mcnUserVo)) {
            bo.setMcnId(mcnUserVo.getMcnUserId());
//            bo.setUserId(userId);
        }
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageMcnTaskList(SohuMcnBusyTaskSiteBo bo, PageQuery pageQuery) {
        bo.setMcnId(LoginHelper.getUserId());
        Page<SohuBusyTaskSiteVo> result = baseMapper.queryPageMcnTaskList(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskStateLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        // 时间转换
        changeDateTimeUtil(bo);
        Page<SohuBusyTaskSiteVo> result = baseMapper.queryPageTaskStateLists(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 将前端传递时间转换成精确到时分秒
     *
     * @param bo SohuBusyTaskSiteBo
     */
    private static void changeDateTimeUtil(SohuBusyTaskSiteBo bo) {
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            DateTime dateTime = DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime()));
            bo.setCreateTime(dateTime);
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            DateTime dateTime = DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime()));
            bo.setUpdateTime(dateTime);
        }
    }

    @Override
    public List<SohuBusyTaskSiteVo> selectBusyTaskReceiveList(List<String> orderIds) {
        LambdaQueryWrapper<SohuBusyTaskSite> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SohuBusyTaskSite::getTaskNumber, orderIds);

        return baseMapper.selectVoList(wrapper);
    }


    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskListOfAirec(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        if (bo.getAiRec()) {
            if (StrUtil.isBlank(bo.getAiRecImei()) && Objects.isNull(LoginHelper.getUserId())) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (!(AliyunAirecConstant.SCENE_TASK_HOMEPAGE.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_TASK_MONEYMAKING.equals(bo.getAiRecSceneId())
                    || AliyunAirecConstant.SCENE_TASK_DISTRIBUTION.equals(bo.getAiRecSceneId()))) {
                throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
            }
            if (Objects.nonNull(LoginHelper.getUserId())) {
                bo.setAiUserId(LoginHelper.getUserId().toString());
            }
            bo.setAiReturnCount(pageQuery.getPageSize());
            //构造过滤的内容
            AliyunAirecJoinFilterRule rootRule = new AliyunAirecJoinFilterRule();
            this.buildAirecJoinFilterRule(bo, rootRule);
            //获取阿里云智能推荐结果
            List<SohuBusyTaskSiteVo> resultList = AliyunAirecUtil.aiRecommendSingleType(rootRule, bo, itemIds -> baseMapper.selectVoBatchIds(itemIds));
            if (CollUtil.isNotEmpty(resultList)) {
                return TableDataInfoUtils.build(resultList);
            }
        }
        TableDataInfo<SohuBusyTaskSiteVo> tableDataInfo = this.queryPageTaskList(bo, pageQuery);
        AliyunAirecUtil.buildAiRecommendSingleType(tableDataInfo.getData(), AliyunAirecContentItemTypeEnum.ITEM.getCode());
        return tableDataInfo;
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageMcnTaskWindowList(SohuMcnBusyTaskSiteBo bo, PageQuery pageQuery) {
        bo.setMcnId(LoginHelper.getUserId());
        Page<SohuBusyTaskSiteVo> result = baseMapper.queryPageMcnTaskWindowList(PageQueryUtils.build(pageQuery), bo);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 构建智能推荐过滤参数
     *
     * @param bo
     * @param rootRule
     */
    private void buildAirecJoinFilterRule(SohuBusyTaskSiteBo bo, AliyunAirecJoinFilterRule rootRule) {
        rootRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
        {
            //内容的类型
            AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
            rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
            rule.setField(AliyunAirecQueryFieldContentEnum.ITEM_TYPE.getCode());
            rule.setValue(AliyunAirecContentItemTypeEnum.ITEM.getCode());
            rootRule.getFilters().add(rule);
        }
        {
            //站点
            if (Objects.nonNull(bo.getSiteId())) {
                AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
                rule.setField(AliyunAirecQueryFieldContentEnum.CITY.getCode());
                rule.setValue(bo.getSiteId().toString());
                rootRule.getFilters().add(rule);
            }
        }
        {
            //站点
            if (Objects.nonNull(bo.getSiteId())) {
                AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                rule.setCond(AliyunAirecQueryCondEnum.EQUAL.getCode());
                rule.setField(AliyunAirecQueryFieldContentEnum.COUNTRY.getCode());
                rootRule.getFilters().add(rule);
            }
        }
        {
            //商单类型
            if (Objects.nonNull(bo.getType())) {
                AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                rule.setCond(AliyunAirecQueryCondEnum.CATEGORY_MATCH.getCode());
                rule.setField(AliyunAirecQueryFieldContentEnum.CATEGORY_PATH.getCode());
                rule.setValue(bo.getType().toString());
                rootRule.getFilters().add(rule);
            }
            if (CollectionUtil.isNotEmpty(bo.getTypeIds())) {
                for (Long type : bo.getTypeIds()) {
                    AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                    rule.setCond(AliyunAirecQueryCondEnum.CATEGORY_MATCH.getCode());
                    rule.setField(AliyunAirecQueryFieldContentEnum.CATEGORY_PATH.getCode());
                    rule.setValue(type.toString());
                    rootRule.getFilters().add(rule);
                }
            }
        }
        {
            //存在tags筛选
            if (Objects.nonNull(bo.getHasKickType()) && bo.getHasKickType()) {
                AliyunAirecJoinFilterRule tagsRule = new AliyunAirecJoinFilterRule();
                tagsRule.setJoin(AliyunAirecQueryJoinEnum.AND.getCode());
                //有佣金
                AliyunAirecSingleFilterRule rule = new AliyunAirecSingleFilterRule();
                rule.setCond(AliyunAirecQueryCondEnum.CONTAIN.getCode());
                rule.setField(AliyunAirecQueryFieldContentEnum.TAGS.getCode());
                rule.setValue(AliyunAirecTagsConstant.HAS_COMMISSION);
                tagsRule.getFilters().add(rule);
                //}
                rootRule.getFilters().add(tagsRule);
            }
        }
    }

    /**
     * 查询商单与站点关联列表
     */
    @Override
    public List<SohuBusyTaskSiteVo> queryList(SohuBusyTaskSiteBo bo) {
        LambdaQueryWrapper<SohuBusyTaskSite> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuBusyTaskSite> buildQueryWrapper(SohuBusyTaskSiteBo bo) {
        LambdaQueryWrapper<SohuBusyTaskSite> lqw = Wrappers.lambdaQuery();
        // todo
        lqw.eq(bo.getSiteId() != null, SohuBusyTaskSite::getSiteId, bo.getSiteId());
        lqw.eq(StrUtil.isNotBlank(bo.getState()), SohuBusyTaskSite::getState, bo.getState());
        lqw.eq(bo.getAuditUser() != null, SohuBusyTaskSite::getAuditUser, bo.getAuditUser());
        lqw.eq(bo.getAuditTime() != null, SohuBusyTaskSite::getAuditTime, bo.getAuditTime());
        lqw.eq(bo.getShelfUser() != null, SohuBusyTaskSite::getShelfUser, bo.getShelfUser());
        lqw.eq(bo.getShelfTime() != null, SohuBusyTaskSite::getShelfTime, bo.getShelfTime());
        lqw.eq((bo.getType() != null && bo.getType() > 0L), SohuBusyTaskSite::getType, bo.getType());
        lqw.eq(bo.getIndustryType() != null && bo.getIndustryType() > 0L, SohuBusyTaskSite::getIndustryType, bo.getIndustryType());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuBusyTaskSite::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuBusyTaskSite::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getShelfState()), SohuBusyTaskSite::getShelfState, bo.getShelfState());
//        lqw.eq(StringUtils.isNotBlank(bo.getContent()), SohuBusyTaskSite::getContent, bo.getContent());
//        lqw.eq(StringUtils.isNotBlank(bo.getAnnex()), SohuBusyTaskSite::getAnnex, bo.getAnnex());
        lqw.eq(bo.getReceiveLimit() != null, SohuBusyTaskSite::getReceiveLimit, bo.getReceiveLimit());
        lqw.eq(bo.getDeliveryDay() != null, SohuBusyTaskSite::getDeliveryDay, bo.getDeliveryDay());
        lqw.eq(StringUtils.isNotBlank(bo.getDeliveryMsg()), SohuBusyTaskSite::getDeliveryMsg, bo.getDeliveryMsg());
        lqw.eq(bo.getSettleType() != null, SohuBusyTaskSite::getSettleType, bo.getSettleType());
        lqw.eq(bo.getDeliveryStep() != null, SohuBusyTaskSite::getDeliveryStep, bo.getDeliveryStep());
        lqw.eq(bo.getFullAmount() != null, SohuBusyTaskSite::getFullAmount, bo.getFullAmount());
        lqw.eq(bo.getFullCurrency() != null, SohuBusyTaskSite::getFullCurrency, bo.getFullCurrency());
        lqw.eq(StringUtils.isNotBlank(bo.getKickbackType()), SohuBusyTaskSite::getKickbackType, bo.getKickbackType());
        lqw.eq(bo.getKickbackValue() != null, SohuBusyTaskSite::getKickbackValue, bo.getKickbackValue());
        return lqw;
    }

    /**
     * 新增商单与站点关联
     */
    @Override
    public Boolean insertByBo(SohuBusyTaskSiteBo bo) {
        SohuBusyTaskSite add = BeanUtil.toBean(bo, SohuBusyTaskSite.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商单与站点关联
     */
    @Override
    public Boolean updateByBo(SohuBusyTaskSiteBo bo) {
        SohuBusyTaskSite update = BeanUtil.toBean(bo, SohuBusyTaskSite.class);
        if (StrUtil.equalsAnyIgnoreCase(update.getKickbackType(), KickbackType.none.getCode())) {
            update.setKickbackValue(BigDecimal.ZERO);
        }
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuBusyTaskSite entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商单与站点关联
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            //TODO 做一些业务上的校验,判断是否需要校验
//        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Map<Long, SohuBusyTaskSite> queryMapByTaskId(Long busyTaskId) {
        // todo
        List<SohuBusyTaskSite> list = this.baseMapper.selectList(SohuBusyTaskSite::getTaskNumber, busyTaskId);
        return CollUtil.isEmpty(list) ? new HashMap<>() : list.stream().collect(Collectors.toMap(SohuBusyTaskSite::getSiteId, u -> u));
    }

    @Override
    public List<SohuBusyTaskSite> listByMasterTaskNo(String masterTaskNumber) {
        List<SohuBusyTaskSite> list = this.baseMapper.selectList(SohuBusyTaskSite::getMasterTaskNumber, masterTaskNumber);
        return CollUtil.isEmpty(list) ? new ArrayList<>() : list;
    }

    @Override
    public List<SohuBusyTaskSiteVo> listVoByMasterTaskNo(String masterTaskNumber) {
        List<SohuBusyTaskSiteVo> list = this.baseMapper.selectTaskList(masterTaskNumber);
        return CollUtil.isEmpty(list) ? new ArrayList<>() : list;
    }

    /**
     * 查询不等于某状态的商单与站点关联列表
     *
     * @param busyTaskId 商单id
     * @param state      状态 {@link SohuBusyTaskState}
     * @return {@link List}
     */
    public List<SohuBusyTaskSiteVo> queryNeList(Long busyTaskId, String state) {
        LambdaQueryWrapper<SohuBusyTaskSite> lqw = new LambdaQueryWrapper<>();
        // todo
        lqw.eq(SohuBusyTaskSite::getTaskNumber, busyTaskId).ne(SohuBusyTaskSite::getState, state);
        List<SohuBusyTaskSiteVo> siteVos = this.baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(siteVos)) {
            Set<Long> userIds = new HashSet<>();
            Set<Long> siteIds = new HashSet<>();
            for (SohuBusyTaskSiteVo siteVo : siteVos) {
                if (siteVo.getAuditUser() != null && siteVo.getAuditUser() > 0L) {
                    userIds.add(siteVo.getAuditUser());
                }
                siteIds.add(siteVo.getSiteId());
            }
            Map<Long, LoginUser> userMap = new HashMap<>();
            if (CollUtil.isNotEmpty(userIds)) {
                userMap = remoteUserService.selectMap(userIds);
            }
            Map<Long, SohuSiteVo> siteModelMap = remoteMiddleSiteService.queryMap(siteIds);
            for (SohuBusyTaskSiteVo record : siteVos) {
                LoginUser loginUser = userMap.get(record.getAuditUser());
                if (Objects.nonNull(loginUser)) {
                    record.setAuditName(loginUser.getNickname());
                    record.setUserName(loginUser.getUsername());
                }
                SohuSiteVo sohuSiteModel = siteModelMap.get(record.getSiteId());
                if (Objects.isNull(sohuSiteModel)) {
                    continue;
                }
                record.setSiteName(sohuSiteModel.getName());
            }

        }
        return siteVos;
    }

    @Override
    public SohuBusyTaskSiteVo queryByTaskNumber(String taskNumber) {
        return queryByTaskNumber(taskNumber, null);
    }

    @Override
    public SohuBusyTaskSiteVo queryByTaskNumber(String taskNumber, Long shareUserId) {
        return this.baseMapper.queryByTaskNumber(taskNumber, shareUserId);
    }

    @Override
    public List<SohuBusyTaskSiteVo> queryByMasterTaskNumber(String masterTaskNumber) {
        LambdaQueryWrapper<SohuBusyTaskSite> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SohuBusyTaskSite::getMasterTaskNumber, masterTaskNumber);
        return this.baseMapper.selectVoList(wrapper);
    }

    @Override
    public Boolean updateById(SohuBusyTaskSite busyTask) {
        return this.baseMapper.updateById(busyTask) > 0;
    }

    /**
     * 修改商单与站点关联数据
     *
     * @param busyTaskId 商单id
     * @param taskSiteBo 状态 {@link com.sohu.common.core.enums.CommonState}
     */
    @Override
    public void updateState(Long busyTaskId, SohuBusyTaskSiteBo taskSiteBo) {
        this.baseMapper.update(new SohuBusyTaskSite(), new LambdaUpdateWrapper<SohuBusyTaskSite>()
                .set(SohuBusyTaskSite::getState, taskSiteBo.getState())
                .set(taskSiteBo.getSiteId() != null, SohuBusyTaskSite::getSiteId, taskSiteBo.getSiteId())
                .set(StrUtil.isNotBlank(taskSiteBo.getRefuseMsg()), SohuBusyTaskSite::getRefuseMsg, taskSiteBo.getRefuseMsg())
                .set((taskSiteBo.getShelfUser() != null && taskSiteBo.getShelfUser() > 0L), SohuBusyTaskSite::getShelfUser, taskSiteBo.getShelfUser())
                // todo
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTaskId)
                .eq(SohuBusyTaskSite::getId, taskSiteBo.getId())
                .ne(SohuBusyTaskSite::getState, SohuBusyTaskState.Delete.name()));
    }

    @Override
    public Boolean updateBatch(List<SohuBusyTaskSite> busyTaskSiteList) {
        if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
            this.baseMapper.updateBatchById(busyTaskSiteList);
            busyTaskSiteList.forEach(busyTaskSite -> {
                // 延迟队列--更新广告缓存信息
                MsgContent msgContent = new MsgContent(busyTaskSite.getId(), busyTaskSite.getShelfState(), BusyType.BusyOrder.name());
                MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
                RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
            });
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteModel> listCollect(String busyTitle, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (userId == null) {
            throw new ServiceException(MessageUtils.message("user.not.login"));
        }
        Page<SohuBusyTaskSiteVo> result = baseMapper.busyTaskCollectList(busyTitle, userId, PageQueryUtils.build(pageQuery));
        return TableDataInfoUtils.copyInfo(TableDataInfoUtils.build(result), SohuBusyTaskSiteModel.class);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryReceiveWindowList(SohuMcnBusyTaskSiteBo bo, PageQuery pageQuery) {
        bo.setMcnId(LoginHelper.getUserId());
        Page<SohuBusyTaskSiteVo> result = baseMapper.queryReceiveWindowList(PageQueryUtils.build(pageQuery), bo);
        List<SohuBusyTaskSiteVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (SohuBusyTaskSiteVo record : records) {
                String userName = remoteUserService.selectUserNameById(record.getUserId());
                record.setUserName(userName);
                String receiveUserName = remoteUserService.selectUserNameById(record.getReceiveUserId());
                record.setReceiveUserName(receiveUserName);
                if (Objects.equals(SettleTypeEnums.EXECUTE_BEFORE_PAY.getCode(), record.getSettleType())) {
                    record.setSettleTypeName(SettleTypeEnums.EXECUTE_BEFORE_PAY.getMsg());
                } else if (Objects.equals(SettleTypeEnums.PROGRESS_PAYMENT.getCode(), record.getSettleType())) {
                    record.setSettleTypeName(SettleTypeEnums.PROGRESS_PAYMENT.getMsg());
                }
            }
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuBusyTaskSiteVo> queryMasterTaskNumber(List<String> taskNumberList) {
        LambdaQueryWrapper<SohuBusyTaskSite> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SohuBusyTaskSite::getTaskNumber, taskNumberList);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryImGroupSiteTask(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuBusyTaskSite> wrapper = Wrappers.lambdaQuery();
        wrapper.in(SohuBusyTaskSite::getTaskNumber, bo.getTaskNumbers());
        wrapper.eq(SohuBusyTaskSite::getShelfState, bo.getShelfState());
        wrapper.last("ORDER BY FIELD(state, '" + SohuBusyTaskState.WaitSplit + "', '" + SohuBusyTaskState.Execute + "', '" + SohuBusyTaskState.Over + "'), create_time DESC");
        IPage<SohuBusyTaskSiteVo> page = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), wrapper);

        if (CollectionUtil.isEmpty(page.getRecords())) {
            return TableDataInfoUtils.build();
        }
        Set<Long> industryTypes = Sets.newHashSet();
        Set<Long> categoryIds = Sets.newHashSet();
        for (SohuBusyTaskSiteVo record : page.getRecords()) {
            industryTypes.add(record.getIndustryType());
            categoryIds.add(record.getType());
        }
        Map<Long, SohuIndustryCategoryVo> industryCategoryMap = sohuIndustryCategoryService.queryMap(industryTypes);
        Map<Long, SohuCategoryVo> categoryMap = sohuCategoryService.queryMap(categoryIds);

        for (SohuBusyTaskSiteVo record : page.getRecords()) {
            String industryName = Optional.ofNullable(industryCategoryMap.get(record.getIndustryType()))
                    .map(SohuIndustryCategoryVo::getIndustryName)
                    .orElse(StringUtils.EMPTY);
            String typeName = Optional.ofNullable(categoryMap.get(record.getType()))
                    .map(SohuCategoryVo::getName)
                    .orElse(StringUtils.EMPTY);
            record.setIndustryName(industryName);
            record.setTypeName(typeName);
        }
        return TableDataInfoUtils.build(page);
    }

    @Override
    public Long countByState(String state) {
        LambdaQueryWrapper<SohuBusyTaskSite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(state), SohuBusyTaskSite::getState, state);
        return this.baseMapper.selectCount(wrapper);
    }

    @Override
    public Long countByTime(String startTime, String endTime) {
        LambdaQueryWrapper<SohuBusyTaskSite> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(SohuBusyTaskSite::getCreateTime, startTime, endTime);
        return this.baseMapper.selectCount(wrapper);
    }

    @Override
    public void expireBusyTask(String taskNumber) {
        log.info("处理商单延时任务,taskNumber:{}", taskNumber);
        SohuBusyTaskSiteVo sohuBusyTaskSiteVo = this.queryByTaskNumber(taskNumber);
        if (Objects.isNull(sohuBusyTaskSiteVo)) {
            log.error("商单延时任务处理异常,商单不存在 taskNumber:{}", taskNumber);
        }
        if (sohuBusyTaskSiteVo.getState().equals(SohuBusyTaskState.OverSettle.name())) {
            log.info("商单已完成,不需要回到商单广场");
            return;
        }
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        site.setId(sohuBusyTaskSiteVo.getId());
        site.setState(SohuBusyTaskState.WaitReceive.name());
        baseMapper.updateById(site);
        // 查询主商单状态
        SohuBusyTask sohuBusyTask = sohuBusyTaskMapper.selectOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, sohuBusyTaskSiteVo.getMasterTaskNumber()));
        if (Objects.nonNull(sohuBusyTask)) {
            sohuBusyTask.setState(SohuBusyTaskState.Pass.name());
            sohuBusyTaskMapper.updateById(sohuBusyTask);
        }
        // 查询接单记录
        List<SohuBusyTaskReceive> receiveList = sohuBusyTaskReceiveMapper.selectList(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getTaskNumber, taskNumber)
                .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.Execute.name()));
        if (CollectionUtil.isNotEmpty(receiveList)) {
            for (SohuBusyTaskReceive receive : receiveList) {
                receive.setState(CommonState.Exceed.getCode());
            }
            sohuBusyTaskReceiveMapper.updateBatchById(receiveList);
        }
        // 查询report,并将report的状态更改为已过期
        List<SohuBusinessEventReportVo> reportVoList = remoteBusinessEventReportService
                .queryListByTaskNumber(taskNumber, SohuBusyTaskState.Execute.name());
        if (CollectionUtil.isNotEmpty(reportVoList)) {
            for (SohuBusinessEventReportVo reportVo : reportVoList) {
                SohuBusinessEventReportBo reportBo = new SohuBusinessEventReportBo();
                reportBo.setId(reportVo.getId());
                reportBo.setState(CommonState.Exceed.getCode());
                remoteBusinessEventReportService.updateByBo(reportBo);
            }
        }
        // 补充事件统计
        SohuEventReportBo reportBo = new SohuEventReportBo();
        reportBo.setEventType(BusyTaskReportEnum.SDGQ.getType());
        reportBo.setBusyCode(taskNumber);
        reportBo.setBusyName(sohuBusyTask.getTitle());
        reportBo.setUserId(receiveList.get(0).getUserId());
        remoteMiddleEventReportService.getEventId(reportBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean overBusyTask(String taskNumber) {
        SohuBusyTaskSiteVo sohuBusyTaskSiteVo = this.queryByTaskNumber(taskNumber);
        if (Objects.isNull(sohuBusyTaskSiteVo)) {
            log.error("完结商单不存在 taskNumber:{}", taskNumber);
            return Boolean.FALSE;
        }
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        site.setId(sohuBusyTaskSiteVo.getId());
        site.setState(SohuBusyTaskState.OverSettle.name());
        baseMapper.updateById(site);
        // 更改接单状态
        List<String> receiveStateList = new ArrayList<>();
        receiveStateList.add(SohuBusyTaskState.WaitSettle.name());
        receiveStateList.add(SohuBusyTaskState.WaitApproveSettle.name());
        SohuBusyTaskReceiveVo receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getTaskNumber, taskNumber)
                .in(SohuBusyTaskReceive::getState, receiveStateList).last("limit 1"));
        if (Objects.nonNull(receiveVo)) {
            SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
            receive.setId(receiveVo.getId());
            receive.setState(SohuBusyTaskState.OverSettle.name());
            sohuBusyTaskReceiveMapper.updateById(receive);
        } else {
            // 执行中由发单方结算
            receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getTaskNumber, taskNumber)
                    .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.Execute));
            if (Objects.nonNull(receiveVo)) {
                SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
                receive.setId(receiveVo.getId());
                receive.setState(SohuBusyTaskState.OverSettle.name());
                sohuBusyTaskReceiveMapper.updateById(receive);
            }
        }
        // 发送系统通知
        sohuBusyTaskNoticeService.sendTaskSiteNotice(sohuBusyTaskSiteVo.getTaskNumber(), TaskNoticeEnum.TASK_END,
                receiveVo.getUserId(), null, null, Boolean.TRUE, null, null);
        SohuBusyTask sohuBusyTask = sohuBusyTaskMapper.selectOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, sohuBusyTaskSiteVo.getMasterTaskNumber()));
        // 解散子群
        SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().childTaskNumber(taskNumber)
                .passPersonJoinGroup(true).build();
        remoteImGroupFormService.handleOverChildGroup(formHandleBo);
        // 补充事件统计
        SohuEventReportBo reportBo = new SohuEventReportBo();
        reportBo.setEventType(BusyTaskReportEnum.WCSD.getType());
        reportBo.setBusyCode(taskNumber);
        reportBo.setBusyName(sohuBusyTaskSiteVo.getTitle());
        reportBo.setUserId(receiveVo.getUserId());
        remoteMiddleEventReportService.getEventId(reportBo);
        // 查询主单下子单是否都是已完结或者已取消,是则更改主单为已完结
        List<String> stateList = new ArrayList<>();
        stateList.add(SohuBusyTaskState.OverSettle.name());
        stateList.add(SohuBusyTaskState.Cancel.name());
        stateList.add(SohuBusyTaskState.WaitReceive.name());
        Long num = baseMapper.selectCount(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, sohuBusyTaskSiteVo.getMasterTaskNumber())
                .notIn(SohuBusyTaskSite::getState, stateList));
        if (num == 0) {
            // 查询达标人数是否满足人数
            Integer passNum = remoteImGroupOrderUserService
                    .getPassPersonUserByMasterTaskNumber(sohuBusyTaskSiteVo.getMasterTaskNumber());
            // 查询待接单子单
            List<SohuBusyTaskSite> siteList = baseMapper.selectList(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                    .eq(SohuBusyTaskSite::getMasterTaskNumber, sohuBusyTaskSiteVo.getMasterTaskNumber())
                    .eq(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive.name()));
            if (passNum < sohuBusyTask.getDeliveryStandard()) {
                // 达标人数不够但是子单均已完结，需要更改主单状态并退钱
                if (CollectionUtils.isEmpty(siteList)) {
                    this.over(sohuBusyTaskSiteVo);
                }
                // 达标人数不够，但是子单存在未完结，则不处理
            } else {
                // 达标人数足够,且存在子单待接单,则更改待接单子单状态为已完结
                if (CollectionUtils.isNotEmpty(siteList)) {
                    for (SohuBusyTaskSite taskSite : siteList) {
                        SohuBusyTaskSite newTaskSite = new SohuBusyTaskSite();
                        newTaskSite.setId(taskSite.getId());
                        newTaskSite.setState(SohuBusyTaskState.OverSettle.name());
                        baseMapper.updateById(newTaskSite);
                    }
                }
                // 更改主单状态并退钱
                this.over(sohuBusyTaskSiteVo);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 完结主单信息
     *
     * @param sohuBusyTaskSiteVo
     */
    private void over(SohuBusyTaskSiteVo sohuBusyTaskSiteVo) {
        sohuBusyTaskMapper.updateTaskState(sohuBusyTaskSiteVo.getMasterTaskNumber(), SohuBusyTaskState.OverSettle.name());
        remotePaySettlementService.overSettle(sohuBusyTaskSiteVo.getMasterTaskNumber());
        SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().mainTaskNumber(sohuBusyTaskSiteVo.getMasterTaskNumber()).build();
        remoteImGroupFormService.handleOverMainGroup(formHandleBo);
    }

    /**
     * 待结算商单
     *
     * @param taskNumber
     * @return
     */
    public Boolean waitSettleBusyTask(String taskNumber) {
        SohuBusyTaskSiteVo sohuBusyTaskSiteVo = this.queryByTaskNumber(taskNumber);
        if (Objects.isNull(sohuBusyTaskSiteVo)) {
            log.error("完结商单不存在 taskNumber:{}", taskNumber);
            return Boolean.FALSE;
        }
        // 更改商单状态为待结算
        SohuBusyTaskSite site = new SohuBusyTaskSite();
        site.setId(sohuBusyTaskSiteVo.getId());
        site.setState(SohuBusyTaskState.WaitSettle.name());
        baseMapper.updateById(site);
        // 更改接单状态
        SohuBusyTaskReceiveVo receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                .eq(SohuBusyTaskReceive::getTaskNumber, taskNumber)
                .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitApproveSettle));
        if (Objects.nonNull(receiveVo)) {
            SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
            receive.setId(receiveVo.getId());
            receive.setState(SohuBusyTaskState.WaitSettle.name());
            sohuBusyTaskReceiveMapper.updateById(receive);
            // 审核记录处理
            SohuAuditVo sohuAuditVo = remoteMiddleAuditService.selectNearByObj(receiveVo.getId(), BusyType.SettleBusyTask.name(), receiveVo.getUserId());
            SohuAuditBo auditBo = new SohuAuditBo();
            auditBo.setId(sohuAuditVo.getId());
            auditBo.setBusyType(BusyType.SettleBusyTask.name());
            auditBo.setBusyCode(receiveVo.getId());
            auditBo.setState(CommonState.OnShelf.getCode());
            remoteMiddleAuditService.audit(auditBo);
        } else {
            // 执行中由发单方结算
            receiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.<SohuBusyTaskReceive>lambdaQuery()
                    .eq(SohuBusyTaskReceive::getTaskNumber, taskNumber)
                    .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.Execute));
            if (Objects.nonNull(receiveVo)) {
                SohuBusyTaskReceive receive = new SohuBusyTaskReceive();
                receive.setId(receiveVo.getId());
                receive.setState(SohuBusyTaskState.WaitSettle.name());
                sohuBusyTaskReceiveMapper.updateById(receive);
            }
        }
        // 解散群
        SohuImGroupFormHandleBo formHandleBo = SohuImGroupFormHandleBo.builder().childTaskNumber(taskNumber).build();
        return remoteImGroupFormService.handleOverChildGroup(formHandleBo);
    }

    @Override
    public List<SohuBusyTaskSiteVo> queryBusyOrderByIds(List<Long> ids, String state) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SohuBusyTaskSite>().in(SohuBusyTaskSite::getId, ids)
                .eq(SohuBusyTaskSite::getShelfState, state));
//                .eq(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive.name()));
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> listDistribution(String busyTitle, PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        if (Objects.isNull(userId)) {
            throw new ServiceException("user.not.login");
        }
        Page<SohuBusyTaskSiteVo> result = baseMapper.listDistribution(userId, busyTitle, PageQueryUtils.build(pageQuery));
        return TableDataInfoUtils.build(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTaskStateAfterPay(String taskNumber, String state, Long userId) {
        // 通用商单与子单一对一关系
        SohuBusyTaskSite sohuBusyTaskSite = baseMapper.selectOne(new LambdaQueryWrapper<SohuBusyTaskSite>().eq(SohuBusyTaskSite::getMasterTaskNumber, taskNumber));
        sohuBusyTaskSite.setState(state);
        baseMapper.updateById(sohuBusyTaskSite);
        // 同步修改主单状态
        sohuBusyTaskMapper.updateTaskState(taskNumber, state);
        if (userId == null) {
            return Boolean.TRUE;
        }
        // 同步接单状态
        SohuBusyTaskReceive receive = sohuBusyTaskReceiveMapper.selectOne(new LambdaQueryWrapper<SohuBusyTaskReceive>()
                .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSite.getTaskNumber())
                .eq(SohuBusyTaskReceive::getUserId, userId)
                .in(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitFullAmountPay.name(), SohuBusyTaskState.WaitApproveSettle.name(), SohuBusyTaskState.WaitPromisePay.name()));
        // 发单方超时未支付商单预算,接单方超时未支付保证金,需要逻辑删除接单记录
        if (Objects.nonNull(receive) && StrUtil.equalsAnyIgnoreCase(state, SohuBusyTaskState.WaitReceive.name())) {
            // 删除待审核记录
            SohuBusyTaskVo sohuBusyTaskVo = sohuBusyTaskMapper.selectVoOne(Wrappers.lambdaQuery(SohuBusyTask.class).eq(SohuBusyTask::getTaskNumber, taskNumber));
            if (Objects.nonNull(sohuBusyTaskVo)) {
                remoteMiddleAuditService.deleteByObjAndUserId(sohuBusyTaskVo.getId(), BusyType.ReceiveBusyTask.name(), receive.getUserId());
            }
            sohuBusyTaskReceiveMapper.deleteById((receive));
        } else {
            receive.setState(state);
            sohuBusyTaskReceiveMapper.updateById(receive);
        }
        // 异步建群,通用商单状态修改为执行中需要创建群聊
        CompletableFuture.runAsync(() -> {
            if (StrUtil.equalsAnyIgnoreCase(state, SohuBusyTaskState.Execute.name())) {
                // 创建群并返回群id
                log.error("调用创建群聊");
                SohuImGroupFormGeneralCreateBo createBo = new SohuImGroupFormGeneralCreateBo();
                createBo.setMasterTaskNumber(sohuBusyTaskSite.getMasterTaskNumber());
                createBo.setTaskNumber(sohuBusyTaskSite.getTaskNumber());
                createBo.setReceiveUserId(userId);
                Long groupId = remoteImGroupFromGeneralService.createGroup(createBo);
                if (Objects.isNull(groupId)) {
                    throw new ServiceException("接单失败,请稍后再试");
                }
                // 回填群id
                receive.setRelationId(groupId);
                sohuBusyTaskReceiveMapper.updateById(receive);
            }
        }, asyncConfig.getAsyncExecutor());
        return Boolean.TRUE;
    }

    @Override
    public Map<String, Long> getUserIdByMasterTaskNumber(List<String> masterTaskNumberList) {
        Map<String, Long> map = new HashMap<>();
        // 检查主任务编号列表是否为空，为空则直接返回空列表
        if (CollUtil.isEmpty(masterTaskNumberList)) {
            return map;
        }

        // 根据主任务编号查询相关的任务站点信息
        List<SohuBusyTaskSite> taskSites = baseMapper.selectList(
                Wrappers.lambdaQuery(SohuBusyTaskSite.class)
                        .in(SohuBusyTaskSite::getMasterTaskNumber, masterTaskNumberList)
        );

        // 如果没有找到相关任务站点，直接返回空列表
        if (CollUtil.isEmpty(taskSites)) {
            return map;
        }

        // 提取所有任务编号
        List<String> taskNumbers = taskSites.stream()
                .map(SohuBusyTaskSite::getTaskNumber)
                .collect(Collectors.toList());

        // 查询待全额支付状态的任务接收记录
        List<SohuBusyTaskReceive> taskReceives = sohuBusyTaskReceiveMapper.selectList(
                Wrappers.lambdaQuery(SohuBusyTaskReceive.class)
                        .in(SohuBusyTaskReceive::getTaskNumber, taskNumbers)
                        .eq(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitFullAmountPay.name())
        );

        // 如果没有待全额支付的任务，直接返回空列表
        if (CollUtil.isEmpty(taskReceives)) {
            return map;
        }

        // 创建主任务编号到用户ID的映射
        for (SohuBusyTaskReceive receive : taskReceives) {
            for (SohuBusyTaskSite site : taskSites) {
                if (site.getTaskNumber().equals(receive.getTaskNumber())) {
                    map.put(site.getMasterTaskNumber(), receive.getUserId());
                    break;
                }
            }
        }

        return map;
    }
}
