package com.sohu.busyOrder.service;

import cn.hutool.core.date.DateTime;
import com.sohu.busyOrder.domain.SohuBusyTaskReceive;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveBo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveInfoVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskWaitSettleVo;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 商单接单Service接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface ISohuBusyTaskReceiveService {

    /**
     * 查询商单接单
     */
    SohuBusyTaskReceiveVo queryById(Long id);

    /**
     * 查询任务方申请接单列表
     */
    TableDataInfo<SohuBusyTaskReceiveVo> queryPageList(SohuBusyTaskReceiveBo bo, PageQuery pageQuery);

    /**
     * 查询商单接单列表
     */
    List<SohuBusyTaskReceiveVo> queryList(SohuBusyTaskReceiveBo bo);

    /**
     * 修改商单接单
     */
    Boolean updateByBo(SohuBusyTaskReceiveBo bo);

    /**
     * 校验并批量删除商单接单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询同意的商单接单
     *
     * @param busyTaskId 商单ID
     * @return
     */
    SohuBusyTaskReceive queryPass(Long busyTaskId);

    /**
     * 接单审核
     *
     * @param bo
     * @return
     */
    Boolean audit(SohuBusyTaskReceiveBo bo);

    /**
     * 接单并且支付保证金
     *
     * @param bo
     */
    String pay(SohuBusyTaskReceiveBo bo);

    /**
     * 查询任务方申请接单列表-接单方
     *
     * @param bo
     * @param pageQuery
     */
    TableDataInfo<SohuBusyTaskReceiveVo> selectTaskReceiveUserPage(SohuBusyTaskReceiveBo bo, PageQuery pageQuery);

    /**
     * 查询已接单的
     *
     * @param taskNumber
     */
    List<SohuBusyTaskReceiveVo> queryByReceive(String taskNumber);

    /**
     * 获取接单人数统计
     */
    Long getReceiveUserStat(Long userId, DateTime startDateTime, DateTime endDateTime);

    /**
     * 获取用户执行中的任务
     *
     * @param userId 用户id
     * @return SohuBusyTaskReceiveVo
     */
    List<SohuBusyTaskReceiveVo> getExecuteBusyTaskByUserId(Long userId);

    /**
     * 通过taskNumber获取接单详情
     *
     * @param id 主键id
     * @return SohuBusyTaskReceiveVo
     */
    SohuBusyTaskReceiveVo getInfoById(String id);

    /**
     * 获取接单详情
     *
     * @param taskNumber 子订单编号
     * @return SohuBusyTaskReceiveVo
     */
    SohuBusyTaskReceiveVo getReceiveDetailByTaskNumber(String taskNumber);

    /**
     * 基于状态统计当前数量
     *
     * @param state
     * @return
     */
    Long countByState(String state);

    /**
     * 更改过期时间
     *
     * @param id
     * @param expireTime
     */
    void updateExpireTime(Long id, Date expireTime);

    /**
     * 新版商单接单
     *
     * @param bo
     * @return
     */
    Boolean receiveTask(SohuBusyTaskReceiveBo bo);

    /**
     * 基于主单的taskNumber查询相关的子单信息
     *
     * @param bo
     * @return
     */
    TableDataInfo<SohuBusyTaskReceiveInfoVo> listInfoByMasterTaskNumber(SohuBusyTaskReceiveBo bo, PageQuery pageQuery);

    /**
     * 校验当前用户是否已经接单
     *
     * @param taskNumber
     * @return
     */
    String checkReceiveTask(String taskNumber);

    /**
     * 批量查询接单商单列表
     */
    List<SohuBusyTaskReceiveVo> queryReceiveListByTaskNumber(List<String> taskNumbers);

    /**
     * 基于状态查询相关接单记录
     *
     * @param state
     * @return
     */
    List<SohuBusyTaskReceiveVo> queryListByState(String state);

    /**
     * 根据id查询接单记录
     * @param id
     * @return
     */
    SohuBusyTaskReceiveVo selectById(Long id);

    /**
     * 基于taskNumber 加 接单人查询最近的一条接单信息
     *
     * @param taskNumber
     * @param receiveUserId
     * @return
     */
    SohuBusyTaskReceiveVo queryByTaskNumberAndReceiveUserId(String taskNumber, Long receiveUserId);

    /**
     * 基于主商单id获取待结算列表信息
     *
     * @param id
     * @return
     */
    SohuBusyTaskWaitSettleVo getWaitSettleList(Long id);

    /**
     * 基于主单编码及状态查询相关接单信息
     *
     * @param masterTaskNumber
     * @param stateList
     * @return
     */
    List<SohuBusyTaskReceiveVo> queryListByMasterTaskNumberAndStateList(String masterTaskNumber, List<String> stateList);

    /**
     * 获取在途任务
     * @param userId
     * @param status
     * @param freezeTime
     * @return
     */
    Long inTransitTask(Long userId,List<String> status,Date freezeTime);

    /**
     * 基于主单编码及接单人查询相关接单信息
     *
     * @param taskNumber
     * @param receiveUserId
     * @return
     */
    SohuBusyTaskReceiveVo queryByMasterTaskNumberrAndReceiveUserId(String taskNumber, Long receiveUserId);
}
