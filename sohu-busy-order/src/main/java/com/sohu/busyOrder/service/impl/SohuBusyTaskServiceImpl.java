package com.sohu.busyOrder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.sohu.busyorder.api.domain.SohuBusyTaskSiteReqBo;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.middle.api.bo.SohuTopicContentQueryBo;
import com.sohu.busyOrder.base.SohuTaskProcessor;
import com.sohu.busyOrder.domain.SohuBusyTask;
import com.sohu.busyOrder.domain.SohuBusyTaskLabel;
import com.sohu.busyOrder.domain.SohuBusyTaskReceive;
import com.sohu.busyOrder.domain.SohuBusyTaskSite;
import com.sohu.busyOrder.mapper.SohuBusyTaskLabelMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskReceiveMapper;
import com.sohu.busyOrder.mapper.SohuBusyTaskSiteMapper;
import com.sohu.busyOrder.service.*;
import com.sohu.busyOrder.utils.AirecContentUtil;
import com.sohu.busyorder.api.bo.*;
import com.sohu.busyorder.api.vo.*;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.enums.*;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.BigDecimalUtils;
import com.sohu.common.core.utils.CalUtils;
import com.sohu.common.core.utils.MessageUtils;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.im.api.bo.SohuImGroupRelateBo;
import com.sohu.im.api.service.RemoteImGroupOrderUserService;
import com.sohu.im.api.service.RemoteImGroupRelateService;
import com.sohu.im.api.service.RemoteImService;
import com.sohu.im.api.vo.SohuImGroupRelateVo;
import com.sohu.middle.api.bo.SohuAuditBo;
import com.sohu.middle.api.bo.SohuUserLabelRelationBo;
import com.sohu.middle.api.bo.airec.SohuAirecContentItemBo;
import com.sohu.middle.api.enums.AiRecTag;
import com.sohu.middle.api.enums.LabelEnum;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecContentItemService;
import com.sohu.middle.api.service.airec.RemoteMiddleAirecTagRelationService;
import com.sohu.middle.api.vo.*;
import com.sohu.pay.api.RemoteAccountService;
import com.sohu.pay.api.enums.PaySceceTypeEnum;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.system.api.RemoteUserService;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuBusyTaskServiceImpl implements ISohuBusyTaskService {

    private final SohuBusyTaskMapper baseMapper;
    private final SohuBusyTaskSiteMapper sohuBusyTaskSiteMapper;
    private final SohuBusyTaskLabelMapper sohuBusyTaskLabelMapper;

    private final ISohuBusyTaskSiteService sohuBusyTaskSiteService;
    private final ISohuBusyTaskDeliveryService sohuBusyTaskDeliveryService;
    private final ISohuBusyTaskReceiveService sohuBusyTaskReceiveService;
    private final SohuBusyTaskReceiveMapper sohuBusyTaskReceiveMapper;
    private final ISohuBusyTaskPayService sohuBusyTaskPayService;

    @Resource
    private SohuTaskProcessor sohuTaskProcessor;
    @DubboReference
    private RemoteMiddleAirecTagRelationService remoteMiddleAirecTagRelationService;
    @DubboReference
    private RemoteMiddleAirecContentItemService remoteMiddleAirecContentItemService;
    @DubboReference
    private RemoteAccountService remoteAccountService;
    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @DubboReference
    private RemoteImGroupRelateService remoteImGroupRelateService;
    @DubboReference
    private RemoteMiddleCategoryService remoteMiddleCategoryService;
    @DubboReference
    private RemoteImService remoteImService;
    @DubboReference
    private RemoteMiddleCommonLabelService remoteMiddleCommonLabelService;
    @DubboReference
    private RemoteImGroupOrderUserService remoteImGroupOrderUserService;
    @DubboReference
    private RemoteMiddleAuditService sohuAuditService;
    @DubboReference
    protected RemoteIndustryCategoryService remoteIndustryCategoryService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteMiddleIndependentMaterialService remoteMiddleIndependentMaterialService;
    @DubboReference
    private RemoteMiddleCategoryService sohuCategoryService;
    @DubboReference
    private RemoteIndustryCategoryService sohuIndustryCategoryService;
    @DubboReference
    private RemotePlatformIndustryService platformIndustryService;
    @DubboReference
    private RemoteBusyBlackService busyBlackService;

    /**
     * 分页查询每页大小,大量查询
     */
    private final static int PAGE_SIZE = 500;

    @Override
    public TableDataInfo<SohuBusyTaskVo> queryPageLists(SohuBusyTaskBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        // 超级管理员
        boolean isAdmin = LoginHelper.hasRole(loginUser, RoleCodeEnum.ADMIN);
        // 国家站长
        boolean isCountry = LoginHelper.hasRole(loginUser, RoleCodeEnum.CountryStationAgent);
        // 城市站长
        boolean isCity = LoginHelper.hasRole(loginUser, RoleCodeEnum.CityStationAgent);

        /*
         * 超级管理员--查询全部任务
         * 国家站长--查询国家下所有任务
         * 个人--查询自己的所有任务
         */
        if (isAdmin && null == bo.getUserId()) {
            this.buildQueryWrapper(bo);
        } else if (isCountry && null == bo.getUserId()) {
            Objects.requireNonNull(bo.getCountrySiteId(), "站点不能为空");
        } else if (isCity) {

        } else if (null == bo.getUserId()) {
            bo.setUserId(loginUser.getUserId());
            //查询下架状态的任务
        }
        // 时间转换
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            DateTime dateTime = DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime()));
            bo.setCreateTime(dateTime);
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            DateTime dateTime = DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime()));
            bo.setUpdateTime(dateTime);
        }
        // 判断站点和行业，分别查询黑名单
        List<Long> handleIds = new ArrayList<>();
        if (Objects.nonNull(bo.getIndustryId())) {
            // 行业id不为空,则查询相关行业对应的分类
            List<SohuPlatformIndustryRelationVo> relationList = platformIndustryService.queryList(bo.getIndustryId(), BusyType.BusyTask.getType());
            List<Long> categoryIds = relationList.stream().map(SohuPlatformIndustryRelationVo::getBusyCategoryId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(categoryIds)) {
                return TableDataInfoUtils.build(new ArrayList<>());
            }
            bo.setIndustryTypeList(categoryIds);
            // 查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getIndustryId(), 2, BusyType.BusyTask.getType());
        } else if (Objects.nonNull(bo.getSiteId()) && LoginHelper.hasRole(LoginHelper.getLoginUser(), RoleCodeEnum.CityStationAgent)) {
            // 站点id不为空,则查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.BusyTask.getType());
        }
        if (bo.getIsBlack() && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(handleIds)) {
            handleIds.add(0L);
        }
        bo.setHandleIds(handleIds);
        // 时间倒序
        Page<SohuBusyTaskVo> selectVoPage = baseMapper.selectTaskPage(bo, PageQueryUtils.build(pageQuery));
        List<String> taskNumberList = selectVoPage.getRecords().stream().map(SohuBusyTaskVo::getTaskNumber).collect(Collectors.toList());
        if (bo.getGroupId() != null && bo.getGroupId() > 0L) {
            SohuImGroupRelateBo imGroupRelateBo = new SohuImGroupRelateBo();
            imGroupRelateBo.setBusyType(BusyType.BusyTask.name());
            imGroupRelateBo.setGroupId(bo.getGroupId());
            imGroupRelateBo.setParentCodeList(taskNumberList);
            List<SohuImGroupRelateVo> groupRelateVos = remoteImGroupRelateService.queryList(imGroupRelateBo);
            if (CollUtil.isNotEmpty(groupRelateVos)) {
                Map<String, SohuImGroupRelateVo> relateVoMap = groupRelateVos.stream().collect(Collectors.toMap(SohuImGroupRelateVo::getParentCode, u -> u));
                selectVoPage.getRecords().forEach(task -> {
                    task.setGroupRelate(Objects.nonNull(relateVoMap.get(task.getTaskNumber())));
                });
            }
        }
        for (SohuBusyTaskVo vo : selectVoPage.getRecords()) {
            Integer actualReceiveNum = sohuBusyTaskReceiveMapper.selectCountByTaskNumber(vo.getTaskNumber());
            vo.setActuallyReceiveNum(actualReceiveNum);
            // 判断接单是否需要审核,如果需要审核则查询申请接单记录
            if (vo.getIsApproveReceive()) {
                Long receiveNum = sohuAuditService.countByBusyCodeAndBusyType(vo.getId(), BusyType.ReceiveBusyTask.getType(), null);
                vo.setIsAuditRecord(receiveNum > 0);
                Long waitApproveNum = sohuAuditService.countByBusyCodeAndBusyType(vo.getId(), BusyType.ReceiveBusyTask.getType(), SohuBusyTaskState.WaitApprove.name());
                vo.setWaitApproveNum(waitApproveNum.intValue());
            }
            // 补充实际支付金额
            SohuBusyTaskPayVo payVo = sohuBusyTaskPayService.queryByPayScene(vo.getTaskNumber(), PayStatus.Paid.name(), PaySceceTypeEnum.BUSY_ORDER_PAY.getCode());
            if (Objects.nonNull(payVo)) {
                vo.setPayAmount(payVo.getPayAmount());
            }
            // 设置移出时间
            if (bo.getIsBlack()) {
                SohuBusyBlackVo sohuBusyBlackVo = null;
                if (Objects.nonNull(bo.getIndustryId())) {
                    sohuBusyBlackVo = busyBlackService.queryByParam(vo.getId(), 2, BusyType.BusyTask.getType());
                } else {
                    sohuBusyBlackVo = busyBlackService.queryByParam(vo.getId(), 1, BusyType.BusyTask.getType());
                }
                vo.setRemoveTime(sohuBusyBlackVo.getCreateTime());
            }
        }
        return TableDataInfoUtils.build(selectVoPage);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageChildLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return sohuBusyTaskSiteService.queryPageList(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageOfOnShelf(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return sohuBusyTaskSiteService.queryPageOfOnShelf(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return sohuBusyTaskSiteService.queryPageTaskListOfAirec(bo, pageQuery);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageTaskStateLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        return sohuBusyTaskSiteService.queryPageTaskStateLists(bo, pageQuery);
    }

    @Override
    public Long queryBusyTaskNumBySite(Long siteId, Long userId) {
        LambdaQueryWrapper<SohuBusyTaskSite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTaskSite::getUserId, userId);
        wrapper.eq(SohuBusyTaskSite::getSiteId, siteId);
        return sohuBusyTaskSiteMapper.selectCount(wrapper);
    }

    @Override
    public Long countByState(String state) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getNeedSplit, Boolean.FALSE);
        wrapper.eq(StringUtils.isNotBlank(state), SohuBusyTask::getState, state);
        return this.baseMapper.selectCount(wrapper);
    }

    @Override
    public Long countByTime(String startTime, String endTime) {
        List<SohuBusyTaskSite> sohuBusyTaskSites = sohuBusyTaskSiteMapper.selectList(Wrappers.
                <SohuBusyTaskSite>lambdaQuery().between(SohuBusyTaskSite::getCreateTime, startTime, endTime));
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(sohuBusyTaskSites)) {
            wrapper.notIn(SohuBusyTask::getTaskNumber, sohuBusyTaskSites.stream().map(SohuBusyTaskSite::getMasterTaskNumber).collect(Collectors.toList()));
        }
        wrapper.eq(SohuBusyTask::getNeedSplit, Boolean.FALSE);
        wrapper.between(SohuBusyTask::getCreateTime, startTime, endTime);
        return this.baseMapper.selectCount(wrapper);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageMyChildLists(SohuBusyTaskSiteBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        return sohuBusyTaskSiteService.queryPageMyList(bo, pageQuery);
    }

    @Override
    public List<SohuBusyTaskSiteVo> getChildList(String masterTaskNumber) {
        return sohuBusyTaskSiteService.listVoByMasterTaskNo(masterTaskNumber);
    }

    @Override
    public SohuBusyTaskSiteVo getChildInfo(String taskNumber) {
        Long userId = LoginHelper.getUserId();
        if (userId != null && userId > 0L) {
            return getChildInfo(taskNumber, null, null, null, userId);
        }
        return getChildInfo(taskNumber, null, null, null, null);
    }

    @Override
    public SohuBusyTaskSiteVo getChildInfo(String taskNumber, Long shareUserId,
                                           Long receiveId, Long receiveUserId, Long userId) {
        Objects.requireNonNull(taskNumber, "子任务编号不能为空");
        SohuBusyTaskSiteVo vo = sohuBusyTaskSiteService.queryByTaskNumber(taskNumber);
        if (Objects.isNull(vo)) {
            throw new ServiceException("当前任务不存在,请联系管理员或其它相关人员");
        }
        return sohuTaskProcessor.getStrategy(vo.getConstMark())
                .getChildInfo(vo, receiveId, receiveUserId, shareUserId, userId);
    }

    @Override
    public Boolean auditChild(SohuBusyTaskSiteBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
        SohuBusyTaskSite busyTask = sohuBusyTaskSiteService.getById(bo.getId());
        Objects.requireNonNull(busyTask, MessageUtils.message("WRONG_PARAMS"));
        // 审核人、审核时间
        busyTask.setAuditUser(loginUser.getUserId());
        // 审核时间
        Date auditTime = new Date();
        busyTask.setAuditTime(auditTime);
        // 超管审核、国家站审核
        if (LoginHelper.hasRole(loginUser, RoleCodeEnum.ADMIN) || LoginHelper.hasRole(loginUser, RoleCodeEnum.CountryStationAgent) || LoginHelper.hasRole(loginUser, RoleCodeEnum.CityStationAgent)) {
            // 主单
            SohuBusyTask sohuBusyTask = this.getByTaskNumber(busyTask.getMasterTaskNumber());
            // 其他子单
            LambdaQueryWrapper<SohuBusyTaskSite> otherLqw = Wrappers.lambdaQuery();
            otherLqw.eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getMasterTaskNumber()).ne(SohuBusyTaskSite::getId, busyTask.getId());
            List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteMapper.selectList(otherLqw);
            // 待审核的情况--审核通过即到任务广场
            if (busyTask.getState().equals(SohuBusyTaskState.WaitApprove.name()) && bo.getState().equals(SohuBusyTaskState.Pass.name())) {
                if (bo.getState().equals(SohuBusyTaskState.Pass.name())) {
                    busyTask.setState(SohuBusyTaskState.WaitReceive.name());
                    busyTask.setShelfState(SohuBusyTaskState.OnShelf.name());
                    //保存智能推荐物料数据
                    this.saveAirecContentItem(busyTask);
                }
                // 是否只有一条
                boolean isCount = sohuBusyTaskSiteMapper.selectCount(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getMasterTaskNumber()) == 1;
                if (CollectionUtils.isNotEmpty(busyTaskSiteList) || isCount) {
                    return transactionTemplate.execute(e -> {
                        if (isCount || (CollectionUtils.isNotEmpty(busyTaskSiteList) && busyTaskSiteList.stream().allMatch(busyTaskSite -> SohuBusyTaskState.Pass.name().equals(busyTaskSite.getState())))) {
                            sohuBusyTask.setState(SohuBusyTaskState.Pass.name());
                            this.baseMapper.updateById(sohuBusyTask);
                        }
                        sohuBusyTaskSiteService.updateById(busyTask);
                        return Boolean.TRUE;
                    });
                }
                return transactionTemplate.execute(e -> {
                    sohuBusyTask.setState(SohuBusyTaskState.Pass.name());
                    this.baseMapper.updateById(sohuBusyTask);
                    sohuBusyTaskSiteService.updateById(busyTask);
                    return Boolean.TRUE;
                });
            }
            // 如果状态为待审核。传入状态为拒绝
            if (busyTask.getState().equals(SohuBusyTaskState.WaitApprove.name()) && bo.getState().equals(SohuBusyTaskState.Refuse.name())) {
                busyTask.setState(SohuBusyTaskState.Refuse.name());
                busyTask.setRefuseMsg(bo.getRefuseMsg());
                // 判断是否还有其他任务是拒绝，如果全部是拒绝那就改变任务状态为已拒绝-拒绝评论为-所有站点全部已驳回，具体看站点审核驳回信息
                if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
                    boolean isRefuse = busyTaskSiteList.stream().allMatch(busyTaskSite -> SohuBusyTaskState.Refuse.name().equals(busyTaskSite.getState()));
                    return transactionTemplate.execute(e -> {
                        if (isRefuse) {
                            sohuBusyTask.setState(SohuBusyTaskState.Refuse.name());
                            sohuBusyTask.setRefuseMsg("所有站点全部已驳回，具体原因看各站点审核驳回信息");
                            this.baseMapper.updateById(sohuBusyTask);
                        }
                        sohuBusyTaskSiteService.updateById(busyTask);
                        return Boolean.TRUE;
                    });
                } else if (sohuBusyTaskSiteMapper.selectCount(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getMasterTaskNumber()) == 1) {
                    return transactionTemplate.execute(e -> {
                        sohuBusyTask.setState(SohuBusyTaskState.Refuse.name());
                        sohuBusyTask.setRefuseMsg("所有站点全部已驳回，具体原因看各站点审核驳回信息");
                        this.baseMapper.updateById(sohuBusyTask);
                        sohuBusyTaskSiteService.updateById(busyTask);
                        return Boolean.TRUE;
                    });
                }
                return transactionTemplate.execute(e -> {
                    sohuBusyTask.setState(SohuBusyTaskState.Refuse.name());
                    sohuBusyTask.setRefuseMsg("站点驳回原因：" + bo.getRefuseMsg());
                    this.baseMapper.updateById(sohuBusyTask);
                    sohuBusyTaskSiteService.updateById(busyTask);
                    return Boolean.TRUE;
                });
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean updateByChildBo(SohuBusyTaskSiteBo bo) {
        if (!Objects.equals(LoginHelper.getUserId(), bo.getUserId())) {
            throw new ServiceException(MessageUtils.message("no.power"));
        }
        SohuBusyTaskSiteVo taskSiteVo = sohuBusyTaskSiteService.queryById(bo.getId());
        Objects.requireNonNull(taskSiteVo, "参数为空");
        // 待审核、审核通过、或下架或强制下架-才能修改--接单了不能修改
        if (taskSiteVo.getState().equals(SohuBusyTaskState.WaitApprove.name()) || taskSiteVo.getState().equals(SohuBusyTaskState.OffShelf.name()) || taskSiteVo.getState().equals(SohuBusyTaskState.CompelOff.name())) {
            bo.setState(SohuBusyTaskState.WaitApprove.name());
            // 查询是否有申请接单的 有的就全部变成Error状态
            List<SohuBusyTaskReceive> taskReceiveList = sohuBusyTaskReceiveMapper.selectList(SohuBusyTaskReceive::getTaskNumber, taskSiteVo.getTaskNumber());
            if (CollectionUtils.isNotEmpty(taskReceiveList)) {
                for (SohuBusyTaskReceive taskReceive : taskReceiveList) {
                    taskReceive.setState(SohuBusyTaskState.Error.name());
                    taskReceive.setRefuseMsg("发单方编辑过任务");
                }
            }
            return transactionTemplate.execute(e -> {
                if (CollectionUtils.isNotEmpty(taskReceiveList)) {
                    sohuBusyTaskReceiveMapper.updateBatchById(taskReceiveList);
                }
                sohuBusyTaskSiteService.updateByBo(bo);
                // 保存阶段性列表
                if (CollectionUtils.isNotEmpty(bo.getDeliveryList())) {
                    for (SohuBusyTaskDeliveryBo delivery : bo.getDeliveryList()) {
                        if (delivery.getId() == null) {
                            sohuBusyTaskDeliveryService.insertByBo(delivery);
                        } else {
                            delivery.setTaskNumber(bo.getTaskNumber());
                            delivery.setMasterTaskNumber(bo.getMasterTaskNumber());
                            sohuBusyTaskDeliveryService.updateByBo(delivery);
                        }
                    }
                }
                return Boolean.TRUE;
            });
        }
        throw new RuntimeException("当前任务状态不能修改任务信息");
    }

    /**
     * 查询任务主体列表
     */
    @Override
    public List<SohuBusyTaskVo> queryList(SohuBusyTaskBo bo) {
        LambdaQueryWrapper<SohuBusyTask> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<SohuBusyTaskVo> queryList(List<Long> ids) {
        return this.baseMapper.selectVoBatchIds(ids);
    }

    @Override
    public List<SohuBusyTaskVo> queryListByNumberList(Collection<? extends Serializable> taskNumberList) {
        LambdaQueryWrapper<SohuBusyTask> lqw = Wrappers.lambdaQuery();
        lqw.in(SohuBusyTask::getTaskNumber, taskNumberList);
        //先按照状态排序：待拆单、执行中、已终止，如果状态相同，按照创建时间倒叙
//        lqw.last("ORDER BY FIELD(state, '" + SohuBusyTaskState.WaitSplit + "', '" + SohuBusyTaskState.Execute + "', '" + SohuBusyTaskState.Over + "'), create_time DESC");
        List<SohuBusyTaskVo> list = this.baseMapper.selectVoList(lqw);
        return list;
//        List<SohuBusyTaskVo> result = new ArrayList<>();
//        if (CollUtil.isNotEmpty(list)) {
//            for (SohuBusyTaskVo taskVo : list) {
//                // todo 优化
//                SohuBusyTaskVo busyTaskVo = this.queryById(taskVo.getId());
//                if (Objects.nonNull(busyTaskVo)) {
//                    result.add(busyTaskVo);
//                }
//            }
//        }
//        return result;
    }

    private LambdaQueryWrapper<SohuBusyTask> buildQueryWrapper(SohuBusyTaskBo bo) {
        LambdaQueryWrapper<SohuBusyTask> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuBusyTask::getUserId, bo.getUserId());
        lqw.eq(bo.getCountrySiteId() != null, SohuBusyTask::getCountrySiteId, bo.getCountrySiteId());
        lqw.eq(StrUtil.isNotBlank(bo.getTaskNumber()), SohuBusyTask::getTaskNumber, bo.getTaskNumber());
        lqw.eq((bo.getType() != null && bo.getType() > 0L), SohuBusyTask::getType, bo.getType());
        lqw.eq(bo.getIndustryType() != null && bo.getIndustryType() > 0L, SohuBusyTask::getIndustryType, bo.getIndustryType());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), SohuBusyTask::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), SohuBusyTask::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), SohuBusyTask::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getAnnex()), SohuBusyTask::getAnnex, bo.getAnnex());
        lqw.eq(bo.getReceiveLimit() != null, SohuBusyTask::getReceiveLimit, bo.getReceiveLimit());
        lqw.eq(bo.getDeliveryDay() != null, SohuBusyTask::getDeliveryDay, bo.getDeliveryDay());
        lqw.eq(StringUtils.isNotBlank(bo.getDeliveryMsg()), SohuBusyTask::getDeliveryMsg, bo.getDeliveryMsg());
        lqw.eq(bo.getSettleType() != null, SohuBusyTask::getSettleType, bo.getSettleType());
        lqw.eq(bo.getDeliveryStep() != null, SohuBusyTask::getDeliveryStep, bo.getDeliveryStep());
        lqw.eq(bo.getFullAmount() != null, SohuBusyTask::getFullAmount, bo.getFullAmount());
        lqw.eq(bo.getFullCurrency() != null, SohuBusyTask::getFullCurrency, bo.getFullCurrency());
        lqw.eq(StringUtils.isNotBlank(bo.getKickbackType()), SohuBusyTask::getKickbackType, bo.getKickbackType());
        lqw.eq(bo.getKickbackValue() != null, SohuBusyTask::getKickbackValue, bo.getKickbackValue());
        lqw.eq(bo.getNeedSplit() != null, SohuBusyTask::getNeedSplit, bo.getNeedSplit());
        lqw.eq(bo.getIsDraft() != null, SohuBusyTask::getIsDraft, bo.getIsDraft());
        lqw.eq(bo.getSplitState() != null, SohuBusyTask::getSplitState, bo.getSplitState());
        if (StrUtil.isNotBlank(bo.getStartTime())) {
            lqw.ge(StrUtil.isNotBlank(bo.getStartTime()), SohuBusyTask::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(bo.getStartTime())));
        }
        if (StrUtil.isNotBlank(bo.getEndTime())) {
            lqw.le(StrUtil.isNotBlank(bo.getEndTime()), SohuBusyTask::getCreateTime, DateUtil.endOfDay(DateUtil.parseDate(bo.getEndTime())));
        }
        if (bo.getSplitQuery() != null && bo.getSplitQuery()) {
            lqw.gt(SohuBusyTask::getTaskNumber, bo.getTaskNumber());
        }
        if (CollUtil.isNotEmpty(bo.getSearchStatus())) {
            lqw.in(SohuBusyTask::getState, bo.getSearchStatus());
        }
        return lqw;
    }

    /**
     * 校验所有满足条件的子任务
     *
     * @param taskNumber
     */
    @Override
    public Boolean exitChildTask(String taskNumber) {
        // 查询当前主任务所有子任务
        List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteService.listByMasterTaskNo(taskNumber);
        if (CollectionUtils.isNotEmpty(busyTaskSiteList)) {
            // 判断可修改的任务
            boolean canUpdateSite = isCanUpdateSite(busyTaskSiteList);
            if (canUpdateSite) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 根据taskNumber查询主任务信息
     *
     * @param taskNumber
     */
    @Override
    public SohuBusyTaskVo getByTaskNo(String taskNumber) {
        LambdaQueryWrapper<SohuBusyTask> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuBusyTask::getTaskNumber, taskNumber);
        return this.baseMapper.selectVoOne(lqw);
    }

    /**
     * 根据taskNumber查询主任务信息
     *
     * @param taskNumber
     */
    public SohuBusyTask getByTaskNumber(String taskNumber) {
        LambdaQueryWrapper<SohuBusyTask> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuBusyTask::getTaskNumber, taskNumber);
        return this.baseMapper.selectOne(lqw);
    }

    @Override
    public Boolean updateByTask(SohuBusyTask busyTask) {
        return this.baseMapper.updateById(busyTask) > 0;
    }

    /**
     * 批量删除任务主体
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public void validExist(SohuBusyTask sohuBusyTask) {
        if (Objects.isNull(sohuBusyTask)) {
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean childShelf(SohuBusyTaskSiteBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        // 查询当前子任务
        SohuBusyTaskSite busyTaskSite = sohuBusyTaskSiteService.getById(bo.getId());
        Objects.requireNonNull(busyTaskSite, MessageUtils.message("WRONG_PARAMS"));
        // 审核时间
        Date auditTime = new Date();
        /*
         * 首先是任务上下架
         * 城市站--子任务。无法下架审核通过以后的子任务
         * 个人--子任务。已接单的任务无法下架。未编辑的任务可以自由上下架不需要审核
         */
        if (!isCanShelfTaskState(bo, busyTaskSite)) {
            return Boolean.FALSE;
        }
        busyTaskSite.setAuditTime(auditTime);
        busyTaskSite.setAuditUser(loginUser.getUserId());
        // 延时队列-做主任务状态是否需要修改工作
        /*MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(busyTaskSite), "busy_task_shelf");
        remoteStreamMqService.sendDelayMsg(mqMessaging, 1L);*/
        return sohuBusyTaskSiteService.updateById(busyTaskSite);
    }

    @Override
    public Boolean exitAuditTask(String taskNumber, String state) {
        SohuBusyTask sohuBusyTask = this.getByTaskNumber(taskNumber);
        if (ObjectUtils.isNull(sohuBusyTask)) {
            throw new RuntimeException(MessageUtils.message("WRONG_PARAMS"));
        }
        List<SohuBusyTaskSite> busyTaskSiteList = sohuBusyTaskSiteService.listByMasterTaskNo(taskNumber);
        if (CollectionUtils.isEmpty(busyTaskSiteList)) {
            return Boolean.TRUE;
        }
        // 如果上架
        if (state.equals(SohuBusyTaskState.OnShelf.name())) {
            boolean isPassOne = busyTaskSiteList.stream().anyMatch(busyTaskSite -> SohuBusyTaskState.CompelOff.name().equals(busyTaskSite.getState()) || SohuBusyTaskState.OffShelf.name().equals(busyTaskSite.getState()));
            if (isPassOne) {
                return Boolean.FALSE;
            }
        }
        // 如果强制下架
        if (state.equals(SohuBusyTaskState.CompelOff.name())) {
            boolean isNotOne = busyTaskSiteList.stream().anyMatch(busyTaskSite -> SohuBusyTaskState.OnShelf.name().equals(busyTaskSite.getState()));
            if (isNotOne) {
                return Boolean.FALSE;
            }
        }
        // 如果拒绝
        if (state.equals(SohuBusyTaskState.Refuse.name())) {
            boolean isPassOne = busyTaskSiteList.stream().anyMatch(busyTaskSite -> SohuBusyTaskState.Pass.name().equals(busyTaskSite.getState()));
            if (isPassOne) {
                return Boolean.FALSE;
            }
        }
        // 如果通过
        if (state.equals(SohuBusyTaskState.Pass.name())) {
            boolean isNotOne = busyTaskSiteList.stream().anyMatch(busyTaskSite -> SohuBusyTaskState.Refuse.name().equals(busyTaskSite.getState()));
            if (isNotOne) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 下架参数判断与组装
     *
     * @param bo
     * @param busyTaskSite
     */
    private boolean isCanShelfTaskState(SohuBusyTaskSiteBo bo, SohuBusyTaskSite busyTaskSite) {
        // 强制下架--进行中的是否需要退款操作？
        if (bo.getShelfState().equals(SohuBusyTaskState.CompelOff.name())) {
            // 查看当前子任务是否满足下架状态
            if (!SohuBusyTaskSite.CAN_UPDATE_STATES.containsKey(busyTaskSite.getState())) {
                return Boolean.FALSE;
            }
            busyTaskSite.setState(SohuBusyTaskState.CompelOff.name());
            busyTaskSite.setShelfState(SohuBusyTaskState.CompelOff.name());
            return Boolean.TRUE;
        }
        // 下架
        if (bo.getShelfState().equals(SohuBusyTaskState.OffShelf.name())) {
            // 查看当前子任务是否满足下架状态
            if (!SohuBusyTaskSite.CAN_UPDATE_STATES.containsKey(busyTaskSite.getState())) {
                return Boolean.FALSE;
            }
            busyTaskSite.setShelfState(SohuBusyTaskState.OffShelf.name());
            return Boolean.TRUE;
        } else if (bo.getShelfState().equals(SohuBusyTaskState.OnShelf.name()) && busyTaskSite.getState().equals(SohuBusyTaskState.Pass.name())) {
            // 上架
            busyTaskSite.setShelfState(SohuBusyTaskState.OnShelf.name());
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 满足下架、修改的状态判断
     *
     * @param busyTaskSiteList
     */
    private static boolean isCanUpdateSite(List<SohuBusyTaskSite> busyTaskSiteList) {
        return busyTaskSiteList.stream().allMatch(taskSite -> SohuBusyTaskSite.CAN_UPDATE_STATES.containsKey(taskSite.getState()));
    }

    @Override
    public Boolean initAirecContentItems() {
        long total = sohuBusyTaskSiteMapper.selectCount();
        // 总页数
        long totalPages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("初始化开始SohuBusyTaskSite, total:{}, totalPages:{},pageSize:{}", total, totalPages, PAGE_SIZE);
        for (int i = 1; i <= totalPages; i++) {
            // 分页查询
            Page<SohuBusyTaskSite> page = new Page<>(i, PAGE_SIZE);
            IPage<SohuBusyTaskSite> pageResult = sohuBusyTaskSiteMapper.selectPage(page, null);
            List<SohuBusyTaskSite> list = pageResult.getRecords();
            // 处理查询结果
            if (CollUtil.isNotEmpty(list)) {
                log.info("初始化中SohuBusyTaskSite, total:{}, totalPages:{},pageSize:{},pageIndex:{}", total, totalPages, PAGE_SIZE, i);
                //物料信息记录
                //List<SohuAirecContentItemReqBo> modelList = list.stream().map(p -> buildAirecContentItemModel(p)).collect(Collectors.toList());
                //物料信息记录
                List<SohuAirecContentItemBo> modelList = new ArrayList<>();
                for (SohuBusyTaskSite busyTaskSite : list) {
                    SohuAirecContentItemBo model = AirecContentUtil.buildAirecContentItemModel(busyTaskSite);
                    model.setTags(remoteMiddleAirecTagRelationService.saveTagStr(busyTaskSite.getId(), AiRecTag.BizTypeEnum.TASK.getCode(), model.getTags()));
                    modelList.add(model);
                }
                //保存物料信息
                remoteMiddleAirecContentItemService.initAirecContentItems(modelList);
            }
        }
        log.info("初始化结束SohuBusyTaskSite");
        return true;
    }

    /**
     * 保存智能推荐物料数据
     *
     * @param busyTaskSite
     */
    private void saveAirecContentItem(SohuBusyTaskSite busyTaskSite) {
        //上架、待接单、全部可以接单
        if (Objects.equals(SohuBusyTaskState.WaitReceive.name(), busyTaskSite.getState()) && Objects.equals(SohuBusyTaskState.OnShelf.name(), busyTaskSite.getShelfState()) && (!busyTaskSite.getReceiveLimit())) {
            SohuAirecContentItemBo model = AirecContentUtil.buildAirecContentItemModel(busyTaskSite);
            model.setTags(remoteMiddleAirecTagRelationService.saveTagStr(busyTaskSite.getId(), AiRecTag.BizTypeEnum.TASK.getCode(), model.getTags()));
            remoteMiddleAirecContentItemService.saveAirecContentItem(model);
        }
    }

    @Override
    public Long getBusyTaskOfPublishStat(Long userId) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getUserId, userId);
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public Long getYesterdayBusyTaskOfPublishStat(Long userId) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getUserId, userId);
        LocalDateTime startOfYesterday = LocalDate.now().minusDays(1).atStartOfDay();
        LocalDateTime endOfYesterday = startOfYesterday.plusDays(1);
        wrapper.between(SohuBusyTask::getCreateTime, LocalDateTimeUtil.format(startOfYesterday, "yyyyMMdd"), LocalDateTimeUtil.format(endOfYesterday, "yyyyMMdd"));
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public Long getBusyTaskOfFinishStat(Long userId) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getUserId, userId);
        wrapper.eq(SohuBusyTask::getState, SohuBusyTaskState.OverSettle);
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public Long getYesterdayBusyTaskOfFinishStat(Long userId) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getUserId, userId);
        wrapper.eq(SohuBusyTask::getState, SohuBusyTaskState.OverSettle);
        LocalDateTime startOfYesterday = LocalDate.now().minusDays(1).atStartOfDay();
        LocalDateTime endOfYesterday = startOfYesterday.plusDays(1);
        wrapper.between(SohuBusyTask::getCreateTime, LocalDateTimeUtil.format(startOfYesterday, "yyyyMMdd"), LocalDateTimeUtil.format(endOfYesterday, "yyyyMMdd"));
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public Long getBusyTaskViewStat(Long userId, String startDate, String endDate) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getUserId, userId);
        wrapper.select(SohuBusyTask::getViewCount);
        DateFormat(startDate, endDate, wrapper);
        return baseMapper.selectList(wrapper).stream().mapToLong(SohuBusyTask::getViewCount).sum();
    }

    private static void DateFormat(String startDate, String endDate, LambdaQueryWrapper<SohuBusyTask> wrapper) {
        // 时间转换
        if (StrUtil.isNotBlank(startDate) && StrUtil.isNotBlank(endDate)) {
            DateTime startDateTime = DateUtil.beginOfDay(DateUtil.parseDate(startDate));
            DateTime endDateTime = DateUtil.endOfDay(DateUtil.parseDate(endDate));
            wrapper.between(SohuBusyTask::getCreateTime, startDateTime, endDateTime);
        }
    }

    @Override
    public Long getBusyTaskWithReceiveUserStat(Long userId, String startDate, String endDate) {
        // 时间转换
        DateTime startDateTime = DateUtil.beginOfDay(DateUtil.parseDate(startDate));
        DateTime endDateTime = DateUtil.endOfDay(DateUtil.parseDate(endDate));
        // 统计接单人数
        return sohuBusyTaskReceiveService.getReceiveUserStat(userId, startDateTime, endDateTime);
    }

    @Override
    public Long getBusyTaskOfExecuteStat(Long userId, String startDate, String endDate) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getUserId, userId);
        wrapper.eq(SohuBusyTask::getState, SohuBusyTaskState.Execute);
        // 时间转换
        DateFormat(startDate, endDate, wrapper);
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public Long getBusyTaskOfFinishWithLimitDayStat(Long userId, String startDate, String endDate) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getUserId, userId);
        wrapper.eq(SohuBusyTask::getState, SohuBusyTaskState.OverSettle);
        // 时间转换
        DateFormat(startDate, endDate, wrapper);
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public BigDecimal getBusyTaskAmountStat(Long userId, String startDate, String endDate) {
        LambdaQueryWrapper<SohuBusyTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuBusyTask::getUserId, userId);
        wrapper.ne(SohuBusyTask::getKickbackType, KickbackType.none.getCode());
        wrapper.select(SohuBusyTask::getFullAmount);
        // 时间转换
        DateFormat(startDate, endDate, wrapper);
        List<SohuBusyTask> sohuBusyTaskList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(sohuBusyTaskList)) {
            return BigDecimal.ZERO;
        }
        return sohuBusyTaskList.stream().map(SohuBusyTask::getFullAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    @Override
    public BigDecimal countSalesAmountByTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<SohuBusyTask> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuBusyTask::getState, SohuBusyTaskState.Execute);
        lqw.between(SohuBusyTask::getAuditTime, startTime, endTime);
        return baseMapper.countSalesAmountByTime(lqw);
    }

    @Override
    public Long countOrderByTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<SohuBusyTask> lqw = new LambdaQueryWrapper<>();
        lqw.in(SohuBusyTask::getState, SohuBusyTaskState.WaitSplit, SohuBusyTaskState.Pass);
        lqw.between(SohuBusyTask::getAuditTime, startTime, endTime);
        return baseMapper.selectCount(lqw);
    }

    @Override
    public Long countAcceptByTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<SohuBusyTask> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuBusyTask::getState, SohuBusyTaskState.Execute);
        lqw.between(SohuBusyTask::getAuditTime, startTime, endTime);
        return baseMapper.selectCount(lqw);
    }

    @Override
    public List<CategoryVo> queryCategoryList(String lang) {
        List<CategoryVo> categoryList = remoteMiddleCategoryService.categoryList("BusyTask", lang);
        if (CollectionUtils.isNotEmpty(categoryList)) {
            categoryList = categoryList.stream()
                    .filter(category -> category.getConstMark().matches("NOVEL|ARTICLE|VIDEO|FLOW_TASK|COMMON_TASK"))
                    .collect(Collectors.toList());
        }
        return categoryList;
    }

    @Override
    public void updateDeliveryTime(Long id, Long deliveryTime) {
        SohuBusyTask sohuBusyTask = new SohuBusyTask();
        sohuBusyTask.setId(id);
        sohuBusyTask.setDeliveryTime(deliveryTime);
        baseMapper.updateById(sohuBusyTask);
    }

    @Override
    public Long insertByBo(SohuBusyTaskBo sohuBusyTaskBo) {
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTaskBo.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return null;
        }
        if (Objects.nonNull(sohuBusyTaskBo.getCategoryType())) {
            SohuCategoryVo taskCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTaskBo.getCategoryType());
            if (Objects.isNull(taskCategoryVo)) {
                throw new ServiceException("愿望分类不存在");
            }
        }
        if (Objects.nonNull(sohuBusyTaskBo.getSupplyType())) {
            SohuCategoryVo supplyCategoryVo = remoteMiddleCategoryService.queryById(sohuBusyTaskBo.getSupplyType());
            if (Objects.isNull(supplyCategoryVo)) {
                throw new ServiceException("供需类型不存在");
            }
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).insertByBo(sohuBusyTaskBo);
    }

    @Override
    public Long updateByBo(SohuBusyTaskBo bo) {
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(bo.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return bo.getId();
        }
        if (Objects.nonNull(bo.getCategoryType())) {
            SohuCategoryVo taskCategoryVo = remoteMiddleCategoryService.queryById(bo.getCategoryType());
            if (Objects.isNull(taskCategoryVo)) {
                throw new ServiceException("愿望分类不存在");
            }
        }
        if (Objects.nonNull(bo.getSupplyType())) {
            SohuCategoryVo supplyCategoryVo = remoteMiddleCategoryService.queryById(bo.getSupplyType());
            if (Objects.isNull(supplyCategoryVo)) {
                throw new ServiceException("供需类型不存在");
            }
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).updateByBo(bo);
    }

    @Override
    public Boolean updateTaskState(String taskNumber, String state) {
        baseMapper.updateTaskState(taskNumber, state);
        return Boolean.TRUE;
    }

    @Override
    public Boolean audit(SohuBusyTaskBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            log.warn("当前用户未登录！");
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
        SohuBusyTask busyTask = baseMapper.selectById(bo.getId());
        validExist(busyTask);
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(busyTask.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return Boolean.FALSE;
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).audit(bo, busyTask, loginUser.getUserId());
    }

    /**
     * 查询任务主体
     */
    @Override
    public SohuBusyTaskVo queryById(Long id) {
        SohuBusyTaskVo vo = baseMapper.queryByTaskId(id);
        if (Objects.isNull(vo)) {
            return new SohuBusyTaskVo();
        }
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(vo.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return new SohuBusyTaskVo();
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).queryById(id);
    }

    @Override
    public Boolean shelf(SohuBusyTaskBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            log.warn("当前用户未登录！");
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
        SohuBusyTask busyTask = this.baseMapper.selectById(bo.getId());
        validExist(busyTask);
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(busyTask.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return Boolean.FALSE;
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).shelf(bo, busyTask, loginUser.getUserId());
    }

    @Override
    public void insertUserLabelByTaskNumber(String masterTaskNumber, Long userId) {
        SohuBusyTaskVo busyTaskVo = this.getByTaskNo(masterTaskNumber);
        if (Objects.isNull(busyTaskVo)) {
            return;
        }
        ArrayList<SohuUserLabelRelationBo> userLabelList = new ArrayList<>();
        // 添加行业标签
        userLabelList.add(createUserLabelRelation(userId, busyTaskVo.getPid(), LabelEnum.INDUSTRY));
        // 添加普通标签
        List<SohuBusyTaskLabel> taskLabelList = sohuBusyTaskLabelMapper.selectList(Wrappers.<SohuBusyTaskLabel>lambdaQuery()
                .eq(SohuBusyTaskLabel::getTaskNumber, masterTaskNumber));
        if (CollectionUtils.isNotEmpty(taskLabelList)) {
            for (SohuBusyTaskLabel taskLabel : taskLabelList) {
                userLabelList.add(createUserLabelRelation(userId, taskLabel.getLabelId(), LabelEnum.COMMON));
            }
        }
        // 批量插入用户标签关联
        remoteMiddleCommonLabelService.insertBatch(userLabelList);
    }

    @Override
    public List<SohuBusyTaskVo> selectFlowBusyTaskOfState() {
        return baseMapper.selectFlowBusyTaskOfState();
    }

    /**
     * 创建用户标签关联对象
     *
     * @param userId    用户id
     * @param labelId   标签id
     * @param labelType 标签类型
     * @return 用户标签关联对象
     */
    private SohuUserLabelRelationBo createUserLabelRelation(Long userId, Long labelId, LabelEnum labelType) {
        SohuUserLabelRelationBo userLabel = new SohuUserLabelRelationBo();
        userLabel.setUserId(userId);
        userLabel.setLabelId(labelId);
        userLabel.setLabelType(labelType.getCode());
        return userLabel;
    }

    @Override
    public SohuBusyTaskVo getMasterInfo(String masterTaskNumber) {
        SohuBusyTaskVo vo = baseMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, masterTaskNumber));
        // 设置子任务佣金
        if (StringUtils.isNotBlank(vo.getKickbackType()) && !StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.none.getCode())) {
            if (StringUtils.equalsAnyIgnoreCase(vo.getKickbackType(), KickbackType.price.getCode())) {
                // 一口价
                vo.setDistributionAmount(vo.getKickbackValue());
            } else {
                BigDecimal taskDivide = BigDecimalUtils.divide(vo.getKickbackValue(), CalUtils.PERCENTAGE);
                // 百分比
                vo.setDistributionAmount(vo.getFullAmount().multiply(taskDivide).setScale(2, RoundingMode.HALF_UP));
            }
        } else {
            vo.setDistributionAmount(BigDecimal.ZERO);
        }
        if (ObjectUtils.isNotNull(vo) && null != vo.getUserId()) {
            LoginUser loginUser = remoteUserService.queryById(vo.getUserId());
            if (Objects.nonNull(loginUser)) {
                vo.setNickName(loginUser.getNickname());
                vo.setUserAvatar(loginUser.getAvatar());
                vo.setRemark(loginUser.getRemark());
            }
        }
        // 是否有行业类型
        if (vo.getIndustryType() != null && vo.getIndustryType() > 0L) {
            // 任务类型名称
            SohuIndustryCategoryVo industryCategoryVo = remoteIndustryCategoryService.queryById(vo.getIndustryType());
            vo.setIndustryName(Objects.nonNull(industryCategoryVo) ? industryCategoryVo.getIndustryName() : null);
        }
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(vo.getType());
        if (Objects.nonNull(sohuCategoryVo)) {
            vo.setTypeName(sohuCategoryVo.getName());
            vo.setConstMark(sohuCategoryVo.getConstMark());
        }
        return vo;
    }

    @Override
    public Boolean updateSortAndEffectiveTimeById(SohuBusyTaskSortBo bo) {
        // 判断开始时间或结束时间是否为空,并且开始时间要小于结束时间
        if (StrUtil.isEmpty(bo.getEndTime()) || StrUtil.isEmpty(bo.getStartTime()) ||
                DateUtil.parse(bo.getStartTime()).getTime() > DateUtil.parse(bo.getEndTime()).getTime()) {
            throw new ServiceException("生效时间不能为空,并且开始时间要小于结束时间");
        }

        SohuBusyTask task = Optional.ofNullable(this.getByTaskNumber(bo.getMasterTaskNumber()))
                .orElseThrow(() -> new ServiceException("数据异常,请联系管理员"));

        if (task.getSortIndex() != Constants.ZERO &&
                !StrUtil.equalsAnyIgnoreCase(task.getState(), SohuBusyTaskState.Pass.name(), SohuBusyTaskState.Execute.name(), SohuBusyTaskState.WaitReceive.name())) {
            throw new ServiceException("当前商单状态无需设置排序以及时间");
        }

        task.setSortIndex(bo.getSortIndex());
        task.setStartTime(DateUtil.parse(bo.getStartTime()));
        task.setEndTime(DateUtil.parse(bo.getEndTime()));
        return baseMapper.updateById(task) > 0;
    }

    @Override
    public void resetTopBusyTaskHandler() {
        // 获取商单生效时间不为空的数据
        List<SohuBusyTask> sohuBusyTaskList = baseMapper.selectList(new QueryWrapper<SohuBusyTask>()
                .select("id", "sort_index")
                .isNotNull("end_time")
                .orderByAsc("sort_index"));
        if (CollectionUtils.isEmpty(sohuBusyTaskList)) {
            return;
        }
        // 判断生效时间是否大于当前时间,是则跳过,否则排序重置并清空生效时间
        for (SohuBusyTask sohuBusyTask : sohuBusyTaskList) {
            if (sohuBusyTask.getEndTime().getTime() > System.currentTimeMillis()) {
                continue;
            }
            sohuBusyTask.setSortIndex(Constants.DEFAULT_SORT_INDEX);
            sohuBusyTask.setStartTime(null);
            sohuBusyTask.setEndTime(null);
            baseMapper.updateById(sohuBusyTask);
        }
    }

    @Override
    public TableDataInfo<SohuBusyTaskAllListVo> getAllListWithRole(SohuBusyTaskAllListBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, MessageUtils.message("WRONG_PARAMS"));
        // 发布时间倒序
        Page<SohuBusyTaskAllListVo> selectVoPage = baseMapper.getAllListWithRole(bo, PageQueryUtils.build(pageQuery));

        return TableDataInfoUtils.build(selectVoPage);
    }

    @Override
    public Long getWaitReceiveNum(Long id) {
        SohuBusyTask busyTask = this.baseMapper.selectById(id);
        if (Objects.isNull(busyTask)) {
            throw new ServiceException("愿望不存在");
        }
        return sohuBusyTaskSiteMapper.selectCount(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                .eq(SohuBusyTaskSite::getMasterTaskNumber, busyTask.getTaskNumber())
                .eq(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive.name()));
    }

    @Override
    public TableDataInfo<SohuBusyTaskReceiveApplyVo> pageTaskReceiveApplyRecord(SohuBusyTaskReceiveApplyBo bo, PageQuery pageQuery) {
        TableDataInfo<SohuBusyTaskReceiveApplyVo> tableDataInfo = new TableDataInfo<>();
        SohuAuditBo auditBo = new SohuAuditBo();
        auditBo.setBusyCode(bo.getId());
        auditBo.setSysAuditState(bo.getAuditState());
        auditBo.setBusyType(BusyType.ReceiveBusyTask.getType());
        auditBo.setId(bo.getApplyId());
        TableDataInfo<SohuAuditListVo> result = sohuAuditService.queryPageAuditList(auditBo, pageQuery);
        List<SohuBusyTaskReceiveApplyVo> applyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result.getData())) {
            for (SohuAuditListVo auditListVo : result.getData()) {
                SohuBusyTaskReceiveApplyVo receiveApplyVo = new SohuBusyTaskReceiveApplyVo();
                receiveApplyVo.setId(auditListVo.getId());
                receiveApplyVo.setUserId(auditListVo.getBusyBelonger());
                receiveApplyVo.setAuditState(auditListVo.getSysAuditState());
                receiveApplyVo.setApplyTime(auditListVo.getPublishTime());
                receiveApplyVo.setApplyMsg(auditListVo.getApplyMsg());
                receiveApplyVo.setApplyAnnex(auditListVo.getApplyAnnex());
                receiveApplyVo.setRejectReason(auditListVo.getRejectReason());
                if (StrUtil.equalsAnyIgnoreCase(auditListVo.getSysAuditState(), CommonState.OnShelf.name())) {
                    receiveApplyVo.setRemainPayTime(CalUtils.getRemainTime(auditListVo.getPublishTime(), 24));
                }
                if (CollectionUtils.isNotEmpty(auditListVo.getRecordList())) {
                    receiveApplyVo.setAuditTime(auditListVo.getRecordList().get(0).getAuditTime());
                }
                // 查询申请人信息
                LoginUser loginUser = remoteUserService.queryById(auditListVo.getBusyBelonger());
                if (Objects.nonNull(loginUser)) {
                    receiveApplyVo.setUserAvatar(loginUser.getAvatar());
                    receiveApplyVo.setUserName(loginUser.getNickname());
                }
                applyList.add(receiveApplyVo);
            }
        }
        tableDataInfo.setData(applyList);
        tableDataInfo.setTotal(result.getTotal());
        tableDataInfo.setCode(result.getCode());
        tableDataInfo.setMsg(result.getMsg());
        return tableDataInfo;
    }

    @Override
    public Boolean process(SohuBusyTaskReceiveApplyProcessBo processBo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            log.warn("当前用户未登录！");
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
        SohuBusyTask busyTask = this.baseMapper.selectById(processBo.getBusyTaskId());
        validExist(busyTask);
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(busyTask.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return Boolean.FALSE;
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).receiveApplyProcess(processBo, busyTask);
    }

    @Override
    public Boolean settleBusyTask(String taskNumber) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            log.warn("当前用户未登录！");
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
        SohuBusyTask busyTask = baseMapper.selectOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, taskNumber));
        validExist(busyTask);
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(busyTask.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return Boolean.FALSE;
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).settleBusyTask(busyTask);
    }

    @Override
    public Boolean applySettleBusyTask(String taskNumber, String rejectReason) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(loginUser)) {
            log.warn("当前用户未登录！");
            throw new ServiceException(MessageUtils.message("WRONG_PARAMS"));
        }
        SohuBusyTask busyTask = baseMapper.selectOne(Wrappers.<SohuBusyTask>lambdaQuery()
                .eq(SohuBusyTask::getTaskNumber, taskNumber));
        validExist(busyTask);
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(busyTask.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return Boolean.FALSE;
        }
        return sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).applySettleBusyTask(busyTask, rejectReason);
    }

    @Override
    public void updatePassNum(SohuBusyTask busyTask) {
        SohuCategoryVo sohuCategoryVo = remoteMiddleCategoryService.queryById(busyTask.getType());
        if (Objects.isNull(sohuCategoryVo)) {
            return;
        }
        sohuTaskProcessor.getStrategy(sohuCategoryVo.getConstMark()).updatePassNum(busyTask);
    }

    @Override
    public void handleFlowBusyTaskPassNum(String taskNumber) {
        if (Objects.nonNull(taskNumber)) {
            SohuBusyTask busyTask = baseMapper.selectOne(Wrappers.<SohuBusyTask>lambdaQuery()
                    .eq(SohuBusyTask::getTaskNumber, taskNumber));
            if (Objects.nonNull(busyTask)) {
                this.updatePassNum(busyTask);
            }
        } else {
            List<SohuBusyTask> busyTaskList = baseMapper.selectList(Wrappers.<SohuBusyTask>lambdaQuery()
                    .eq(SohuBusyTask::getState, SohuBusyTaskState.Execute.name()));
            if (CollectionUtils.isNotEmpty(busyTaskList)) {
                for (SohuBusyTask busyTask : busyTaskList) {
                    this.updatePassNum(busyTask);
                }
            }
        }
    }

    @Override
    public Date getTaskTimeWithMasterTaskNumber(String masterTaskNumber) {
        // 若未开启审核则获取接单时间,否则获取审核通过时间
        SohuBusyTaskVo sohuBusyTaskVo = baseMapper.selectVoOne(Wrappers.lambdaQuery(SohuBusyTask.class).eq(SohuBusyTask::getTaskNumber, masterTaskNumber));
        if (Objects.isNull(sohuBusyTaskVo)) {
            return new Date();
        }
        List<SohuBusyTaskSiteVo> sohuBusyTaskSiteVoList = sohuBusyTaskSiteMapper.selectTaskList(masterTaskNumber);
        if (CollectionUtils.isEmpty(sohuBusyTaskSiteVoList)) {
            return new Date();
        }
        SohuBusyTaskSiteVo sohuBusyTaskSiteVo = sohuBusyTaskSiteVoList.get(0);
        if (Objects.isNull(sohuBusyTaskSiteVo.getAuditTime())) {
            return new Date();
        }
        SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = sohuBusyTaskReceiveMapper.selectVoOne(Wrappers.lambdaQuery(SohuBusyTaskReceive.class)
                .eq(SohuBusyTaskReceive::getTaskNumber, sohuBusyTaskSiteVo.getTaskNumber())
                .in(SohuBusyTaskReceive::getState, SohuBusyTaskState.WaitFullAmountPay.name()));
        if (Objects.isNull(sohuBusyTaskReceiveVo)) {
            return new Date();
        }
        SohuAuditVo sohuAuditVo = sohuAuditService.selectByObj(sohuBusyTaskVo.getId(), BusyType.ReceiveBusyTask.name());
        if (Objects.nonNull(sohuAuditVo) && StrUtil.equalsAnyIgnoreCase(sohuAuditVo.getSysAuditState(), CommonState.OnShelf.getCode())) {
            return sohuAuditVo.getPublishTime();
        }
        return sohuBusyTaskReceiveVo.getPassTime();
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> getTopicList(SohuTopicContentQueryBo bo, PageQuery pageQuery) {
        Page<SohuBusyTaskSiteVo> sohuBusyTaskSiteVoPage = baseMapper.getTopicList(bo, PageQueryUtils.build(pageQuery));
        if (sohuBusyTaskSiteVoPage == null || CollUtil.isEmpty(sohuBusyTaskSiteVoPage.getRecords())) {
            return TableDataInfoUtils.build();
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> categoryIds = Sets.newHashSet();
        Set<Long> industryTypes = Sets.newHashSet();
        for (SohuBusyTaskSiteVo record : sohuBusyTaskSiteVoPage.getRecords()) {
            userIds.add(record.getUserId());
            categoryIds.add(record.getType());
            industryTypes.add(record.getIndustryType());
            SohuBusyTaskVo sohuBusyTaskVo = baseMapper.selectVoOne(Wrappers.<SohuBusyTask>lambdaQuery()
                    .eq(SohuBusyTask::getTaskNumber, record.getMasterTaskNumber()));
            record.setImageUrl(sohuBusyTaskVo.getImageUrl());
            // 基于master_task_number 统计状态为非待接单的数量
            Long count = sohuBusyTaskSiteMapper.selectCount(Wrappers.<SohuBusyTaskSite>lambdaQuery()
                    .ge(SohuBusyTaskSite::getState, SohuBusyTaskState.WaitReceive));
            record.setReceivedNum(count.intValue());
        }
        // 查询处理
        Map<Long, SohuCategoryVo> categoryMap = sohuCategoryService.queryMap(categoryIds);
        Map<Long, LoginUser> userMap = remoteUserService.selectMap(userIds);
        Map<Long, SohuIndustryCategoryVo> industryCategoryMap = sohuIndustryCategoryService.queryMap(industryTypes);
        for (SohuBusyTaskSiteVo record : sohuBusyTaskSiteVoPage.getRecords()) {
            String typeName = Optional.ofNullable(categoryMap.get(record.getType()))
                    .map(SohuCategoryVo::getName)
                    .orElse(StringUtils.EMPTY);
            String industryName = Optional.ofNullable(industryCategoryMap.get(record.getIndustryType()))
                    .map(SohuIndustryCategoryVo::getIndustryName)
                    .orElse(StringUtils.EMPTY);
            record.setTypeName(typeName);
            LoginUser user = userMap.get(record.getUserId());
            if (Objects.isNull(user)) {
                continue;
            }
            record.setUserName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : user.getUsername());
            record.setUserAvatar(StrUtil.isNotBlank(user.getAvatar()) ? user.getAvatar() : Constants.DEFAULT_USER_AVATAR);
            record.setIndustryName(industryName);
        }
        return TableDataInfoUtils.build(sohuBusyTaskSiteVoPage);
    }

    @Override
    public TableDataInfo<SohuBusyTaskSiteVo> queryPageTopOfTaskSite(SohuBusyTaskSiteReqBo bo, PageQuery pageQuery) {
        Page<SohuBusyTaskSiteVo> sohuBusyTaskSiteVoPage = baseMapper.queryTopTaskList(bo, PageQueryUtils.build(pageQuery));
        return TableDataInfoUtils.build(sohuBusyTaskSiteVoPage);
    }
}
