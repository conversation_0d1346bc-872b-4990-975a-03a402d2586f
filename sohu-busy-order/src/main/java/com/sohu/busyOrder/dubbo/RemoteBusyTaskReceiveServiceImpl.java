package com.sohu.busyOrder.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.sohu.busyOrder.domain.SohuBusyTaskSite;
import com.sohu.busyOrder.service.ISohuBusyTaskNoticeService;
import com.sohu.busyOrder.service.ISohuBusyTaskReceiveService;
import com.sohu.busyorder.api.RemoteBusyTaskReceiveService;
import com.sohu.busyorder.api.bo.SohuBusyTaskReceiveBo;
import com.sohu.busyorder.api.domain.SohuBusyTaskReceiveReqBo;
import com.sohu.busyorder.api.enums.TaskNoticeEnum;
import com.sohu.busyorder.api.model.SohuBusyTaskReceiveModel;
import com.sohu.busyorder.api.model.SohuBusyTaskSiteModel;
import com.sohu.busyorder.api.vo.SohuBusyTaskPayVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskReceiveVo;
import com.sohu.busyorder.api.vo.SohuBusyTaskSiteVo;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.utils.BeanCopyUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteBusyTaskReceiveServiceImpl implements RemoteBusyTaskReceiveService {

    private final AsyncConfig asyncConfig;
    private final ISohuBusyTaskReceiveService iSohuBusyTaskReceiveService;
    private final ISohuBusyTaskNoticeService sohuBusyTaskNoticeService;

    @Override
    public SohuBusyTaskReceiveModel queryById(Long id) {
        SohuBusyTaskReceiveVo sohuBusyTaskReceiveVo = iSohuBusyTaskReceiveService.queryById(id);
        return BeanCopyUtils.copy(sohuBusyTaskReceiveVo, SohuBusyTaskReceiveModel.class);
    }

    @Override
    public TableDataInfo<SohuBusyTaskReceiveModel> queryPageList(SohuBusyTaskReceiveReqBo bo, PageQuery pageQuery) {
        SohuBusyTaskReceiveBo busyTaskReceiveBo = BeanCopyUtils.copy(bo, SohuBusyTaskReceiveBo.class);
        TableDataInfo<SohuBusyTaskReceiveVo> sohuBusyTaskReceiveVoTableDataInfo = iSohuBusyTaskReceiveService.queryPageList(busyTaskReceiveBo, pageQuery);
        List<SohuBusyTaskReceiveModel> sohuBusyTaskReceiveModelList = BeanCopyUtils.copyList(sohuBusyTaskReceiveVoTableDataInfo.getData(), SohuBusyTaskReceiveModel.class);
        TableDataInfo<SohuBusyTaskReceiveModel> modelTableDataInfo = TableDataInfoUtils.build(sohuBusyTaskReceiveModelList);
        modelTableDataInfo.setTotal(sohuBusyTaskReceiveVoTableDataInfo.getTotal());
        return modelTableDataInfo;
    }

    @Override
    public List<SohuBusyTaskReceiveModel> queryList(SohuBusyTaskReceiveReqBo bo) {
        SohuBusyTaskReceiveBo busyTaskReceiveBo = BeanCopyUtils.copy(bo, SohuBusyTaskReceiveBo.class);
        List<SohuBusyTaskReceiveVo> sohuBusyTaskReceiveVoList = iSohuBusyTaskReceiveService.queryList(busyTaskReceiveBo);
        return BeanCopyUtils.copyList(sohuBusyTaskReceiveVoList, SohuBusyTaskReceiveModel.class);
    }

    @Override
    public Boolean insertByBo(SohuBusyTaskReceiveReqBo bo) {
        SohuBusyTaskReceiveBo busyTaskReceiveBo = BeanCopyUtils.copy(bo, SohuBusyTaskReceiveBo.class);
        return iSohuBusyTaskReceiveService.receiveTask(busyTaskReceiveBo);
    }

    @Override
    public Boolean updateByBo(SohuBusyTaskReceiveReqBo bo) {
        SohuBusyTaskReceiveBo busyTaskReceiveBo = BeanCopyUtils.copy(bo, SohuBusyTaskReceiveBo.class);
        return iSohuBusyTaskReceiveService.updateByBo(busyTaskReceiveBo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iSohuBusyTaskReceiveService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean audit(SohuBusyTaskReceiveReqBo bo) {
        SohuBusyTaskReceiveBo busyTaskReceiveBo = BeanCopyUtils.copy(bo, SohuBusyTaskReceiveBo.class);
        return iSohuBusyTaskReceiveService.audit(busyTaskReceiveBo);
    }

    @Override
    public String pay(SohuBusyTaskReceiveReqBo bo) {
        SohuBusyTaskReceiveBo busyTaskReceiveBo = BeanCopyUtils.copy(bo, SohuBusyTaskReceiveBo.class);
        return iSohuBusyTaskReceiveService.pay(busyTaskReceiveBo);
    }

    @Override
    public TableDataInfo<SohuBusyTaskReceiveModel> selectTaskReceiveUserPage(SohuBusyTaskReceiveReqBo bo, PageQuery pageQuery) {
        SohuBusyTaskReceiveBo busyTaskReceiveBo = BeanCopyUtils.copy(bo, SohuBusyTaskReceiveBo.class);
        TableDataInfo<SohuBusyTaskReceiveVo> sohuBusyTaskReceiveVoTableDataInfo = iSohuBusyTaskReceiveService.selectTaskReceiveUserPage(busyTaskReceiveBo, pageQuery);
        List<SohuBusyTaskReceiveModel> sohuBusyTaskReceiveModelList = BeanCopyUtils.copyList(sohuBusyTaskReceiveVoTableDataInfo.getData(), SohuBusyTaskReceiveModel.class);
        TableDataInfo<SohuBusyTaskReceiveModel> modelTableDataInfo = TableDataInfoUtils.build(sohuBusyTaskReceiveModelList);
        modelTableDataInfo.setTotal(sohuBusyTaskReceiveVoTableDataInfo.getTotal());
        return modelTableDataInfo;
    }

    @Override
    public List<SohuBusyTaskReceiveVo> queryByReceive(String taskNumber) {
        return iSohuBusyTaskReceiveService.queryByReceive(taskNumber);
    }

    @Override
    public List<SohuBusyTaskReceiveVo> queryReceiveListByTaskNumber(List<String> taskNumbers) {
        return iSohuBusyTaskReceiveService.queryReceiveListByTaskNumber(taskNumbers);
    }

    @Override
    public void pushApplyTaskOutNotice(SohuBusyTaskSiteModel bo) {
        SohuBusyTaskReceiveBo receiveBo = new SohuBusyTaskReceiveBo();
        receiveBo.setTaskNumber(bo.getTaskNumber());
        receiveBo.setState(CommonState.WaitApprove.getCode());
        List<SohuBusyTaskReceiveVo> list = iSohuBusyTaskReceiveService.queryList(receiveBo);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(f -> {
                SohuBusyTaskSite sohuBusyTaskSite = BeanCopyUtils.copy(f, SohuBusyTaskSite.class);
                sohuBusyTaskSite.setTitle(bo.getTitle());
                // 发送极光推送
                CompletableFuture.runAsync(() -> sohuBusyTaskNoticeService.pushJiguangNotice(sohuBusyTaskSite.getUserId(), TaskNoticeEnum.TASK_APPLY_REFUSE, sohuBusyTaskSite), asyncConfig.getAsyncExecutor());
            });
        }
    }

    @Override
    public void updateExpireTime(Long id, Date expireTime) {
        iSohuBusyTaskReceiveService.updateExpireTime(id, expireTime);
    }

    @Override
    public List<SohuBusyTaskReceiveVo> queryListByState(String state) {
        return iSohuBusyTaskReceiveService.queryListByState(state);
    }

    @Override
    public SohuBusyTaskReceiveVo selectById(Long id) {
        return iSohuBusyTaskReceiveService.selectById(id);
    }

    @Override
    public SohuBusyTaskReceiveVo getReceiveDetailByTaskNumber(String taskNumber) {
        return iSohuBusyTaskReceiveService.getReceiveDetailByTaskNumber(taskNumber);
    }

    @Override
    public List<SohuBusyTaskReceiveVo> queryListByMasterTaskNumberAndStateList(String masterTaskNumber, List<String> stateList) {
        return iSohuBusyTaskReceiveService.queryListByMasterTaskNumberAndStateList(masterTaskNumber, stateList);
    }

    @Override
    public Long inTransitTask(Long userId, List<String> status, Date freezeTime) {
        return iSohuBusyTaskReceiveService.inTransitTask(userId, status, freezeTime);
    }

    @Override
    public SohuBusyTaskReceiveVo queryByMasterTaskNumberrAndReceiveUserId(String taskNumber, Long receiveUserId) {
        return iSohuBusyTaskReceiveService.queryByMasterTaskNumberrAndReceiveUserId(taskNumber, receiveUserId);
    }
}
