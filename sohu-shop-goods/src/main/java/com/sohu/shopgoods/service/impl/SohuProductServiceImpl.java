package com.sohu.shopgoods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sohu.admin.api.RemoteMerchantBondService;
import com.sohu.admin.api.RemoteMerchantInfoService;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantInfoModel;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.common.core.constant.CacheConstants;
import com.sohu.common.core.constant.Constants;
import com.sohu.common.core.constant.ProductConstants;
import com.sohu.common.core.domain.MsgContent;
import com.sohu.common.core.enums.BusyType;
import com.sohu.common.core.enums.CommonState;
import com.sohu.common.core.enums.RoleCodeEnum;
import com.sohu.common.core.enums.SysSourceEnum;
import com.sohu.common.core.exception.ServiceException;
import com.sohu.common.core.utils.*;
import com.sohu.common.core.vo.UserBaseVo;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.redis.utils.RedisUtils;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.middle.api.bo.SohuIndependentMaterialBo;
import com.sohu.middle.api.bo.SohuPlatformIndustryRelationBo;
import com.sohu.middle.api.bo.playlet.SohuPlayletCartBo;
import com.sohu.middle.api.service.*;
import com.sohu.middle.api.service.mcn.RemoteMiddleMcnUserService;
import com.sohu.middle.api.service.playlet.RemoteMiddlePlayletCartService;
import com.sohu.middle.api.service.shop.RemoteMiddleOpenClientMerchantService;
import com.sohu.middle.api.vo.SohuBusyBlackVo;
import com.sohu.middle.api.vo.SohuIndependentMaterialVo;
import com.sohu.middle.api.vo.SohuPlatformIndustryRelationVo;
import com.sohu.middle.api.vo.SohuSiteVo;
import com.sohu.middle.api.vo.mcn.SohuMcnUserVo;
import com.sohu.middle.api.vo.playlet.SohuPlayletCartVo;
import com.sohu.middle.api.vo.shop.SohuOpenClientMerchantVo;
import com.sohu.pay.api.RemoteIndependentTemplateService;
import com.sohu.pay.api.model.SohuIndependentTemplateModel;
import com.sohu.pm.api.RemotePmSharePubService;
import com.sohu.shopgoods.api.bo.*;
import com.sohu.shopgoods.api.domain.*;
import com.sohu.shopgoods.api.model.*;
import com.sohu.shopgoods.api.vo.*;
import com.sohu.shopgoods.domain.*;
import com.sohu.shopgoods.mapper.SohuProductAttrValueMapper;
import com.sohu.shopgoods.mapper.SohuProductGuaranteeMapper;
import com.sohu.shopgoods.mapper.SohuProductMapper;
import com.sohu.shopgoods.mapper.SohuProductOverviewReportMapper;
import com.sohu.shopgoods.service.*;
import com.sohu.shoporder.api.RemoteShopOrderService;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * app商户相关接口
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SohuProductServiceImpl extends ServiceImpl<SohuProductMapper, SohuProduct> implements ISohuProductService {

    private final SohuProductMapper baseMapper;
    private final SohuProductAttrValueMapper sohuProductAttrValueMapper;

    private final SohuProductOverviewReportMapper sohuProductOverviewReportMapper;
    private final TransactionTemplate transactionTemplate;

    private final ISohuProductCategoryService sohuProductCategoryService;
    private final ISohuProductBrandService sohuProductBrandService;
    private final ISohuProductGuaranteeService sohuProductGuaranteeService;
    private final ISohuFreightTemplateService sohuFreightTemplateService;
    @Resource
    private ISohuProductCategoryPcService productCategoryPcService;
    @Resource
    private ISohuProductAttrService productAttrService;
    @Resource
    private ISohuProductAttrValueService productAttrValueService;
    @Resource
    private ISohuProductDescriptionService productDescriptionService;
    @Resource
    private ISohuProductCouponService productCouponService;
    @Resource
    private ISohuProductCountService productCountService;
    @Resource
    private ISohuProductRelationService productRelationService;
    @Resource
    private ISohuProductReplyService productReplyService;
    @Resource
    private SohuProductGuaranteeMapper guaranteeMapper;
    @Resource
    private ISohuShopCartService shopCartService;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;
    @DubboReference
    private RemoteMiddlePlayletCartService remoteMiddlePlayletCartService;
    @DubboReference
    private RemoteMerchantInfoService remoteMerchantInfoService;

    @DubboReference
    private RemoteMiddleViewRecordService remoteMiddleViewRecordService;
    @DubboReference
    private RemoteIndependentTemplateService remoteTemplateService;
    @DubboReference
    private RemoteMiddleMcnUserService remoteMcnUserService;
    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteShopOrderService remoteShopOrderService;
    @DubboReference
    private RemoteMiddleOpenClientMerchantService remoteMiddleOpenClientMerchantService;
    @DubboReference
    private RemoteMiddleSiteService remoteMiddleSiteService;
    @DubboReference
    private RemoteMiddleIndependentMaterialService remoteMiddleIndependentMaterialService;
    @DubboReference
    private RemotePmSharePubService remotePmSharePubService;
    @DubboReference
    private RemoteBusyBlackService busyBlackService;
    @DubboReference
    private RemotePlatformIndustryService platformIndustryService;
    @Resource
    private ISohuProductOverviewReportService sohuProductOverviewReportService;

    @DubboReference
    private RemoteMerchantBondService remoteMerchantBondService;

    /**
     * 百分百 1.00
     */
    private static final BigDecimal RATION = new BigDecimal(1);

    /**
     * 查询商品
     */
    @Override
    public SohuProductVo queryById(Long id) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SohuProductVo storeProduct = baseMapper.selectVoById(id);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new RuntimeException("未找到对应商品信息");
        }
        // 商品属性
        List<SohuProductAttrVo> attrList = productAttrService.getListByProductIdAndType(storeProduct.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        storeProduct.setAttrList(attrList);
        // 商品属性详情
        List<SohuProductAttrValueVo> attrValueList = productAttrValueService.getListByProductIdAndType(storeProduct.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        // todo 可不要
//        List<AttrValueResponse> valueResponseList = attrValueList.stream().map(e -> {
//            AttrValueResponse valueResponse = new AttrValueResponse();
//            BeanUtils.copyProperties(e, valueResponse);
//            return valueResponse;
//        }).collect(Collectors.toList());
        storeProduct.setAttrValueList(attrValueList);
        // 商品描述
        SohuProductDescription sd = productDescriptionService.getByProductIdAndType(storeProduct.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        if (ObjectUtil.isNotNull(sd)) {
            storeProduct.setContent(ObjectUtil.isNull(sd.getDescription()) ? "" : sd.getDescription());
        }
        // 获取已关联的优惠券
        List<SohuProductCoupon> storeProductCoupons = productCouponService.getListByProductId(storeProduct.getId());
        if (CollUtil.isNotEmpty(storeProductCoupons)) {
            List<Long> ids = storeProductCoupons.stream().map(SohuProductCoupon::getIssueCouponId).collect(Collectors.toList());
            // todo 商户相关
//            SystemAdmin systemAdmin = SecurityUtil.getLoginUserVo().getUser();
//            if (systemAdmin.getMerId() > 0) {
//                storeProductResponse.setCouponIds(ids);
//            }
//            LoginUser loginUser = LoginHelper.getLoginUser();
            // todo 商户相关
//            SohuMerchantModel sohuMerchantModel = remoteMerchantService.selectByUserIdAndCitySiteId(LoginHelper.getUserId(), storeProduct.getSiteId());
            // 判断用户角色是否是管理员/国家站站长 todo 本国家的
            Objects.requireNonNull(loginUser, "未登录请登录");
            boolean containsRole;
            if (ObjectUtils.isNull(loginUser)) {
                containsRole = Boolean.FALSE;
            } else {
                containsRole = LoginHelper.hasRole(loginUser, RoleCodeEnum.ADMIN) || LoginHelper.hasRole(loginUser, RoleCodeEnum.CountryStationAgent);
            }
            if (containsRole) {
                // todo 表缺失 优惠券表
//                storeProduct.setCouponList(productCouponService.findSimpleListByIdList(ids));
            } else {
                storeProduct.setCouponIds(ids);
            }
        }
        // 收藏数
        storeProduct.setCollectCount(productRelationService.getCollectCountByProductId(id));
        if (ObjectUtils.isNotNull(loginUser)) {
            // 记录访问记录
            Long userId = loginUser.getUserId();
            if (userId != null && userId > 0L) {
                remoteMiddleViewRecordService.saveViewRecord(userId, id, BusyType.Goods.name(), storeProduct.getMerId(), storeProduct.getIndependentIsShow() ? Constants.Y : Constants.N);
            }
        }
        return storeProduct;
    }

    @Override
    public SohuProductVo get(Long id) {
        return this.baseMapper.selectVoById(id);
    }

    /**
     * 查询商品列表
     */
    @Override
    public TableDataInfo<SohuProductVo> queryPageList(SohuProductBo bo, PageQuery pageQuery) {
        /*
         * 查询通过登录用户查询当前商户id--查询自带
         * 带 StoreProduct 类的多条件查询
         */
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ObjectUtil.isNotEmpty(bo.getSysSource()), SohuProduct::getSysSource, bo.getSysSource());
        // 添加查询商户id
        if (null == bo.getMerId()) {
            // 没有传查所有
            List<Long> idList = remoteMerchantService.selectByUserId(LoginHelper.getUserId()).stream()
                    .map(SohuMerchantModel::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(idList)) {
                lqw.in(SohuProduct::getMerId, idList);
            }
        } else {
            // 根据商户id查询
            lqw.eq(SohuProduct::getMerId, bo.getMerId());
        }
        // 类型搜索
        switch (bo.getType()) {
            case 1:
                // 出售中（已上架）
                lqw.eq(SohuProduct::getIsForced, false);
                lqw.eq(SohuProduct::getIsShow, true);
                lqw.eq(SohuProduct::getIsRecycle, false);
                lqw.eq(SohuProduct::getIsDel, false);
                lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
                break;
            case 2:
                // 仓库中（未上架）
                lqw.eq(SohuProduct::getIsShow, false);
                lqw.eq(SohuProduct::getIsRecycle, false);
                lqw.eq(SohuProduct::getIsDel, false);
                lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
                break;
            case 3:
                // 已售罄
                lqw.le(SohuProduct::getStock, 0);
                lqw.eq(SohuProduct::getIsRecycle, false);
                lqw.eq(SohuProduct::getIsDel, false);
                lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
                break;
            case 4:
                // 商户警戒库存
                SohuMerchantInfoModel sohuMerchantInfoModel = remoteMerchantInfoService.selectByMerId(bo.getMerId());
                lqw.le(SohuProduct::getStock, sohuMerchantInfoModel.getAlertStock());
                lqw.eq(SohuProduct::getIsRecycle, false);
                lqw.eq(SohuProduct::getIsDel, false);
                lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
                break;
            case 5:
                // 回收站
                lqw.eq(SohuProduct::getIsRecycle, true);
                lqw.eq(SohuProduct::getIsDel, false);
                break;
            case 6:
                //待审核
                lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_WAIT);
                lqw.eq(SohuProduct::getIsRecycle, false);
                lqw.eq(SohuProduct::getIsDel, false);
                break;
            case 7:
                // 审核失败
                lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_FAIL);
                lqw.eq(SohuProduct::getIsRecycle, false);
                lqw.eq(SohuProduct::getIsDel, false);
                break;
            default:
                break;
        }
        // 关键字搜索
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            lqw.and(i -> i.like(SohuProduct::getStoreName, bo.getKeyword())
                    .or().like(SohuProduct::getKeyword, bo.getKeyword()));
        }
        lqw.apply(StrUtil.isNotBlank(bo.getCateId()), "FIND_IN_SET ('" + bo.getCateId() + "', cate_id)");
        if (ObjectUtil.isNotNull(bo.getCategoryId())) {
            lqw.eq(SohuProduct::getCategoryId, bo.getCategoryId());
        }
        // 判断是普通商品还是返哺商品
        if (null != bo.getProductType()) {
            lqw.eq(SohuProduct::getProductType, bo.getProductType());
        }
//        // 判断是否是分佣商品
//        if (ObjectUtil.isNotNull(bo.getIndependentType())) {
//            lqw.eq(SohuProduct::getIndependentType, bo.getIndependentType());
//        }
        // 判断分佣状态是否开启
        if (ObjectUtil.isNotNull(bo.getIndependentIsShow())) {
            lqw.eq(SohuProduct::getIndependentIsShow, bo.getIndependentIsShow());
        }
        // 排序
        lqw.orderByDesc(SohuProduct::getSort).orderByDesc(SohuProduct::getId);
        // 组装分页查询
        Page<SohuProductVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuProductVo> productVoList = result.getRecords();
        if (CollUtil.isEmpty(productVoList)) {
            return TableDataInfoUtils.build(CollUtil.newArrayList());
        }
        // 暂时列表不需要
//        for (SohuProductVo product : productVoList) {
//            // 收藏数
//            product.setCollectCount(productRelationService.getCollectCountByProductId(product.getId()));
//        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public TableDataInfo<SohuProductVo> queryPageListOfOnShelf(SohuProductAdBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        // 出售中（已上架）
        lqw.eq(SohuProduct::getIsForced, false);
        lqw.eq(SohuProduct::getIsShow, true);
        lqw.eq(SohuProduct::getIsRecycle, false);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
        lqw.eq(StrUtil.isNotBlank(bo.getProductNo()), SohuProduct::getId, bo.getProductNo());
        lqw.like(StrUtil.isNotBlank(bo.getProductName()), SohuProduct::getStoreName, bo.getProductName());
        // 排序
        lqw.orderByDesc(SohuProduct::getSort).orderByDesc(SohuProduct::getId);
        // 组装分页查询
        Page<SohuProductVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuProductVo> productVoList = result.getRecords();
        if (CollUtil.isEmpty(productVoList)) {
            return TableDataInfoUtils.build(CollUtil.newArrayList());
        }
        Set<Long> merIdSet = productVoList.stream().map(p -> p.getMerId()).collect(Collectors.toSet());
        Map<Long, SohuMerchantModel> merchantModelMap = remoteMerchantService.selectMapByIds(merIdSet);
        for (SohuProductVo vo : productVoList) {
            SohuMerchantModel model = merchantModelMap.get(vo.getMerId());
            if (Objects.nonNull(model)) {
                vo.setMerchantName(model.getName());
            }
        }
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询商品列表
     */
    @Override
    public List<SohuProductVo> queryList(SohuProductBo bo) {
        LambdaQueryWrapper<SohuProduct> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuProduct> buildQueryWrapper(SohuProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSiteId() != null, SohuProduct::getSiteId, bo.getSiteId());
        lqw.eq(bo.getMerId() != null, SohuProduct::getMerId, bo.getMerId());
        lqw.like(StringUtils.isNotBlank(bo.getStoreName()), SohuProduct::getStoreName, bo.getStoreName());
        lqw.eq(StringUtils.isNotBlank(bo.getStoreInfo()), SohuProduct::getStoreInfo, bo.getStoreInfo());
        lqw.eq(StringUtils.isNotBlank(bo.getKeyword()), SohuProduct::getKeyword, bo.getKeyword());
        lqw.eq(StringUtils.isNotBlank(bo.getCateId()), SohuProduct::getCateId, bo.getCateId());
        lqw.eq(bo.getBrandId() != null, SohuProduct::getBrandId, bo.getBrandId());
        lqw.eq(bo.getCategoryId() != null, SohuProduct::getCategoryId, bo.getCategoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getGuaranteeIds()), SohuProduct::getGuaranteeIds, bo.getGuaranteeIds());
        lqw.eq(bo.getPrice() != null, SohuProduct::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), SohuProduct::getCurrency, bo.getCurrency());
        lqw.eq(bo.getPostage() != null, SohuProduct::getPostage, bo.getPostage());
        lqw.eq(null != bo.getSales(), SohuProduct::getSales, bo.getSales());
        lqw.eq(null != bo.getIsShow(), SohuProduct::getIsShow, bo.getIsShow());
        lqw.eq(bo.getBrowse() != null, SohuProduct::getBrowse, bo.getBrowse());
        lqw.eq(bo.getSort() != null, SohuProduct::getSort, bo.getSort());
        lqw.eq(bo.getSpecType() != null, SohuProduct::getSpecType, bo.getSpecType());
        lqw.eq(bo.getProductType() != null, SohuProduct::getProductType, bo.getProductType());
        lqw.eq(bo.getIsRecycle() != null, SohuProduct::getIsRecycle, bo.getIsRecycle());
        lqw.eq(null != bo.getIsForced(), SohuProduct::getIsForced, bo.getIsForced());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditStatus()), SohuProduct::getAuditStatus, bo.getAuditStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), SohuProduct::getReason, bo.getReason());
        lqw.eq(null != bo.getIsDel(), SohuProduct::getIsDel, bo.getIsDel());
        lqw.eq(null != bo.getIndependentIsShow(), SohuProduct::getIsDel, bo.getIsDel());
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuProduct::getSysSource, bo.getSysSource());
        return lqw;
    }

    /**
     * 新增商品
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(SohuProductBo bo) {
        // 多规格需要校验规格参数
        if (!bo.getSpecType()) {
            if (bo.getAttrValueList().size() > 1) {
                throw new RuntimeException("单规格商品属性值不能大于1");
            }
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new RuntimeException("登录过期");
        }
        // 判断是否有狐少少店铺商家的角色
//        if (loginUser.getRoles().stream().noneMatch(role -> RoleCodeEnum.SHOP.getCode().equals(role.getRoleKey()) ||
//                RoleCodeEnum.MERCHANT.getCode().equals(role.getRoleKey()))) {
//            throw new RuntimeException("只有商户管理员才能创建商品");
//        }
        SohuProduct add = BeanCopyUtils.copy(bo, SohuProduct.class);
        // 商户
        SohuMerchantModel sohuMerchantModel = remoteMerchantService.selectById(bo.getMerId());
        if (Objects.isNull(sohuMerchantModel)) {
            throw new RuntimeException("商户不存在");
        }
        add.setIsSelf(sohuMerchantModel.getIsSelf());
        add.setSiteId(sohuMerchantModel.getCitySiteId());
        // 计算价格
        List<SohuProductAttrValueBo> attrValueList = bo.getAttrValueList();
        SohuProductAttrValueBo minAttrValue = attrValueList.stream().min(Comparator.comparing(SohuProductAttrValueBo::getPrice)).get();
        add.setPrice(minAttrValue.getPrice());
        add.setOtPrice(minAttrValue.getOtPrice());
        add.setCost(minAttrValue.getCost());
        add.setStock(attrValueList.stream().mapToInt(SohuProductAttrValueBo::getStock).sum());
        // 商品分佣费率计算
        extractedGoodRation(add);
        // 商品属性
        List<SohuProductAttrBo> addRequestList = bo.getAttrList();
        List<SohuProductAttr> attrList = addRequestList.stream().map(e -> {
            SohuProductAttr attr = new SohuProductAttr();
            BeanUtils.copyProperties(e, attr);
            attr.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            return attr;
        }).collect(Collectors.toList());
        // 商品属性详情
        List<SohuProductAttrValue> addAttrValueList = attrValueList.stream().map(e -> {
            SohuProductAttrValue attrValue = new SohuProductAttrValue();
            BeanUtils.copyProperties(e, attrValue);
            attrValue.setId(null);
            attrValue.setSku(getSku(e.getAttrValue()));
            attrValue.setQuota(0);
            attrValue.setQuotaShow(0);
            attrValue.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            return attrValue;
        }).collect(Collectors.toList());
        // 处理富文本
        SohuProductDescription spd = new SohuProductDescription();
        if (StringUtils.isNotBlank(bo.getContent())) {
            spd.setDescription(bo.getContent());
        }
        spd.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        return transactionTemplate.execute(e -> {
            // 商户新增商品是否需要审核
            if (sohuMerchantModel.getProductSwitch()) {
                add.setAuditStatus(ProductConstants.AUDIT_STATUS_WAIT);
            }
            this.baseMapper.insert(add);
            // 数量
            SohuProductCount sohuProductCount = new SohuProductCount();
            sohuProductCount.setProductId(add.getId()).setStock(add.getStock()).setSales(add.getSales())
                    .setVarSales(add.getVarSales()).setBrowse(add.getBrowse());
            productCountService.save(sohuProductCount);
            // 属性-属性详情
            attrList.forEach(attr -> attr.setProductId(add.getId()));
            addAttrValueList.forEach(value -> value.setProductId(add.getId()).setSkuId(System.currentTimeMillis()));
            productAttrService.saveBatch(attrList);
            productAttrValueService.saveBatch(addAttrValueList);
            // 描述
            spd.setProductId(add.getId());
            productDescriptionService.deleteByProductId(add.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            productDescriptionService.save(spd);
            // 优惠券
            if (CollUtil.isNotEmpty(bo.getCouponIds())) {
                List<SohuProductCoupon> couponList = new ArrayList<>();
                for (Long couponId : bo.getCouponIds()) {
                    SohuProductCoupon spc = new SohuProductCoupon();
                    spc.setProductId(add.getId()).setIssueCouponId(couponId).setCreateTime(new Date());
                    couponList.add(spc);
                }
                productCouponService.saveBatch(couponList);
            }
            Map<String, String> upMap = new HashMap<>();
            upMap.put("product_num", "+1");
            sohuProductOverviewReportService.syncProductOverviewReport(upMap);
            return Boolean.TRUE;
        });
    }

    /**
     * 商品分佣费率计算
     *
     * @param bo 商品对象
     */
    @Override
    public BigDecimal extractedGoodRation(SohuProduct bo) {
        if (bo.getIndependentIsShow()) {
            // 根据城市站点、商品分账模板类型 查询分账模板
            SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(bo.getSiteId(), 1);
            Objects.requireNonNull(templateModel, "当前站点没有设置分账模版，请联系管理员");
            // 根据分销比例计算分销金额
            BigDecimal independentUserPrice = getIndependentUserPrice(bo.getPrice(), bo.getIndependentRatio(), templateModel);
            bo.setIndependentPrice(independentUserPrice);
            return independentUserPrice;
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取商品规格列表
     */
    @Override
    public List<SohuProductAttrVo> getListByProductIdAndType(Long id, String type) {
        return productAttrService.getListByProductIdAndType(id, type);
    }

    @Override
    public Long countByAuditStatus(String auditStatus) {
        return baseMapper.selectCount(Wrappers.<SohuProduct>lambdaQuery().eq(SohuProduct::getAuditStatus, auditStatus));
    }

    @Override
    public void productOverviewExecute(String day) {
        String startTime = day + " 00:00:00";
        String endTime = day + " 23:59:59";
        Long productNum = baseMapper.selectCount(Wrappers.<SohuProduct>lambdaQuery().eq(SohuProduct::getIsDel, false).lt(SohuProduct::getCreateTime, endTime));
        Long stockNum = baseMapper.selectCount(new LambdaQueryWrapper<SohuProduct>().eq(SohuProduct::getIsDel, false).lt(SohuProduct::getStock, 5));
        List<ProductShowVo> productShowVo = baseMapper.groupShowNum(endTime);
        int listingNum = 0;
        int delistingNum = 0;
        if (CollUtil.isNotEmpty(productShowVo)) {
            for (ProductShowVo item : productShowVo
            ) {
                if (item.getIsShow() == Constants.ZERO) { //待上架
                    delistingNum = item.getCnt();
                }
                if (item.getIsShow() == Constants.ONE) { //已上架
                    listingNum = item.getCnt();
                }
            }
        }
        SohuProductOverviewReport overviewReport = sohuProductOverviewReportMapper.selectOne(Wrappers.<SohuProductOverviewReport>lambdaQuery().eq(SohuProductOverviewReport::getDate, day));
        boolean isSave = false;
        if (Objects.isNull(overviewReport)) {
            overviewReport = new SohuProductOverviewReport();
            overviewReport.setDate(day);
            isSave = true;
        }
        overviewReport.setProductNum(productNum.intValue());
        overviewReport.setProductListingNum(listingNum);
        overviewReport.setProductDelistingNum(delistingNum);
        overviewReport.setProductStockNum(stockNum.intValue());
        if (isSave) {
            sohuProductOverviewReportMapper.insert(overviewReport);
        } else {
            sohuProductOverviewReportMapper.updateById(overviewReport);
        }
        //插入今日默认数据
        String today = DateUtils.getDateMap(Constants.ONE).get(DateUtils.START_DAY);
        SohuProductOverviewReport todayOverviewReport = sohuProductOverviewReportMapper.selectOne(Wrappers.<SohuProductOverviewReport>lambdaQuery()
                .eq(SohuProductOverviewReport::getDate, DateUtils.getDateMap(Constants.ONE).get(DateUtils.START_DAY)));
        if (Objects.isNull(todayOverviewReport)) {
            todayOverviewReport = new SohuProductOverviewReport();
            todayOverviewReport.setDate(today);
            todayOverviewReport.setProductNum(productNum.intValue());
            todayOverviewReport.setProductListingNum(listingNum);
            todayOverviewReport.setProductDelistingNum(delistingNum);
            todayOverviewReport.setProductStockNum(stockNum.intValue());
            sohuProductOverviewReportMapper.insert(todayOverviewReport);
        }
    }

    @Override
    public List<SohuProductVo> queryGoodsByIds(List<Long> ids, String state) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SohuProduct>().in(SohuProduct::getId, ids)
                .eq(SohuProduct::getAuditStatus, "PASS")
                .eq(SohuProduct::getIsDel, Constants.ZERO));

    }

    @Override
    public Long productCount(Long merId, String auditStatus) {
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuProduct::getMerId, merId);
        lqw.eq(StrUtil.isNotBlank(auditStatus), SohuProduct::getAuditStatus, auditStatus);
        return baseMapper.selectCount(lqw);
    }

    /**
     * 分账算价
     *
     * @param price
     * @param independentRatio
     * @param templateModel
     */
    private static BigDecimal getIndependentUserPrice(BigDecimal price, BigDecimal independentRatio, SohuIndependentTemplateModel templateModel) {
        // 消费者拉新人手续费百分比
        BigDecimal distributorDivide = BigDecimalUtils.divide(templateModel.getDistributorRatio(), CalUtils.PERCENTAGE);
        log.warn("消费者拉新人手续费百分比：{}", distributorDivide);
        // 消费者拉新人收益 = 商品价格 * 平台系数 * 消费者拉新人手续费比例 = 100 * 5% * 10% = 0.5
        // 商户扣除平台手续费后的金额 = 商品价格 * （1-平台手续费比例）
        BigDecimal independentDistributorPrice = price.multiply(BigDecimalUtils.divide(independentRatio, CalUtils.PERCENTAGE)).multiply(distributorDivide)
                .setScale(2, RoundingMode.HALF_UP);
        log.warn("消费者拉新人收益：{}", independentDistributorPrice);
        return independentDistributorPrice;
    }

    /**
     * 修改商品
     */
    @Override
    public Boolean updateByBo(SohuProductBo bo) {

        // 商品id判断
        if (ObjectUtil.isNull(bo.getId())) {
            throw new RuntimeException("商品ID不能为空");
        }
        // 属性规格判断
        if (!bo.getSpecType()) {
            if (bo.getAttrValueList().size() > 1) {
                throw new RuntimeException("单规格商品属性值不能大于1");
            }
        }
        // 查询商品
        SohuProduct tempProduct = getProduct(bo.getId());
        if (tempProduct.getIsRecycle() || tempProduct.getIsDel()) {
            throw new RuntimeException("商品已删除");
        }
        if (tempProduct.getIsShow()) {
            throw new RuntimeException("请先下架商品，再进行修改");
        }
        SohuProduct storeProduct = BeanUtil.toBean(bo, SohuProduct.class);
        BeanUtils.copyProperties(bo, storeProduct);
        storeProduct.setIsForced(tempProduct.getIsForced());
        storeProduct.setAuditStatus(tempProduct.getAuditStatus());
        // 商户
        SohuMerchantModel merchant = remoteMerchantService.selectById(tempProduct.getMerId());
        if (Objects.isNull(merchant)) {
            throw new RuntimeException("商户不存在");
        }
        storeProduct.setIsSelf(merchant.getIsSelf());
        List<SohuProductAttrValueBo> attrValueAddRequestList = bo.getAttrValueList();
        //计算价格
        SohuProductAttrValueBo minAttrValue = attrValueAddRequestList.stream().min(Comparator.comparing(SohuProductAttrValueBo::getPrice)).get();
        storeProduct.setPrice(minAttrValue.getPrice());
        storeProduct.setOtPrice(minAttrValue.getOtPrice());
        storeProduct.setCost(minAttrValue.getCost());
        storeProduct.setStock(attrValueAddRequestList.stream().mapToInt(SohuProductAttrValueBo::getStock).sum());
        // 商品分销人分佣金额相关
        if (bo.getIndependentIsShow()) {
            SohuProduct sohuProduct = this.baseMapper.selectById(bo.getId());
            if (!bo.getIndependentRatio().equals(sohuProduct.getIndependentRatio()) || !minAttrValue.getPrice().equals(sohuProduct.getPrice())) {
                // 根据城市站点、商品分账模板类型 查询分账模板
                SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(bo.getSiteId(), 1);
                BigDecimal independentUserPrice = getIndependentUserPrice(storeProduct.getPrice(), bo.getIndependentRatio(), templateModel);
                storeProduct.setIndependentPrice(independentUserPrice);
            }
        }
        // attr部分
        List<SohuProductAttrBo> addRequestList = bo.getAttrList();
        List<SohuProductAttr> attrAddList = CollUtil.newArrayList();
        List<SohuProductAttr> attrUpdateList = CollUtil.newArrayList();
        addRequestList.forEach(e -> {
            SohuProductAttr attr = new SohuProductAttr();
            BeanUtils.copyProperties(e, attr);
            if (ObjectUtil.isNull(attr.getId())) {
                attr.setProductId(storeProduct.getId());
                attr.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
                attrAddList.add(attr);
            } else {
                attr.setIsDel(false);
                attrUpdateList.add(attr);
            }
        });
        // attrValue部分
        List<SohuProductAttrValue> attrValueAddList = CollUtil.newArrayList();
        List<SohuProductAttrValue> attrValueUpdateList = CollUtil.newArrayList();
        attrValueAddRequestList.forEach(e -> {
            SohuProductAttrValue attrValue = new SohuProductAttrValue();
            BeanUtils.copyProperties(e, attrValue);
            attrValue.setSku(getSku(e.getAttrValue()));
            if (ObjectUtil.isNull(attrValue.getId()) || attrValue.getId().equals(0)) {
                attrValue.setId(null);
                attrValue.setProductId(storeProduct.getId());
                attrValue.setQuota(0);
                attrValue.setQuotaShow(0);
                attrValue.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
                attrValueAddList.add(attrValue);
            } else {
                attrValue.setProductId(storeProduct.getId());
                attrValue.setIsDel(false);
                attrValueUpdateList.add(attrValue);
            }
        });
        // 处理富文本
        SohuProductDescription spd = new SohuProductDescription();
        if (StringUtils.isNotBlank(bo.getContent())) {
            spd.setDescription(bo.getContent());
        }
        spd.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        spd.setProductId(storeProduct.getId());

        return transactionTemplate.execute(e -> {
            // 商户是否开启审核
            if (merchant.getProductSwitch() || storeProduct.getAuditStatus().equals(ProductConstants.AUDIT_STATUS_FAIL)) {
                storeProduct.setAuditStatus(ProductConstants.AUDIT_STATUS_WAIT);
            }
            if (storeProduct.getIsForced()) {
                storeProduct.setIsForced(false);
            }
            // 商品信息
            this.baseMapper.updateById(storeProduct);

            // 先删除原用attr+value
            productAttrService.deleteByProductIdAndType(storeProduct.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            productAttrValueService.deleteByProductIdAndType(storeProduct.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            // 商品属性
            if (CollUtil.isNotEmpty(attrAddList)) {
                productAttrService.saveBatch(attrAddList);
            }
            if (CollUtil.isNotEmpty(attrUpdateList)) {
                productAttrService.saveOrUpdateBatch(attrUpdateList);
            }
            // 商品属性详情
            if (CollUtil.isNotEmpty(attrValueAddList)) {
                productAttrValueService.saveBatch(attrValueAddList);
            }
            if (CollUtil.isNotEmpty(attrValueUpdateList)) {
                productAttrValueService.saveOrUpdateBatch(attrValueUpdateList);
            }
            // 描述
            productDescriptionService.deleteByProductId(storeProduct.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            productDescriptionService.save(spd);
            // 优惠券
            if (CollUtil.isNotEmpty(bo.getCouponIds())) {
                productCouponService.deleteByProductId(storeProduct.getId());
                List<SohuProductCoupon> couponList = new ArrayList<>();
                for (Long couponId : bo.getCouponIds()) {
                    SohuProductCoupon spc = new SohuProductCoupon();
                    spc.setCreateTime(new Date()).setProductId(storeProduct.getId()).setIssueCouponId(couponId);
                    couponList.add(spc);
                }
                productCouponService.saveBatch(couponList);
            } else {
                productCouponService.deleteByProductId(storeProduct.getId());
            }
            return Boolean.TRUE;
        });
    }

    /**
     * 批量删除商品
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
//        if (isValid) {
//            // TODO 做一些业务上的校验,判断是否需要校验
//        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public boolean isUsePlatformCategory(Long categoryId) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProduct::getId);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getCategoryId, categoryId);
        lqw.last("limit 1");
        SohuProduct storeProduct = this.baseMapper.selectOne(lqw);
        return ObjectUtil.isNotNull(storeProduct);
    }

    @Override
    public boolean isExistStoreCategory(Long id) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProduct::getId);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.apply(" find_in_set({0}, cate_id)", id);
        lqw.last(" limit 1");
        SohuProduct storeProduct = this.baseMapper.selectOne(lqw);
        return ObjectUtil.isNotNull(storeProduct);
    }

    @Override
    public TableDataInfo<SohuProductVo> queryPcPageList(SohuProductPcBo bo, PageQuery pageQuery) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("type", bo.getType());
        if (ObjectUtil.isNotNull(bo.getCategoryId())) {
            SohuProductCategoryPcVo category = productCategoryPcService.queryById(bo.getCategoryId());
            if (category.getLevel() == 3) {
                map.put("categoryIds", bo.getCategoryId());
            } else {
                List<SohuProductCategoryPc> categoryList = productCategoryPcService.findAllChildListByPid(category.getId(), category.getLevel());
                List<String> cateIdList = categoryList.stream().filter(e -> e.getLevel() == 3).map(e -> e.getId().toString()).collect(Collectors.toList());
                String categoryIds = String.join(",", cateIdList);
                map.put("categoryIds", categoryIds);
            }
        }
        if (ObjectUtil.isNotNull(bo.getId())) {
            map.put("id", bo.getId());
        }
        if (StringUtils.isNotEmpty(bo.getStoreName())) {
            map.put("storeName", bo.getStoreName());
        }
        if (ObjectUtil.isNotNull(bo.getMerId())) {
            map.put("merId", bo.getMerId());
        }
        if (ObjectUtil.isNotNull(bo.getIsSelf())) {
            map.put("self", bo.getIsSelf());
        }
        if (StrUtil.isNotEmpty(bo.getKeyword())) {
            map.put("keywords", bo.getKeyword());
        }
        if (null != bo.getProductType()) {
            map.put("productType", bo.getProductType());
        }
        if (ObjectUtil.isNotNull(bo.getSysSource())) {
            map.put("sysSource", bo.getSysSource());
        }
        List<Long> handleIds = new ArrayList<>();
        if (Objects.nonNull(bo.getIndustryId())) {
            // 行业id不为空,则查询相关行业对应的分类
            List<SohuPlatformIndustryRelationVo> relationList = platformIndustryService.queryList(bo.getIndustryId(), BusyType.Goods.getType());
            List<String> categoryIds = relationList.stream().map(e -> e.getBusyCategoryId().toString()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(categoryIds)) {
                return TableDataInfoUtils.build(new ArrayList<>());
            }
            if (Objects.isNull(map.get("categoryIds"))) {
                map.put("categoryIds", String.join(",", categoryIds));
            }
            // 查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getIndustryId(), 2, BusyType.Goods.getType());
            map.put("isBlack", bo.getIsBlack());
        } else if (Objects.nonNull(bo.getSiteId()) && LoginHelper.hasRole(LoginHelper.getLoginUser(), RoleCodeEnum.CityStationAgent)) {
            // 站点id不为空,则查询行业黑名单
            handleIds = busyBlackService.listBusyIds(bo.getSiteId(), 1, BusyType.Goods.getType());
            map.put("isBlack", bo.getIsBlack());
        }
        if (bo.getIsBlack() && CollectionUtils.isEmpty(handleIds)) {
            handleIds.add(0L);
        }
        if (CollectionUtils.isNotEmpty(handleIds)) {
            map.put("productIds", handleIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        Page page = new Page(pageQuery.getPageNum(), pageQuery.getPageSize());
        Page<SohuProductVo> pageList = this.baseMapper.getPlatformPageList(map, page);
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            for (SohuProductVo productVo : pageList.getRecords()) {
                // 设置移出时间
                if (bo.getIsBlack()) {
                    SohuBusyBlackVo sohuBusyBlackVo = null;
                    if (Objects.nonNull(bo.getIndustryId())) {
                        sohuBusyBlackVo = busyBlackService.queryByParam(productVo.getId(), 2, BusyType.Goods.getType());
                    } else {
                        sohuBusyBlackVo = busyBlackService.queryByParam(productVo.getId(), 1, BusyType.Goods.getType());
                    }
                    productVo.setRemoveTime(sohuBusyBlackVo.getCreateTime());
                }
            }
        }
        return TableDataInfoUtils.build(pageList);
    }

    @Override
    public TableDataInfo<SohuProductVo> queryPcPageListNew(SohuProductReqNewBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuProduct::getSysSource, bo.getSysSource());
        //商户搜索
        if (ObjectUtil.isNotNull(bo.getMerId())) {
            lqw.eq(SohuProduct::getMerId, bo.getMerId());
        }
        // 关键字搜索
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            lqw.and(i -> i.like(SohuProduct::getStoreName, bo.getKeyword())
                    .or().like(SohuProduct::getKeyword, bo.getKeyword()));
        }
        // 一级分类Id
        if (ObjectUtil.isNotNull(bo.getFirstCategoryId())) {
            lqw.eq(SohuProduct::getFirstCategoryId, bo.getFirstCategoryId());
        }
        //分类
        if (ObjectUtil.isNotNull(bo.getCategoryId())) {
            lqw.eq(SohuProduct::getCategoryId, bo.getCategoryId());
        }
        //热门
        if (ObjectUtil.isNotNull(bo.getIsHot())) {
            lqw.eq(SohuProduct::getIsHot, true);
        }
        //猜你喜欢
//        if (ObjectUtil.isNotNull(bo.getIsLike())) {
//            lqw.eq(SohuProduct::getIsLike, true);
//        }
        // 已开启的商户
        List<SohuMerchantModel> merchantCloseList = remoteMerchantService.getMerchantOpenList(bo.getSysSource());
        if (CollUtil.isNotEmpty(merchantCloseList)) {
            List<Long> merIds = merchantCloseList.stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
            lqw.in(SohuProduct::getMerId, merIds);
        }
        lqw.eq(SohuProduct::getIsForced, false);
        lqw.eq(SohuProduct::getIsShow, true);
        lqw.eq(SohuProduct::getIsRecycle, false);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
        if (ObjectUtil.isNotNull(bo.getIsRank())) {
            lqw.orderByDesc(SohuProduct::getSales);
        }
        if (ObjectUtil.isNotNull(bo.getIsLatest())) {
//            lqw.gt(SohuProduct::getCreateTime, DateUtil.offsetHour(new Date(), -4));
            lqw.orderByDesc(SohuProduct::getCreateTime);
        }
        lqw.orderByDesc(SohuProduct::getSort).orderByDesc(SohuProduct::getId);
        // 组装分页查询
        Page<SohuProductVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        List<SohuProductVo> productVoList = result.getRecords();
        if (CollUtil.isEmpty(productVoList)) {
            return TableDataInfoUtils.build(CollUtil.newArrayList());
        }
        if (CollUtil.isNotEmpty(productVoList)) {
            productVoList.stream().forEach(productVo -> {
                //判断该用户是否购买过该店铺商品
                if (ObjectUtil.isNotNull(bo.getUserId())) {
                    Long count = remoteShopOrderService.getUserOrderByStore(productVo.getMerId(), bo.getUserId());
                    if (count > 0) {
                        productVo.setIsBuy(true);
                    }
                }
                SohuMerchantModel merchant = remoteMerchantService.selectById(productVo.getMerId());
                productVo.setMerchantName(merchant != null ? merchant.getName() : "");
            });
        }
        return TableDataInfoUtils.build(result);
    }

    @Override
    public List<SohuProduct> findUseGuarantee(Long id) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProduct::getId, SohuProduct::getMerId);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.apply(" find_in_set({0}, guarantee_ids)", id);
        return this.baseMapper.selectList(lqw);
    }

    @Override
    public Boolean isUseGuarantee(Long id) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProduct::getId);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.apply(" find_in_set({0}, guarantee_ids)", id);
        lqw.last("limit 1");
        SohuProduct storeProduct = this.baseMapper.selectOne(lqw);
        return ObjectUtil.isNotNull(storeProduct);
    }

    /**
     * 商品sku
     *
     * @param attrValue json字符串
     * @return sku
     */
    private String getSku(String attrValue) {
        Type type = new TypeReference<LinkedHashMap<String, String>>() {
        }.getType();
        LinkedHashMap<String, String> linkedHashMap = JSONObject.parseObject(attrValue, type, Feature.OrderedField);
        return String.join(",", linkedHashMap.values());
    }

    /**
     * 根据id集合获取商品简单信息
     *
     * @param productIds id集合
     * @return 商品信息
     */
    @Override
    public List<SimpleProductVo> getSimpleListInIds(List<Long> productIds) {
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        lqw.select(SohuProduct::getId, SohuProduct::getStoreName, SohuProduct::getImage);
        lqw.in(SohuProduct::getId, productIds);
        lqw.eq(SohuProduct::getIsDel, false);
        List<SohuProduct> selectList = this.baseMapper.selectList(lqw);
        return selectList.stream().map(e -> {
            SimpleProductVo vo = new SimpleProductVo();
            BeanUtils.copyProperties(e, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean deleteByIdAndType(Long id, String type) {
        SohuProduct product = getProduct(id);
        if (ProductConstants.PRODUCT_DELETE_TYPE_RECYCLE.equals(type) && product.getIsRecycle()) {
            throw new RuntimeException("商品已存在回收站");
        }
        // 分佣开启状态不可删除
        if (ProductConstants.PRODUCT_INDEPENDENT_IS_SHOW.equals(product.getIndependentIsShow())) {
            throw new RuntimeException("商品当前处于分佣状态");
        }
        LambdaUpdateWrapper<SohuProduct> wrapper = new LambdaUpdateWrapper<>();
        if (ProductConstants.PRODUCT_DELETE_TYPE_DELETE.equals(type)) {
            wrapper.eq(SohuProduct::getId, id);
            wrapper.set(SohuProduct::getIsDel, true);
            return this.baseMapper.update(new SohuProduct(), wrapper) > 0;
        }
        wrapper.eq(SohuProduct::getId, id);
        wrapper.set(SohuProduct::getIsRecycle, true);
        return this.baseMapper.update(new SohuProduct(), wrapper) > 0;
    }

    @Override
    public boolean reStoreProduct(Long id) {
        LambdaUpdateWrapper<SohuProduct> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SohuProduct::getId, id);
        wrapper.set(SohuProduct::getIsRecycle, false);
        wrapper.set(SohuProduct::getIsShow, false);
        return this.baseMapper.update(new SohuProduct(), wrapper) > 0;
    }

//    @Override
//    public Boolean offOrPutShelf(SohuOffOrPutBo bo) {
//        SohuProduct product = getProduct(bo.getId());
//        // 是否是下架状态判断
//        if (ProductConstants.PRODUCT_TYPE_OFF.equals(bo.getType()) && !product.getIsShow()) {
//            return true;
//        }
//        // 是否是下架状态并且判断是否是已开启分佣状态

    /// /        if (ProductConstants.PRODUCT_TYPE_OFF.equals(bo.getType()) && ProductConstants.PRODUCT_INDEPENDENT_IS_SHOW
    /// /            .equals(product.getIndependentIsShow())) {
    /// /            throw new RuntimeException("已开启分佣状态请先关闭分佣");
    /// /        }
//        // 是否是上架状态判断
//        if (ProductConstants.PRODUCT_TYPE_PUT.equals(bo.getType()) && product.getIsShow()) {
//            return true;
//        }
//        if (ProductConstants.PRODUCT_TYPE_OFF.equals(bo.getType())) {
//            product.setIsShow(false);
//            return transactionTemplate.execute(e -> {
//                baseMapper.updateById(product);
//                // 购物车状态
//                shopCartService.productStatusNotEnable(bo.getId());
//                // 商品下架时，清除用户收藏
//                productRelationService.deleteByProId(product.getId());
//                return Boolean.TRUE;
//            });
//        }
//        if (ProductConstants.PRODUCT_TYPE_PUT.equals(bo.getType())) {
//            // 根据当前操作用户id查询商户
//            SohuMerchantModel sohuMerchantModel = remoteMerchantService.selectById(product.getMerId());
//            if (product.getIsForced() || sohuMerchantModel.getProductSwitch()) {
//                product.setIsShow(false);
//                product.setIsForced(false);
//                product.setAuditStatus(ProductConstants.AUDIT_STATUS_WAIT);
//                return this.baseMapper.updateById(product) > 0;
//            }
//            if (product.getIsShow()) {
//                return true;
//            }
//            // 获取商品skuid
//            List<SohuProductAttrValueVo> skuList = productAttrValueService.getListByProductIdAndType(product.getId(),
//                    ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
//            List<Long> skuIdList = skuList.stream().map(SohuProductAttrValueVo::getId).collect(Collectors.toList());
//            product.setIsShow(true);
//            return transactionTemplate.execute(e -> {
//                baseMapper.updateById(product);
//                if (CollectionUtils.isNotEmpty(skuIdList)) {
//                    // todo 购物车
//                    shopCartService.updateSku(skuIdList);
//                }
//                return Boolean.TRUE;
//            });
//        }
//        return Boolean.FALSE;
//    }
    @Override
    public Boolean offOrPutShelf(SohuOffOrPutBo bo) {
        // 将商品ids拆分成List
        List<Long> productIds = Arrays.stream(bo.getIds().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        Date today = DateUtil.beginOfDay(new Date());
        boolean isExcute = false;
        Map<String, String> productOverviewMap = new HashMap<>();
        for (Long productId : productIds) {
            SohuProduct product = getProduct(productId);
            // 是否是下架状态判断
            if (ProductConstants.PRODUCT_TYPE_OFF.equals(bo.getType()) && !product.getIsShow()) {
                continue;
            }
            // 是否是上架状态判断
            if (ProductConstants.PRODUCT_TYPE_PUT.equals(bo.getType()) && product.getIsShow()) {
                continue;
            }
            //如果上下架时间是当天，说明当天该商品有操作过上下架，统计数量需对应的加1、减1
            if (product.getListingTime() != null && product.getListingTime().after(today)) {
                isExcute = true;
            }
            // 获取素材对象
            SohuIndependentMaterialVo sohuIndependentMaterialVo = remoteMiddleIndependentMaterialService.queryByCodeAndType(productId.toString(), BusyType.Goods.getType());
            if (ProductConstants.PRODUCT_TYPE_OFF.equals(bo.getType())) {
                product.setIsShow(false);
                productOverviewMap.put("product_delisting_num", "+1");
                if (isExcute) {
                    productOverviewMap.put("product_listing_num", "-1");
                }
                product.setListingTime(new Date());
                transactionTemplate.execute(e -> {
                    if (Objects.nonNull(sohuIndependentMaterialVo)) {
                        remoteMiddleIndependentMaterialService.updateByBo(new SohuIndependentMaterialBo()
                                .setId(sohuIndependentMaterialVo.getId())
                                .setStatus(CommonState.OffShelf.getCode())
                        );
                    }
                    remoteMiddleIndependentMaterialService.deleteByCodeAndType(productId.toString(), BusyType.Goods.getType());
                    baseMapper.updateById(product);
                    // 购物车状态
                    shopCartService.productStatusNotEnable(productId);
                    // 商品下架时，清除用户收藏
                    productRelationService.deleteByProId(productId);
                    sohuProductOverviewReportService.syncProductOverviewReport(productOverviewMap);
                    return Boolean.TRUE;
                });
            }

            if (ProductConstants.PRODUCT_TYPE_PUT.equals(bo.getType())) {
                // 根据当前操作用户id查询商户
                SohuMerchantModel sohuMerchantModel = remoteMerchantService.selectById(product.getMerId());
                if (product.getIsForced() || sohuMerchantModel.getProductSwitch()) {
                    product.setIsShow(false);
                    product.setIsForced(false);
                    product.setAuditStatus(ProductConstants.AUDIT_STATUS_WAIT);
                    product.setListingTime(new Date());
                    baseMapper.updateById(product);
                    // 更新数据总览统计
                    productOverviewMap.put("product_listing_num", "+1");
                    if (isExcute) {
                        productOverviewMap.put("product_delisting_num", "-1");
                    }
                    sohuProductOverviewReportService.syncProductOverviewReport(productOverviewMap);
                } else {
                    // 获取商品skuid
                    List<SohuProductAttrValueVo> skuList = productAttrValueService.getListByProductIdAndType(product.getId(),
                            ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
                    List<Long> skuIdList = skuList.stream().map(SohuProductAttrValueVo::getId).collect(Collectors.toList());
                    product.setIsShow(true);
                    product.setListingTime(new Date());
                    productOverviewMap.put("product_listing_num", "+1");
                    if (isExcute) {
                        productOverviewMap.put("product_delisting_num", "-1");
                    }
                    transactionTemplate.execute(e -> {
                        baseMapper.updateById(product);
//                        if (Objects.nonNull(sohuIndependentMaterialVo)) {
//                            remoteMiddleIndependentMaterialService.updateByBo(new SohuIndependentMaterialBo()
//                                    .setId(sohuIndependentMaterialVo.getId())
//                                    .setStatus(CommonState.OnShelf.getCode())
//                            );
//                        }
                        if (CollectionUtils.isNotEmpty(skuIdList)) {
                            // todo 购物车
                            shopCartService.updateSku(skuIdList);
                        }
                        // 更新数据总览统计
                        sohuProductOverviewReportService.syncProductOverviewReport(productOverviewMap);
                        return Boolean.TRUE;
                    });
                }
            }
            // 延迟队列--更新广告缓存信息
            MsgContent msgContent = new MsgContent(productId, product.getIsShow() ? CommonState.OnShelf.getCode() : CommonState.OffShelf.getCode(), BusyType.Goods.name());
            MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
            RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
        }
        return Boolean.TRUE;
    }

    private SohuProduct getProduct(Long bo) {
        SohuProduct product = this.baseMapper.selectById(bo);
        if (ObjectUtil.isNull(product)) {
            throw new RuntimeException("商品不存在");
        }
        return product;
    }

    @Override
    public List<SohuProductTabsVo> getPlatformTabsHeader(String sysSource) {
        // 类型（1：出售中（已上架），2：仓库中（未上架），3：已售罄，4：警戒库存，5：回收站,6:待审核，7：审核失败）
        List<SohuProductTabsVo> headers = new ArrayList<>();
        SohuProductTabsVo sell = new SohuProductTabsVo(0, 1);
        SohuProductTabsVo warehouse = new SohuProductTabsVo(0, 2);
        SohuProductTabsVo waitApprove = new SohuProductTabsVo(0, 6);
        SohuProductTabsVo approveFalse = new SohuProductTabsVo(0, 7);
        headers.add(sell);
        headers.add(warehouse);
        headers.add(waitApprove);
        headers.add(approveFalse);
        // 查询组装
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        for (SohuProductTabsVo h : headers) {
            lqw.clear();
            lqw.eq(SohuProduct::getIsRecycle, false);
            lqw.eq(SohuProduct::getIsDel, false);
            lqw.eq(StringUtils.isNotBlank(sysSource), SohuProduct::getSysSource, sysSource);
            switch (h.getType()) {
                case 1:
                    //出售中（已上架）
                    lqw.eq(SohuProduct::getIsForced, false);
                    lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
                    lqw.eq(SohuProduct::getIsShow, true);
                    break;
                case 2:
                    //仓库中（未上架）
                    lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
                    lqw.eq(SohuProduct::getIsShow, false);
                    break;
                case 6:
                    //待审核
                    lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_WAIT);
                    break;
                case 7:
                    //审核失败
                    lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_FAIL);
                    break;
                default:
                    break;
            }
            List<SohuProduct> storeProducts = this.baseMapper.selectList(lqw);
            h.setCount(storeProducts.size());
        }
        return headers;
    }

    @Override
    public boolean forceDown(SohuOffOrPutBo bo) {
        List<Long> idList = Arrays.stream(bo.getIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        LambdaUpdateWrapper<SohuProduct> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(SohuProduct::getIsForced, true);
        wrapper.set(SohuProduct::getIsShow, false);
        wrapper.set(SohuProduct::getIndependentIsShow, false);
        wrapper.in(SohuProduct::getId, idList);
        boolean update = this.baseMapper.update(new SohuProduct(), wrapper) > 0;
        if (update) {
            idList.forEach(id -> {
                // 购物车状态
                shopCartService.productStatusNotEnable(id);
                // 商品下架时，清除用户收藏
                productRelationService.deleteByProId(id);
                // 延迟队列--更新广告缓存信息
                MsgContent msgContent = new MsgContent(id, CommonState.OffShelf.getCode(), BusyType.Goods.name());
                MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
                RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
            });
        }
        return update;
    }

    @Override
    public Boolean updatePcByBo(SohuPcUpdateBo request) {
        SohuProduct product = getProduct(request.getId());
        // 修改排序
        if (request.getType().equals("sort")) {
            if (request.getNum() < 0 || request.getNum() > 9999) {
                throw new RuntimeException("商品后台排序范围为0~9999");
            }
            if (product.getRanks().equals(request.getNum())) {
                return Boolean.TRUE;
            }
            SohuProduct storeProduct = new SohuProduct();
            storeProduct.setId(request.getId());
            storeProduct.setRanks(request.getNum());
            return this.baseMapper.updateById(storeProduct) > 0;
        } else {
            // 修改虚拟销量
            if (request.getType().equals("sub") && product.getVarSales() - request.getNum() < 0) {
                throw new RuntimeException("修改后虚拟销量不能小于0");
            }
            UpdateWrapper<SohuProduct> wrapper = Wrappers.update();
            if (request.getType().equals("add")) {
                wrapper.setSql(StrUtil.format(" var_sales = var_sales + {}", request.getNum()));
            } else {
                wrapper.setSql(StrUtil.format(" var_sales = var_sales - {}", request.getNum()));
                wrapper.last(StrUtil.format(" and var_sales - {} >= 0", request.getNum()));
            }
            wrapper.eq("id", request.getId());
            return this.baseMapper.update(new SohuProduct(), wrapper) > 0;
        }
    }

    @Override
    public Boolean pcAudit(SohuProductAuditBo request) {
        if (request.getAuditStatus().equals("fail") && StrUtil.isEmpty(request.getReason())) {
            throw new RuntimeException("审核拒绝请填写拒绝原因");
        }
        SohuProduct product = getProduct(request.getId());
        if (!product.getAuditStatus().equals(ProductConstants.AUDIT_STATUS_WAIT)) {
            throw new RuntimeException("商品并非等待审核状态");
        }
        if (request.getAuditStatus().equals("fail")) {
            product.setAuditStatus(ProductConstants.AUDIT_STATUS_FAIL);
            product.setReason(request.getReason());
            return this.baseMapper.updateById(product) > 0;
        }
        // 如果是返哺库商品就 设置兑换商品所积分。商品价格暂时没有做个预留
        List<SohuProductAttrValueVo> storeProductAttrValues;
        if (null != request.getProductType() && request.getProductType() == 1) {
            List<SohuProductAttrValueVo> productAttrValueList = productAttrValueService.getListByProductIdAndType(request.getId(), ProductConstants.AUDIT_STATUS_WAIT);
            if (null != request.getProductPrice()) {
                product.setPrice(request.getProductPrice());
            }
            storeProductAttrValues = productAttrValueList.stream().map(s -> {
                if (null != request.getProductPrice()) {
                    s.setPrice(request.getProductPrice());
                }
                s.setWantIntegral(request.getWantIntegral());
                return s;
            }).collect(Collectors.toList());
            List<SohuProductAttrValue> storeProductAttrValueEntityList = new ArrayList<>();
            BeanCopyUtils.copyList(storeProductAttrValues, SohuProductAttrValue.class);
            productAttrValueService.updateBatchById(storeProductAttrValueEntityList);
        }
        // 审核成功
        product.setAuditStatus(ProductConstants.AUDIT_STATUS_SUCCESS);
        product.setIsForced(false);
        product.setIsShow(true);
        // todo 缓存
        // 审核清除缓存
//        if (product.getAuditStatus().equals(ProductConstants.AUDIT_STATUS_SUCCESS)){
//            redisSync(product.getSiteId());
//        }
        // 审核通过之后进入素材库
        if (product.getIndependentIsShow()) {
            remoteMiddleIndependentMaterialService.insertByBo(buildBo(product.getId()));
        }
        this.baseMapper.updateById(product);
        // 延迟队列--更新广告缓存信息
        MsgContent msgContent = new MsgContent(product.getId(), CommonState.OnShelf.getCode(), BusyType.Goods.name());
        MqMessaging mqMessaging = new MqMessaging(JSONUtil.toJsonStr(msgContent), Constants.ADVERTISEMENT);
        RedisUtils.delayQueue(JSONUtil.toJsonStr(mqMessaging), Constants.AD_DELAY_LONG, TimeUnit.SECONDS);
        return Boolean.TRUE;
    }

    /**
     * 构建Bo
     *
     * @param id 商品主键id
     * @return SohuIndependentMaterialBo
     */
    private SohuIndependentMaterialBo buildBo(Long id) {
        SohuProductVo sohuProductVo = this.queryById(id);
        SohuIndependentMaterialBo independentMaterialBo = new SohuIndependentMaterialBo();
        independentMaterialBo.setMaterialName(sohuProductVo.getStoreName());
        independentMaterialBo.setMaterialType(BusyType.Goods.getType());
        independentMaterialBo.setPrice(sohuProductVo.getPrice());
        independentMaterialBo.setMaterialUserId(sohuProductVo.getMerId());
        independentMaterialBo.setSiteId(sohuProductVo.getSiteId());
        independentMaterialBo.setMaterialCode(sohuProductVo.getId().toString());
        independentMaterialBo.setIndependentPrice(sohuProductVo.getIndependentPrice());
        independentMaterialBo.setCategoryId(sohuProductVo.getCategoryId());
        independentMaterialBo.setStatus(CommonState.OnShelf.getCode());

        return independentMaterialBo;
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> homeIndexPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        // id、名称、图片、价格、销量
        lqw.select(SohuProduct::getId, SohuProduct::getStoreName, SohuProduct::getImage, SohuProduct::getPrice,
                SohuProduct::getSales, SohuProduct::getVarSales, SohuProduct::getUnitName, SohuProduct::getStock,
                SohuProduct::getCurrency, SohuProduct::getIndependentIsShow, SohuProduct::getIndependentPrice,
                SohuProduct::getCateId, SohuProduct::getCategoryId, SohuProduct::getMerId);
        // 获取出售中商品的Where条件
        getForSaleWhere(lqw);
        // 根据site_id筛选商品
        if (null != shopProductReqBo.getSiteId()) {
            lqw.eq(SohuProduct::getSiteId, shopProductReqBo.getSiteId());
        }
        List<Integer> proType = Lists.newArrayList();
        if (null != shopProductReqBo.getProductType()) {
            proType.add(shopProductReqBo.getProductType());
        }
        // 分类
        if (ObjectUtil.isNotNull(shopProductReqBo.getCid()) && shopProductReqBo.getCid() > 0L) {
            lqw.eq(SohuProduct::getCategoryId, shopProductReqBo.getCid());
        }
        // 一级分类
        if (ObjectUtil.isNotNull(shopProductReqBo.getFirstCategoryId()) && shopProductReqBo.getFirstCategoryId() > 0L) {
            lqw.eq(SohuProduct::getFirstCategoryId, shopProductReqBo.getFirstCategoryId());
        }
        // 名称、关键字模糊查询
        if (StrUtil.isNotBlank(shopProductReqBo.getKeyword())) {
            lqw.and(i -> i.like(SohuProduct::getStoreName, shopProductReqBo.getKeyword())
                    .or().like(SohuProduct::getKeyword, shopProductReqBo.getKeyword()));
        }
        // 售价-高
        if (ObjectUtil.isNotNull(shopProductReqBo.getMaxPrice())) {
            lqw.le(SohuProduct::getPrice, shopProductReqBo.getMaxPrice());
        }
        // 售价-低
        if (ObjectUtil.isNotNull(shopProductReqBo.getMinPrice())) {
            lqw.ge(SohuProduct::getPrice, shopProductReqBo.getMinPrice());
        }
        // 是否分销商品
        if (ObjectUtil.isNotNull(shopProductReqBo.getIndependentType()) || (ObjectUtil.isNotNull(shopProductReqBo.getIndependentIsShow()) && shopProductReqBo.getIndependentIsShow())) {
            lqw.eq(SohuProduct::getIndependentType, true);
            lqw.eq(SohuProduct::getIndependentIsShow, 1);
            proType.add(2);
        }
        // 根据productType筛选商品
        // 商品类型条件
        if (CollUtil.isNotEmpty(proType)) {
            lqw.in(SohuProduct::getProductType, proType);
        }
        // 分类查询
        if (CollectionUtils.isNotEmpty(shopProductReqBo.getCategoryIds())) {
            lqw.in(SohuProduct :: getCategoryId, shopProductReqBo.getCategoryIds());
        }
        List<SohuMerchantModel> merchantCloseList = remoteMerchantService.getMerchantOpenList(SysSourceEnum.SOHUGLOBAL.getCode());
        if (CollUtil.isNotEmpty(merchantCloseList)) {
            List<Long> merIds = merchantCloseList.stream().map(SohuMerchantModel::getId).collect(Collectors.toList());
            lqw.in(SohuProduct::getMerId, merIds);
        }
        // 判断分佣状态是否开启
        if (ObjectUtil.isNotNull(shopProductReqBo.getIndependentIsShow())) {
            lqw.eq(SohuProduct::getIndependentIsShow, shopProductReqBo.getIndependentIsShow());
        }
        // 排序部分
        if (StrUtil.isNotBlank(shopProductReqBo.getSalesOrder())) {
            if (shopProductReqBo.getSalesOrder().equals(Constants.SORT_DESC)) {
                lqw.last(" order by (sales + var_sales) desc, `ranks` desc, sort desc, id desc");
            } else {
                lqw.last(" order by (sales + var_sales) asc, `ranks` desc, sort asc, id asc");
            }
        } else {
            if (StrUtil.isNotBlank(shopProductReqBo.getPriceOrder())) {
                if (shopProductReqBo.getPriceOrder().equals(Constants.SORT_DESC)) {
                    lqw.orderByDesc(SohuProduct::getPrice);
                } else {
                    lqw.orderByAsc(SohuProduct::getPrice);
                }
            }

            lqw.orderByDesc(SohuProduct::getRanks);
            lqw.orderByDesc(SohuProduct::getId);
        }
        Page<SohuProductVo> productPage = this.baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        if (ObjectUtil.isNull(productPage) || CollUtil.isEmpty(productPage.getRecords())) {
            return TableDataInfoUtils.build(CollUtil.newArrayList());
        }
        Map<Long, String> merchantMap = merchantCloseList.stream()
                .collect(Collectors.toMap(
                        SohuMerchantModel::getId,
                        SohuMerchantModel::getName
                ));
        productPage.getRecords().forEach(product -> {
            String merchantName = merchantMap.get(product.getMerId());
            if (merchantName != null) {
                product.setMerchantName(merchantName);
            }
        });
        Page<SohuIndexProductModel> productPages = new Page<>();
        BeanUtils.copyProperties(productPage, productPages);
        return TableDataInfoUtils.build(productPages);
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> userShopWindowPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery) {
        HashMap<String, Object> map = new HashMap<>();
        if (null == shopProductReqBo.getUserId()) {
            LoginUser loginUser = LoginHelper.getLoginUser();
            Objects.requireNonNull(loginUser, "您未登录！");
            map.put("userId", loginUser.getUserId());
        } else {
            map.put("userId", shopProductReqBo.getUserId());
        }
        if (StrUtil.isNotEmpty(shopProductReqBo.getKeyword())) {
            map.put("keywords", shopProductReqBo.getKeyword());
        }
        if (null != shopProductReqBo.getSiteId()) {
            map.put("siteId", shopProductReqBo.getSiteId());
        }
        if (null != shopProductReqBo.getProductType()) {
            map.put("productType", shopProductReqBo.getProductType());
        }
        if (null != shopProductReqBo.getIndependentIsShow()) {
            map.put("independentIsShow", shopProductReqBo.getIndependentIsShow());
        }
        if (CollectionUtils.isNotEmpty(shopProductReqBo.getProductIds())) {
            map.put("productIds", shopProductReqBo.getProductIds().stream().collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotEmpty(shopProductReqBo.getSysSource())) {
            map.put("sysSource", shopProductReqBo.getSysSource());
        }
        // 排序部分
        if (StrUtil.isNotBlank(shopProductReqBo.getSalesOrder())) {
            map.put("salesOrder", shopProductReqBo.getSalesOrder());
        } else {
            if (StrUtil.isNotBlank(shopProductReqBo.getPriceOrder())) {
                map.put("price", shopProductReqBo.getPriceOrder());
            }
        }
        if (map.size() == 3 || (StrUtil.isBlank(shopProductReqBo.getSalesOrder()) && StrUtil.isBlank(shopProductReqBo.getPriceOrder()))) {
            map.put("sort", StringUtils.isBlank(shopProductReqBo.getSort()) ? "desc" : shopProductReqBo.getSort());
        }
        Page<SohuProduct> productPage = this.baseMapper.getUserShopWindowPage(map, PageQueryUtils.build(pageQuery));
        if (ObjectUtil.isNull(productPage) || CollUtil.isEmpty(productPage.getRecords())) {
            return TableDataInfoUtils.build(CollUtil.newArrayList());
        }
        Page<SohuIndexProductModel> productPages = new Page<>();
        BeanUtils.copyProperties(productPage, productPages);
        return TableDataInfoUtils.build(productPages);
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> mcnShopWindowPage(McnShopProductReqBo bo, PageQuery pageQuery) {
        Map<String, Object> map = new HashMap<>();
        if (StrUtil.isNotEmpty(bo.getProductName())) {
            map.put("productName", bo.getProductName());
        }
        if (Objects.nonNull(bo.getCategoryId())) {
            map.put("categoryId", bo.getCategoryId());
        }
        if (Objects.nonNull(bo.getMaxRatio())) {
            map.put("maxRatio", bo.getMaxRatio());
        }
        if (Objects.nonNull(bo.getMinRatio())) {
            map.put("minRatio", bo.getMinRatio());
        }
        if (Objects.nonNull(bo.getHasCommission())) {
            if (bo.getHasCommission()) {
                if (Objects.isNull(bo.getMaxRatio()) && Objects.isNull(bo.getMinRatio())) {
                    map.put("minRatio", BigDecimal.ZERO);
                }
            } else {
                map.put("independentRatio", BigDecimal.ZERO);
            }
        }
        if (StrUtil.isNotEmpty(bo.getSysSource())) {
            map.put("sysSource", bo.getSysSource());
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, "您未登录！");
        map.put("mcnId", loginUser.getUserId());
        Page<SohuProduct> productPage = this.baseMapper.getMcnShopWindowPage(map, PageQueryUtils.build(pageQuery));
        if (ObjectUtil.isNull(productPage) || CollUtil.isEmpty(productPage.getRecords())) {
            return TableDataInfoUtils.build(CollUtil.newArrayList());
        }
        Page<SohuIndexProductModel> newProductPages = new Page<>();
        BeanUtils.copyProperties(productPage, newProductPages);
        List<SohuIndexProductModel> modelList = new ArrayList<>();
        newProductPages.setRecords(modelList);
        List<Long> merIdList = productPage.getRecords().stream().map(SohuProduct::getMerId).distinct().collect(Collectors.toList());
        Map<Long, SohuMerchantModel> merchantMap = this.remoteMerchantService.getMerIdMapByIdList(merIdList);
        for (SohuProduct product : productPage.getRecords()) {
            SohuIndexProductModel model = BeanUtil.copyProperties(product, SohuIndexProductModel.class);
            SohuMerchantModel merchant = merchantMap.get(model.getMerId());
            if (Objects.nonNull(merchant)) {
                model.setMerCategoryName(merchant.getCategoryName());
                model.setMerName(merchant.getName());
            }
            modelList.add(model);
        }
        return TableDataInfoUtils.build(newProductPages);
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> userShopWindowIndependentPage(ShopProductReqBo shopProductReqBo, PageQuery pageQuery) {
        HashMap<String, Object> map = new HashMap<>();
        this.setMcnQueryParam(map);
        if (null == shopProductReqBo.getUserId()) {
            LoginUser loginUser = LoginHelper.getLoginUser();
            Objects.requireNonNull(loginUser, "您未登录！");
            map.put("userId", loginUser.getUserId());
        } else {
            map.put("userId", shopProductReqBo.getUserId());
        }
        if (StrUtil.isNotEmpty(shopProductReqBo.getKeyword())) {
            map.put("keywords", shopProductReqBo.getKeyword());
        }
        if (null != shopProductReqBo.getSiteId()) {
            map.put("siteId", shopProductReqBo.getSiteId());
        }
        if (null != shopProductReqBo.getProductType()) {
            map.put("productType", shopProductReqBo.getProductType());
        }
        if (null != shopProductReqBo.getIndependentIsShow()) {
            map.put("independentIsShow", shopProductReqBo.getIndependentIsShow());
        }
        if (CollectionUtils.isNotEmpty(shopProductReqBo.getProductIds())) {
            map.put("productIds", shopProductReqBo.getProductIds());
        }
        if (StrUtil.isNotEmpty(shopProductReqBo.getSysSource())) {
            map.put("sysSource", shopProductReqBo.getSysSource());
        }
        // 排序部分
        if (StrUtil.isNotBlank(shopProductReqBo.getSalesOrder())) {
            map.put("salesOrder", shopProductReqBo.getSalesOrder());
            map.put("sort", shopProductReqBo.getSalesOrder());
        } else {
            if (StrUtil.isNotBlank(shopProductReqBo.getPriceOrder())) {
                map.put("price", shopProductReqBo.getPriceOrder());
                map.put("sort", shopProductReqBo.getSalesOrder());
            }
        }
        if (map.size() == 4 || (StrUtil.isBlank(shopProductReqBo.getSalesOrder()) && StrUtil.isBlank(shopProductReqBo.getPriceOrder()))) {
            map.put("sort", StringUtils.isBlank(shopProductReqBo.getSort()) ? "desc" : shopProductReqBo.getSort());
        }
        Page<SohuProduct> productPage = this.baseMapper.getUserShopWindowIndependentPage(map, PageQueryUtils.build(pageQuery));
        if (ObjectUtil.isNull(productPage) || CollUtil.isEmpty(productPage.getRecords())) {
            return TableDataInfoUtils.build(CollUtil.newArrayList());
        }
        Page<SohuIndexProductModel> productPages = new Page<>();
        BeanUtils.copyProperties(productPage, productPages);
        return TableDataInfoUtils.build(productPages);
    }

    /**
     * 设置MCN请求参数
     *
     * @param map
     */
    private void setMcnQueryParam(HashMap<String, Object> map) {
        //如果当前用户加入了mcn机构，那么数据需与mcn机构有关联
        SohuMcnUserVo model = remoteMcnUserService.getInfoByUserId(LoginHelper.getUserId());
        if (Objects.nonNull(model)) {
            map.put("mcnId", model.getMcnUserId());
            //带货过期时间
            map.put("expirationTime", new Date());
        }
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> mcnShopWindowIndependentPage(McnShopProductReqBo bo, PageQuery pageQuery) {
        Map<String, Object> map = new HashMap<>();
        if (StrUtil.isNotEmpty(bo.getProductName())) {
            map.put("productName", bo.getProductName());
        }
        if (Objects.nonNull(bo.getCategoryId())) {
            map.put("categoryId", bo.getCategoryId());
        }
        if (Objects.nonNull(bo.getMaxRatio())) {
            map.put("maxRatio", bo.getMaxRatio());
        }
        if (Objects.nonNull(bo.getMinRatio())) {
            map.put("minRatio", bo.getMinRatio());
        }
        if (Objects.nonNull(bo.getHasCommission())) {
            if (bo.getHasCommission()) {
                if (Objects.isNull(bo.getMaxRatio()) && Objects.isNull(bo.getMinRatio())) {
                    map.put("minRatio", BigDecimal.ZERO);
                }
            } else {
                map.put("independentRatio", BigDecimal.ZERO);
            }
        }
        if (StrUtil.isNotEmpty(bo.getSysSource())) {
            map.put("sysSource", bo.getSysSource());
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        Objects.requireNonNull(loginUser, "您未登录！");
        map.put("mcnId", loginUser.getUserId());
        Page<SohuProduct> productPage = this.baseMapper.getMcnShopWindowIndependentPage(map, PageQueryUtils.build(pageQuery));
        if (ObjectUtil.isNull(productPage) || CollUtil.isEmpty(productPage.getRecords())) {
            return TableDataInfoUtils.build(CollUtil.newArrayList());
        }
        Page<SohuIndexProductModel> newProductPages = new Page<>();
        BeanUtils.copyProperties(productPage, newProductPages);
        List<SohuIndexProductModel> modelList = new ArrayList<>();
        newProductPages.setRecords(modelList);
        List<Long> merIdList = productPage.getRecords().stream().map(SohuProduct::getMerId).distinct().collect(Collectors.toList());
        Map<Long, SohuMerchantModel> merchantMap = getMerIdMapByIdList(merIdList);
        for (SohuProduct product : productPage.getRecords()) {
            SohuIndexProductModel model = BeanUtil.copyProperties(product, SohuIndexProductModel.class);
            SohuMerchantModel merchant = merchantMap.get(product.getMerId());
            if (Objects.nonNull(merchant)) {
                model.setMerCategoryName(merchant.getCategoryName());
                model.setMerName(merchant.getName());
            }
            modelList.add(model);
        }
        return TableDataInfoUtils.build(newProductPages);
    }

    /**
     * 获取所有店铺id根据店铺id做key管理店铺信息
     *
     * @param merIdList
     */
    private Map<Long, SohuMerchantModel> getMerIdMapByIdList(List<Long> merIdList) {
        return this.remoteMerchantService.getMerIdMapByIdList(merIdList);
    }

    @Override
    public SohuIndexProductInfoModel getMcnShopWindowInfo(Long id, Boolean isIndependent) {
        SohuIndexProductInfoModel productDetailResponse = new SohuIndexProductInfoModel();
        // 查询商品
        SohuProduct storeProduct = this.getMoveInfo(id);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new RuntimeException("未找到对应商品信息");
        }
        SohuProductModel sohuProductModel = new SohuProductModel();
        BeanUtils.copyProperties(storeProduct, sohuProductModel);
        productDetailResponse.setProductInfo(sohuProductModel);
        // 商品描述
        SohuProductDescription sd = productDescriptionService.getByProductIdAndType(storeProduct.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        if (ObjectUtil.isNotNull(sd)) {
            storeProduct.setContent(ObjectUtil.isNull(sd.getDescription()) ? "" : sd.getDescription());
        }
        // 获取商户信息
        SohuMerchantModel sohuMerchantModel = remoteMerchantService.selectById(storeProduct.getMerId());
        productDetailResponse.setMerchantStatus(sohuMerchantModel.getIsSwitch());
        SohuAppProductMerchantModel merchantResponse = new SohuAppProductMerchantModel();
        BeanUtils.copyProperties(sohuMerchantModel, merchantResponse);
        // 获取商户推荐商品
        List<SohuIndexProductModel> merchantProductResponseList = getRecommendedProductsByMerId(sohuMerchantModel.getId(), 4, isIndependent);
        merchantResponse.setProList(merchantProductResponseList);
        merchantResponse.setIsCollect(false);
        productDetailResponse.setMerchantInfo(merchantResponse);
        return productDetailResponse;
    }

    @Override
    public SohuProductModel queryProductById(Long id) {
        SohuProductVo storeProduct = baseMapper.selectVoById(id);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new RuntimeException("未找到对应商品信息");
        }
        return BeanUtil.copyProperties(storeProduct, SohuProductModel.class);
    }

    @Override
    public List<SohuProductSyncVo> queryList() {
        List<SohuProductVo> list = baseMapper.selectVoList(new QueryWrapper<>());
        return BeanCopyUtils.copyList(list, SohuProductSyncVo.class);
    }

    @Override
    public Boolean updateBatchById(List<SohuProductSyncBo> productList) {
        return baseMapper.updateBatchById(BeanCopyUtils.copyList(productList, SohuProduct.class));
    }

    @Override
    public int syncFirstCategoryToProduct() {
        List<SohuProductSyncVo> list = this.queryList();
        list.forEach(f -> {
            Long firstLevel = getFirstLevel(f.getCategoryId());
            f.setFirstCategoryId(firstLevel);
        });
        this.updateBatchById(BeanCopyUtils.copyList(list, SohuProductSyncBo.class));
        return list.size();

    }

    @Override
    public TableDataInfo<SohuProductPlayletListVo> shopCartPlayletList(SohuPlayletShopCartQueryBo bo, PageQuery pageQuery) {
        Page<SohuProductPlayletListVo> productPlayletListVoPage = baseMapper.selectProducPlayletList(bo, PageQueryUtils.build(pageQuery));
        return TableDataInfoUtils.build(productPlayletListVoPage);
    }

    @Override
    public TableDataInfo<SohuProductVo> shopCartList(Long videoId, PageQuery pageQuery) {
        SohuPlayletCartBo cartBo = new SohuPlayletCartBo();
        cartBo.setState(CommonState.OnShelf.getCode());
        cartBo.setVideoId(videoId);
        List<SohuProductVo> products = new ArrayList<>();
        TableDataInfo<SohuPlayletCartVo> playletCartVoPage = remoteMiddlePlayletCartService.queryShopCartList(cartBo, pageQuery);
        List<SohuPlayletCartVo> playletCartVos = playletCartVoPage.getData();
        for (SohuPlayletCartVo playletCartVo : playletCartVos) {
            Long relevanceId = playletCartVo.getRelevanceId();
            SohuProductVo sohuProductVo = queryById(relevanceId);
            products.add(sohuProductVo);
        }
        TableDataInfo<SohuProductVo> vos = new TableDataInfo();
        vos.setTotal(playletCartVoPage.getTotal());
        vos.setData(products);
        return vos;
    }

    @Override
    public Boolean operateBatchPlaylet(ShopOperateBatchBo playletOperateBo) {
        //查询所有商品
        List<SohuProduct> sohuProducts = baseMapper.selectList();
        List<Long> productIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(sohuProducts)) {
            for (SohuProduct sohuProduct : sohuProducts) {
                productIds.add(sohuProduct.getId());
            }
            Long[] playletIds = playletOperateBo.getPlayletIds();
            remoteMiddlePlayletCartService.operateBatchPlaylet(productIds, playletIds, playletOperateBo.getState());
        }
        //将上架标识在redis中
        if (playletOperateBo.getPlayletIds().length == 1) {
            Long[] playletIds = playletOperateBo.getPlayletIds();
            String state = RedisUtils.getCacheObject(CacheConstants.PLAYLET_CART_STATE + +playletIds[0]);
            if (StrUtil.isNotBlank(state)) {
                RedisUtils.deleteObject(CacheConstants.PLAYLET_CART_STATE + +playletIds[0]);
            }
            RedisUtils.setCacheObject(CacheConstants.PLAYLET_CART_STATE + playletIds[0], playletOperateBo.getState());
        }
        return true;
    }

    private Long getFirstLevel(Long categoryId) {
        Long firstCategoryId = 0L;
        SohuProductCategoryPcVo categoryPcVo = productCategoryPcService.queryById(categoryId);
        if (Objects.nonNull(categoryPcVo)) {
            if (0 == categoryPcVo.getPid()) {
                firstCategoryId = categoryPcVo.getId();
            } else {
                firstCategoryId = getFirstLevel(categoryPcVo.getPid());
            }
        }
        return firstCategoryId;
    }

    @Override
    public SohuIndexProductInfoModel getInfo(Long id, Boolean isIndependent) {
        SohuIndexProductInfoModel productDetailResponse = new SohuIndexProductInfoModel();
        // 查询商品
        SohuProduct storeProduct = this.getMoveInfo(id);
        SohuProductModel sohuProductModel = new SohuProductModel();
        BeanUtils.copyProperties(storeProduct, sohuProductModel);
        productDetailResponse.setProductInfo(sohuProductModel);
        if (StrUtil.isNotBlank(storeProduct.getGuaranteeIds())) {
            List<SohuProductGuaranteeModel> guaranteeModelList = Lists.newArrayList();
            // 查询商品保障服务
            LambdaQueryWrapper<SohuProductGuarantee> lqw = Wrappers.lambdaQuery();
            lqw.eq(SohuProductGuarantee::getIsDel, false);
            lqw.eq(SohuProductGuarantee::getIsShow, true);
            lqw.in(SohuProductGuarantee::getId, Arrays.stream(sohuProductModel
                    .getGuaranteeIds().split(",")).map(Long::valueOf).collect(Collectors.toList()));
            List<SohuProductGuarantee> guaranteeList = guaranteeMapper.selectList(lqw);
            for (SohuProductGuarantee guarantee : guaranteeList) {
                SohuProductGuaranteeModel sohuProductGuaranteeModel = new SohuProductGuaranteeModel();
                BeanUtils.copyProperties(guarantee, sohuProductGuaranteeModel);
                guaranteeModelList.add(sohuProductGuaranteeModel);
            }
            productDetailResponse.setGuaranteeList(guaranteeModelList);
        }
        // 获取商品规格
        List<SohuProductAttrVo> attrList = productAttrService.getListByProductIdAndType(storeProduct.getId(),
                ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        // 根据制式设置attr属性
        List<SohuProductAttrVo> productAttr = Lists.newArrayList();
        if (CollectionUtils.isEmpty(productAttr)) {
            for (SohuProductAttrVo sohuProductAttr : attrList) {
                productAttr.add(sohuProductAttr);
            }
        }
        productDetailResponse.setProductAttr(productAttr);
        // 根据制式设置sku属性
        HashMap<String, Object> skuMap = new HashMap<>();
        List<SohuProductAttrValueVo> storeProductAttrValues = productAttrValueService
                .getListByProductIdAndType(storeProduct.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        for (SohuProductAttrValueVo storeProductAttrValue : storeProductAttrValues) {
            skuMap.put(storeProductAttrValue.getSku(), storeProductAttrValue);
        }
        productDetailResponse.setProductValue(skuMap);
        // 获取商户信息
        SohuMerchantModel sohuMerchantModel = remoteMerchantService.selectById(storeProduct.getMerId());
        productDetailResponse.setMerchantStatus(sohuMerchantModel.getIsSwitch());
        SohuAppProductMerchantModel merchantResponse = new SohuAppProductMerchantModel();
        BeanUtils.copyProperties(sohuMerchantModel, merchantResponse);

        LoginUser merchantUser = remoteUserService.queryById(sohuMerchantModel.getUserId());
        UserBaseVo userBaseVo = UserBaseVo.builder().userId(merchantUser.getUserId()).
                userName(StrUtil.isBlankIfStr(merchantUser.getNickname()) ? merchantUser.getUsername() : merchantUser.getNickname()).
                userAvatar(StrUtil.isBlankIfStr(merchantUser.getAvatar()) ? Constants.DEFAULT_USER_AVATAR : merchantUser.getAvatar()).build();
        // 设置商家用户信息
        productDetailResponse.setUser(userBaseVo);

        // 获取商户推荐商品
        List<SohuIndexProductModel> merchantProductResponseList = getRecommendedProductsByMerId(sohuMerchantModel.getId(), 4, isIndependent);
        merchantResponse.setProList(merchantProductResponseList);
        merchantResponse.setIsCollect(false);
        // 获取用户
        Long userId = LoginHelper.getUserId();
        // 用户收藏
        if (ObjectUtil.isNotNull(userId)) {
            // 查询用户是否收藏收藏
            productDetailResponse.setUserCollect(productRelationService.existCollectByUser(userId, storeProduct.getId()));
            // todo 商户是否被关注
//            merchantResponse.setIsCollect(userMerchantCollectService.isCollect(user.getUid(), merchant.getId()));
        } else {
            productDetailResponse.setUserCollect(false);
        }
        // 商户相关信息
        productDetailResponse.setMerchantInfo(merchantResponse);
        //查询该商品是否无法销售
        long secondCateId = productCategoryPcService.getSecondCateByCateId(storeProduct.getCategoryId());
        boolean isSale = remoteMerchantBondService.checkMerchantBondPayStatus(storeProduct.getMerId(), secondCateId);
        productDetailResponse.setIsSale(isSale);
        return productDetailResponse;
    }

    /**
     * 获取商户推荐商品
     *
     * @param merId
     * @param num
     * @param isIndependent
     * @return List
     */
    private List<SohuIndexProductModel> getRecommendedProductsByMerId(Long merId, Integer num, Boolean isIndependent) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProduct::getId, SohuProduct::getMerId, SohuProduct::getImage, SohuProduct::getStoreName,
                SohuProduct::getPrice, SohuProduct::getSales, SohuProduct::getVarSales, SohuProduct::getIndependentRatio,
                SohuProduct::getIndependentIsShow, SohuProduct::getIndependentPrice);
        lqw.eq(SohuProduct::getMerId, merId);
        lqw.eq(SohuProduct::getIsRecycle, false);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getIsShow, true);
        lqw.eq(SohuProduct::getIsForced, false);
        // 分销商品分离推荐-没有传就不做校验
        if (null != isIndependent) {
            lqw.eq(SohuProduct::getIndependentIsShow, isIndependent);
        }
        lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
        lqw.orderByDesc(SohuProduct::getSort);
        lqw.last("limit " + num);
        List<SohuProduct> productList = this.baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(productList)) {
            return CollUtil.newArrayList();
        }
        return productList.stream().map(product -> {
            SohuIndexProductModel response = new SohuIndexProductModel();
            BeanUtils.copyProperties(product, response);
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 获取商户推荐商品
     *
     * @param num
     * @param merIds
     * @return List
     */
    private Map<Long, List<SohuIndexProductModel>> getRecommendedProductsByMerId(Integer num, List<Long> merIds) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProduct::getId, SohuProduct::getMerId, SohuProduct::getImage, SohuProduct::getStoreName,
                SohuProduct::getPrice, SohuProduct::getSales, SohuProduct::getVarSales, SohuProduct::getIndependentPrice,
                SohuProduct::getIndependentIsShow, SohuProduct::getIndependentRatio);
        if (CollectionUtils.isNotEmpty(merIds)) {
            lqw.in(SohuProduct::getMerId, merIds);
        }
        lqw.eq(SohuProduct::getIsRecycle, false);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getIsShow, true);
        lqw.eq(SohuProduct::getIsForced, false);
        // 分销商品分离推荐
        lqw.eq(SohuProduct::getIndependentIsShow, true);
        lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
        lqw.orderByDesc(SohuProduct::getSort);
        lqw.last("limit " + num);
        List<SohuProduct> productList = this.baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(productList)) {
            return new HashMap<>();
        }
        // 获取所有有数据的商户ids
        List<Long> ids = productList.stream().map(SohuProduct::getMerId).collect(Collectors.toList());
        Map<Long, List<SohuIndexProductModel>> indexProductModelList = new HashMap<>();
        Map<Long, List<SohuProduct>> merIdToProducts = new HashMap<>();
        for (SohuProduct product : productList) {
            Long merchantId = product.getMerId();
            List<SohuProduct> products = merIdToProducts.getOrDefault(merchantId, new ArrayList<>());
            products.add(product);
            merIdToProducts.put(merchantId, products);
        }
        // 获取 merId 的商品列表
        for (Long id : ids) {
            List<SohuProduct> productsWithMerId = merIdToProducts.get(id);
            // 将它们转换为 SohuIndexProductModel
            List<SohuIndexProductModel> result = productsWithMerId.stream().map(product -> {
                SohuIndexProductModel response = new SohuIndexProductModel();
                BeanUtils.copyProperties(product, response);
                return response;
            }).collect(Collectors.toList());
            indexProductModelList.put(id, result);
        }
        return indexProductModelList;
    }


    @Override
    public SohuProductReplyCountModel getReplyCount(Long id) {
        Map<String, Object> mapCount = productReplyService.getCount(id);
        if (CollectionUtils.isEmpty(mapCount)) {
            return new SohuProductReplyCountModel();
        }
        return JSONObject.parseObject(JSON.toJSONString(mapCount), SohuProductReplyCountModel.class);
    }

    @Override
    public SohuProductInfoModel getSkuInfo(Long id) {
        SohuProductInfoModel productInfoModel = new SohuProductInfoModel();
        // 查询商品
        SohuProduct sohuProduct = this.getMoveInfo(id);
        // 获取商品规格,根据制式设置attr属性
        List<SohuProductAttrModel> productAttr = Lists.newArrayList();
        BeanUtils.copyProperties(productAttrService.getListByProductIdAndType(sohuProduct.getId(),
                ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS), productAttr);
        productInfoModel.setProductAttr(productAttr);
        // 根据制式设置sku属性
        HashMap<String, Object> skuMap = new HashMap<>();
        List<SohuProductAttrValueVo> storeProductAttrValues = productAttrValueService
                .getListByProductIdAndType(sohuProduct.getId(),
                        ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        for (SohuProductAttrValueVo storeProductAttrValue : storeProductAttrValues) {
            skuMap.put(storeProductAttrValue.getSku(), storeProductAttrValue);
        }
        productInfoModel.setProductValue(skuMap);
        return productInfoModel;
    }

    @Override
    public List<SohuProductModel> getLevel() {
        QueryWrapper<SohuProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "store_name", "image", "price", "ot_price", "(sales + var_sales) as sales");
        queryWrapper.eq("is_show", true);
        queryWrapper.eq("is_recycle", false);
        queryWrapper.eq("is_del", false);
        queryWrapper.eq("is_forced", false);
        queryWrapper.eq("audit_status", ProductConstants.AUDIT_STATUS_SUCCESS);
        queryWrapper.orderByDesc("sales");
        queryWrapper.last("limit 20");
        List<SohuProductModel> productModelList = Lists.newArrayList();
        BeanUtils.copyProperties(this.baseMapper.selectList(queryWrapper), productModelList);
        return productModelList;
    }

    @Override
    public List<SohuProduct> listByIds(List<Long> productIds) {
        LambdaQueryWrapper<SohuProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SohuProduct::getId, productIds);
        return this.baseMapper.selectList(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean operationStock(Long productId, Integer num, String type) {
        SohuProduct product = getById(productId);
        if (Objects.isNull(product)) {
            throw new ServiceException("商品不存在");
        }
        int stockNum = product.getStock();
        UpdateWrapper<SohuProduct> updateWrapper = new UpdateWrapper<>();
        if (type.equals("add")) {
            updateWrapper.setSql(StrUtil.format("stock = stock + {}", num));
            updateWrapper.setSql(StrUtil.format("sales = sales - {}", num));
            stockNum += num;
        }
        if (type.equals("sub")) {
            updateWrapper.setSql(StrUtil.format("stock = stock - {}", num));
            updateWrapper.setSql(StrUtil.format("sales = sales + {}", num));
            // 扣减时加乐观锁保证库存不为负
            updateWrapper.last(StrUtil.format(" and (stock - {} >= 0)", num));
            stockNum -= num;
        }
        updateWrapper.eq("id", productId);
        boolean update = update(updateWrapper);
        if (!update) {
            throw new ServiceException("更新普通商品库存失败,商品id = " + productId);
        }
        //统计商品总览统计
        if (stockNum < 5) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
            //如果存在说明当天该商品库存数量已经累加
            if (RedisUtils.setObjectIfAbsent(ProductConstants.PRODUCT_STOCK_OVERVIEW + product.getId(), stockNum, Duration.between(now, endOfDay))) {
                Map<String, String> upMap = new HashMap<>();
                upMap.put("product_stock_num", "+1");
                sohuProductOverviewReportService.syncProductOverviewReport(upMap);
            }
        } else {
            //如果当天该商品补充库存且库存数量大于5，则对应库存数量减1
            Object object = RedisUtils.getCacheObject(ProductConstants.PRODUCT_STOCK_OVERVIEW + product.getId());
            if (Objects.nonNull(object)) {
                Map<String, String> upMap = new HashMap<>();
                upMap.put("product_stock_num", "-1");
                sohuProductOverviewReportService.syncProductOverviewReport(upMap);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public TableDataInfo<SohuIndexProductModel> getMerchantProductList(ShopProductReqBo request, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        // id、名称、图片、价格、销量
        lqw.select(SohuProduct::getId, SohuProduct::getStoreName, SohuProduct::getImage, SohuProduct::getPrice,
                SohuProduct::getOtPrice, SohuProduct::getSales, SohuProduct::getVarSales, SohuProduct::getUnitName,
                SohuProduct::getStock, SohuProduct::getIndependentPrice, SohuProduct::getIndependentIsShow);
        getForSaleWhere(lqw);
        lqw.eq(SohuProduct::getMerId, request.getMerId());
        if (StrUtil.isNotBlank(request.getKeyword())) {
            lqw.and(i -> i.like(SohuProduct::getStoreName, request.getKeyword())
                    .or().like(SohuProduct::getKeyword, request.getKeyword()));
        }
        if (ObjectUtil.isNotNull(request.getCid()) && request.getCid() > 0) {
            lqw.apply(StrUtil.format(" find_in_set({}, cate_id)", request.getCid()));
        }
        if (ObjectUtil.isNotNull(request.getSiteId())) {
            lqw.eq(SohuProduct::getSiteId, request.getSiteId());
        }
        if (ObjectUtil.isNotNull(request.getMaxPrice())) {
            lqw.le(SohuProduct::getPrice, request.getMaxPrice());
        }
        if (ObjectUtil.isNotNull(request.getMinPrice())) {
            lqw.ge(SohuProduct::getPrice, request.getMinPrice());
        }
        // 判断分佣状态是否开启
        if (ObjectUtil.isNotNull(request.getIndependentIsShow())) {
            lqw.eq(SohuProduct::getIndependentIsShow, request.getIndependentIsShow());
        }
        // 排序部分
        if (StrUtil.isNotBlank(request.getSalesOrder())) {
            if (request.getSalesOrder().equals(Constants.SORT_DESC)) {
                lqw.last(" order by (sales + var_sales) desc, sort desc, id desc");
            } else {
                lqw.last(" order by (sales + var_sales) asc, sort desc, id desc");
            }
        } else {
            if (StrUtil.isNotBlank(request.getPriceOrder())) {
                if (request.getPriceOrder().equals(Constants.SORT_DESC)) {
                    lqw.orderByDesc(SohuProduct::getPrice);
                } else {
                    lqw.orderByAsc(SohuProduct::getPrice);
                }
            }

            lqw.orderByDesc(SohuProduct::getSort);
            lqw.orderByDesc(SohuProduct::getId);
        }
        Page<SohuProductVo> productVoIPage = this.baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        Page<SohuIndexProductModel> productPages = new Page<>();
        BeanUtils.copyProperties(productVoIPage, productPages);
        return TableDataInfoUtils.build(productPages);
    }

    /**
     * 获取移动端商品详情
     *
     * @param id 商品id
     * @return StoreProduct
     */
    public SohuProduct getMoveInfo(Long id) {
        LambdaQueryWrapper<SohuProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(SohuProduct::getId, SohuProduct::getMerId, SohuProduct::getImage, SohuProduct::getStoreName,
                SohuProduct::getSliderImage, SohuProduct::getOtPrice, SohuProduct::getStock, SohuProduct::getSales,
                SohuProduct::getPrice, SohuProduct::getVarSales, SohuProduct::getStoreInfo, SohuProduct::getBrowse,
                SohuProduct::getUnitName, SohuProduct::getGuaranteeIds, SohuProduct::getIndependentRatio,
                SohuProduct::getIndependentIsShow, SohuProduct::getIndependentPrice, SohuProduct::getCategoryId);
        lqw.eq(SohuProduct::getId, id);
        lqw.eq(SohuProduct::getIsRecycle, false);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getIsShow, true);
        lqw.eq(SohuProduct::getIsForced, false);
        lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
        SohuProduct storeProduct = this.baseMapper.selectOne(lqw);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new RuntimeException(StrUtil.format("Item with id {} not found", id));
        }

        SohuProductDescription sd = productDescriptionService.getOne(
                new LambdaQueryWrapper<SohuProductDescription>()
                        .eq(SohuProductDescription::getProductId, storeProduct.getId())
                        .eq(SohuProductDescription::getType, ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS));
        if (ObjectUtil.isNotNull(sd)) {
            storeProduct.setContent(StrUtil.isBlank(sd.getDescription()) ? "" : sd.getDescription());
        }
        return storeProduct;
    }

    /**
     * 获取出售中商品的Where条件
     */
    private void getForSaleWhere(LambdaQueryWrapper<SohuProduct> lqw) {
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getIsRecycle, false);
        lqw.eq(SohuProduct::getIsShow, true);
        lqw.eq(SohuProduct::getIsForced, false);
        lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
    }

    @Override
    public Boolean forcedRemovalAll(Long id, Long siteId) {
        UpdateWrapper<SohuProduct> wrapper = Wrappers.update();
        wrapper.set("is_show", 0);
        wrapper.set("is_forced", 1);
        wrapper.set("independent_is_show", 0);
        wrapper.eq("mer_id", id);
        wrapper.ne("audit_status", ProductConstants.AUDIT_STATUS_FAIL);
        boolean update = update(wrapper);
        if (!update) {
            return update;
        }
        LambdaQueryWrapper<SohuProduct> query = Wrappers.lambdaQuery();
        query.select(SohuProduct::getId);
        query.eq(SohuProduct::getMerId, id);
        query.eq(SohuProduct::getIsDel, false);
        List<SohuProduct> productList = baseMapper.selectList(query);
        productList.forEach(product -> {
            shopCartService.productStatusNotEnable(product.getId());
            // 商品下架时，清除用户收藏
            // storeProductRelationService.deleteByProId(product.getId());
        });
        // redisSync(siteId);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean independentIsShow(SohuProductBo bo) {
        if (bo.getIsShow()) {
            throw new RuntimeException("请先下架商品，再进行修改");
        }
        // 商品分销人分佣金额相关
        if (CollectionUtils.isEmpty(bo.getIds())) {
            throw new RuntimeException("参数为空");
        }
        List<SohuProduct> productList = this.listByIds(bo.getIds());
        if (bo.getIndependentIsShow() && ObjectUtil.isNotNull(bo.getIndependentRatio())) {
            SohuIndependentTemplateModel templateModel = remoteTemplateService.queryByIdAndType(bo.getSiteId(), 1);
            List<SohuProduct> products = Lists.newArrayList();
            for (SohuProduct product : productList) {
                if (bo.getIndependentRatio().compareTo(product.getIndependentRatio()) != 0) {
                    // 根据城市站点、商品分账模板类型 查询分账模板
                    BigDecimal independentUserPrice = getIndependentUserPrice(product.getPrice(), bo.getIndependentRatio(), templateModel);
                    product.setIndependentPrice(independentUserPrice);
                    product.setIndependentRatio(bo.getIndependentRatio());
                    product.setIndependentIsShow(bo.getIndependentIsShow());
                    products.add(product);
                }
            }
            return !CollectionUtils.isNotEmpty(products) || this.baseMapper.updateBatchById(products);
        }
        productList.forEach(product -> product.setIndependentIsShow(bo.getIndependentIsShow()));
        return this.baseMapper.updateBatchById(productList);
    }

    @Override
    public TableDataInfo<SohuAppProductMerchantModel> getMerchantIndependentProductList(ShopProductReqBo productReqBo, PageQuery pageQuery) {
        // 获取商户信息
        TableDataInfo<SohuMerchantModel> merchantModelTableDataInfo = remoteMerchantService.pageBySiteId(StringUtils
                .isNotBlank(productReqBo.getKeyword()) ? productReqBo.getKeyword() : null, StringUtils
                .isNotBlank(productReqBo.getSalesOrder()) ? productReqBo.getSalesOrder() : null, null, productReqBo.getSiteId(), pageQuery);
        List<SohuMerchantModel> data1 = merchantModelTableDataInfo.getData();
        TableDataInfo<SohuAppProductMerchantModel> merchantResponse = TableDataInfoUtils.build();
        // 数据转换
        List<SohuAppProductMerchantModel> data = data1.stream().map(e -> {
            SohuAppProductMerchantModel valueResponse = new SohuAppProductMerchantModel();
            BeanUtils.copyProperties(e, valueResponse);
            return valueResponse;
        }).collect(Collectors.toList());
        merchantResponse.setData(data);
        merchantResponse.setTotal(merchantModelTableDataInfo.getTotal());
        List<SohuAppProductMerchantModel> responseData = merchantResponse.getData();
        List<Long> merIds = responseData.stream().map(SohuAppProductMerchantModel::getId).collect(Collectors.toList());
        Map<Long, List<SohuIndexProductModel>> recommendedProductsByMerId = getRecommendedProductsByMerId(4, merIds);
        merchantResponse.getData().forEach(merchant -> {
            List<SohuIndexProductModel> productList = recommendedProductsByMerId.get(merchant.getId());
            Optional.ofNullable(productList).ifPresent(merchant::setProList);
        });
        // 如果销量排序为空就 有分销商品的排前面，并且按照有分销商品数量排序
        if (StringUtils.isBlank(productReqBo.getSalesOrder())) {
            merchantResponse.getData().sort((merchant1, merchant2) -> {
                List<SohuIndexProductModel> productList1 = recommendedProductsByMerId.get(merchant1.getId());
                List<SohuIndexProductModel> productList2 = recommendedProductsByMerId.get(merchant2.getId());
                // 获取 productList1 和 productList2 的大小
                int size1 = productList1 != null ? productList1.size() : 0;
                int size2 = productList2 != null ? productList2.size() : 0;
                // 首先按照 size 大小进行排序
                int sizeComparison = Integer.compare(size2, size1);
                if (sizeComparison != 0) {
                    return sizeComparison; // 如果大小不相等，直接返回比较结果
                }
                // 如果大小相等，按照 sales + valeSales 的总和进行排序
                long sum1 = productList1 != null ? productList1.stream()
                        .mapToLong(product -> product.getSales() + product.getVarSales())
                        .sum() : 0;

                long sum2 = productList2 != null ? productList2.stream()
                        .mapToLong(product -> product.getSales() + product.getVarSales())
                        .sum() : 0;
                return Long.compare(sum2, sum1); // 降序排序
            });
        }
        return merchantResponse;
    }

    @Override
    public Boolean isUseBrand(Long brandId) {
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        lqw.select(SohuProduct::getId);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getBrandId, brandId);
        lqw.last("limit 1");
        return baseMapper.exists(lqw);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean openProductSaveV1(SohuOpenProductSaveV1Bo bo) {
        SohuProduct entity = new SohuProduct();
        SohuOpenClientMerchantVo merchantVo = this.remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantName(bo.getOpenClientId(), bo.getMerchantName());
        if (Objects.isNull(merchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        entity.setMerId(merchantVo.getMerchantId());
        entity.setIsSelf(merchantVo.getIsSelf());
        SohuProduct product = this.getBythirdProductId(bo.getThirdProductId(), entity.getMerId(), bo.getOpenClientId());
        if (Objects.nonNull(product)) {
            this.verifyProductStatusOfWarehouse(product);
            entity.setId(product.getId());
        }
        entity.setThirdProductId(bo.getThirdProductId());
        SohuSiteVo siteVo = this.remoteMiddleSiteService.getBySiteNameOfEnable(bo.getSiteName());
        if (Objects.isNull(siteVo)) {
            throw new RuntimeException("请联系客服维护站点");
        }
        entity.setSiteId(siteVo.getId());
        //String cateId = this.sohuProductCategoryService.getCateIdStrByMerchantCategoryName(entity.getMerId(), bo.getMerchantCategoryName());
        String cateId = this.sohuProductCategoryService.getCateIdsByMerchantCategoryName(entity.getMerId(), bo.getMerchantCategoryName());
        entity.setCateId(cateId);
        Long categoryId = this.productCategoryPcService.getCategoryIdByCategoryName(bo.getCategoryName());
        entity.setCategoryId(categoryId);

        SohuProductBrand productBrand = this.sohuProductBrandService.getByName(bo.getBrandName());
        if (Objects.isNull(productBrand)) {
            throw new RuntimeException("请联系客服维护品牌");
        }
        entity.setBrandId(productBrand.getId());

        String guaranteeIds = sohuProductGuaranteeService.getIdsByNames(bo.getGuaranteeName());
        entity.setGuaranteeIds(guaranteeIds);

        SohuFreightTemplate freightTemplate = sohuFreightTemplateService.getByNameAndMerId(bo.getFreightTemplateName(), entity.getMerId());
        if (Objects.isNull(freightTemplate)) {
            throw new RuntimeException("请联系客服维护运费模板");
        }
        entity.setFreightTemplateId(freightTemplate.getId());

        entity.setImage(bo.getImage());
        entity.setSliderImage(bo.getSliderImage());
        entity.setStoreName(bo.getProductName());
        entity.setStoreInfo(bo.getProductInfo());
        entity.setKeyword(bo.getKeyword());
        entity.setUnitName(bo.getUnitName());
        entity.setProductType(bo.getProductType());
        entity.setSpecType(bo.getSpecType());
        entity.setOpenClientId(bo.getOpenClientId());
        //entity.setContent(bo.getContent());

        // 多规格需要校验规格参数
        if (!bo.getSpecType()) {
            if (bo.getAttrValueList().size() > 1) {
                throw new RuntimeException("单规格商品属性值不能大于1");
            }
        }

        // 计算价格
        List<SohuOpenProductAttrValueSaveBo> attrValueList = bo.getAttrValueList();
        SohuOpenProductAttrValueSaveBo minAttrValue = attrValueList.stream().min(Comparator.comparing(SohuOpenProductAttrValueSaveBo::getPrice)).get();
        entity.setPrice(minAttrValue.getPrice());
        entity.setOtPrice(minAttrValue.getOtPrice());
        entity.setCost(minAttrValue.getCost());
        //entity.setAuditStatus(ProductConstants.AUDIT_STATUS_WAIT);

        //库存
        entity.setStock(attrValueList.stream().mapToInt(SohuOpenProductAttrValueSaveBo::getStock).sum());

        // 商品属性
        List<SohuProductAttr> attrList = bo.getAttrList().stream().map(e -> {
            SohuProductAttr attr = new SohuProductAttr();
            BeanUtils.copyProperties(e, attr);
            attr.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            return attr;
        }).collect(Collectors.toList());

        // 商品属性详情
        List<SohuProductAttrValue> addAttrValueList = attrValueList.stream().map(e -> {
            SohuProductAttrValue attrValue = new SohuProductAttrValue();
            BeanUtils.copyProperties(e, attrValue);
            attrValue.setId(null);
            attrValue.setSku(getSku(e.getAttrValue()));
            attrValue.setQuota(0);
            attrValue.setQuotaShow(0);
            attrValue.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            return attrValue;
        }).collect(Collectors.toList());

        if (Objects.isNull(entity.getId())) {
            // 商户新增商品是否需要审核
            if (merchantVo.getProductSwitch()) {
                entity.setAuditStatus(ProductConstants.AUDIT_STATUS_WAIT);
            }
            this.baseMapper.insert(entity);
        } else {
            this.baseMapper.updateById(entity);
            // 先删除原用attr+value
            productAttrService.deleteByProductIdAndType(entity.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            productAttrValueService.deleteByProductIdAndType(entity.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        }
        // 数量
        productCountService.updateStockByProductId(entity.getId(), entity.getStock());
        // 属性-属性详情
        attrList.forEach(attr -> attr.setProductId(entity.getId()));
        addAttrValueList.forEach(value -> value.setProductId(entity.getId()));
        productAttrService.saveBatch(attrList);
        productAttrValueService.saveBatch(addAttrValueList);
        return true;
    }

    @Override
    public Boolean openProductSaveV2(SohuOpenProductSaveV2Bo bo) {
        SohuProduct entity = new SohuProduct();
        SohuOpenClientMerchantVo merchantVo = this.remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantId(bo.getOpenClientId(), bo.getMerchantId());
        if (Objects.isNull(merchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        entity.setMerId(merchantVo.getMerchantId());
        entity.setIsSelf(merchantVo.getIsSelf());
        SohuProduct product = this.getBythirdProductId(bo.getThirdProductId(), entity.getMerId(), bo.getOpenClientId());
        if (Objects.nonNull(product)) {
            this.verifyProductStatusOfWarehouse(product);
            entity.setId(product.getId());
        }
        entity.setThirdProductId(bo.getThirdProductId());
        SohuSiteVo siteVo = this.remoteMiddleSiteService.queryById(bo.getSiteId());
        if (Objects.isNull(siteVo)) {
            throw new RuntimeException("请联系客服维护站点");
        }
        entity.setSiteId(siteVo.getId());

        String[] merchantCategoryIdArray = bo.getMerchantCategoryId().split(",");
        List<Long> merchantCategoryIdList = Arrays.stream(merchantCategoryIdArray).map(Long::parseLong).collect(Collectors.toList());
        List<SohuProductCategoryVo> productCategoryVos = sohuProductCategoryService.queryVoBatchIds(merchantCategoryIdList);
        if (CollUtil.isEmpty(productCategoryVos) || productCategoryVos.size() != merchantCategoryIdArray.length) {
            throw new RuntimeException("请联系客服维护商城商户商品分类");
        }
        entity.setCateId(bo.getMerchantCategoryId());

        SohuProductCategoryPcVo productCategoryPcVo = this.productCategoryPcService.queryById(bo.getCategoryId());
        if (Objects.isNull(productCategoryPcVo)) {
            throw new RuntimeException("请联系客服维护商城平台商品分类");
        }
        entity.setCategoryId(bo.getCategoryId());

        SohuProductBrandVo productBrandVo = this.sohuProductBrandService.queryById(bo.getBrandId());
        if (Objects.isNull(productBrandVo)) {
            throw new RuntimeException("请联系客服维护品牌");
        }
        entity.setBrandId(bo.getBrandId());

        String guaranteeIds = sohuProductGuaranteeService.getIdsByIdsOfAppendDefault(bo.getGuaranteeIds());
        entity.setGuaranteeIds(guaranteeIds);

        SohuFreightTemplateVo freightTemplateVo = sohuFreightTemplateService.queryById(bo.getFreightTemplateId());
        if (Objects.isNull(freightTemplateVo)) {
            SohuFreightTemplate defaultFreightTemplate = sohuFreightTemplateService.getDefaultFreightTemplate();
            if (Objects.isNull(defaultFreightTemplate)) {
                throw new RuntimeException("请联系客服维护运费模板");
            }
            entity.setFreightTemplateId(defaultFreightTemplate.getId());
        } else {
            entity.setFreightTemplateId(freightTemplateVo.getId());
        }

        entity.setImage(bo.getImage());
        entity.setSliderImage(bo.getSliderImage());
        entity.setStoreName(bo.getProductName());
        entity.setStoreInfo(bo.getProductInfo());
        entity.setKeyword(bo.getKeyword());
        entity.setUnitName(bo.getUnitName());
        entity.setProductType(bo.getProductType());
        entity.setSpecType(bo.getSpecType());
        entity.setOpenClientId(bo.getOpenClientId());
        //entity.setContent(bo.getContent());

        // 多规格需要校验规格参数
        if (!bo.getSpecType()) {
            if (bo.getAttrValueList().size() > 1) {
                throw new RuntimeException("单规格商品属性值不能大于1");
            }
        }

        // 计算价格
        List<SohuOpenProductAttrValueSaveBo> attrValueList = bo.getAttrValueList();
        SohuOpenProductAttrValueSaveBo minAttrValue = attrValueList.stream().min(Comparator.comparing(SohuOpenProductAttrValueSaveBo::getPrice)).get();
        entity.setPrice(minAttrValue.getPrice());
        entity.setOtPrice(minAttrValue.getOtPrice());
        entity.setCost(minAttrValue.getCost());
        //entity.setAuditStatus(ProductConstants.AUDIT_STATUS_WAIT);

        //库存
        entity.setStock(attrValueList.stream().mapToInt(SohuOpenProductAttrValueSaveBo::getStock).sum());

        // 商品属性
        List<SohuProductAttr> attrList = bo.getAttrList().stream().map(e -> {
            SohuProductAttr attr = new SohuProductAttr();
            BeanUtils.copyProperties(e, attr);
            attr.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            return attr;
        }).collect(Collectors.toList());

        // 商品属性详情
        List<SohuProductAttrValue> addAttrValueList = attrValueList.stream().map(e -> {
            SohuProductAttrValue attrValue = new SohuProductAttrValue();
            BeanUtils.copyProperties(e, attrValue);
            attrValue.setId(null);
            attrValue.setSku(getSku(e.getAttrValue()));
            attrValue.setQuota(0);
            attrValue.setQuotaShow(0);
            attrValue.setType(ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            return attrValue;
        }).collect(Collectors.toList());

        if (Objects.isNull(entity.getId())) {
            // 商户新增商品是否需要审核
            if (merchantVo.getProductSwitch()) {
                entity.setAuditStatus(ProductConstants.AUDIT_STATUS_WAIT);
            }
            this.baseMapper.insert(entity);
        } else {
            this.baseMapper.updateById(entity);
            // 先删除原用attr+value
            productAttrService.deleteByProductIdAndType(entity.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
            productAttrValueService.deleteByProductIdAndType(entity.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        }
        // 数量
        productCountService.updateStockByProductId(entity.getId(), entity.getStock());
        // 属性-属性详情
        attrList.forEach(attr -> attr.setProductId(entity.getId()));
        addAttrValueList.forEach(value -> value.setProductId(entity.getId()));
        productAttrService.saveBatch(attrList);
        productAttrValueService.saveBatch(addAttrValueList);
        return true;
    }

    /**
     * 校验商品是否在仓库中
     */
    private void verifyProductStatusOfWarehouse(SohuProduct entity) {
        if (entity.getIsShow() || entity.getIsRecycle() || (!ProductConstants.AUDIT_STATUS_SUCCESS.equals(entity.getAuditStatus()))) {
            throw new RuntimeException("商品不在仓库中，不能操作");
        }
    }

    /**
     * 根据第三方商品唯一标识获取商品数据
     */
    private SohuProduct getBythirdProductId(String thirdProductId, Long merchantId, Long openClientId) {
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuProduct::getIsDel, false)
                .eq(SohuProduct::getMerId, merchantId)
                .eq(SohuProduct::getThirdProductId, thirdProductId)
                .eq(SohuProduct::getOpenClientId, openClientId)
                .last("limit 1");
        return this.baseMapper.selectOne(lqw);
    }

    @Override
    public Boolean openUpdateProductShelfV1(SohuOpenProductShelfEditV1Bo bo) {
        SohuOpenClientMerchantVo merchantVo = this.remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantName(bo.getOpenClientId(), bo.getMerchantName());
        if (Objects.isNull(merchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        if (merchantVo.getIsSelf()) {
            throw new RuntimeException("自营商单，无权操作");
        }
        SohuProduct product = this.getBythirdProductId(bo.getThirdProductId(), merchantVo.getMerchantId(), bo.getOpenClientId());
        if (Objects.isNull(product)) {
            throw new RuntimeException("商品不存在，请检查");
        }
        SohuOffOrPutBo sohuOffOrPutBo = new SohuOffOrPutBo();
        sohuOffOrPutBo.setId(product.getId());
        sohuOffOrPutBo.setMerId(merchantVo.getMerchantId());
        sohuOffOrPutBo.setType(bo.getIsAdded() ? ProductConstants.PRODUCT_TYPE_PUT : ProductConstants.PRODUCT_TYPE_OFF);
        this.offOrPutShelf(sohuOffOrPutBo);
        return true;
    }

    @Override
    public Boolean openUpdateProductShelfV2(SohuOpenProductShelfEditV2Bo bo) {
        SohuOpenClientMerchantVo merchantVo = this.remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantId(bo.getOpenClientId(), bo.getMerchantId());
        if (Objects.isNull(merchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        if (merchantVo.getIsSelf()) {
            throw new RuntimeException("自营商单，无权操作");
        }
        SohuProduct product = this.getBythirdProductId(bo.getThirdProductId(), merchantVo.getMerchantId(), bo.getOpenClientId());
        if (Objects.isNull(product)) {
            throw new RuntimeException("商品不存在，请检查");
        }
        SohuOffOrPutBo sohuOffOrPutBo = new SohuOffOrPutBo();
        sohuOffOrPutBo.setId(product.getId());
        sohuOffOrPutBo.setMerId(merchantVo.getMerchantId());
        sohuOffOrPutBo.setType(bo.getIsAdded() ? ProductConstants.PRODUCT_TYPE_PUT : ProductConstants.PRODUCT_TYPE_OFF);
        this.offOrPutShelf(sohuOffOrPutBo);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean openUpdateProductStockV1(SohuOpenProductStockEditV1Bo bo) {
        SohuOpenClientMerchantVo merchantVo = this.remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantName(bo.getOpenClientId(), bo.getMerchantName());
        if (Objects.isNull(merchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        if (merchantVo.getIsSelf()) {
            throw new RuntimeException("自营商单，无权操作");
        }
        SohuProduct product = this.getBythirdProductId(bo.getThirdProductId(), merchantVo.getMerchantId(), bo.getOpenClientId());
        if (Objects.isNull(product)) {
            throw new RuntimeException("商品不存在，请检查");
        }
        int stockNum = product.getStock();
        UpdateWrapper<SohuProduct> productUw = new UpdateWrapper<>();
        if (bo.getIsAdd()) {
            productUw.setSql(StrUtil.format("stock = stock + {}", bo.getStock()));
            stockNum += bo.getStock();
        } else {
            productUw.setSql(StrUtil.format("stock = stock - {}", bo.getStock()));
            // 扣减时保证库存不为负
            productUw.last(StrUtil.format(" and (stock - {} >= 0)", bo.getStock()));
            stockNum -= bo.getStock();
        }
        productUw.eq("id", product.getId());
        if (this.baseMapper.update(new SohuProduct(), productUw) == 0) {
            throw new ServiceException("更新普通商品库存失败");
        }
        UpdateWrapper<SohuProductAttrValue> productAttrValueUw = new UpdateWrapper<>();
        LambdaUpdateWrapper<SohuProductAttrValue> productAttrValueLuw = new LambdaUpdateWrapper<>();
        productAttrValueLuw.setSql(StrUtil.format("stock = stock + {}", bo.getStock()));
        if (bo.getIsAdd()) {
            productAttrValueUw.setSql(StrUtil.format("stock = stock + {}", bo.getStock()));
        } else {
            productAttrValueUw.setSql(StrUtil.format("stock = stock - {}", bo.getStock()));
            // 扣减时保证库存不为负
            productAttrValueUw.last(StrUtil.format(" and (stock - {} >= 0)", bo.getStock()));
        }
        productAttrValueUw.eq("sku_id", bo.getSkuId());
        productAttrValueUw.eq("product_id", product.getId());
        productAttrValueUw.eq("type", ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        productAttrValueUw.eq("is_del", 0);
        if (sohuProductAttrValueMapper.update(new SohuProductAttrValue(), productAttrValueUw) == 0) {
            throw new ServiceException("更新商品attrValue失败");
        }
        //统计商品总览统计
        if (stockNum < 5) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
            //如果存在说明当天该商品库存数量已经累加
            if (RedisUtils.setObjectIfAbsent(ProductConstants.PRODUCT_STOCK_OVERVIEW + product.getId(), stockNum, Duration.between(now, endOfDay))) {
                Map<String, String> upMap = new HashMap<>();
                upMap.put("product_stock_num", "+1");
                sohuProductOverviewReportService.syncProductOverviewReport(upMap);
            }
        } else {
            //如果当天该商品补充库存且库存数量大于5，则对应库存数量减1
            Object object = RedisUtils.getCacheObject(ProductConstants.PRODUCT_STOCK_OVERVIEW + product.getId());
            if (Objects.nonNull(object)) {
                Map<String, String> upMap = new HashMap<>();
                upMap.put("product_stock_num", "-1");
                sohuProductOverviewReportService.syncProductOverviewReport(upMap);
            }
        }
        return true;
    }

    @Override
    public Boolean openUpdateProductStockV2(SohuOpenProductStockEditV2Bo bo) {
        SohuOpenClientMerchantVo merchantVo = this.remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantId(bo.getOpenClientId(), bo.getMerchantId());
        if (Objects.isNull(merchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        if (merchantVo.getIsSelf()) {
            throw new RuntimeException("自营商单，无权操作");
        }
        SohuProduct product = this.getBythirdProductId(bo.getThirdProductId(), merchantVo.getMerchantId(), bo.getOpenClientId());
        int stockNum = product.getStock();
        if (Objects.isNull(product)) {
            throw new RuntimeException("商品不存在，请检查");
        }
        UpdateWrapper<SohuProduct> productUw = new UpdateWrapper<>();
        if (bo.getIsAdd()) {
            productUw.setSql(StrUtil.format("stock = stock + {}", bo.getStock()));
            stockNum += bo.getStock();
        } else {
            productUw.setSql(StrUtil.format("stock = stock - {}", bo.getStock()));
            // 扣减时保证库存不为负
            productUw.last(StrUtil.format(" and (stock - {} >= 0)", bo.getStock()));
            stockNum -= bo.getStock();
        }
        productUw.eq("id", product.getId());
        if (this.baseMapper.update(new SohuProduct(), productUw) == 0) {
            throw new ServiceException("更新普通商品库存失败");
        }
        UpdateWrapper<SohuProductAttrValue> productAttrValueUw = new UpdateWrapper<>();
        LambdaUpdateWrapper<SohuProductAttrValue> productAttrValueLuw = new LambdaUpdateWrapper<>();
        productAttrValueLuw.setSql(StrUtil.format("stock = stock + {}", bo.getStock()));
        if (bo.getIsAdd()) {
            productAttrValueUw.setSql(StrUtil.format("stock = stock + {}", bo.getStock()));
        } else {
            productAttrValueUw.setSql(StrUtil.format("stock = stock - {}", bo.getStock()));
            // 扣减时保证库存不为负
            productAttrValueUw.last(StrUtil.format(" and (stock - {} >= 0)", bo.getStock()));
        }
        productAttrValueUw.eq("sku_id", bo.getSkuId());
        productAttrValueUw.eq("product_id", product.getId());
        productAttrValueUw.eq("type", ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        productAttrValueUw.eq("is_del", 0);
        if (sohuProductAttrValueMapper.update(new SohuProductAttrValue(), productAttrValueUw) == 0) {
            throw new ServiceException("更新商品attrValue失败");
        }
        //统计商品总览统计
        if (stockNum < 5) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
            //如果存在说明当天该商品库存数量已经累加
            if (RedisUtils.setObjectIfAbsent(ProductConstants.PRODUCT_STOCK_OVERVIEW + product.getId(), stockNum, Duration.between(now, endOfDay))) {
                Map<String, String> upMap = new HashMap<>();
                upMap.put("product_stock_num", "+1");
                sohuProductOverviewReportService.syncProductOverviewReport(upMap);
            }
        } else {
            //如果当天该商品补充库存且库存数量大于5，则对应库存数量减1
            Object object = RedisUtils.getCacheObject(ProductConstants.PRODUCT_STOCK_OVERVIEW + product.getId());
            if (Objects.nonNull(object)) {
                Map<String, String> upMap = new HashMap<>();
                upMap.put("product_stock_num", "-1");
                sohuProductOverviewReportService.syncProductOverviewReport(upMap);
            }
        }
        return true;
    }

    @Override
    public SohuOpenProductV1Vo getBythirdProductIdV1(SohuOpenProductQueryV1Bo bo) {
        SohuOpenClientMerchantVo merchantVo = this.remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantName(bo.getOpenClientId(), bo.getMerchantName());
        if (Objects.isNull(merchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        SohuOpenProductV1Vo vo = new SohuOpenProductV1Vo();
        SohuProduct product = this.getBythirdProductId(bo.getThirdProductId(), merchantVo.getMerchantId(), bo.getOpenClientId());
        if (Objects.isNull(product)) {
            return null;
        }
        vo.setMerchantName(merchantVo.getMerchantName());
        vo.setThirdProductId(product.getThirdProductId());
        SohuSiteVo sohuSiteVo = remoteMiddleSiteService.queryById(product.getSiteId());
        if (Objects.nonNull(sohuSiteVo)) {
            vo.setSiteName(sohuSiteVo.getName());
        }
        String merchantCategoryName = this.sohuProductCategoryService.getMerchantCategoryNameByCateId(product.getCateId());
        vo.setMerchantCategoryName(merchantCategoryName);
        String fullCategoryName = this.productCategoryPcService.getFullCategoryNameByCategoryId(product.getCategoryId());
        vo.setCategoryName(fullCategoryName);
        SohuProductBrandVo sohuProductBrandVo = this.sohuProductBrandService.queryById(product.getBrandId());
        if (Objects.nonNull(sohuProductBrandVo)) {
            vo.setBrandName(sohuProductBrandVo.getName());
        }
        String guaranteeNames = sohuProductGuaranteeService.getNamesByIds(product.getGuaranteeIds());
        vo.setGuaranteeName(guaranteeNames);
        SohuFreightTemplateVo sohuFreightTemplateVo = sohuFreightTemplateService.queryById(product.getFreightTemplateId());
        if (Objects.nonNull(sohuFreightTemplateVo)) {
            vo.setFreightTemplateName(sohuFreightTemplateVo.getName());
        }
        vo.setImage(product.getImage());
        vo.setSliderImage(product.getSliderImage());
        vo.setProductName(product.getStoreName());
        vo.setProductInfo(product.getStoreInfo());
        vo.setKeyword(product.getKeyword());
        vo.setUnitName(product.getUnitName());
        vo.setProductType(product.getProductType());
        vo.setSpecType(product.getSpecType());

        List<SohuProductAttrVo> productAttrList = productAttrService.getListByProductIdAndType(product.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        vo.setAttrList(BeanCopyUtils.copyList(productAttrList, SohuOpenProductAttrVo.class));
        List<SohuProductAttrValueVo> productAttrValueList = productAttrValueService.getListByProductIdAndType(product.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        vo.setAttrValueList(BeanCopyUtils.copyList(productAttrValueList, SohuOpenProductAttrValueVo.class));
        return vo;
    }

    @Override
    public SohuOpenProductV2Vo getBythirdProductIdV2(SohuOpenProductQueryV2Bo bo) {
        SohuOpenClientMerchantVo merchantVo = this.remoteMiddleOpenClientMerchantService.getByClientIdAndMerchantId(bo.getOpenClientId(), bo.getMerchantId());
        if (Objects.isNull(merchantVo)) {
            throw new RuntimeException("请检查此商户是否已对您授权或商户状态");
        }
        SohuOpenProductV2Vo vo = new SohuOpenProductV2Vo();
        SohuProduct product = this.getBythirdProductId(bo.getThirdProductId(), merchantVo.getMerchantId(), bo.getOpenClientId());
        if (Objects.isNull(product)) {
            return null;
        }
        vo.setMerchantId(product.getMerId());
        vo.setThirdProductId(product.getThirdProductId());
        vo.setSiteId(product.getSiteId());
        vo.setMerchantCategoryId(product.getCateId());
        vo.setCategoryId(product.getCategoryId());
        vo.setBrandId(product.getBrandId());
        vo.setGuaranteeIds(product.getGuaranteeIds());
        //SohuFreightTemplateVo sohuFreightTemplateVo = sohuFreightTemplateService.queryById(product.getFreightTemplateId());
        vo.setFreightTemplateId(product.getFreightTemplateId());
        vo.setImage(product.getImage());
        vo.setSliderImage(product.getSliderImage());
        vo.setProductName(product.getStoreName());
        vo.setProductInfo(product.getStoreInfo());
        vo.setKeyword(product.getKeyword());
        vo.setUnitName(product.getUnitName());
        vo.setProductType(product.getProductType());
        vo.setSpecType(product.getSpecType());

        List<SohuProductAttrVo> productAttrList = productAttrService.getListByProductIdAndType(product.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        vo.setAttrList(BeanCopyUtils.copyList(productAttrList, SohuOpenProductAttrVo.class));
        List<SohuProductAttrValueVo> productAttrValueList = productAttrValueService.getListByProductIdAndType(product.getId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        vo.setAttrValueList(BeanCopyUtils.copyList(productAttrValueList, SohuOpenProductAttrValueVo.class));
        return vo;
    }

    @Override
    public TableDataInfo<SohuProductVo> queryPmList(SohuProductBo bo, PageQuery pageQuery) {
        // 查询自营商品
        LambdaQueryWrapper<SohuProduct> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuProduct::getIsSelf, true);
        lqw.eq(SohuProduct::getIsForced, false);
        lqw.eq(SohuProduct::getIsShow, true);
        lqw.eq(SohuProduct::getIsRecycle, false);
        lqw.eq(SohuProduct::getIsDel, false);
        lqw.eq(SohuProduct::getSysSource, bo.getSysSource());
        lqw.gt(SohuProduct::getStock, 0);
        lqw.eq(SohuProduct::getAuditStatus, ProductConstants.AUDIT_STATUS_SUCCESS);
        lqw.like(bo.getId() != null, SohuProduct::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getStoreName()), SohuProduct::getStoreName, bo.getStoreName());
        lqw.eq(StringUtils.isNotBlank(bo.getSysSource()), SohuProduct::getSysSource, bo.getSysSource());
        lqw.orderByDesc(SohuProduct::getId);
        Page<SohuProductVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }
}
