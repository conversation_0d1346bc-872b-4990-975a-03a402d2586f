package com.sohu.shopgoods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.admin.api.RemoteMerchantService;
import com.sohu.admin.api.model.SohuMerchantModel;
import com.sohu.common.core.constant.ProductConstants;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.shopgoods.api.bo.SohuShopCartBo;
import com.sohu.shopgoods.api.domain.ShopCartAgainReqBo;
import com.sohu.shopgoods.api.domain.ShopCartNumReqBo;
import com.sohu.shopgoods.api.domain.ShopCartNumUpdateReqBo;
import com.sohu.shopgoods.api.domain.ShopCartReqBo;
import com.sohu.shopgoods.api.model.SohuCartInfoModel;
import com.sohu.shopgoods.api.model.SohuCartMerchantModel;
import com.sohu.shopgoods.api.model.SohuShopCartModel;
import com.sohu.shopgoods.api.vo.SohuProductAttrValueVo;
import com.sohu.shopgoods.api.vo.SohuProductVo;
import com.sohu.shopgoods.api.vo.SohuShopCartVo;
import com.sohu.shopgoods.domain.SohuProduct;
import com.sohu.shopgoods.domain.SohuShopCart;
import com.sohu.shopgoods.mapper.SohuProductMapper;
import com.sohu.shopgoods.mapper.SohuShopCartMapper;
import com.sohu.shopgoods.service.ISohuProductAttrValueService;
import com.sohu.shopgoods.service.ISohuShopCartService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 购物车Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@RequiredArgsConstructor
@Service
public class SohuShopCartServiceImpl implements ISohuShopCartService {

    private final SohuShopCartMapper baseMapper;
    @Resource
    private final SohuProductMapper productMapper;
    @Resource
    private final ISohuProductAttrValueService productAttrValueService;
    @Resource
    private final TransactionTemplate transactionTemplate;
    @DubboReference
    private RemoteMerchantService remoteMerchantService;

    /**
     * 查询购物车
     */
    @Override
    public SohuShopCartVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询购物车列表
     */
    @Override
    public TableDataInfo<SohuShopCartVo> queryPageList(SohuShopCartBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuShopCart> lqw = buildQueryWrapper(bo);
        Page<SohuShopCartVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

    /**
     * 查询购物车列表
     */
    @Override
    public List<SohuShopCartVo> queryList(SohuShopCartBo bo) {
        LambdaQueryWrapper<SohuShopCart> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SohuShopCart> buildQueryWrapper(SohuShopCartBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SohuShopCart> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SohuShopCart::getUserId, bo.getUserId());
        lqw.eq(bo.getMerId() != null, SohuShopCart::getMerId, bo.getMerId());
        lqw.eq(bo.getProductId() != null, SohuShopCart::getProductId, bo.getProductId());
        lqw.eq(bo.getProductAttrId() != null, SohuShopCart::getProductAttrId, bo.getProductAttrId());
        lqw.eq(bo.getCartNum() != null, SohuShopCart::getCartNum, bo.getCartNum());
        lqw.eq(bo.getType() != null, SohuShopCart::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增购物车
     */
    @Override
    public Boolean insertByBo(SohuShopCartBo bo) {
        SohuShopCart add = BeanUtil.toBean(bo, SohuShopCart.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改购物车
     */
    @Override
    public Boolean updateByBo(SohuShopCartBo bo) {
        SohuShopCart update = BeanUtil.toBean(bo, SohuShopCart.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuShopCart entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除购物车
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量删除购物车
     */
    @Override
    public Boolean deleteByIds(Collection<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SohuShopCart getByAttrIdAndUserId(Long attrValueId, Long userId) {
        LambdaQueryWrapper<SohuShopCart> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SohuShopCart::getUserId, userId).eq(SohuShopCart::getProductAttrId, attrValueId);
        return this.baseMapper.selectOne(wrapper);
    }

    @Override
    public void updateCart(ShopCartReqBo storeCartRequest, SohuProductVo product, SohuProductAttrValueVo attrValue, SohuShopCart sohuShopCart) {
        // 购物车添加数量
        int newAddCount = sohuShopCart.getCartNum() + storeCartRequest.getCartNum();
        // 判断是否是多规格商品——多规格
        addCart(product, attrValue, sohuShopCart, newAddCount);
    }

    @Override
    public void addCart(SohuProductVo product, SohuProductAttrValueVo attrValue, SohuShopCart storeCart, int newAddCount) {
        // 判断是否是多规格商品——多规格
        if (Boolean.TRUE.equals(product.getSpecType())) {
            // 校验购物车数量与库存数量
            if (attrValue.getStock() <= newAddCount) {
                storeCart.setCartNum(attrValue.getStock());
            } else {
                storeCart.setCartNum(newAddCount);
            }
        } else if (Boolean.FALSE.equals(product.getSpecType())) {
            // 校验购物车数量与库存数量
            if (product.getStock() <= newAddCount) {
                storeCart.setCartNum(product.getStock());
            } else {
                storeCart.setCartNum(newAddCount);
            }
        }
    }

    @Override
    public boolean updateCateNum(ShopCartNumUpdateReqBo cartNumUpdateReqBo) {
        // 判断数量
        if (cartNumUpdateReqBo.getCartNum() <= 0) {
            throw new RuntimeException("The number of items cannot be less than 1");
        }
        SohuShopCart storeCart = this.baseMapper.selectById(cartNumUpdateReqBo.getCartId());
        if (ObjectUtil.isNull(storeCart)) {
            throw new RuntimeException("The current cart does not exist");
        }
        if (storeCart.getCartNum().equals(cartNumUpdateReqBo.getCartNum())) {
            return Boolean.TRUE;
        }
        SohuProductAttrValueVo attrValue = productAttrValueService.getByProductIdAndAttrId(storeCart.getProductId(),
            storeCart.getProductAttrId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        if (ObjectUtil.isNull(attrValue)) {
            throw new RuntimeException("No corresponding product SKU found");
        }
        SohuProductVo product = productMapper.selectVoById(attrValue.getProductId());
        // 判断是否是多规格商品——多规格
        if (Boolean.TRUE.equals(product.getSpecType())) {
            // 校验购物车数量与库存数量
            if (attrValue.getStock() <= cartNumUpdateReqBo.getCartNum()) {
                storeCart.setCartNum(attrValue.getStock());
            } else {
                storeCart.setCartNum(cartNumUpdateReqBo.getCartNum());
            }
        } else if (Boolean.FALSE.equals(product.getSpecType())) {
            // 校验购物车数量与库存数量
            if (product.getStock() <= cartNumUpdateReqBo.getCartNum()) {
                storeCart.setCartNum(product.getStock());
            } else {
                storeCart.setCartNum(cartNumUpdateReqBo.getCartNum());
            }
        }
        return this.baseMapper.updateById(storeCart) > 0;
    }

    @Override
    public Map<String, Integer> getCartNum(ShopCartNumReqBo cartNumReqBo) {
        Long userId = LoginHelper.getUserId();
        Map<String, Integer> map = new HashMap<>();
        int num;
        if (cartNumReqBo.getType().equals("total")) {
            num = getUserCountByStatus(userId, cartNumReqBo.getNumType());
        } else {
            num = getUserSumByStatus(userId, cartNumReqBo.getNumType());
        }
        map.put("count", num);
        return map;
    }

    @Override
    public boolean reSetCart(ShopCartAgainReqBo cartAgainReqBos) {
        LambdaQueryWrapper<SohuShopCart> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuShopCart::getId, cartAgainReqBos.getId());
        SohuShopCart storeCart = this.baseMapper.selectOne(lqw);
        if (ObjectUtil.isNull(storeCart)) throw new RuntimeException("Cart does not exist");
        // 判断商品正常
        SohuProductVo product = productMapper.selectVoById(cartAgainReqBos.getProductId());
        if (ObjectUtil.isNull(product) || product.getIsDel() || !product.getIsShow()) {
            throw new RuntimeException("No matching product found");
        }
        SohuProductAttrValueVo attrValue = productAttrValueService.getByProductIdAndAttrId(product.getId(),
            cartAgainReqBos.getProductAttrUnique(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
        if (ObjectUtil.isNull(attrValue)) {
            throw new RuntimeException("No corresponding product SKU found");
        }
        storeCart.setCartNum(cartAgainReqBos.getCartNum());
        storeCart.setProductAttrId(cartAgainReqBos.getProductAttrUnique());
        storeCart.setType(true);
        storeCart.setMerId(product.getMerId());
        boolean updateResult = this.baseMapper.updateById(storeCart) > 0;
        if (updateResult) {
            return Boolean.TRUE;
        } else {
            throw new RuntimeException("Failed to reselect add to cart");
        }

    }

    @Override
    public boolean updateById(SohuShopCart sohuShopCart) {
        return this.baseMapper.updateById(sohuShopCart) > 0;
    }

    @Override
    public boolean insert(SohuShopCart storeCart) {
        return this.baseMapper.insert(storeCart) > 0;
    }

    @Override
    public void shopCartStatistics(Long productId) {
        // todo 统计加购数量
    }

    @Override
    public void productStatusNotEnable(Long productId) {
        this.baseMapper.updateTypeByProduct(productId);
    }

    @Override
    public Boolean updateSku(List<Long> skuIdList) {
        return this.baseMapper.updateSku(skuIdList);
    }

    @Override
    public SohuShopCartModel getByIdAndUserId(Long shoppingCartId, Long userId) {
        LambdaQueryWrapper<SohuShopCart> lqw = Wrappers.lambdaQuery();
        lqw.eq(SohuShopCart::getId, shoppingCartId);
        lqw.eq(SohuShopCart::getUserId, userId);
        lqw.eq(SohuShopCart::getType, true);
        SohuShopCartModel sohuShopCartModel = new SohuShopCartModel();
        BeanUtils.copyProperties(this.baseMapper.selectOne(lqw), sohuShopCartModel);
        return sohuShopCartModel;
    }

    @Override
    public Boolean saveCart(List<ShopCartReqBo> storeCartListRequest) {
        Long userId = LoginHelper.getUserId();
        return transactionTemplate.execute(exec -> {
            storeCartListRequest.forEach(storeCartRequest -> {
                // 判断商品正常
                SohuProductVo product = productMapper.selectVoById(storeCartRequest.getProductId());
                if (ObjectUtil.isNull(product) || product.getIsDel() || !product.getIsShow()) {
                    throw new RuntimeException("No matching product found");
                }
                SohuProductAttrValueVo attrValue = productAttrValueService.getByProductIdAndAttrId(product.getId(),
                    storeCartRequest.getProductAttrUnique(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
                if (ObjectUtil.isNull(attrValue)) {
                    throw new RuntimeException("No corresponding product SKU found");
                }
//                if (attrValue.getStock() < storeCartRequest.getCartNum()) {
//                    throw new RuntimeException("Insufficient stock of goods");
//                }

                // 普通商品部分(只有普通商品才能添加购物车)
                // 是否已经有同类型商品在购物车，有则添加数量没有则新增
                SohuShopCart forUpdateStoreCart = this.getByAttrIdAndUserId(storeCartRequest.getProductAttrUnique(), userId);
                if (ObjectUtil.isNotNull(forUpdateStoreCart)) {
                    this.updateCart(storeCartRequest, product, attrValue, forUpdateStoreCart);
                    forUpdateStoreCart.setSiteType(storeCartRequest.getSiteType());
                    forUpdateStoreCart.setSiteId(storeCartRequest.getSiteId());
                    // 修改购物车数据
                    boolean updateResult = this.updateById(forUpdateStoreCart);
                    if (!updateResult) throw new RuntimeException("fail to add in order cart");
                } else {
                    // 新增购物车数据
                    SohuShopCart storeCart = new SohuShopCart();
                    // 购物车添加数量
                    int newAddCount = storeCartRequest.getCartNum();
                    BeanUtils.copyProperties(storeCartRequest, storeCart);
                    this.addCart(product, attrValue, storeCart, newAddCount);
                    storeCart.setUserId(userId);
                    storeCart.setProductAttrId(storeCartRequest.getProductAttrUnique());
                    storeCart.setMerId(product.getMerId());
                    if (!this.insert(storeCart)) throw new RuntimeException("fail to add in order cart");
                }
                // todo 加入购物车商品统计
                this.shopCartStatistics(storeCartRequest.getProductId());
            });
            return Boolean.TRUE;
        });
    }

    @Override
    public List<SohuCartMerchantModel> getCartList(Boolean isValid) {
        Long userId = LoginHelper.getUserId();
        //带 StoreCart 类的多条件查询
        LambdaQueryWrapper<SohuShopCart> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SohuShopCart::getUserId, userId);
        lambdaQueryWrapper.eq(SohuShopCart::getType, isValid);
        lambdaQueryWrapper.orderByDesc(SohuShopCart::getId);
        List<SohuShopCart> cartList = this.baseMapper.selectList(lambdaQueryWrapper);
        if (CollUtil.isEmpty(cartList)) {
            return CollUtil.newArrayList();
        }
        // 组装数据
        List<Long> merIdList = cartList.stream().map(SohuShopCart::getMerId).distinct().collect(Collectors.toList());
        Map<Long, SohuMerchantModel> merchantMap = remoteMerchantService.getMerIdMapByIdList(merIdList);
        List<SohuCartMerchantModel> responseList = CollUtil.newArrayList();
        merIdList.forEach(merId -> {
            SohuCartMerchantModel merchantResponse = new SohuCartMerchantModel();
            merchantResponse.setMerId(merId);
            merchantResponse.setMerName(merchantMap.get(merId).getName());
            List<SohuShopCart> merCartList = cartList.stream().filter(e -> e.getMerId().equals(merId)).collect(Collectors.toList());
            List<SohuCartInfoModel> infoResponseList = merCartList.stream().map(storeCart -> {
                SohuCartInfoModel cartInfoResponse = new SohuCartInfoModel();
                BeanUtils.copyProperties(storeCart, cartInfoResponse);
                // 获取商品信息
                SohuProduct storeProduct = productMapper.selectById(storeCart.getProductId());
                cartInfoResponse.setImage(storeProduct.getImage());
                cartInfoResponse.setStoreName(storeProduct.getStoreName());
                cartInfoResponse.setIsShow(storeProduct.getIsShow());
                if (!isValid) {// 失效商品直接掠过
                    cartInfoResponse.setAttrStatus(false);
                    return cartInfoResponse;
                }
                // 获取对应的商品规格信息(只会有一条信息)
                SohuProductAttrValueVo attrValue = productAttrValueService.getByProductIdAndAttrId(storeCart.getProductId(),
                    storeCart.getProductAttrId(), ProductConstants.PRODUCT_ACTIVITY_TYPE_NORMALS);
                // 规格不存在即失效
                if (ObjectUtil.isNull(attrValue)) {
                    cartInfoResponse.setAttrStatus(false);
                    return cartInfoResponse;
                }
                if (StrUtil.isNotBlank(attrValue.getImage())) {
                    cartInfoResponse.setImage(attrValue.getImage());
                }
                cartInfoResponse.setSuk(attrValue.getSku());
                cartInfoResponse.setPrice(attrValue.getPrice());
                cartInfoResponse.setAttrId(attrValue.getId());
                cartInfoResponse.setAttrStatus(attrValue.getStock() > 0);
                cartInfoResponse.setStock(attrValue.getStock());
                return cartInfoResponse;
            }).collect(Collectors.toList());
            merchantResponse.setCartInfoList(infoResponseList);
            responseList.add(merchantResponse);
        });
        return responseList;
    }

    /**
     * 购物车商品数量（条数）
     *
     * @param userId Integer 用户id
     * @param status Boolean 商品类型：true-有效商品，false-无效商品
     * @return Integer
     */
    private Integer getUserCountByStatus(Long userId, Boolean status) {
        //购物车商品种类数量
        LambdaQueryWrapper<SohuShopCart> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SohuShopCart::getUserId, userId);
        lambdaQueryWrapper.eq(SohuShopCart::getType, status);
        return Math.toIntExact(this.baseMapper.selectCount(lambdaQueryWrapper));
    }

    /**
     * 购物车购买商品总数量
     *
     * @param userId Integer 用户id
     * @param status 商品类型：true-有效商品，false-无效商品
     * @return Integer
     */
    private Integer getUserSumByStatus(Long userId, Boolean status) {
        QueryWrapper<SohuShopCart> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("ifnull(sum(cart_num), 0) as cart_num");
        queryWrapper.eq("uid", userId);
        queryWrapper.eq("status", status);
        SohuShopCart storeCart = this.baseMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(storeCart)) {
            return 0;
        }
        return storeCart.getCartNum();
    }

}
