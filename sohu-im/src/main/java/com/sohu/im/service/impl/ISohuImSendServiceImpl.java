package com.sohu.im.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sohu.common.core.config.AsyncConfig;
import com.sohu.common.core.enums.AppPathTypeEnum;
import com.sohu.common.core.enums.ApplyStateEnum;
import com.sohu.common.core.enums.MsgTypeEnum;
import com.sohu.common.core.enums.PushJiGuangBizTypeEnum;
import com.sohu.common.core.utils.StringUtils;
import com.sohu.common.mybatis.db.CacheMgr;
import com.sohu.common.satoken.config.LoginUser;
import com.sohu.im.api.bo.ImChatRequestBo;
import com.sohu.im.api.bo.ImShareBo;
import com.sohu.im.api.enums.ImCommandTypeEnum;
import com.sohu.im.api.enums.ImMessageTypeEnum;
import com.sohu.im.api.enums.ImSessionTypeEnum;
import com.sohu.im.api.enums.MediaCallTypeEnum;
import com.sohu.im.api.vo.SohuImGroupUserVo;
import com.sohu.im.api.vo.SohuImGroupVo;
import com.sohu.im.domain.SohuImChatLastMessage;
import com.sohu.im.domain.SohuImChatMessage;
import com.sohu.im.mapper.SohuImChatLastMessageMapper;
import com.sohu.im.mapper.SohuImChatMessageMapper;
import com.sohu.im.server.Const;
import com.sohu.im.service.ISohuImCommonService;
import com.sohu.im.service.ISohuImSendService;
import com.sohu.im.utfil.ImGroupUtil;
import com.sohu.im.utfil.ImSocketResponseUtil;
import com.sohu.im.utfil.MP3DurationCalculator;
import com.sohu.middle.api.service.RemoteMiddleFriendService;
import com.sohu.middle.api.vo.SohuFriendsVo;
import com.sohu.resource.api.RemoteJiguangService;
import com.sohu.resource.api.domain.bo.SohuJiguangPush2UserReqBo;
import com.sohu.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 群发内容记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ISohuImSendServiceImpl implements ISohuImSendService {

    private final SohuImChatLastMessageMapper baseMapper;
    private final SohuImChatMessageMapper sohuImChatMessageMapper;

    private final AsyncConfig asyncConfig;

    private final ISohuImCommonService sohuImCommonService;

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteJiguangService remoteJiguangService;
    @DubboReference
    private RemoteMiddleFriendService remoteMiddleFriendService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(Long senderId, ImChatRequestBo bo) {
        // 如果是指令消息，且指令类型是撤回消息，则处理撤回逻辑
        //if (StrUtil.isNotBlank(bo.getCommandType())) {
        if (StrUtil.equalsAnyIgnoreCase(bo.getCommandType(), ImCommandTypeEnum.recall.getCode())) {
            handleRecallMessage(bo);
            return; // 撤回操作后不继续发送消息
        }
        //return; // 其他指令消息直接返回，不做处理
        // }

        // 如果没有传入 chatId，生成一个默认的
        if (bo.getChatId() == null) {
            bo.setChatId(System.nanoTime());
        }
        if (StrUtil.isBlankIfStr(bo.getLocalId())) {
            bo.setLocalId(RandomUtil.randomString(16));
        }

        // 根据会话类型选择不同的发送逻辑
        String sessionType = bo.getSessionType();
        if (ImSessionTypeEnum.single.getCode().equals(sessionType)) {
            sendSingleMessage(senderId, bo);
        } else if (ImSessionTypeEnum.merchant.getCode().equals(sessionType)) {
            sendMerchantMessage(senderId, bo);
        } else if (ImGroupUtil.isGroup(sessionType)) {
            sendGroupMessage(senderId, bo);
        } else {
            throw new IllegalArgumentException("Invalid session type: " + sessionType);
        }
    }

    // 处理撤回消息的逻辑
    private void handleRecallMessage(ImChatRequestBo bo) {
        SohuImChatMessage message = sohuImChatMessageMapper.selectOne(SohuImChatMessage::getChatId, bo.getChatId());
        if (Objects.nonNull(message)) {
            message.setUpdateTime(new Date());
            message.setCommandType(bo.getCommandType());
            log.info("消息撤回了，messageId={}", message.getId());
            sohuImChatMessageMapper.updateById(message);
        }
    }

    // 发送单聊消息
    private void sendSingleMessage(Long senderId, ImChatRequestBo bo) {
        Long msgId = senderId ^ bo.getReceiverId();
        if (bo.getSaveOuterMessage()) {
            // 处理消息的插入或更新
            saveOrUpdateMessage(senderId, bo, msgId, ImSessionTypeEnum.single.getCode(), bo.getReceiverId());
        }
        this.saveInnerMessage(senderId, bo, msgId);
    }

    // 发送商户消息
    private void sendMerchantMessage(Long senderId, ImChatRequestBo bo) {
        Long msgId = senderId ^ bo.getReceiverId();
        if (bo.getSaveOuterMessage()) {
            // 处理消息的插入或更新
            saveOrUpdateMessage(senderId, bo, msgId, ImSessionTypeEnum.merchant.getCode(), bo.getReceiverId());
        }
        this.saveInnerMessage(senderId, bo, msgId);
    }

    // 发送群聊消息
    private void sendGroupMessage(Long senderId, ImChatRequestBo bo) {
        Long msgId = senderId ^ bo.getReceiverId();
        String sessionType = StrUtil.isBlank(bo.getSessionType()) ? ImSessionTypeEnum.group.getCode() : bo.getSessionType();
        if (bo.getSaveOuterMessage()) {
            // 处理消息的插入或更新
            saveOrUpdateMessage(senderId, bo, msgId, sessionType, bo.getReceiverId());
        }
        this.saveInnerMessage(senderId, bo, msgId);
    }

    // 统一的消息保存与更新逻辑
    private void saveOrUpdateMessage(Long senderId, ImChatRequestBo bo, Long msgId, String sessionType, Long receiverId) {
        // 查找现有消息
        LambdaQueryWrapper<SohuImChatLastMessage> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SohuImChatLastMessage::getSessionType, sessionType);
        lqw.eq(SohuImChatLastMessage::getReceiverId, receiverId);
        if (StrUtil.equalsAnyIgnoreCase(sessionType, ImSessionTypeEnum.single.name(), ImSessionTypeEnum.merchant.name())) {
            lqw.eq(SohuImChatLastMessage::getSenderId, senderId);
        }
        lqw.last("limit 1");
        SohuImChatLastMessage exist = baseMapper.selectOne(lqw);
        if (Objects.isNull(exist)) {
            // 插入新的消息
            SohuImChatLastMessage last = new SohuImChatLastMessage();
            setMessageFields(last, senderId, sessionType, receiverId, bo, msgId);
            baseMapper.insert(last);
        } else {
            // 更新现有的消息
            setMessageFields(exist, senderId, sessionType, receiverId, bo, msgId);
            baseMapper.updateById(exist);
        }
    }

    // 设置消息字段
    private void setMessageFields(SohuImChatLastMessage message, Long senderId, String sessionType, Long receiverId, ImChatRequestBo bo, Long msgId) {
        message.setSenderId(senderId);
        message.setSessionType(sessionType);
        message.setReceiverId(receiverId);
        message.setContent(bo.getContent());
        message.setMessageType(bo.getMessageType());
        message.setMsgId(msgId);
        message.setCreateTime(new Date());
        message.setUpdateTime(new Date());
        message.setChatId(bo.getChatId());
        message.setLocalId(bo.getLocalId());
        message.setErr(bo.getErr());
        message.setAtIds(bo.getAtIds());
        message.setCommandType(bo.getCommandType());
        message.setDuration(bo.getDuration());
        message.setMsgSource(bo.getMsgSource());

        if (Objects.nonNull(bo.getShare())) {
            ImShareBo share = bo.getShare();
            message.setShareId(share.getId());
            message.setShareType(share.getType());
            message.setShareParam(JSONUtil.toJsonStr(bo.getShare()));
        }

        if (StrUtil.equalsAnyIgnoreCase(MsgTypeEnum.Voice.getType(), bo.getMessageType())) {
            message.setDuration((int) MP3DurationCalculator.getMP3DurationInSeconds(bo.getContent()));
        }
        ImSocketResponseUtil.buildFile(bo, message);
    }

    /**
     * 是否保存里层消息
     *
     * @param senderId 发送者ID
     * @param bo       socket消息入参
     * @param msgId    会话ID
     */
    private void saveInnerMessage(Long senderId, ImChatRequestBo bo, Long msgId) {
        if (bo.getSaveInnerMessage()) {
            SohuImChatMessage message = new SohuImChatMessage();
            message.setSenderId(senderId);
            message.setSessionType(bo.getSessionType());
            message.setReceiverId(bo.getReceiverId());
            message.setContent(bo.getContent());
            message.setMessageType(bo.getMessageType());
            message.setMsgId(msgId);
            message.setShareType(bo.getShareType());
            message.setShareId(bo.getShareId());
            message.setCreateTime(new Date());
            message.setUpdateTime(new Date());
            message.setChatId(bo.getChatId());
            if (StrUtil.equalsAnyIgnoreCase(MsgTypeEnum.Voice.getType(), bo.getMessageType())) {
                message.setDuration((int) MP3DurationCalculator.getMP3DurationInSeconds(bo.getContent()));
            }
            if (Objects.nonNull(bo.getShare())) {
                ImShareBo share = bo.getShare();
                message.setShareId(share.getId());
                message.setShareType(share.getType());
                message.setShareParam(JSONUtil.toJsonStr(bo.getShare()));
            }
            message.setLocalId(bo.getLocalId());
            message.setErr(bo.getErr());
            message.setDuration(bo.getDuration());
            message.setMediaType(Objects.nonNull(bo.getMediaCall()) ? bo.getMediaCall().getType() : null);
            message.setAtIds(bo.getAtIds());
            message.setCommandType(bo.getCommandType());
            message.setMerchantId(bo.getMerchantId());
            message.setMsgSource(bo.getMsgSource());
            message.setNanoTime(bo.getNanoTime());
            ImSocketResponseUtil.buildFile(bo, message);
            sohuImChatMessageMapper.insert(message);

            //发送极光通知
            CompletableFuture.runAsync(() -> pushJiGuangMsgNotice(bo), asyncConfig.getAsyncExecutor());
        }
    }

    @Override
    public Boolean pushJiGuangMsgNotice(ImChatRequestBo bo) {
        if (BooleanUtil.isTrue(bo.getHidden())) {
            return Boolean.TRUE;
        }

        if (Objects.nonNull(bo.getMediaCall()) && MediaCallTypeEnum.IGNORE_JIGUANG_MSG.contains(bo.getMediaCall().getType())) {
            log.info("忽略发送极光消息通知，mediaCallType={}", bo.getMediaCall().getType());
            return Boolean.TRUE;
        }

        SohuJiguangPush2UserReqBo reqBo = new SohuJiguangPush2UserReqBo();
        reqBo.setJumpPathType(AppPathTypeEnum.IM_CHAT_LIST);
        reqBo.setBizType(PushJiGuangBizTypeEnum.IM);

        // 构建消息内容
        String alert = buildAlertMessage(bo);
        reqBo.setOriginAlert(alert);
        reqBo.setAlert(alert);
        reqBo.setVoipParam(Collections.singletonMap("messageType", bo.getMessageType()));

        LoginUser sender = remoteUserService.queryById(bo.getRealSenderId());
        if (sender == null) {
            log.warn("发送者信息未找到, realSenderId={}", bo.getRealSenderId());
            return Boolean.FALSE;
        }

        if (isPrivateChat(bo)) {
            return handlePrivateChat(bo, reqBo, sender);
        } else {
            return handleGroupChat(bo, reqBo, sender);
        }
    }

    private String buildAlertMessage(ImChatRequestBo bo) {
        String alert = bo.getContent();
        if (StringUtils.isNotBlank(bo.getShareType()) && !StringUtils.equals("none", bo.getShareType())) {
            //ImShareTypeEnum typeEnum = ImShareTypeEnum.getByCode(bo.getShareType());
            //alert = (typeEnum != null) ? typeEnum.getDesc() + ": " + alert : alert;
        }
        if (Objects.nonNull(bo.getFile()) && StrUtil.isNotBlank(bo.getFile().getFileName())) {
            // 发送文件
            alert = bo.getFile().getFileName();
        }
        if (StrUtil.equalsAnyIgnoreCase(bo.getMessageType(), ImMessageTypeEnum.Image.getCode())) {
            // 发送图片
            alert = "[图片]";
        }
        if (StrUtil.equalsAnyIgnoreCase(bo.getMessageType(), ImMessageTypeEnum.Video.getCode())) {
            // 发送视频
            alert = "[视频]";
        }
        if (StrUtil.equalsAnyIgnoreCase(bo.getMessageType(), ImMessageTypeEnum.Voice.getCode())) {
            // 发送语音
            alert = "[语音]";
        }
        return alert;
    }

    private Boolean handlePrivateChat(ImChatRequestBo bo, SohuJiguangPush2UserReqBo reqBo, LoginUser sender) {
        Long receiverId = bo.getRealReceiverId();
        String title = resolveUserTitle(sender, receiverId);

        reqBo.setTitle(title);
        reqBo.setUserIds(Collections.singletonList(receiverId));
        Map<String, Object> params = new HashMap<>();
        params.put("conversationId", sender.getUserId());
        params.put("sessionType", ImSessionTypeEnum.single.name());
        params.put("merchantId", bo.getMerchantId());
        if (Objects.nonNull(bo.getMediaCall())) {
            String mediaType = StrUtil.containsAnyIgnoreCase(bo.getMessageType(), ImMessageTypeEnum.Video.getCode()) ? ImMessageTypeEnum.Video.name() : ImMessageTypeEnum.Voice.name();
            params.put("mediaType", mediaType);
            params.put("type", bo.getMediaCall().getType());
            params.put("roomId", bo.getMediaCall().getRoomId());
            Long originUserId = bo.getMediaCall().getOriginUserId();
            params.put("originUser", remoteUserService.selectById(originUserId));
            params.put("callToTime", CacheMgr.get(Const.ICE_ROOM_CALL_TIME, bo.getMediaCall().getRoomId()));
        }
        reqBo.setParameters(params);
        return sendJiguang(reqBo);
    }

    private String resolveUserTitle(LoginUser sender, Long receiverId) {
        String title = StrUtil.isNotBlank(sender.getNickname()) ? sender.getNickname() : sender.getUsername();
        SohuFriendsVo friendsVo = remoteMiddleFriendService.queryOne(receiverId, sender.getUserId(), ApplyStateEnum.pass.name());
        if (friendsVo != null && StrUtil.isNotBlank(friendsVo.getAlias())) {
            title = friendsVo.getAlias();
        }
        return title;
    }

    private Boolean handleGroupChat(ImChatRequestBo bo, SohuJiguangPush2UserReqBo reqBo, LoginUser sender) {
        SohuImGroupVo groupVo = sohuImCommonService.queryGroup(bo.getReceiverId());
        if (groupVo == null) {
            log.warn("发送极光消息，群组信息未找到, receiverId={}", bo.getReceiverId());
            return Boolean.FALSE;
        }

        Collection<Long> groupUserIds = sohuImCommonService.queryGroupUsers(groupVo.getId());
        if (CollUtil.isEmpty(groupUserIds)) {
            log.warn("发送极光消息，群组用户列表为空, groupId={}", groupVo.getId());
            return Boolean.FALSE;
        }

        if (Objects.nonNull(bo.getMediaCall())) {
            Set<Long> members = bo.getMediaCall().getMembers();
            if (!members.isEmpty()) {
                groupUserIds = members;
            }
        }
        if (CollUtil.isEmpty(groupUserIds)) {
            log.warn("发送极光消息，群组用户列表为空, groupId={}", groupVo.getId());
            return Boolean.FALSE;
        }

        reqBo.setTitle(groupVo.getName());

        Map<Long, String> friendAliasMap = remoteMiddleFriendService.friendAliasMap(groupUserIds, bo.getRealSenderId(), ApplyStateEnum.pass.name());
        SohuImGroupUserVo groupUserVo = sohuImCommonService.queryGroupUser(groupVo.getId(), bo.getRealSenderId());

        List<Long> userIds = new ArrayList<>();
        for (Long userId : groupUserIds) {
            if (Objects.equals(userId, bo.getRealSenderId())) {
                continue; // 跳过消息发送者
            }
            String groupAlert = buildGroupAlert(friendAliasMap.get(userId), sender, groupUserVo, reqBo.getOriginAlert(), bo);
            reqBo.setAlert(groupAlert);
            userIds.add(userId);
            Map<String, Object> params = new HashMap<>();
            params.put("conversationId", groupVo.getId());
            params.put("sessionType", groupVo.getGroupType());
            if (Objects.nonNull(bo.getMediaCall())) {
                String mediaType = StrUtil.containsAnyIgnoreCase(bo.getMessageType(), ImMessageTypeEnum.Video.getCode()) ? ImMessageTypeEnum.Video.name() : ImMessageTypeEnum.Voice.name();
                params.put("mediaType", mediaType);
                params.put("type", bo.getMediaCall().getType());
                params.put("roomId", bo.getMediaCall().getRoomId());
                params.put("members", bo.getMediaCall().getMembers());
                Long originUserId = bo.getMediaCall().getOriginUserId();
                params.put("originUser", remoteUserService.selectById(originUserId));
                params.put("callToTime", CacheMgr.get(Const.ICE_ROOM_CALL_TIME, bo.getMediaCall().getRoomId()));
            }
            reqBo.setParameters(params);
        }
        reqBo.setUserIds(userIds);
        sendJiguang(reqBo);

        return Boolean.TRUE;
    }

    private String buildGroupAlert(String alias, LoginUser sender, SohuImGroupUserVo groupUserVo, String content, ImChatRequestBo bo) {
        if (Objects.isNull(bo.getMediaCall())) {
            return content;
        }
        if (StrUtil.isNotBlank(alias)) {
            return String.format("%s: %s", alias, content);
        }
        String senderName = StringUtils.getValidString(groupUserVo.getGroupUserNickName(), sender.getNickname(), sender.getUsername());
        if (StrUtil.isBlankIfStr(senderName)) {
            return content;
        }
        return String.format("%s: %s", senderName, content);
    }

    /**
     * 调用发送极光方法
     *
     * @param reqBo
     * @return
     */
    private Boolean sendJiguang(SohuJiguangPush2UserReqBo reqBo) {
        try {
            remoteJiguangService.push2User(reqBo);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.warn("发送IM消息通知极光推送异常，原因: {}", e.getMessage());
            return Boolean.FALSE;
        }
    }

    /**
     * 判断是否是私聊
     *
     * @param bo
     * @return
     */
    private boolean isPrivateChat(ImChatRequestBo bo) {
        return StrUtil.equalsAnyIgnoreCase(bo.getSessionType(), ImSessionTypeEnum.single.name(), ImSessionTypeEnum.merchant.name());
    }

}
