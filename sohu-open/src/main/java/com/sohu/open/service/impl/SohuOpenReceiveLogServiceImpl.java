package com.sohu.open.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sohu.common.core.web.domain.SohuEntity;
import com.sohu.common.mybatis.core.page.PageQueryUtils;
import com.sohu.common.mybatis.core.page.TableDataInfoUtils;
import com.sohu.common.page.core.PageQuery;
import com.sohu.common.page.core.TableDataInfo;
import com.sohu.common.satoken.utils.LoginHelper;
import com.sohu.open.domain.SohuOpenReceiveLog;
import com.sohu.open.domain.bo.SohuOpenReceiveLogPageQueryBo;
import com.sohu.open.domain.enums.OpenReceiveEnums;
import com.sohu.open.domain.vo.SohuOpenReceiveLogVo;
import com.sohu.open.mapper.SohuOpenReceiveLogMapper;
import com.sohu.open.service.ISohuOpenReceiveLogService;
import com.sohu.streamrocketmq.api.RemoteStreamMqService;
import com.sohu.streamrocketmq.api.enums.MqKeyEnum;
import com.sohu.streamrocketmq.api.model.MqMessaging;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.Objects;

/**
 * 批量接收外部消息日志记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
@RequiredArgsConstructor
@Service
public class SohuOpenReceiveLogServiceImpl implements ISohuOpenReceiveLogService {

    private final SohuOpenReceiveLogMapper baseMapper;

    @DubboReference
    private RemoteStreamMqService remoteStreamMqService;

    /**
     * 查询批量接收外部消息日志记录
     */
    @Override
    public SohuOpenReceiveLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public SohuOpenReceiveLog selectByIdOfWait(Long id) {
        return baseMapper.selectOne(SohuOpenReceiveLog::getId, id, SohuOpenReceiveLog::getResultStatus, OpenReceiveEnums.RequestHandleStatusEnum.WAIT.getCode());
    }

    @Override
    public TableDataInfo<SohuOpenReceiveLogVo> clientList(SohuOpenReceiveLogPageQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SohuOpenReceiveLog> lqw =new LambdaQueryWrapper<>();
        lqw.eq(SohuOpenReceiveLog::getClientId,LoginHelper.getOpenClientId());
        lqw.eq(StrUtil.isNotBlank(bo.getRequestId()),SohuOpenReceiveLog::getRequestId,bo.getRequestId());
        lqw.eq(StrUtil.isNotBlank(bo.getBusinessType()),SohuOpenReceiveLog::getBusinessType,bo.getBusinessType());
        lqw.eq(StrUtil.isNotBlank(bo.getResultStatus()),SohuOpenReceiveLog::getResultStatus,bo.getResultStatus());
        lqw.ge(Objects.nonNull(bo.getRequestTimeStart()), SohuOpenReceiveLog::getRequestTime,bo.getRequestTimeStart());
        lqw.le(Objects.nonNull(bo.getRequestTimeEnd()),SohuOpenReceiveLog::getRequestTime,bo.getRequestTimeEnd());
        Page<SohuOpenReceiveLogVo> result = baseMapper.selectVoPage(PageQueryUtils.build(pageQuery), lqw);
        return TableDataInfoUtils.build(result);
    }

//    /**
//     * 查询批量接收外部消息日志记录列表
//     */
//    @Override
//    public TableDataInfo<SohuOpenReceiveLogVo> queryPageList(SohuOpenReceiveLogBo bo, PageQuery pageQuery) {
//        LambdaQueryWrapper<SohuOpenReceiveLog> lqw = buildQueryWrapper(bo);
//        Page<SohuOpenReceiveLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
//        return TableDataInfo.build(result);
//    }
//
//    /**
//     * 查询批量接收外部消息日志记录列表
//     */
//    @Override
//    public List<SohuOpenReceiveLogVo> queryList(SohuOpenReceiveLogBo bo) {
//        LambdaQueryWrapper<SohuOpenReceiveLog> lqw = buildQueryWrapper(bo);
//        return baseMapper.selectVoList(lqw);
//    }
//
//    private LambdaQueryWrapper<SohuOpenReceiveLog> buildQueryWrapper(SohuOpenReceiveLogBo bo) {
//        Map<String, Object> params = bo.getParams();
//        LambdaQueryWrapper<SohuOpenReceiveLog> lqw = Wrappers.lambdaQuery();
//        lqw.eq(StringUtils.isNotBlank(bo.getBatchNo()), SohuOpenReceiveLog::getBatchNo, bo.getBatchNo());
//        lqw.eq(bo.getBatchTime() != null, SohuOpenReceiveLog::getBatchTime, bo.getBatchTime());
//        lqw.eq(StringUtils.isNotBlank(bo.getClientId()), SohuOpenReceiveLog::getClientId, bo.getClientId());
//        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), SohuOpenReceiveLog::getBusinessType, bo.getBusinessType());
//        lqw.eq(StringUtils.isNotBlank(bo.getMsgText()), SohuOpenReceiveLog::getMsgText, bo.getMsgText());
//        lqw.eq(bo.getProduceTime() != null, SohuOpenReceiveLog::getProduceTime, bo.getProduceTime());
//        lqw.eq(bo.getConsumeTime() != null, SohuOpenReceiveLog::getConsumeTime, bo.getConsumeTime());
//        lqw.eq(StringUtils.isNotBlank(bo.getResultStatus()), SohuOpenReceiveLog::getResultStatus, bo.getResultStatus());
//        lqw.eq(StringUtils.isNotBlank(bo.getErrorMsg()), SohuOpenReceiveLog::getErrorMsg, bo.getErrorMsg());
//        return lqw;
//    }

//    /**
//     * 新增批量接收外部消息日志记录
//     */
//    @Override
//    public Boolean insertByBo(SohuOpenReceiveLogBo bo) {
//        SohuOpenReceiveLog add = BeanUtil.toBean(bo, SohuOpenReceiveLog.class);
//        validEntityBeforeSave(add);
//        boolean flag = baseMapper.insert(add) > 0;
//        if (flag) {
//            bo.setId(add.getId());
//        }
//        return flag;
//    }

//    /**
//     * 修改批量接收外部消息日志记录
//     */
//    @Override
//    public Boolean updateByBo(SohuOpenReceiveLogBo bo) {
//        SohuOpenReceiveLog update = BeanUtil.toBean(bo, SohuOpenReceiveLog.class);
//        validEntityBeforeSave(update);
//        return baseMapper.updateById(update) > 0;
//    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SohuOpenReceiveLog entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除批量接收外部消息日志记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean createReceiveLog(SohuOpenReceiveLog entity) {
        if (StrUtil.isEmpty(entity.getRequestId())) {
            entity.setRequestId(UUID.randomUUID().toString());
            entity.setRequestTime(new Date());
        }
        entity.setProduceTime(new Date());
        entity.setResultStatus(OpenReceiveEnums.RequestHandleStatusEnum.WAIT.getCode());
        entity.setClientId(LoginHelper.getOpenClientId());
        this.baseMapper.insert(entity);
        //发消息到队列
        MqMessaging mqMessaging = new MqMessaging(entity.getId().toString(), MqKeyEnum.OPEN_RECEIVE_LOG.getKey());
        remoteStreamMqService.sendDelayMsg(mqMessaging, 1L);
        return true;
    }

    @Override
    public Boolean updateStatus(Long id, OpenReceiveEnums.RequestHandleStatusEnum statusEnum, String errorMsg) {
        SohuOpenReceiveLog updateEntity = new SohuOpenReceiveLog();
        updateEntity.setId(id);
        updateEntity.setConsumeTime(new Date());
        updateEntity.setResultStatus(statusEnum.getCode());
        updateEntity.setErrorMsg(errorMsg);
        return this.baseMapper.updateById(updateEntity)>0;
    }

}

